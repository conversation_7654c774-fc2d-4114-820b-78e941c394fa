const mysql = require('mysql2/promise');
require('dotenv').config();

async function cleanupDsaDuplicatesOrdered() {
  let connection;
  
  try {
    console.log('🚨 ORDERED DSA DUPLICATE CLEANUP - RESPECTING FOREIGN KEYS');
    console.log('📅 Analysis Date:', new Date().toISOString());
    console.log('🔗 Deleting in order: DSA assignments → sale items → sold barcodes → sales');
    
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT || 3306,
    });

    console.log('✅ Connected to database successfully');

    // Get current state
    const [currentStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_dsa_sales,
        SUM(total_amount) as total_dsa_value,
        COUNT(CASE WHEN receipt_number IS NOT NULL THEN 1 END) as sales_with_receipts,
        COUNT(CASE WHEN receipt_number IS NULL THEN 1 END) as sales_without_receipts
      FROM sales 
      WHERE is_dsa = 1;
    `);

    const stats = currentStats[0];
    console.log(`📊 CURRENT STATE:`);
    console.log(`   Total DSA Sales: ${stats.total_dsa_sales}`);
    console.log(`   Sales without Receipts: ${stats.sales_without_receipts}`);
    console.log(`   Duplicate Rate: ${((stats.sales_without_receipts / stats.total_dsa_sales) * 100).toFixed(2)}%`);
    console.log('');

    if (stats.sales_without_receipts === 0) {
      console.log('✅ No null receipt sales found - cleanup already complete!');
      return;
    }

    // Get all null receipt sale IDs
    const [nullReceiptSales] = await connection.execute(`
      SELECT id, total_amount, customer_id, created_at
      FROM sales 
      WHERE receipt_number IS NULL AND is_dsa = 1
      ORDER BY id;
    `);

    const saleIds = nullReceiptSales.map(sale => sale.id);
    const totalValue = nullReceiptSales.reduce((sum, sale) => sum + parseFloat(sale.total_amount), 0);
    
    console.log(`🎯 Found ${nullReceiptSales.length} null receipt sales to delete`);
    console.log(`💰 Total value to clean: ${totalValue.toFixed(2)} KES`);
    console.log(`📋 Sale ID range: ${Math.min(...saleIds)} to ${Math.max(...saleIds)}`);
    console.log('');

    // Start the cleanup transaction
    console.log('🚀 Starting ordered cleanup transaction...');
    await connection.beginTransaction();

    try {
      // STEP 1: Delete DSA stock assignments
      console.log('🗑️  Step 1: Deleting DSA stock assignments...');
      const deleteDsaAssignmentsQuery = `
        DELETE FROM dsa_stock_assignments 
        WHERE sale_id IN (${saleIds.map(() => '?').join(',')})
      `;
      
      const [deleteDsaResult] = await connection.execute(deleteDsaAssignmentsQuery, saleIds);
      console.log(`   ✅ Deleted ${deleteDsaResult.affectedRows} DSA stock assignments`);

      // STEP 2: Delete sale items
      console.log('🗑️  Step 2: Deleting sale items...');
      const deleteSaleItemsQuery = `
        DELETE FROM sale_items 
        WHERE sale_id IN (${saleIds.map(() => '?').join(',')})
      `;
      
      const [deleteSaleItemsResult] = await connection.execute(deleteSaleItemsQuery, saleIds);
      console.log(`   ✅ Deleted ${deleteSaleItemsResult.affectedRows} sale items`);

      // STEP 3: Delete sold barcodes
      console.log('🗑️  Step 3: Deleting sold barcodes...');
      const deleteSoldBarcodesQuery = `
        DELETE FROM sold_barcodes 
        WHERE sale_id IN (${saleIds.map(() => '?').join(',')})
      `;
      
      const [deleteSoldBarcodesResult] = await connection.execute(deleteSoldBarcodesQuery, saleIds);
      console.log(`   ✅ Deleted ${deleteSoldBarcodesResult.affectedRows} sold barcodes`);

      // STEP 4: Delete sales records
      console.log('🗑️  Step 4: Deleting sales records...');
      const deleteSalesQuery = `
        DELETE FROM sales 
        WHERE id IN (${saleIds.map(() => '?').join(',')})
      `;
      
      const [deleteSalesResult] = await connection.execute(deleteSalesQuery, saleIds);
      console.log(`   ✅ Deleted ${deleteSalesResult.affectedRows} sales records`);

      // STEP 5: Verification
      console.log('🔍 Step 5: Verifying cleanup...');
      
      // Check for remaining null receipt DSA sales
      const [verifyNullSales] = await connection.execute(`
        SELECT COUNT(*) as remaining_null_sales
        FROM sales
        WHERE receipt_number IS NULL AND is_dsa = 1
      `);
      
      // Check for orphaned records
      const [verifyOrphans] = await connection.execute(`
        SELECT 
          (SELECT COUNT(*) FROM sale_items si LEFT JOIN sales s ON si.sale_id = s.id WHERE s.id IS NULL) as orphaned_items,
          (SELECT COUNT(*) FROM dsa_stock_assignments dsa LEFT JOIN sales s ON dsa.sale_id = s.id WHERE s.id IS NULL) as orphaned_assignments,
          (SELECT COUNT(*) FROM sold_barcodes sb LEFT JOIN sales s ON sb.sale_id = s.id WHERE s.id IS NULL) as orphaned_barcodes
      `);

      // Get final statistics
      const [finalStats] = await connection.execute(`
        SELECT 
          COUNT(*) as total_dsa_sales,
          SUM(total_amount) as total_dsa_value,
          COUNT(CASE WHEN receipt_number IS NOT NULL THEN 1 END) as sales_with_receipts,
          COUNT(CASE WHEN receipt_number IS NULL THEN 1 END) as sales_without_receipts
        FROM sales 
        WHERE is_dsa = 1;
      `);

      const finalStatsData = finalStats[0];
      const remainingNullSales = verifyNullSales[0].remaining_null_sales;
      const orphans = verifyOrphans[0];

      console.log('📊 VERIFICATION RESULTS:');
      console.log(`   Remaining null receipt DSA sales: ${remainingNullSales}`);
      console.log(`   Orphaned sale items: ${orphans.orphaned_items}`);
      console.log(`   Orphaned DSA assignments: ${orphans.orphaned_assignments}`);
      console.log(`   Orphaned sold barcodes: ${orphans.orphaned_barcodes}`);
      console.log(`   Final DSA sales count: ${finalStatsData.total_dsa_sales}`);
      console.log(`   Final DSA value: ${finalStatsData.total_dsa_value} KES`);

      if (remainingNullSales === 0 && orphans.orphaned_items === 0 && orphans.orphaned_assignments === 0 && orphans.orphaned_barcodes === 0) {
        console.log('✅ VERIFICATION PASSED: Cleanup successful!');
        await connection.commit();
        console.log('🎉 TRANSACTION COMMITTED SUCCESSFULLY!');
        
        // Final success summary
        console.log('');
        console.log('🎉 MASSIVE DSA DUPLICATE CLEANUP COMPLETED!');
        console.log('');
        console.log('📊 CLEANUP RESULTS:');
        console.log(`   ✅ Deleted ${deleteDsaResult.affectedRows} DSA stock assignments`);
        console.log(`   ✅ Deleted ${deleteSaleItemsResult.affectedRows} sale items`);
        console.log(`   ✅ Deleted ${deleteSoldBarcodesResult.affectedRows} sold barcodes`);
        console.log(`   ✅ Deleted ${deleteSalesResult.affectedRows} duplicate sales`);
        console.log(`   ✅ Cleaned ${totalValue.toFixed(2)} KES worth of duplicate data`);
        console.log(`   ✅ Restored data integrity to 100%`);
        console.log(`   ✅ Eliminated ${((stats.sales_without_receipts / stats.total_dsa_sales) * 100).toFixed(1)}% duplicate rate`);
        console.log('');
        console.log('🎯 FINAL DATABASE STATE:');
        console.log(`   Total DSA Sales: ${finalStatsData.total_dsa_sales}`);
        console.log(`   Total DSA Value: ${finalStatsData.total_dsa_value} KES`);
        console.log(`   Sales with Receipts: ${finalStatsData.sales_with_receipts}`);
        console.log(`   Sales without Receipts: ${finalStatsData.sales_without_receipts}`);
        console.log(`   Duplicate Rate: 0.00%`);
        console.log('');
        console.log('💡 The DSA system now has clean, accurate data!');
        console.log('🚀 Ready to deploy the DSA fix to prevent future duplicates!');
        
      } else {
        console.log('❌ VERIFICATION FAILED: Issues detected');
        console.log(`   - Null sales remaining: ${remainingNullSales}`);
        console.log(`   - Orphaned items: ${orphans.orphaned_items}`);
        console.log(`   - Orphaned assignments: ${orphans.orphaned_assignments}`);
        console.log(`   - Orphaned barcodes: ${orphans.orphaned_barcodes}`);
        await connection.rollback();
        console.log('🔄 TRANSACTION ROLLED BACK FOR SAFETY');
      }

    } catch (deleteError) {
      console.error('❌ Error during ordered cleanup:', deleteError.message);
      await connection.rollback();
      console.log('🔄 Transaction rolled back due to error');
      throw deleteError;
    }

  } catch (error) {
    console.error('❌ Error in ordered DSA cleanup:', error.message);
    console.error('Full error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the ordered cleanup
cleanupDsaDuplicatesOrdered();
