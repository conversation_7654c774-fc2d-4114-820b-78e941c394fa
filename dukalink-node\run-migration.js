const { Sequelize, DataTypes } = require('sequelize');
const config = require('./config/database');

// Specify which migration to run
const migrationName = process.argv[2] || '';

async function runMigration() {
  try {
    if (migrationName === '20240530000000' || migrationName === '') {
      // Run the migration to add the assignment_identifier column to dsa_stock_reconciliations
      const migration = require('./migrations/20240530000000-add-assignment-identifier-to-dsa-reconciliations');
      console.log('Running migration: add-assignment-identifier-to-dsa-reconciliations');
      try {
        await migration.up(config.getQueryInterface(), Sequelize);
        console.log('Successfully added assignment_identifier column to dsa_stock_reconciliations table');
      } catch (err) {
        if (err.message.includes('column already exists') ||
            err.message.includes('duplicate column') ||
            (err.parent && err.parent.code === 'ER_DUP_FIELDNAME')) {
          console.log('Column already exists, skipping...');
        } else {
          throw err;
        }
      }
    }

    if (migrationName === '20240530000001' || migrationName === '') {
      // Run the migration to add indexes
      const migration = require('./migrations/20240530000001-add-assignment-identifier-indexes');
      console.log('Running migration: add-assignment-identifier-indexes');
      try {
        await migration.up(config.getQueryInterface(), Sequelize);
        console.log('Successfully added indexes for assignment_identifier');
      } catch (err) {
        if (err.message.includes('index already exists') ||
            err.message.includes('duplicate key') ||
            (err.parent && (err.parent.code === 'ER_DUP_KEYNAME' || err.parent.code === 'ER_MULTIPLE_PRI_KEY'))) {
          console.log('Indexes already exist, skipping...');
        } else {
          throw err;
        }
      }
    }

    if (migrationName === '20250115-add-sale-id' || migrationName === '') {
      // Run the migration to add sale_id to dsa_stock_assignments
      const migration = require('./migrations/20250115-add-sale-id-to-dsa-stock-assignments');
      console.log('Running migration: add-sale-id-to-dsa-stock-assignments');
      try {
        await migration.up(config.getQueryInterface(), Sequelize);
        console.log('Successfully added sale_id column to dsa_stock_assignments table');
      } catch (err) {
        if (err.message.includes('column already exists') ||
            err.message.includes('duplicate column') ||
            (err.parent && err.parent.code === 'ER_DUP_FIELDNAME')) {
          console.log('Column already exists, skipping...');
        } else {
          throw err;
        }
      }
    }

    if (migrationName === '20250115-populate-sale-id' || migrationName === '') {
      // Run the data migration to populate sale_id
      const migration = require('./migrations/20250115-populate-sale-id-in-dsa-assignments');
      console.log('Running migration: populate-sale-id-in-dsa-assignments');
      try {
        await migration.up(config.getQueryInterface(), Sequelize);
        console.log('Successfully populated sale_id in existing dsa_stock_assignments');
      } catch (err) {
        console.error('Error in data migration:', err.message);
        // Don't throw for data migration errors, just log them
      }
    }

    console.log('Migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    console.error(error.stack);
    process.exit(1);
  }
}

runMigration();
