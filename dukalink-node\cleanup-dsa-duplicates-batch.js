const mysql = require('mysql2/promise');
require('dotenv').config();

async function cleanupDsaDuplicatesBatch() {
  let connection;
  
  try {
    console.log('🚨 BATCH DSA DUPLICATE CLEANUP - ALL HISTORICAL DATA');
    console.log('📅 Analysis Date:', new Date().toISOString());
    console.log('⚠️  Processing in smaller batches for better performance');
    
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT || 3306,
    });

    console.log('✅ Connected to database successfully');

    // Get current state
    const [currentStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_dsa_sales,
        SUM(total_amount) as total_dsa_value,
        COUNT(CASE WHEN receipt_number IS NOT NULL THEN 1 END) as sales_with_receipts,
        COUNT(CASE WHEN receipt_number IS NULL THEN 1 END) as sales_without_receipts
      FROM sales 
      WHERE is_dsa = 1;
    `);

    const stats = currentStats[0];
    console.log(`📊 CURRENT STATE:`);
    console.log(`   Total DSA Sales: ${stats.total_dsa_sales}`);
    console.log(`   Sales without Receipts: ${stats.sales_without_receipts}`);
    console.log(`   Duplicate Rate: ${((stats.sales_without_receipts / stats.total_dsa_sales) * 100).toFixed(2)}%`);
    console.log('');

    if (stats.sales_without_receipts === 0) {
      console.log('✅ No null receipt sales found - cleanup already complete!');
      return;
    }

    // Get all null receipt sale IDs in batches
    const [nullReceiptSales] = await connection.execute(`
      SELECT id, total_amount, customer_id, created_at
      FROM sales 
      WHERE receipt_number IS NULL AND is_dsa = 1
      ORDER BY id;
    `);

    console.log(`🎯 Found ${nullReceiptSales.length} null receipt sales to delete`);
    
    // Process in batches of 10 sales at a time
    const BATCH_SIZE = 10;
    let totalDeleted = 0;
    let totalItemsDeleted = 0;
    let totalValueDeleted = 0;

    for (let i = 0; i < nullReceiptSales.length; i += BATCH_SIZE) {
      const batch = nullReceiptSales.slice(i, i + BATCH_SIZE);
      const batchIds = batch.map(sale => sale.id);
      const batchValue = batch.reduce((sum, sale) => sum + parseFloat(sale.total_amount), 0);
      
      console.log(`🔄 Processing batch ${Math.floor(i/BATCH_SIZE) + 1}/${Math.ceil(nullReceiptSales.length/BATCH_SIZE)}: Sales ${batchIds[0]} to ${batchIds[batchIds.length-1]}`);
      
      try {
        // Start transaction for this batch
        await connection.beginTransaction();
        
        // Delete sale items for this batch
        const deleteItemsQuery = `
          DELETE FROM sale_items 
          WHERE sale_id IN (${batchIds.map(() => '?').join(',')})
        `;
        
        const [deleteItemsResult] = await connection.execute(deleteItemsQuery, batchIds);
        console.log(`   ✅ Deleted ${deleteItemsResult.affectedRows} sale items`);
        
        // Delete sales for this batch
        const deleteSalesQuery = `
          DELETE FROM sales 
          WHERE id IN (${batchIds.map(() => '?').join(',')})
        `;
        
        const [deleteSalesResult] = await connection.execute(deleteSalesQuery, batchIds);
        console.log(`   ✅ Deleted ${deleteSalesResult.affectedRows} sales`);
        
        // Commit this batch
        await connection.commit();
        
        totalDeleted += deleteSalesResult.affectedRows;
        totalItemsDeleted += deleteItemsResult.affectedRows;
        totalValueDeleted += batchValue;
        
        console.log(`   📊 Batch complete: ${deleteSalesResult.affectedRows} sales, ${deleteItemsResult.affectedRows} items, ${batchValue.toFixed(2)} KES`);
        
        // Small delay to prevent overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (batchError) {
        console.error(`   ❌ Error in batch: ${batchError.message}`);
        await connection.rollback();
        console.log(`   🔄 Batch rolled back, continuing with next batch...`);
      }
    }

    // Final verification
    console.log('');
    console.log('🔍 Final verification...');
    
    const [finalStats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_dsa_sales,
        SUM(total_amount) as total_dsa_value,
        COUNT(CASE WHEN receipt_number IS NOT NULL THEN 1 END) as sales_with_receipts,
        COUNT(CASE WHEN receipt_number IS NULL THEN 1 END) as sales_without_receipts
      FROM sales 
      WHERE is_dsa = 1;
    `);

    const finalData = finalStats[0];
    
    console.log('📊 FINAL RESULTS:');
    console.log(`   ✅ Deleted ${totalDeleted} duplicate sales`);
    console.log(`   ✅ Removed ${totalItemsDeleted} duplicate sale items`);
    console.log(`   ✅ Cleaned ${totalValueDeleted.toFixed(2)} KES worth of duplicate data`);
    console.log('');
    console.log(`📊 FINAL DATABASE STATE:`);
    console.log(`   Total DSA Sales: ${finalData.total_dsa_sales}`);
    console.log(`   Total DSA Value: ${finalData.total_dsa_value} KES`);
    console.log(`   Sales with Receipts: ${finalData.sales_with_receipts}`);
    console.log(`   Sales without Receipts: ${finalData.sales_without_receipts}`);
    console.log(`   Duplicate Rate: ${finalData.sales_without_receipts === 0 ? '0.00' : ((finalData.sales_without_receipts / finalData.total_dsa_sales) * 100).toFixed(2)}%`);
    
    if (finalData.sales_without_receipts === 0) {
      console.log('');
      console.log('🎉 MASSIVE CLEANUP COMPLETED SUCCESSFULLY!');
      console.log('✅ All duplicate DSA sales have been eliminated');
      console.log('✅ Data integrity restored to 100%');
      console.log('✅ Financial reports now show accurate data');
    } else {
      console.log('');
      console.log(`⚠️  ${finalData.sales_without_receipts} null receipt sales still remain`);
      console.log('   Some batches may have failed - check logs above');
    }

  } catch (error) {
    console.error('❌ Error in batch DSA cleanup:', error.message);
    console.error('Full error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the batch cleanup
cleanupDsaDuplicatesBatch();
