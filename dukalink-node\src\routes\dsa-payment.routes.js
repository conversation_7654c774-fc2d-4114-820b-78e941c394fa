const express = require('express');
const router = express.Router();
const dsaPaymentController = require('../controllers/dsa-payment.controller');
const enhancedDsaPaymentController = require("../controllers/enhanced-dsa-payment.controller");
const { authenticate } = require('../middleware/auth');
const rbac = require('../middleware/rbac.middleware');

/**
 * @swagger
 * /api/v1/dsa-payments:
 *   post:
 *     summary: Create a new DSA payment
 *     description: Record a payment for a DSA stock assignment
 *     tags: [DSA Payments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assignment_identifier
 *               - customer_id
 *               - branch_id
 *             properties:
 *               assignment_identifier:
 *                 type: string
 *                 description: The identifier of the assignment batch
 *               customer_id:
 *                 type: integer
 *                 description: The ID of the DSA agent (customer)
 *               branch_id:
 *                 type: integer
 *                 description: The ID of the branch
 *               cash_amount:
 *                 type: number
 *                 description: The amount paid in cash
 *               paybill_amount:
 *                 type: number
 *                 description: The amount paid via paybill
 *               notes:
 *                 type: string
 *                 description: Additional notes about the payment
 *     responses:
 *       201:
 *         description: Payment created successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA agent or branch not found
 *       500:
 *         description: Server error
 */
router.post('/', authenticate, rbac.checkPermission('dsa', 'create'), dsaPaymentController.createPayment);

/**
 * @swagger
 * /api/v1/dsa-payments/assignment/{assignment_identifier}:
 *   get:
 *     summary: Get payments for a DSA assignment
 *     description: Retrieve all payments for a specific DSA assignment
 *     tags: [DSA Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: assignment_identifier
 *         required: true
 *         schema:
 *           type: string
 *         description: The identifier of the assignment batch
 *     responses:
 *       200:
 *         description: Payments retrieved successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: No payments found
 *       500:
 *         description: Server error
 */
router.get('/assignment/:assignment_identifier', authenticate, rbac.checkPermission('dsa', 'read'), dsaPaymentController.getPaymentsByAssignment);

/**
 * @swagger
 * /api/v1/dsa-payments/cash-collections/branch/{branch_id}:
 *   get:
 *     summary: Get DSA cash collections for a branch
 *     description: Retrieve all DSA cash collections for a specific branch and date range
 *     tags: [DSA Payments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: branch_id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the branch
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for filtering (YYYY-MM-DD). Defaults to today if not provided.
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for filtering (YYYY-MM-DD). Defaults to today if not provided.
 *       - in: query
 *         name: shift_id
 *         schema:
 *           type: integer
 *         description: POS Session ID to filter collections for a specific shift period. Takes precedence over date parameters.
 *     responses:
 *       200:
 *         description: DSA cash collections retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     branch_id:
 *                       type: integer
 *                     date_range:
 *                       type: object
 *                       properties:
 *                         start_date:
 *                           type: string
 *                         end_date:
 *                           type: string
 *                     summary:
 *                       type: object
 *                       properties:
 *                         total_cash_collected:
 *                           type: number
 *                         total_paybill_amount:
 *                           type: number
 *                         total_payments:
 *                           type: integer
 *                         dsa_count:
 *                           type: integer
 *                     collections_by_dsa:
 *                       type: array
 *                       items:
 *                         type: object
 *                     all_payments:
 *                       type: array
 *                       items:
 *                         type: object
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/cash-collections/branch/:branch_id', authenticate, rbac.checkPermission('dsa', 'read'), dsaPaymentController.getDsaCashCollections);


// Enhanced DSA Payment Routes
router.post('/enhanced', authenticate, rbac.checkPermission('dsa', 'create'), enhancedDsaPaymentController.createEnhancedPayment);
router.post('/validate-amount', authenticate, rbac.checkPermission('dsa', 'read'), enhancedDsaPaymentController.validatePaymentAmount);

module.exports = router;

