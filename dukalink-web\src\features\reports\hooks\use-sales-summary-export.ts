import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import salesSummaryExportService, {
  SalesSummaryExportParams,
  CustomSalesSummaryExportParams,
} from "../api/sales-summary-export-service";

/**
 * Enhanced hook for comprehensive sales summary export
 * Exports all data with multiple sheets
 */
export const useSalesSummaryExportAll = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: SalesSummaryExportParams) => {
      // Validate parameters
      const validation = salesSummaryExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await salesSummaryExportService.exportAllSalesSummary(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = salesSummaryExportService.generateFilename(params, 'comprehensive');
        salesSummaryExportService.downloadBlob(blob, filename);

        toast.success("Sales summary exported successfully!", {
          description: `Comprehensive report with all data sheets downloaded as ${filename}`,
          duration: 5000,
        });

        // Invalidate related queries to refresh any cached data
        queryClient.invalidateQueries({ queryKey: ["sales-summary"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Export error:', error);

      let errorMessage = "Failed to export sales summary";

      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export sales reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try filtering the data or use summary export.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for custom sales summary export
 * Allows format and sheet selection
 */
export const useSalesSummaryExportCustom = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: CustomSalesSummaryExportParams) => {
      // Validate parameters
      const validation = salesSummaryExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await salesSummaryExportService.exportCustomSalesSummary(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const formatType = params.format_type || 'detailed';
        const filename = salesSummaryExportService.generateFilename(params, formatType);
        salesSummaryExportService.downloadBlob(blob, filename);

        toast.success("Custom sales summary export completed!", {
          description: `${formatType.charAt(0).toUpperCase() + formatType.slice(1)} report downloaded as ${filename}`,
          duration: 5000,
        });

        queryClient.invalidateQueries({ queryKey: ["sales-summary"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Custom export error:', error);

      let errorMessage = "Failed to export custom sales summary";

      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export sales reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try using summary format or filtering the data.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Custom Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for quick summary export
 * Lightweight export with essential data only
 */
export const useSalesSummaryExportSummary = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: SalesSummaryExportParams) => {
      const validation = salesSummaryExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await salesSummaryExportService.exportSummarySales(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = salesSummaryExportService.generateFilename(params, 'summary');
        salesSummaryExportService.downloadBlob(blob, filename);

        toast.success("Summary export completed!", {
          description: `Quick summary report downloaded as ${filename}`,
          duration: 4000,
        });

        queryClient.invalidateQueries({ queryKey: ["sales-summary"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Summary export error:', error);

      let errorMessage = "Failed to export summary sales data";

      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export sales reports";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Summary Export Failed", {
        description: errorMessage,
        duration: 6000,
      });
    },
  });
};

/**
 * Hook for ultra-lightweight export
 * Fastest export with basic sales data only
 */
export const useSalesSummaryExportLightweight = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: SalesSummaryExportParams) => {
      const validation = salesSummaryExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await salesSummaryExportService.exportLightweightSales(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = salesSummaryExportService.generateFilename(params, 'lightweight');
        salesSummaryExportService.downloadBlob(blob, filename);

        toast.success("Quick export completed!", {
          description: `Basic sales data downloaded as ${filename}`,
          duration: 3000,
        });

        queryClient.invalidateQueries({ queryKey: ["sales-summary"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Lightweight export error:', error);

      let errorMessage = "Failed to export sales data";

      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export sales reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. The system may be busy, please try again.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Quick Export Failed", {
        description: errorMessage,
        duration: 5000,
      });
    },
  });
};

/**
 * Hook to get export recommendations based on data size
 */
export const useSalesSummaryExportRecommendation = (estimatedRecords: number) => {
  return salesSummaryExportService.getExportRecommendation(estimatedRecords);
};

/**
 * Hook to get export format options for UI
 */
export const useSalesSummaryExportFormatOptions = () => {
  return salesSummaryExportService.getExportFormatOptions();
};

/**
 * Hook to get column options for custom export
 */
export const useSalesSummaryExportColumnOptions = () => {
  return salesSummaryExportService.getColumnOptions();
};

/**
 * Combined hook that provides all export functionality
 * Convenient single hook for components that need multiple export options
 */
export const useSalesSummaryExportSuite = () => {
  const exportAll = useSalesSummaryExportAll();
  const exportCustom = useSalesSummaryExportCustom();
  const exportSummary = useSalesSummaryExportSummary();
  const exportLightweight = useSalesSummaryExportLightweight();

  const isAnyExporting = exportAll.isPending || exportCustom.isPending || exportSummary.isPending || exportLightweight.isPending;

  return {
    // Individual export methods
    exportAll: exportAll.mutateAsync,
    exportCustom: exportCustom.mutateAsync,
    exportSummary: exportSummary.mutateAsync,
    exportLightweight: exportLightweight.mutateAsync,

    // Loading states
    isExportingAll: exportAll.isPending,
    isExportingCustom: exportCustom.isPending,
    isExportingSummary: exportSummary.isPending,
    isExportingLightweight: exportLightweight.isPending,
    isAnyExporting,

    // Error states
    exportAllError: exportAll.error,
    exportCustomError: exportCustom.error,
    exportSummaryError: exportSummary.error,
    exportLightweightError: exportLightweight.error,

    // Utility functions
    getRecommendation: salesSummaryExportService.getExportRecommendation,
    getFormatOptions: salesSummaryExportService.getExportFormatOptions,
    getColumnOptions: salesSummaryExportService.getColumnOptions,
    validateParams: salesSummaryExportService.validateExportParams,
    generateFilename: salesSummaryExportService.generateFilename,

    // Reset functions
    resetAll: () => {
      exportAll.reset();
      exportCustom.reset();
      exportSummary.reset();
      exportLightweight.reset();
    },
  };
};
