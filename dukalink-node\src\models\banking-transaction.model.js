const { DataTypes } = require('sequelize');
const sequelize = require('../../config/database');

const BankingTransaction = sequelize.define('BankingTransaction', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  tenant_id: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  branch_id: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  pos_session_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'POS Session ID when banking transaction was recorded'
  },
  transaction_date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'Date of the banking transaction'
  },
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false
  },
  banking_method: {
    type: DataTypes.ENUM('bank', 'agent', 'mpesa'),
    allowNull: false,
    comment: 'Method used for banking: bank, agent, or mpesa'
  },
  bank_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'ID of the bank or agent used for the transaction'
  },
  reference_number: {
    type: DataTypes.STRING(100),
    allowNull: true,
    comment: 'TID/CBSREF for bank/agent, transaction_id for MPESA - Optional for CBA bank'
  },

  status: {
    type: DataTypes.ENUM('pending', 'completed', 'failed', 'reconciled'),
    allowNull: false,
    defaultValue: 'pending',
    comment: 'Status of the banking transaction. Only changes to completed after approval by company admin.'
  },
  approved_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'ID of the user who approved or rejected the transaction'
  },
  approval_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Date when the transaction was approved or rejected'
  },
  rejection_reason: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Reason for rejection if the transaction was rejected'
  },
  expected_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'Expected amount to be banked based on sales'
  },
  discrepancy: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'Difference between expected and actual banked amount'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  last_updated_by: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  deleted_at: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'banking_transactions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  deletedAt: 'deleted_at',
  paranoid: true // Soft deletes
});

module.exports = BankingTransaction;
