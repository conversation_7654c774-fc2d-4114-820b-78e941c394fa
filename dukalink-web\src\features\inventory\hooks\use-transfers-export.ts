import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import transfersExportService, {
  TransfersExportParams,
  CustomTransfersExportParams,
} from "../api/transfers-export-service";

/**
 * Enhanced hook for comprehensive transfers export
 * Exports all data with multiple sheets
 */
export const useTransfersExportAll = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: TransfersExportParams) => {
      // Validate parameters
      const validation = transfersExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await transfersExportService.exportAllTransfers(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = transfersExportService.generateFilename(params, 'comprehensive');
        transfersExportService.downloadBlob(blob, filename);
        
        toast.success("Transfers exported successfully!", {
          description: `Comprehensive report with all data sheets downloaded as ${filename}`,
          duration: 5000,
        });

        // Invalidate related queries to refresh any cached data
        queryClient.invalidateQueries({ queryKey: ["stock-transfers"] });
        queryClient.invalidateQueries({ queryKey: ["stock-movements"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Export error:', error);
      
      let errorMessage = "Failed to export transfers";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export transfer reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try filtering the data or use summary export.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for custom transfers export
 * Allows format and column selection
 */
export const useTransfersExportCustom = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: CustomTransfersExportParams) => {
      // Validate parameters
      const validation = transfersExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await transfersExportService.exportCustomTransfers(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const formatType = params.format_type || 'detailed';
        const filename = transfersExportService.generateFilename(params, formatType);
        transfersExportService.downloadBlob(blob, filename);
        
        toast.success("Custom transfers export completed!", {
          description: `${formatType.charAt(0).toUpperCase() + formatType.slice(1)} report downloaded as ${filename}`,
          duration: 5000,
        });

        queryClient.invalidateQueries({ queryKey: ["stock-transfers"] });
        queryClient.invalidateQueries({ queryKey: ["stock-movements"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Custom export error:', error);
      
      let errorMessage = "Failed to export custom transfers";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export transfer reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try using summary format or filtering the data.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Custom Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for quick summary export
 * Lightweight export with essential data only
 */
export const useTransfersExportSummary = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: TransfersExportParams) => {
      const validation = transfersExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await transfersExportService.exportSummaryTransfers(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = transfersExportService.generateFilename(params, 'summary');
        transfersExportService.downloadBlob(blob, filename);
        
        toast.success("Summary export completed!", {
          description: `Quick summary report downloaded as ${filename}`,
          duration: 4000,
        });

        queryClient.invalidateQueries({ queryKey: ["stock-transfers"] });
        queryClient.invalidateQueries({ queryKey: ["stock-movements"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Summary export error:', error);
      
      let errorMessage = "Failed to export summary transfers";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export transfer reports";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Summary Export Failed", {
        description: errorMessage,
        duration: 6000,
      });
    },
  });
};

/**
 * Hook to get export recommendations based on data size
 */
export const useTransfersExportRecommendation = (estimatedRecords: number) => {
  return transfersExportService.getExportRecommendation(estimatedRecords);
};

/**
 * Hook to get export format options for UI
 */
export const useTransfersExportFormatOptions = () => {
  return transfersExportService.getExportFormatOptions();
};

/**
 * Hook to get column options for custom export
 */
export const useTransfersExportColumnOptions = () => {
  return transfersExportService.getColumnOptions();
};

/**
 * Combined hook that provides all export functionality
 * Convenient single hook for components that need multiple export options
 */
export const useTransfersExportSuite = () => {
  const exportAll = useTransfersExportAll();
  const exportCustom = useTransfersExportCustom();
  const exportSummary = useTransfersExportSummary();

  const isAnyExporting = exportAll.isPending || exportCustom.isPending || exportSummary.isPending;

  return {
    // Individual export methods
    exportAll: exportAll.mutateAsync,
    exportCustom: exportCustom.mutateAsync,
    exportSummary: exportSummary.mutateAsync,
    
    // Loading states
    isExportingAll: exportAll.isPending,
    isExportingCustom: exportCustom.isPending,
    isExportingSummary: exportSummary.isPending,
    isAnyExporting,
    
    // Error states
    exportAllError: exportAll.error,
    exportCustomError: exportCustom.error,
    exportSummaryError: exportSummary.error,
    
    // Utility functions
    getRecommendation: transfersExportService.getExportRecommendation,
    getFormatOptions: transfersExportService.getExportFormatOptions,
    getColumnOptions: transfersExportService.getColumnOptions,
    validateParams: transfersExportService.validateExportParams,
    generateFilename: transfersExportService.generateFilename,
    
    // Reset functions
    resetAll: () => {
      exportAll.reset();
      exportCustom.reset();
      exportSummary.reset();
    },
  };
};
