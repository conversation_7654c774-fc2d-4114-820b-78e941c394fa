const User = require("./user.model");
const Tenant = require("./tenant.model");
const Branch = require("./branch.model");
const Region = require("./region.model");
const Product = require("./product.model");
const ProductCategory = require("./product-category.model");
const ProductDiscount = require("./product-discount.model");
const Brand = require("./brand.model");
const BrandType = require("./brand-type.model");
const VatRate = require("./vat-rate.model");
const Supplier = require("./supplier.model");
const StockItem = require("./stock-item.model");
const InventoryItem = require("./inventory-item.model");
const SoldBarcode = require("./sold-barcode.model");
const Customer = require("./customer.model");
const Employee = require("./employee.model");
const PhoneRepair = require("./phone-repair.model");
const PhoneRepairStatus = require("./phone-repair-status.model");
const PhoneRepairTransfer = require("./phone-repair-transfer.model");
const Bank = require("./bank.model");
const CreditPartner = require("./credit-partner.model");
const ExpenseCategory = require("./expense-category.model");
const ExpenseCategoryGroup = require("./expense-category-group.model");
const Expense = require("./expense.model");
const Purchase = require("./purchase.model");
const PurchaseItem = require("./purchase-item.model");
const PurchaseReturn = require("./purchase-return.model");
const PurchaseReturnItem = require("./purchase-return-item.model");
const InventoryTransaction = require("./inventory-transaction.model");
// DsaAgent model removed - now using User model with is_dsa flag
const DsaStockAssignment = require("./dsa-stock-assignment.model");
const DsaPayment = require("./dsa-payment.model");
// DsaSale model removed - now using Sale model with is_dsa flag
const setupDsaAssociations = require("./dsa-associations");
const setupExpenseAssociations = require("./expense-associations");
// Procurement models
const ProcurementRequest = require("./procurement-request.model");
const ProcurementRequestItem = require("./procurement-request-item.model");
const ProcurementReceipt = require("./procurement-receipt.model");
const ProcurementReceiptItem = require("./procurement-receipt-item.model");
const setupProcurementAssociations = require("./procurement-associations");
// MpesaFloatAllocation model removed
// MpesaFloatAssignment model removed
const MpesaFloatBalance = require("./mpesaFloatBalance.model");
const MpesaFloatMovement = require("./mpesa-float-movement.model");
const MpesaFloatTopup = require("./mpesa-float-topup.model");
const MpesaTransaction = require("./mpesa-transaction.model");
const MpesaTransactionAudit = require("./mpesa-transaction-audit.model");
const MpesaTransactionReceipt = require("./mpesa-transaction-receipt.model");
const PosSession = require("./pos-session.model");
const PosSessionReconciliation = require("./pos-session-reconciliation.model");
const DsaStockReconciliation = require("./dsa-stock-reconciliation.model");
const MpesaFloatReconciliation = require("./mpesa-float-reconciliation.model");
const Sale = require("./sale.model");
const SaleItem = require("./sale-item.model");
const PaymentMethod = require("./payment-method.model");
const BankingTransaction = require("./banking-transaction.model");
const BankingTransactionReceipt = require("./banking-transaction-receipt.model");
const StockMovement = require("./stock-movement.model");
const StockMovementItem = require("./stock-movement-item.model");
const CashTransaction = require("./cash-transaction.model");
const Location = require("./location.model");
const Role = require("./role.model");
const Permission = require("./permission.model");
const RolePermission = require("./role-permission.model");
const RBACGrants = require("./rbac-grants.model");
const StockAdjustment = require("./stockAdjustment.model");
const StockAdjustmentItem = require("./stockAdjustmentItem.model");
const StockAdjustmentType = require("./stockAdjustmentType.model");
const Invoice = require("./invoice.model");
const InvoiceItem = require("./invoice-item.model");
const InvoicePayment = require("./invoice-payment.model");
const CreditNote = require("./credit-note.model");
const CreditNoteItem = require("./credit-note-item.model");
const setupCreditNoteAssociations = require("./credit-note-associations");
const TimsResponseLog = require("./tims-response-log.model");
const StockSnapshot = require("./stock-snapshot.model");

// Import sequelize instance
const sequelize = require("../../config/database");

// Define associations

// Setup DSA-specific associations
setupDsaAssociations();

// Setup Expense-specific associations
setupExpenseAssociations();

// Setup Credit Note-specific associations
setupCreditNoteAssociations();

// Setup Procurement-specific associations
setupProcurementAssociations({
  ProcurementRequest,
  ProcurementRequestItem,
  ProcurementReceipt,
  ProcurementReceiptItem,
  Product,
  Supplier,
  Branch,
  User,
  Tenant,
});

// Tenant associations
Tenant.hasMany(Branch, { foreignKey: "tenant_id" });
Tenant.hasMany(User, { foreignKey: "tenant_id" });
Tenant.hasMany(Product, { foreignKey: "tenant_id" });
Tenant.hasMany(ProductCategory, { foreignKey: "tenant_id" });
Tenant.hasMany(Customer, { foreignKey: "tenant_id" });
// Tenant.hasMany(DsaAgent, { foreignKey: 'tenant_id' }); - Removed
// Tenant.hasMany(MpesaFloatAllocation, { foreignKey: 'tenant_id' }); - Removed
Tenant.hasMany(Location, { foreignKey: "tenant_id" });
Tenant.hasMany(StockMovement, { foreignKey: "tenant_id" });
Tenant.hasMany(MpesaFloatMovement, { foreignKey: "tenant_id" });
Tenant.hasMany(MpesaFloatBalance, { foreignKey: "tenant_id" });
Tenant.hasMany(MpesaFloatTopup, { foreignKey: "tenant_id" });
Tenant.hasMany(StockItem, { foreignKey: "tenant_id" });
Tenant.hasMany(BankingTransaction, { foreignKey: "tenant_id" });
// Procurement associations
Tenant.hasMany(ProcurementRequest, { foreignKey: "tenant_id" });
Tenant.hasMany(ProcurementRequestItem, { foreignKey: "tenant_id" });
Tenant.hasMany(ProcurementReceipt, { foreignKey: "tenant_id" });
Tenant.hasMany(ProcurementReceiptItem, { foreignKey: "tenant_id" });

// Region associations
Region.hasMany(Branch, { foreignKey: "region_id" });

// Branch associations
Branch.belongsTo(Tenant, { foreignKey: "tenant_id" });
Branch.belongsTo(Region, { foreignKey: "region_id" });
Branch.hasMany(User, { foreignKey: "branch_id" });
Branch.hasMany(StockItem, { foreignKey: "branch_id" });
Branch.hasMany(StockSnapshot, { foreignKey: "branch_id" });
Branch.hasMany(PhoneRepair, { foreignKey: "branch_id" });
Branch.hasMany(PhoneRepair, {
  foreignKey: "current_location_id",
  as: "CurrentPhoneRepairs",
});
Branch.hasMany(PhoneRepairTransfer, {
  foreignKey: "from_branch_id",
  as: "OutgoingPhoneRepairTransfers",
});
Branch.hasMany(PhoneRepairTransfer, {
  foreignKey: "to_branch_id",
  as: "IncomingPhoneRepairTransfers",
});
// Branch.hasMany(DsaAgent, { foreignKey: 'branch_id' }); - Removed
Branch.hasMany(DsaStockAssignment, { foreignKey: "branch_id" });
// Branch.hasMany(MpesaFloatAllocation, { foreignKey: 'branch_id' }); - Removed
// Branch.hasMany(MpesaFloatAssignment, { foreignKey: 'branch_id' }); - Removed
Branch.hasMany(MpesaTransaction, { foreignKey: "branch_id" });
Branch.hasMany(PosSession, { foreignKey: "branch_id" });
Branch.hasMany(MpesaFloatReconciliation, { foreignKey: "branch_id" });
Branch.hasMany(BankingTransaction, { foreignKey: "branch_id" });
Branch.hasMany(StockMovement, {
  as: "OutgoingMovements",
  foreignKey: "from_branch_id",
});
Branch.hasMany(MpesaFloatMovement, {
  as: "OutgoingMpesaFloatMovements",
  foreignKey: "from_branch_id",
});
Branch.hasMany(MpesaFloatMovement, {
  as: "IncomingMpesaFloatMovements",
  foreignKey: "to_branch_id",
});
Branch.hasMany(StockMovement, {
  as: "IncomingMovements",
  foreignKey: "to_branch_id",
});

// User associations
User.belongsTo(Tenant, { foreignKey: "tenant_id" });
User.belongsTo(Branch, { foreignKey: "branch_id" });
User.belongsTo(Location, { foreignKey: "location_id" });
User.belongsTo(Role, { foreignKey: "role_id" });
User.hasMany(PhoneRepair, {
  foreignKey: "received_by",
  as: "ReceivedPhoneRepairs",
});
User.hasMany(PhoneRepairStatus, {
  foreignKey: "changed_by",
  as: "ChangedPhoneRepairStatuses",
});
User.hasMany(PhoneRepairTransfer, {
  foreignKey: "transferred_by",
  as: "InitiatedPhoneRepairTransfers",
});
User.hasMany(PhoneRepairTransfer, {
  foreignKey: "received_by",
  as: "ReceivedPhoneRepairTransfers",
});
// User.hasMany(MpesaFloatAssignment, { foreignKey: 'user_id' }); - Removed
User.hasMany(MpesaTransaction, { foreignKey: "user_id" });
User.hasMany(MpesaTransactionAudit, { foreignKey: "performed_by" });
User.hasMany(PosSession, { foreignKey: "user_id" });
User.hasMany(MpesaFloatReconciliation, { foreignKey: "user_id" });
User.hasMany(MpesaFloatTopup, {
  foreignKey: "created_by",
  as: "CreatedMpesaFloatTopups",
});
User.hasMany(MpesaFloatTopup, {
  foreignKey: "last_updated_by",
  as: "UpdatedMpesaFloatTopups",
});
// DSA associations are set up in setupDsaAssociations()

// ProductCategory associations
ProductCategory.belongsTo(Tenant, { foreignKey: "tenant_id" });
ProductCategory.hasMany(Product, { foreignKey: "category_id" });

// Self-referential associations for category hierarchy
ProductCategory.belongsTo(ProductCategory, {
  foreignKey: "parent_id",
  as: "Parent",
});
ProductCategory.hasMany(ProductCategory, {
  foreignKey: "parent_id",
  as: "Subcategories",
});

// ProductDiscount associations
ProductDiscount.belongsTo(Product, { foreignKey: "product_id" });
ProductDiscount.belongsTo(Tenant, { foreignKey: "tenant_id" });

// Brand associations
Brand.belongsTo(Tenant, { foreignKey: "tenant_id" });
Brand.hasMany(BrandType, { foreignKey: "brand_id" });
Brand.hasMany(Product, { foreignKey: "brand_id" });
Brand.belongsTo(User, { foreignKey: "created_by", as: "Creator" });
Brand.belongsTo(User, { foreignKey: "last_updated_by", as: "Updater" });

// BrandType associations
BrandType.belongsTo(Brand, { foreignKey: "brand_id" });
BrandType.belongsTo(Tenant, { foreignKey: "tenant_id" });
BrandType.hasMany(Product, { foreignKey: "brand_type_id" });
BrandType.belongsTo(User, { foreignKey: "created_by", as: "Creator" });
BrandType.belongsTo(User, { foreignKey: "last_updated_by", as: "Updater" });

// Product associations
Product.belongsTo(Tenant, { foreignKey: "tenant_id" });
Product.belongsTo(ProductCategory, { foreignKey: "category_id" });
Product.belongsTo(Brand, { foreignKey: "brand_id" });
Product.belongsTo(BrandType, { foreignKey: "brand_type_id" });
Product.hasMany(StockItem, { foreignKey: "product_id" });
Product.hasMany(ProductDiscount, { foreignKey: "product_id" });
Product.hasMany(StockSnapshot, { foreignKey: "product_id" });
// HqStockItem associations removed
Product.hasMany(DsaStockAssignment, { foreignKey: "product_id" });
// Product.hasMany(DsaSale, { foreignKey: 'product_id' }); - Removed

// StockItem associations
StockItem.belongsTo(Branch, { foreignKey: "branch_id" });
StockItem.belongsTo(Product, { foreignKey: "product_id" });
StockItem.belongsTo(Tenant, { foreignKey: "tenant_id" });
StockItem.hasMany(InventoryItem, { foreignKey: "stock_item_id" });
StockItem.hasMany(InventoryTransaction, { foreignKey: "stock_item_id" });
StockItem.hasMany(StockSnapshot, { foreignKey: "stock_item_id" });

// InventoryItem associations
InventoryItem.belongsTo(StockItem, { foreignKey: "stock_item_id" });
InventoryItem.belongsTo(Product, { foreignKey: "product_id" });
InventoryItem.hasOne(SoldBarcode, {
  foreignKey: "barcode",
  sourceKey: "barcode",
});

// SoldBarcode associations
SoldBarcode.belongsTo(Product, { foreignKey: "product_id" });
SoldBarcode.belongsTo(Sale, { foreignKey: "sale_id" });
SoldBarcode.belongsTo(SaleItem, { foreignKey: "sale_item_id" });

// HqStockItem associations removed

// StockMovement associations
StockMovement.belongsTo(Tenant, { foreignKey: "tenant_id" });
StockMovement.belongsTo(Branch, {
  as: "FromBranch",
  foreignKey: "from_branch_id",
});
StockMovement.belongsTo(Branch, { as: "ToBranch", foreignKey: "to_branch_id" });
StockMovement.hasMany(StockMovementItem, { foreignKey: "stock_movement_id" });
StockMovement.belongsTo(User, {
  foreignKey: "requested_by",
  as: "RequestedBy",
});
StockMovement.belongsTo(User, { foreignKey: "approved_by", as: "ApprovedBy" });
StockMovement.belongsTo(User, {
  foreignKey: "dispatched_by",
  as: "DispatchedBy",
});
StockMovement.belongsTo(User, { foreignKey: "received_by", as: "ReceivedBy" });
StockMovement.belongsTo(User, { foreignKey: "created_by", as: "CreatedBy" });
StockMovement.belongsTo(User, {
  foreignKey: "last_updated_by",
  as: "LastUpdatedBy",
});

// StockMovementItem associations
StockMovementItem.belongsTo(StockMovement, { foreignKey: "stock_movement_id" });
StockMovementItem.belongsTo(Product, { foreignKey: "product_id" });

// MpesaFloatMovement associations
MpesaFloatMovement.belongsTo(Tenant, { foreignKey: "tenant_id" });
MpesaFloatMovement.belongsTo(Branch, {
  as: "FromBranch",
  foreignKey: "from_branch_id",
});
MpesaFloatMovement.belongsTo(Branch, {
  as: "ToBranch",
  foreignKey: "to_branch_id",
});
MpesaFloatMovement.belongsTo(User, {
  foreignKey: "created_by",
  as: "CreatedBy",
});
MpesaFloatMovement.belongsTo(User, {
  foreignKey: "last_updated_by",
  as: "LastUpdatedBy",
});

// DsaAgent associations removed - now using User model with is_dsa flag

// DsaStockAssignment associations are set up in setupDsaAssociations()

// DsaSale associations removed - now using Sale model with is_dsa flag

// MpesaFloatAllocation associations removed

// MpesaFloatAssignment associations removed

// MpesaFloatBalance associations
MpesaFloatBalance.belongsTo(Branch, { foreignKey: "branch_id" });
MpesaFloatBalance.belongsTo(Tenant, { foreignKey: "tenant_id" });
MpesaFloatBalance.belongsTo(User, { foreignKey: "updated_by", as: "Updater" });

// MpesaFloatTopup associations
MpesaFloatTopup.belongsTo(Tenant, { foreignKey: "tenant_id" });
MpesaFloatTopup.belongsTo(User, { foreignKey: "created_by", as: "CreatedBy" });
MpesaFloatTopup.belongsTo(User, {
  foreignKey: "last_updated_by",
  as: "LastUpdatedBy",
});

// MpesaTransaction associations
MpesaTransaction.belongsTo(Tenant, { foreignKey: "tenant_id" });
MpesaTransaction.belongsTo(Branch, { foreignKey: "branch_id" });
MpesaTransaction.belongsTo(User, { foreignKey: "user_id" });
// MpesaTransaction.belongsTo(Employee, { foreignKey: 'employee_id' }); // Removed as Employee model is deprecated
MpesaTransaction.belongsTo(User, { foreignKey: "created_by", as: "Creator" });
MpesaTransaction.belongsTo(User, {
  foreignKey: "last_updated_by",
  as: "Updater",
});
MpesaTransaction.hasMany(MpesaTransactionAudit, {
  foreignKey: "transaction_id",
});
MpesaTransaction.hasMany(MpesaTransactionReceipt, {
  foreignKey: "mpesa_transaction_id",
  as: "receipts",
});

// MpesaTransactionReceipt associations
MpesaTransactionReceipt.belongsTo(MpesaTransaction, {
  foreignKey: "mpesa_transaction_id",
});
MpesaTransactionReceipt.belongsTo(User, {
  foreignKey: "created_by",
  as: "Creator",
});
MpesaTransactionReceipt.belongsTo(User, {
  foreignKey: "last_updated_by",
  as: "Updater",
});

// MpesaTransactionAudit associations
MpesaTransactionAudit.belongsTo(MpesaTransaction, {
  foreignKey: "transaction_id",
});
MpesaTransactionAudit.belongsTo(User, { foreignKey: "performed_by" });

// PosSession associations
PosSession.belongsTo(User, { foreignKey: "user_id" });
PosSession.belongsTo(Branch, { foreignKey: "branch_id" });
PosSession.hasOne(PosSessionReconciliation, {
  foreignKey: "pos_session_id",
  as: "reconciliation",
});
PosSession.hasMany(CashTransaction, { foreignKey: "pos_session_id" });
PosSession.hasMany(Expense, { foreignKey: "pos_session_id" });
PosSession.hasMany(StockSnapshot, { foreignKey: "pos_session_id" });
PosSession.hasMany(BankingTransaction, { foreignKey: "pos_session_id" });
PosSession.hasMany(DsaPayment, { foreignKey: "pos_session_id" });

// StockSnapshot associations
StockSnapshot.belongsTo(StockItem, { foreignKey: "stock_item_id" });
StockSnapshot.belongsTo(Product, { foreignKey: "product_id" });
StockSnapshot.belongsTo(Branch, { foreignKey: "branch_id" });
StockSnapshot.belongsTo(PosSession, { foreignKey: "pos_session_id" });

// CashTransaction associations
CashTransaction.belongsTo(PosSession, { foreignKey: "pos_session_id" });
CashTransaction.belongsTo(User, { foreignKey: "user_id" });

// PosSessionReconciliation associations
PosSessionReconciliation.belongsTo(PosSession, {
  foreignKey: "pos_session_id",
  as: "session",
});

// DsaStockReconciliation associations are set up in setupDsaAssociations()

// MpesaFloatReconciliation associations
MpesaFloatReconciliation.belongsTo(User, { foreignKey: "user_id", as: "User" });
MpesaFloatReconciliation.belongsTo(Branch, { foreignKey: "branch_id" });
MpesaFloatReconciliation.belongsTo(User, {
  foreignKey: "created_by",
  as: "Creator",
});
MpesaFloatReconciliation.belongsTo(User, {
  foreignKey: "last_updated_by",
  as: "Updater",
});

// Banking Transaction associations
BankingTransaction.belongsTo(Tenant, { foreignKey: "tenant_id" });
BankingTransaction.belongsTo(Branch, { foreignKey: "branch_id" });
BankingTransaction.belongsTo(User, { foreignKey: "user_id" });
BankingTransaction.belongsTo(PosSession, { foreignKey: "pos_session_id" });
// BankingTransaction.belongsTo(Employee, { foreignKey: 'employee_id' }); // Removed as Employee model is deprecated
BankingTransaction.belongsTo(User, { foreignKey: "created_by", as: "Creator" });
BankingTransaction.belongsTo(User, {
  foreignKey: "last_updated_by",
  as: "Updater",
});
BankingTransaction.belongsTo(User, {
  foreignKey: "approved_by",
  as: "Approver",
});
BankingTransaction.hasMany(BankingTransactionReceipt, {
  foreignKey: "banking_transaction_id",
  as: "receipts",
});

// Banking Transaction Receipt associations
BankingTransactionReceipt.belongsTo(BankingTransaction, {
  foreignKey: "banking_transaction_id",
});
BankingTransactionReceipt.belongsTo(User, {
  foreignKey: "created_by",
  as: "Creator",
});
BankingTransactionReceipt.belongsTo(User, {
  foreignKey: "last_updated_by",
  as: "Updater",
});

// Bank associations
Bank.belongsTo(Tenant, { foreignKey: "tenant_id" });
Bank.belongsTo(User, { foreignKey: "created_by", as: "Creator" });
Bank.belongsTo(User, { foreignKey: "last_updated_by", as: "Updater" });
Bank.hasMany(BankingTransaction, { foreignKey: "bank_id" });

// Banking Transaction associations with Bank
BankingTransaction.belongsTo(Bank, { foreignKey: "bank_id" });

// Payment Method associations
PaymentMethod.belongsTo(Tenant, { foreignKey: "tenant_id" });
PaymentMethod.hasMany(Sale, { foreignKey: "payment_method_id" });

// Credit Partner associations
CreditPartner.belongsTo(Tenant, { foreignKey: "tenant_id" });
CreditPartner.hasMany(Sale, { foreignKey: "credit_partner_id" });

// Supplier associations
Supplier.belongsTo(Tenant, { foreignKey: "tenant_id", as: "Tenant" });
Supplier.belongsTo(User, { foreignKey: "created_by", as: "CreatedBy" });
Supplier.hasMany(Purchase, { foreignKey: "supplier_id", as: "Purchases" });
Supplier.hasMany(PurchaseReturn, {
  foreignKey: "supplier_id",
  as: "PurchaseReturns",
});

// Purchase associations
Purchase.belongsTo(Supplier, { foreignKey: "supplier_id", as: "Supplier" });
Purchase.belongsTo(Branch, { foreignKey: "branch_id", as: "Branch" });
Purchase.belongsTo(Branch, { foreignKey: "for_branch_id", as: "ForBranch" });
Purchase.belongsTo(Tenant, { foreignKey: "tenant_id", as: "Tenant" });
Purchase.belongsTo(User, { foreignKey: "created_by", as: "CreatedBy" });
Purchase.hasMany(PurchaseItem, {
  foreignKey: "purchase_id",
  as: "PurchaseItems",
});
Purchase.hasMany(PurchaseReturn, {
  foreignKey: "purchase_id",
  as: "PurchaseReturns",
});

// PurchaseItem associations
PurchaseItem.belongsTo(Purchase, { foreignKey: "purchase_id", as: "Purchase" });
PurchaseItem.belongsTo(Product, { foreignKey: "product_id", as: "Product" });

// PurchaseReturn associations
PurchaseReturn.belongsTo(Purchase, {
  foreignKey: "purchase_id",
  as: "Purchase",
});
PurchaseReturn.belongsTo(Supplier, {
  foreignKey: "supplier_id",
  as: "Supplier",
});
PurchaseReturn.belongsTo(Branch, { foreignKey: "branch_id", as: "Branch" });
PurchaseReturn.belongsTo(Tenant, { foreignKey: "tenant_id", as: "Tenant" });
PurchaseReturn.belongsTo(User, { foreignKey: "created_by", as: "CreatedBy" });
PurchaseReturn.hasMany(PurchaseReturnItem, {
  foreignKey: "purchase_return_id",
  as: "PurchaseReturnItems",
});

// PurchaseReturnItem associations
PurchaseReturnItem.belongsTo(PurchaseReturn, {
  foreignKey: "purchase_return_id",
  as: "PurchaseReturn",
});
PurchaseReturnItem.belongsTo(Product, {
  foreignKey: "product_id",
  as: "Product",
});

// Sale associations
Sale.belongsTo(PosSession, { foreignKey: "pos_session_id" });
Sale.belongsTo(User, { foreignKey: "user_id" });
Sale.belongsTo(Branch, { foreignKey: "branch_id" });
Sale.belongsTo(PaymentMethod, { foreignKey: "payment_method_id" });
Sale.belongsTo(CreditPartner, { foreignKey: "credit_partner_id" });
Sale.belongsTo(Customer, { foreignKey: "customer_id" });
// Sale.belongsTo(Employee, { foreignKey: 'employee_id' }); // Removed as Employee model is deprecated
Sale.hasMany(SaleItem, { foreignKey: "sale_id", paranoid: false }); // Disable paranoid for SaleItem
Sale.belongsTo(User, { foreignKey: "created_by", as: "SaleCreator" });
Sale.belongsTo(User, { foreignKey: "last_updated_by", as: "SaleUpdater" });

// SaleItem associations
SaleItem.belongsTo(Sale, { foreignKey: "sale_id" });
SaleItem.belongsTo(Product, { foreignKey: "product_id" });

// PosSession associations with Sales
PosSession.hasMany(Sale, { foreignKey: "pos_session_id" });

// StockMovementItem associations
StockMovementItem.belongsTo(StockMovement, { foreignKey: "stock_movement_id" });
StockMovementItem.belongsTo(Product, { foreignKey: "product_id" });

// Location associations
Location.belongsTo(Tenant, { foreignKey: "tenant_id" });
Location.belongsTo(Location, {
  as: "ParentLocation",
  foreignKey: "parent_location_id",
});
Location.hasMany(Location, {
  as: "ChildLocations",
  foreignKey: "parent_location_id",
});
Location.hasMany(User, { foreignKey: "location_id" });
Location.belongsTo(User, { as: "Creator", foreignKey: "created_by" });
Location.belongsTo(User, { as: "Updater", foreignKey: "last_updated_by" });

// Role associations
Role.hasMany(User, { foreignKey: "role_id" });
Role.belongsToMany(Permission, {
  through: RolePermission,
  foreignKey: "role_id",
});

// Permission associations
Permission.belongsToMany(Role, {
  through: RolePermission,
  foreignKey: "permission_id",
});

// StockAdjustment associations
StockAdjustment.belongsTo(Location, { foreignKey: "location_id" });
StockAdjustment.belongsTo(StockAdjustmentType, {
  foreignKey: "adjustment_type_id",
});
StockAdjustment.belongsTo(User, {
  as: "Requester",
  foreignKey: "requested_by",
});
StockAdjustment.belongsTo(User, { as: "Approver", foreignKey: "approved_by" });
StockAdjustment.hasMany(StockAdjustmentItem, { foreignKey: "adjustment_id" });

// StockAdjustmentItem associations
StockAdjustmentItem.belongsTo(StockAdjustment, { foreignKey: "adjustment_id" });
StockAdjustmentItem.belongsTo(StockItem, { foreignKey: "stock_item_id" });

// StockAdjustmentType associations
StockAdjustmentType.hasMany(StockAdjustment, {
  foreignKey: "adjustment_type_id",
});

// Location associations with StockAdjustment
Location.hasMany(StockAdjustment, { foreignKey: "location_id" });

// User associations with StockAdjustment
User.hasMany(StockAdjustment, {
  as: "RequestedAdjustments",
  foreignKey: "requested_by",
});
User.hasMany(StockAdjustment, {
  as: "ApprovedAdjustments",
  foreignKey: "approved_by",
});

// VatRate associations
VatRate.belongsTo(Tenant, { foreignKey: "tenant_id" });
VatRate.hasMany(Product, { foreignKey: "vat_rate_id" });
Product.belongsTo(VatRate, { foreignKey: "vat_rate_id" });

// Customer associations
Customer.belongsTo(Tenant, { foreignKey: "tenant_id" });
Customer.hasMany(Sale, { foreignKey: "customer_id" });
Customer.hasMany(PhoneRepair, { foreignKey: "customer_id" });
Customer.belongsTo(User, { foreignKey: "created_by", as: "CustomerCreator" });
Customer.belongsTo(User, {
  foreignKey: "last_updated_by",
  as: "CustomerUpdater",
});

// Employee associations
Employee.belongsTo(Tenant, { foreignKey: "tenant_id" });
Employee.belongsTo(Branch, { foreignKey: "branch_id" });
// Employee.hasMany(Sale, { foreignKey: 'employee_id' }); // Removed as Employee model is deprecated
// Employee.hasMany(BankingTransaction, { foreignKey: 'employee_id' }); // Removed as Employee model is deprecated
Employee.belongsTo(User, { foreignKey: "created_by", as: "EmployeeCreator" });
Employee.belongsTo(User, {
  foreignKey: "last_updated_by",
  as: "EmployeeUpdater",
});

// PhoneRepair associations
PhoneRepair.belongsTo(Customer, { foreignKey: "customer_id" });
PhoneRepair.belongsTo(Branch, { foreignKey: "branch_id" });
PhoneRepair.belongsTo(Branch, {
  foreignKey: "current_location_id",
  as: "CurrentLocation",
});
PhoneRepair.belongsTo(User, {
  foreignKey: "received_by",
  as: "ReceivedByUser",
});
PhoneRepair.belongsTo(User, { foreignKey: "created_by", as: "Creator" });
PhoneRepair.belongsTo(User, { foreignKey: "last_updated_by", as: "Updater" });
PhoneRepair.hasMany(PhoneRepairStatus, { foreignKey: "phone_repair_id" });
PhoneRepair.hasMany(PhoneRepairTransfer, { foreignKey: "phone_repair_id" });

// PhoneRepairStatus associations
PhoneRepairStatus.belongsTo(PhoneRepair, { foreignKey: "phone_repair_id" });
PhoneRepairStatus.belongsTo(User, {
  foreignKey: "changed_by",
  as: "StatusChanger",
});

// PhoneRepairTransfer associations
PhoneRepairTransfer.belongsTo(PhoneRepair, { foreignKey: "phone_repair_id" });
PhoneRepairTransfer.belongsTo(Branch, {
  foreignKey: "from_branch_id",
  as: "FromBranch",
});
PhoneRepairTransfer.belongsTo(Branch, {
  foreignKey: "to_branch_id",
  as: "ToBranch",
});
PhoneRepairTransfer.belongsTo(User, {
  foreignKey: "transferred_by",
  as: "TransferredBy",
});
PhoneRepairTransfer.belongsTo(User, {
  foreignKey: "received_by",
  as: "ReceivedBy",
});
PhoneRepairTransfer.belongsTo(User, {
  foreignKey: "created_by",
  as: "Creator",
});
PhoneRepairTransfer.belongsTo(User, {
  foreignKey: "last_updated_by",
  as: "Updater",
});

// InventoryTransaction associations
InventoryTransaction.belongsTo(StockItem, { foreignKey: "stock_item_id" });
InventoryTransaction.belongsTo(Branch, {
  foreignKey: "from_branch_id",
  as: "FromBranch",
});
InventoryTransaction.belongsTo(Branch, {
  foreignKey: "to_branch_id",
  as: "ToBranch",
});
InventoryTransaction.belongsTo(User, {
  foreignKey: "created_by",
  as: "CreatedBy",
});

// Invoice associations
Invoice.hasMany(InvoiceItem, { foreignKey: "invoice_id", as: "items" });
InvoiceItem.belongsTo(Invoice, { foreignKey: "invoice_id" });

Invoice.hasMany(InvoicePayment, { foreignKey: "invoice_id", as: "payments" });
InvoicePayment.belongsTo(Invoice, { foreignKey: "invoice_id" });

Invoice.belongsTo(Customer, { foreignKey: "customer_id", as: "customer" });
Customer.hasMany(Invoice, { foreignKey: "customer_id", as: "invoices" });

Invoice.belongsTo(Supplier, { foreignKey: "supplier_id", as: "supplier" });
Supplier.hasMany(Invoice, { foreignKey: "supplier_id", as: "invoices" });

module.exports = {
  // Sequelize instance
  sequelize,
  User,
  Tenant,
  Branch,
  Region,
  Product,
  StockItem,
  InventoryItem,
  SoldBarcode,
  // DsaAgent removed
  DsaStockAssignment,
  DsaPayment,
  // DsaSale removed
  // MpesaFloatAllocation removed
  // MpesaFloatAssignment removed
  MpesaFloatBalance,
  MpesaFloatMovement,
  MpesaFloatTopup,
  MpesaTransaction,
  MpesaTransactionAudit,
  MpesaTransactionReceipt,
  PosSession,
  PosSessionReconciliation,
  DsaStockReconciliation,
  MpesaFloatReconciliation,
  Sale,
  SaleItem,
  PaymentMethod,
  StockMovement,
  StockMovementItem,
  Location,
  Role,
  Permission,
  RolePermission,
  RBACGrants, // Use the model name directly
  rbacGrants: RBACGrants, // Add lowercase version for Sequelize convention
  StockAdjustment,
  StockAdjustmentItem,
  StockAdjustmentType,
  ProductCategory,
  ProductDiscount,
  Customer,
  CreditNote,
  CreditNoteItem,
  Employee,
  PhoneRepair,
  PhoneRepairStatus,
  PhoneRepairTransfer,
  BankingTransaction,
  BankingTransactionReceipt,
  Brand,
  BrandType,
  CashTransaction,
  Bank,
  CreditPartner,
  ExpenseCategory,
  ExpenseCategoryGroup,
  Expense,
  VatRate, // Add VatRate model
  Supplier,
  Purchase,
  PurchaseItem,
  // Procurement models
  ProcurementRequest,
  ProcurementRequestItem,
  ProcurementReceipt,
  ProcurementReceiptItem,
  PurchaseReturn,
  PurchaseReturnItem,
  InventoryTransaction, // Add InventoryTransaction model
  Invoice,
  InvoiceItem,
  InvoicePayment,
  TimsResponseLog,
  StockSnapshot,
};
