const { DataTypes } = require('sequelize');
const sequelize = require('../../config/database');

/**
 * DSA Payment Model
 * Tracks payments made by DSA agents for their stock assignments
 */
const DsaPayment = sequelize.define('DsaPayment', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  assignment_identifier: {
    type: DataTypes.STRING(36),
    allowNull: false,
    comment: 'UUID to identify the assignment batch'
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Customer ID for DSA'
  },
  branch_id: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  pos_session_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'POS Session ID when payment was recorded'
  },
  cash_amount: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
    defaultValue: 0,
    comment: 'Amount paid in cash'
  },
  paybill_amount: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
    defaultValue: 0,
    comment: 'Amount paid via paybill'
  },
  total_amount: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
    comment: 'Total payment amount (cash + paybill)'
  },
  payment_date: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: 'Date when payment was made'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Additional notes about the payment'
  },
  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'User ID who recorded this payment'
  },
  last_updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true
  }
}, {
  tableName: 'dsa_payments',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  paranoid: false
});

module.exports = DsaPayment;
