const express = require('express');
const router = express.Router();
const stockMovementController = require('../controllers/stock-movement.controller');
const { exportAllStockMovements, exportCustomStockMovements } = require('../controllers/report/stock-movements-export.controller');
const { exportAllStockMovementItems, exportCustomStockMovementItems } = require('../controllers/report/stock-movement-items-export.controller');
const { authenticate, rbac } = require('../middleware/auth.middleware');

/**
 * @swagger
 * tags:
 *   name: Stock Movements
 *   description: Stock movement management for transferring stock between branches or from HQ to branches
 */

/**
 * @swagger
 * /stock-movements:
 *   get:
 *     summary: Get all stock movements
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: tenant_id
 *         schema:
 *           type: integer
 *         description: Filter by tenant ID
 *       - in: query
 *         name: from_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by source branch ID
 *       - in: query
 *         name: from_hq
 *         schema:
 *           type: boolean
 *         description: Filter for movements from HQ branch (level=0)
 *       - in: query
 *         name: to_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by destination branch ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, in_transit, received, cancelled]
 *         description: Filter by status
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by start date (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by end date (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: List of stock movements
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/StockMovement'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authenticate, rbac.checkPermission('stock_movement', 'read'), stockMovementController.getAllStockMovements);

/**
 * @swagger
 * /stock-movements/{id}:
 *   get:
 *     summary: Get stock movement by ID
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Stock movement ID
 *     responses:
 *       200:
 *         description: Stock movement details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/StockMovement'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Stock movement not found
 *       500:
 *         description: Server error
 */
router.get('/:id', authenticate, rbac.checkPermission('stock_movement', 'read'), stockMovementController.getStockMovementById);

/**
 * @swagger
 * /stock-movements:
 *   post:
 *     summary: Create a new stock movement
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - tenant_id
 *               - to_branch_id
 *               - items
 *             properties:
 *               tenant_id:
 *                 type: integer
 *                 description: The tenant ID
 *               from_branch_id:
 *                 type: integer
 *                 nullable: true
 *                 description: The source branch ID (HQ branch has level=0)
 *               to_branch_id:
 *                 type: integer
 *                 description: The destination branch ID
 *               notes:
 *                 type: string
 *                 description: Additional notes about the movement
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - product_id
 *                     - quantity_sent
 *                   properties:
 *                     product_id:
 *                       type: integer
 *                     quantity_sent:
 *                       type: integer
 *                     notes:
 *                       type: string
 *             examples:
 *               branch_to_branch:
 *                 summary: Branch to Branch Movement
 *                 value:
 *                   tenant_id: 1
 *                   from_branch_id: 2
 *                   to_branch_id: 3
 *                   notes: "Regular branch transfer"
 *                   items: [
 *                     {
 *                       product_id: 1,
 *                       quantity_sent: 10,
 *                       notes: "Regular stock"
 *                     }
 *                   ]
 *               hq_to_branch:
 *                 summary: HQ to Branch Movement
 *                 value:
 *                   tenant_id: 1
 *                   from_branch_id: 1
 *                   to_branch_id: 3
 *                   notes: "HQ to branch transfer"
 *                   items: [
 *                     {
 *                       product_id: 1,
 *                       quantity_sent: 50,
 *                       notes: "Stock from headquarters"
 *                     }
 *                   ]
 *     responses:
 *       201:
 *         description: Stock movement created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/StockMovement'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', authenticate, rbac.checkPermission('stock_movement', 'create'), stockMovementController.createStockMovement);

/**
 * @swagger
 * /stock-movements/{id}/status:
 *   put:
 *     summary: Update stock movement status
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Stock movement ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [pending, in_transit, received, cancelled]
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Stock movement status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/StockMovement'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Stock movement not found
 *       500:
 *         description: Server error
 */
router.put('/:id/status', authenticate, rbac.checkPermission('stock_movement', 'update'), stockMovementController.updateStockMovementStatus);

/**
 * @swagger
 * /stock-movements/{id}/items:
 *   put:
 *     summary: Update stock movement items
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Stock movement ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - items
 *             properties:
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - id
 *                     - quantity_received
 *                   properties:
 *                     id:
 *                       type: integer
 *                     quantity_received:
 *                       type: integer
 *                     notes:
 *                       type: string
 *     responses:
 *       200:
 *         description: Stock movement items updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/StockMovement'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Stock movement not found
 *       500:
 *         description: Server error
 */
router.put('/:id/items', authenticate, rbac.checkPermission('stock_movement', 'update'), stockMovementController.updateStockMovementItems);

/**
 * @swagger
 * /stock-movements/{id}/receive:
 *   post:
 *     summary: Receive stock movement
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Stock movement ID
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - id
 *                     - quantity_received
 *                   properties:
 *                     id:
 *                       type: integer
 *                     quantity_received:
 *                       type: integer
 *                     notes:
 *                       type: string
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Stock movement received successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/StockMovement'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Stock movement not found
 *       500:
 *         description: Server error
 */
router.post('/:id/receive', authenticate, rbac.checkPermission('stock_movement', 'update'), stockMovementController.receiveStockMovement);

/**
 * @swagger
 * /stock-movements/{id}:
 *   delete:
 *     summary: Delete a stock movement
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Stock movement ID
 *     responses:
 *       200:
 *         description: Stock movement deleted successfully
 *       400:
 *         description: Cannot delete a received stock movement
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Stock movement not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', authenticate, rbac.checkPermission('stock_movement', 'update'), stockMovementController.deleteStockMovement);

/**
 * @swagger
 * /stock-movements/hq/stats:
 *   get:
 *     summary: Get statistics for HQ stock movements
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: tenant_id
 *         schema:
 *           type: integer
 *         description: Filter by tenant ID
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by start date (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by end date (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: HQ stock movement statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 total_movements:
 *                   type: integer
 *                   description: Total number of HQ stock movements
 *                 status_breakdown:
 *                   type: object
 *                   properties:
 *                     pending:
 *                       type: integer
 *                     in_transit:
 *                       type: integer
 *                     received:
 *                       type: integer
 *                     cancelled:
 *                       type: integer
 *                 total_quantity_sent:
 *                   type: integer
 *                   description: Total quantity sent from HQ
 *                 total_quantity_received:
 *                   type: integer
 *                   description: Total quantity received at branches
 *                 discrepancy:
 *                   type: integer
 *                   description: Difference between sent and received quantities
 *                 top_branches:
 *                   type: array
 *                   items:
 *                     type: object
 *                 top_products:
 *                   type: array
 *                   items:
 *                     type: object
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/hq/stats', authenticate, rbac.checkPermission('stock_reports', 'read'), stockMovementController.getHqStockMovementStats);

/**
 * @swagger
 * /stock-movements/export/all:
 *   get:
 *     summary: Export all stock movements to Excel
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status
 *       - in: query
 *         name: source_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by source branch ID
 *       - in: query
 *         name: destination_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by destination branch ID
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in reference number or notes
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter from date (YYYY-MM-DD)
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter to date (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: Excel file with comprehensive stock movements data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/export/all', authenticate, rbac.checkPermission('stock_reports', 'read'), exportAllStockMovements);

/**
 * @swagger
 * /stock-movements/export/custom:
 *   get:
 *     summary: Export custom stock movements to Excel
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status
 *       - in: query
 *         name: source_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by source branch ID
 *       - in: query
 *         name: destination_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by destination branch ID
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in reference number or notes
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter from date (YYYY-MM-DD)
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter to date (YYYY-MM-DD)
 *       - in: query
 *         name: include_summary
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include summary sheet
 *       - in: query
 *         name: include_details
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include details sheet
 *       - in: query
 *         name: include_status_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include status breakdown sheet
 *       - in: query
 *         name: include_branch_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include branch breakdown sheet
 *       - in: query
 *         name: format_type
 *         schema:
 *           type: string
 *           enum: [detailed, summary]
 *           default: detailed
 *         description: Format type for export
 *     responses:
 *       200:
 *         description: Excel file with custom stock movements data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/export/custom', authenticate, rbac.checkPermission('stock_reports', 'read'), exportCustomStockMovements);

/**
 * @swagger
 * /stock-movements/items/export/all:
 *   get:
 *     summary: Export all stock movement items to Excel
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status
 *       - in: query
 *         name: from_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by source branch ID
 *       - in: query
 *         name: to_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by destination branch ID
 *       - in: query
 *         name: product_id
 *         schema:
 *           type: integer
 *         description: Filter by product ID
 *       - in: query
 *         name: reference_number
 *         schema:
 *           type: string
 *         description: Filter by reference number
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in reference numbers and notes
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date filter
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: End date filter
 *     responses:
 *       200:
 *         description: Excel file with comprehensive stock movement items data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/items/export/all', authenticate, rbac.checkPermission('stock_reports', 'read'), exportAllStockMovementItems);

/**
 * @swagger
 * /stock-movements/items/export/custom:
 *   get:
 *     summary: Export custom stock movement items to Excel
 *     tags: [Stock Movements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status
 *       - in: query
 *         name: from_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by source branch ID
 *       - in: query
 *         name: to_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by destination branch ID
 *       - in: query
 *         name: product_id
 *         schema:
 *           type: integer
 *         description: Filter by product ID
 *       - in: query
 *         name: reference_number
 *         schema:
 *           type: string
 *         description: Filter by reference number
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in reference numbers and notes
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date filter
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: End date filter
 *       - in: query
 *         name: include_summary
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include summary sheet
 *       - in: query
 *         name: include_details
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include details sheet
 *       - in: query
 *         name: include_status_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include status breakdown sheet
 *       - in: query
 *         name: include_branch_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include branch breakdown sheet
 *       - in: query
 *         name: include_product_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include product breakdown sheet
 *       - in: query
 *         name: format_type
 *         schema:
 *           type: string
 *           enum: [detailed, summary]
 *           default: detailed
 *         description: Format type for export
 *     responses:
 *       200:
 *         description: Excel file with custom stock movement items data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/items/export/custom', authenticate, rbac.checkPermission('stock_reports', 'read'), exportCustomStockMovementItems);

module.exports = router;
