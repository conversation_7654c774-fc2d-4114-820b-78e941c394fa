const { Invoice, InvoiceItem, Customer, InvoicePayment, Supplier } = require('../models');
const { generateReferenceNumber } = require('../utils/reference-number');
const { Op, literal } = require('sequelize');
const logger = require('../utils/logger');
const InvoiceCalculator = require('../utils/invoice-calculator');

class InvoiceService {
  /**
   * Create a new invoice
   * @param {Object} invoiceData - Invoice data
   * @returns {Promise<Object>} Created invoice
   */
  async createInvoice(invoiceData) {
    try {
      // Generate invoice number
      const invoiceNumber = await generateReferenceNumber('INV', 'invoices', 'invoice_number');

      // Process invoice items to calculate totals
      let processedItems = [];
      let invoiceTotals = { total_amount: 0, vat_amount: 0, subtotal: 0 };

      if (invoiceData.items && invoiceData.items.length > 0) {
        // Process items with our calculator, passing KRA integration flag
        processedItems = InvoiceCalculator.processInvoiceItems(
          invoiceData.items,
          invoiceData.type,
          invoiceData.kra_integrated || false
        );

        // Calculate invoice totals
        invoiceTotals = InvoiceCalculator.calculateInvoiceTotals(processedItems);
      }

      // Create invoice with calculated totals
      const invoiceCreateData = {
        ...invoiceData,
        invoice_number: invoiceNumber,
        subtotal: invoiceTotals.subtotal,
        vat_amount: invoiceTotals.vat_amount,
        total_amount: invoiceTotals.total_amount
      };

      const invoice = await Invoice.create(invoiceCreateData);

      // Create invoice items
      if (processedItems.length > 0) {
        const items = processedItems.map(item => ({
          ...item,
          invoice_id: invoice.id
        }));

        await InvoiceItem.bulkCreate(items);
      }

      // Return complete invoice with items
      return this.getInvoiceById(invoice.id);
    } catch (error) {
      logger.error(`Error creating invoice: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get invoice by ID
   * @param {string} id - Invoice ID
   * @returns {Promise<Object>} Invoice with items, customer/supplier, and payments
   */
  async getInvoiceById(id) {
    try {
      const invoice = await Invoice.findByPk(id, {
        include: [
          { model: InvoiceItem, as: 'items' },
          { model: Customer, as: 'customer', required: false },
          { model: Supplier, as: 'supplier', required: false },
          { model: InvoicePayment, as: 'payments' }
        ]
      });

      if (!invoice) {
        logger.warn(`Invoice not found with ID: ${id}`);
        return null;
      }

      logger.info(`Retrieved invoice with ID: ${id}, number: ${invoice.invoice_number}`);
      return invoice;
    } catch (error) {
      logger.error(`Error retrieving invoice with ID ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get invoices with pagination and filters
   * @param {Object} options - Filter and pagination options
   * @returns {Promise<Object>} Invoices with count
   */
  async getInvoices(options) {
    try {
      const {
        tenant_id,
        page = 1,
        limit = 10,
        status,
        customer_id,
        supplier_id,
        type,
        search,
        date_range,
        kra_integration_status,
        sort_by = 'created_at',
        sort_direction = 'DESC'
      } = options;

      logger.info(`Fetching invoices with options: ${JSON.stringify({
        tenant_id,
        page,
        limit,
        status,
        customer_id,
        supplier_id,
        type,
        search: search ? 'provided' : 'not provided',
        date_range: date_range ? 'provided' : 'not provided',
        sort_by,
        sort_direction
      })}`);

      // Build where clause
      const where = {
        tenant_id,
      };

      // Add optional filters
      if (status) where.status = status;
      if (customer_id) where.customer_id = customer_id;
      if (supplier_id) where.supplier_id = supplier_id;
      if (type) where.type = type;
      if (kra_integration_status) where.kra_integration_status = kra_integration_status;

      // Add date range filter
      if (date_range) {
        where.invoice_date = {
          [Op.between]: [date_range.start, date_range.end]
        };
      }

      // Handle search functionality
      if (search) {
        const searchTerm = `%${search}%`;

        // First, find invoice IDs that match customer or supplier names
        let customerInvoiceIds = [];
        let supplierInvoiceIds = [];

        try {
          // Find invoices by customer name
          const customersWithName = await Customer.findAll({
            attributes: ['id'],
            where: {
              tenant_id,
              name: { [Op.like]: searchTerm }
            }
          });

          if (customersWithName.length > 0) {
            customerInvoiceIds = customersWithName.map(c => c.id);
          }

          // Find invoices by supplier name
          const suppliersWithName = await Supplier.findAll({
            attributes: ['id'],
            where: {
              tenant_id,
              name: { [Op.like]: searchTerm }
            }
          });

          if (suppliersWithName.length > 0) {
            supplierInvoiceIds = suppliersWithName.map(s => s.id);
          }
        } catch (searchError) {
          logger.error(`Error searching for customer/supplier names: ${searchError.message}`);
          // Continue with the main query even if this part fails
        }

        // Build the OR conditions for the search
        const searchConditions = [
          { invoice_number: { [Op.like]: searchTerm } },
          { lpo_number: { [Op.like]: searchTerm } },
          { dno: { [Op.like]: searchTerm } }
        ];

        // Add customer/supplier ID conditions if we found any matches
        if (customerInvoiceIds.length > 0) {
          searchConditions.push({ customer_id: { [Op.in]: customerInvoiceIds } });
        }

        if (supplierInvoiceIds.length > 0) {
          searchConditions.push({ supplier_id: { [Op.in]: supplierInvoiceIds } });
        }

        // Set the OR conditions
        where[Op.or] = searchConditions;
      }

      // Prepare include array for associations
      const include = [
        { model: InvoiceItem, as: 'items' }
      ];

      // Always include both customer and supplier models with proper attributes
      include.push({
        model: Customer,
        as: 'customer',
        required: false
      });

      include.push({
        model: Supplier,
        as: 'supplier',
        required: false
      });

      // Add payments to the include array
      include.push({
        model: InvoicePayment,
        as: 'payments',
        required: false
      });

      // Validate and sanitize sort parameters
      const validSortFields = ['invoice_number', 'invoice_date', 'due_date', 'total_amount', 'status', 'created_at'];
      const validSortDirections = ['ASC', 'DESC'];

      const sanitizedSortBy = validSortFields.includes(sort_by) ? sort_by : 'created_at';
      const sanitizedSortDirection = validSortDirections.includes(sort_direction.toUpperCase())
        ? sort_direction.toUpperCase()
        : 'DESC';

      // Get invoices with pagination
      const { count, rows } = await Invoice.findAndCountAll({
        where,
        include,
        order: [[sanitizedSortBy, sanitizedSortDirection]],
        limit,
        offset: (page - 1) * limit,
        distinct: true // Ensure correct count with associations
      });

      logger.info(`Found ${count} invoices matching criteria`);
      return { count, rows };
    } catch (error) {
      logger.error(`Error fetching invoices: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check if an invoice is editable based on its status
   * @param {Object} invoice - Invoice object
   * @returns {boolean} True if invoice is editable, false otherwise
   */
  isInvoiceEditable(invoice) {
    // Invoices with 'paid' status cannot be edited
    if (invoice.status === 'paid') {
      return false;
    }

    // Optionally, you can add more status checks here if needed
    // For example, you might want to prevent editing 'reconciled' invoices as well
    if (invoice.status === 'reconciled') {
      return false;
    }

    return true;
  }

  /**
   * Update invoice
   * @param {string} id - Invoice ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Result with success status and message
   */
  async updateInvoice(id, updateData) {
    try {
      const invoice = await Invoice.findByPk(id);

      if (!invoice) {
        return { success: false, message: 'Invoice not found' };
      }

      // Check if invoice is editable based on its status
      if (!this.isInvoiceEditable(invoice)) {
        logger.warn(`Attempted to update locked invoice ${id} with status ${invoice.status}`);
        return {
          success: false,
          message: `Invoice cannot be updated because it has a status of '${invoice.status}'`
        };
      }

      let dataToUpdate = { ...updateData };

      // If items are provided, recalculate totals
      if (updateData.items && updateData.items.length > 0) {
        // Process items with our calculator, passing KRA integration flag
        const processedItems = InvoiceCalculator.processInvoiceItems(
          updateData.items,
          updateData.type || invoice.type,
          updateData.kra_integrated !== undefined ? updateData.kra_integrated : invoice.kra_integrated || false
        );

        // Calculate invoice totals
        const invoiceTotals = InvoiceCalculator.calculateInvoiceTotals(processedItems);

        // Update the data with calculated totals
        dataToUpdate = {
          ...dataToUpdate,
          subtotal: invoiceTotals.subtotal,
          vat_amount: invoiceTotals.vat_amount,
          total_amount: invoiceTotals.total_amount
        };

        // Delete existing items
        await InvoiceItem.destroy({ where: { invoice_id: id } });

        // Create new items
        const items = processedItems.map(item => ({
          ...item,
          invoice_id: id
        }));

        await InvoiceItem.bulkCreate(items);
      }

      // Update invoice
      await invoice.update(dataToUpdate);

      return { success: true };
    } catch (error) {
      logger.error(`Error updating invoice ${id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete invoice
   * @param {string} id - Invoice ID
   * @returns {Promise<Object>} Result with success status and message
   */
  async deleteInvoice(id) {
    const invoice = await Invoice.findByPk(id);

    if (!invoice) {
      return { success: false, message: 'Invoice not found' };
    }

    // Check if invoice is editable based on its status
    if (!this.isInvoiceEditable(invoice)) {
      logger.warn(`Attempted to delete locked invoice ${id} with status ${invoice.status}`);
      return {
        success: false,
        message: `Invoice cannot be deleted because it has a status of '${invoice.status}'`
      };
    }

    // Delete invoice items
    await InvoiceItem.destroy({ where: { invoice_id: id } });

    // Delete invoice
    await invoice.destroy();

    return { success: true };
  }

  /**
   * Add invoice payment
   * @param {Object} paymentData - Payment data
   * @returns {Promise<Object>} Created payment
   */
  async addInvoicePayment(paymentData) {
    // Create payment
    const payment = await InvoicePayment.create(paymentData);

    return payment;
  }

  /**
   * Update invoice status after payment
   * @param {string} id - Invoice ID
   * @returns {Promise<boolean>} Success status
   */
  async updateInvoiceStatusAfterPayment(id) {
    const invoice = await Invoice.findByPk(id);

    if (!invoice) {
      return false;
    }

    // Get total payments
    const payments = await InvoicePayment.findAll({
      where: { invoice_id: id }
    });

    const totalPaid = payments.reduce((sum, payment) => sum + parseFloat(payment.amount), 0);
    const totalAmount = parseFloat(invoice.total_amount);

    // Update status based on payment amount
    let status;
    if (totalPaid >= totalAmount) {
      status = 'paid';
    } else if (totalPaid > 0) {
      status = 'partially_paid';
    } else {
      status = 'sent';
    }

    // Update invoice status and total_paid
    await invoice.update({
      status,
      total_paid: totalPaid
    });

    logger.info(`Updated invoice ${id} status to ${status} with total_paid ${totalPaid}`);
    return true;
  }

  /**
   * Get invoice payments
   * @param {string} id - Invoice ID
   * @returns {Promise<Array>} Invoice payments
   */
  async getInvoicePayments(id) {
    return InvoicePayment.findAll({
      where: { invoice_id: id },
      order: [['payment_date', 'DESC']]
    });
  }
}

module.exports = new InvoiceService();