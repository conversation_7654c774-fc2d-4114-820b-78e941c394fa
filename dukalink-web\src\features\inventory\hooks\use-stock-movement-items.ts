import { useQuery } from "@tanstack/react-query";
import apiClient from "@/lib/api-client";
import { StockMovementItemWithDetails } from "../components/stock-movement-items-table";

export const STOCK_MOVEMENT_ITEMS_QUERY_KEY = "stock-movement-items";

/**
 * Fetches all stock movement items with their details
 */
export const useAllStockMovementItems = (
  params?: Record<string, any>,
  options?: {
    enabled?: boolean;
  }
) => {
  // Get the token from localStorage to check if user is authenticated
  const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;

  return useQuery({
    queryKey: [STOCK_MOVEMENT_ITEMS_QUERY_KEY, params],
    queryFn: async () => {
      // Ensure the token is set in the API client before making the request
      if (token) {
        apiClient.setAuthToken(token);
      }

      // First, get all stock movements (skip role-based filtering to get all data)
      const movementsResponse = await apiClient.get<any>("/stock-movements", {
        params: {
          ...params,
          limit: 10000, // Get all items to ensure we capture all transfers
          skip_role_filter: 'true' // Skip role-based filtering to get all movements
        }
      });

      let movements = [];
      // Handle both paginated and non-paginated responses
      if (movementsResponse && movementsResponse.data && Array.isArray(movementsResponse.data)) {
        // Paginated response format
        movements = movementsResponse.data;
      } else if (Array.isArray(movementsResponse)) {
        // Direct array response format
        movements = movementsResponse;
      }

      console.log(`Fetched ${movements.length} stock movements for bulk transfers view`);

      // For each movement, get its items
      const itemsPromises = movements.map(async (movement) => {
        try {
          const response = await apiClient.get<any>(`/stock-movements/${movement.id}`, {
            params: {
              skip_role_filter: 'true' // Skip role-based filtering for individual movements
            }
          });

          if (response && response.StockMovementItems && Array.isArray(response.StockMovementItems)) {
            // Map each item to include movement details
            return response.StockMovementItems.map((item: any) => ({
                id: item.id,
                stock_movement_id: movement.id,
                reference_number: movement.reference_number || `SM-${movement.id}`,
                from_branch_name: movement.FromBranch?.name || 'HQ',
                to_branch_name: movement.ToBranch?.name || 'Unknown',
                product_id: item.product_id,
                product_name: item.Product?.name || `Product #${item.product_id}`,
                product_sku: item.Product?.sku || 'N/A',
                requested_quantity: item.requested_quantity || 0,
                approved_quantity: item.approved_quantity || 0,
                dispatched_quantity: item.dispatched_quantity || 0,
                received_quantity: item.received_quantity || 0,
                status: movement.status || 'pending',
                created_at: movement.created_at || new Date().toISOString()
              }));
          }
          return [];
        } catch (error) {
          console.error(`Error fetching items for movement ${movement.id}:`, error);
          return [];
        }
      });

      // Wait for all promises to resolve
      const itemsArrays = await Promise.all(itemsPromises);

      // Flatten the array of arrays into a single array of items
      const allItems = itemsArrays.flat();

      // Apply search filter if provided
      let filteredItems = allItems;
      if (params?.search) {
        const searchTerm = params.search.toLowerCase().trim();
        console.log(`Searching stock movement items for: "${searchTerm}"`);

        filteredItems = allItems.filter(item => {
          // Search in reference number
          const referenceMatch = item.reference_number?.toLowerCase().includes(searchTerm);

          // Search in product name (primary search field)
          const productNameMatch = item.product_name?.toLowerCase().includes(searchTerm);

          // Search in product SKU
          const productSkuMatch = item.product_sku?.toLowerCase().includes(searchTerm);

          // Search in branch names
          const fromBranchMatch = item.from_branch_name?.toLowerCase().includes(searchTerm);
          const toBranchMatch = item.to_branch_name?.toLowerCase().includes(searchTerm);

          // Search in status
          const statusMatch = item.status?.toLowerCase().includes(searchTerm);

          // Return true if any field matches
          return referenceMatch || productNameMatch || productSkuMatch ||
                 fromBranchMatch || toBranchMatch || statusMatch;
        });

        console.log(`Found ${filteredItems.length} items matching search term "${searchTerm}"`);
      }

      // Sort by date (newest first)
      filteredItems.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      // Apply pagination
      const page = params?.page || 1;
      const limit = params?.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedItems = filteredItems.slice(startIndex, endIndex);

      return {
        data: paginatedItems,
        pagination: {
          total: filteredItems.length,
          page: page,
          limit: limit,
          totalPages: Math.ceil(filteredItems.length / limit)
        }
      };
    },
    ...options,
    enabled: !!token && (options?.enabled ?? true),
  });
};
