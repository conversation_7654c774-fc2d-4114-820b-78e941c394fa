const express = require('express');
const router = express.Router();
const bankingTransactionsController = require('../../controllers/report/banking-transactions.controller');
const { exportAllBankingTransactions, exportCustomBankingTransactions, exportLightweightBankingTransactions } = require('../../controllers/report/banking-transactions-export.controller');
const { authenticate, rbac } = require('../../middleware/auth.middleware');

/**
 * @swagger
 * /api/v1/reports/banking-transactions:
 *   get:
 *     summary: Get banking transactions report
 *     description: Retrieve banking transactions with filtering by branch, method, type, and date
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: banking_method
 *         schema:
 *           type: string
 *           enum: [bank, agent, mpesa]
 *         required: false
 *         description: Filter by banking method
 *       - in: query
 *         name: transaction_type
 *         schema:
 *           type: string
 *           enum: [deposit, withdrawal, transfer]
 *         required: false
 *         description: Filter by transaction type
 *       - in: query
 *         name: bank_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by bank ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, completed, failed, reconciled]
 *         required: false
 *         description: Filter by transaction status
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, excel]
 *         required: false
 *         description: Response format (default is json)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         required: false
 *         description: Page number for pagination (default is 1)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 1000
 *         required: false
 *         description: Number of records per page (default is 100)
 *     responses:
 *       200:
 *         description: Banking transactions report
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 filters:
 *                   type: object
 *                 pagination:
 *                   type: object
 *                 summary:
 *                   type: object
 *                 transactions:
 *                   type: array
 *                   items:
 *                     type: object
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/',
  authenticate,
  rbac.checkPermission('banking_reports', 'read'),
  bankingTransactionsController.getBankingTransactionsReport
);

/**
 * @swagger
 * /api/v1/reports/banking-transactions/export/all:
 *   get:
 *     summary: Export comprehensive banking transactions to Excel
 *     description: Export all transaction data with multiple sheets including summary, details, and breakdowns
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by region ID
 *       - in: query
 *         name: banking_method
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by banking method
 *       - in: query
 *         name: transaction_type
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by transaction type
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by status
 *       - in: query
 *         name: bank_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by bank ID
 *     responses:
 *       200:
 *         description: Excel file with comprehensive banking transactions data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No transaction data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/all',
  authenticate,
  rbac.checkPermission('banking_reports', 'read'),
  exportAllBankingTransactions
);

/**
 * @swagger
 * /api/v1/reports/banking-transactions/export/custom:
 *   get:
 *     summary: Export custom banking transactions to Excel
 *     description: Export transaction data with customizable sheets and format options
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by region ID
 *       - in: query
 *         name: banking_method
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by banking method
 *       - in: query
 *         name: transaction_type
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by transaction type
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by status
 *       - in: query
 *         name: bank_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by bank ID
 *       - in: query
 *         name: include_summary
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include summary sheet
 *       - in: query
 *         name: include_details
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include transaction details sheet
 *       - in: query
 *         name: include_method_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include method breakdown sheet
 *       - in: query
 *         name: include_type_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include type breakdown sheet
 *       - in: query
 *         name: include_status_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include status breakdown sheet
 *       - in: query
 *         name: include_charts
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include charts and graphs
 *       - in: query
 *         name: format_type
 *         schema:
 *           type: string
 *           enum: [detailed, summary]
 *           default: detailed
 *         description: Format type for export
 *     responses:
 *       200:
 *         description: Excel file with custom banking transactions data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No transaction data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/custom',
  authenticate,
  rbac.checkPermission('banking_reports', 'read'),
  exportCustomBankingTransactions
);

/**
 * @swagger
 * /api/v1/reports/banking-transactions/export/lightweight:
 *   get:
 *     summary: Export lightweight banking transactions to Excel (fastest option)
 *     description: Export basic transaction data only for fastest processing
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by region ID
 *       - in: query
 *         name: banking_method
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by banking method
 *       - in: query
 *         name: transaction_type
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by transaction type
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by status
 *       - in: query
 *         name: bank_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by bank ID
 *     responses:
 *       200:
 *         description: Excel file with basic transaction data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No transaction data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/lightweight',
  authenticate,
  rbac.checkPermission('banking_reports', 'read'),
  exportLightweightBankingTransactions
);

module.exports = router;
