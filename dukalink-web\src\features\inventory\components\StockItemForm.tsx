import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from 'sonner';
import { StockItem, CreateStockItemDto, UpdateStockItemDto } from '../types';
import { useProduct } from '@/features/products/hooks/use-products';
import { SerialNumberInput } from './SerialNumberInput';

const stockItemSchema = z.object({
  product_id: z.coerce.number().min(1, 'Product is required'),
  branch_id: z.coerce.number().min(1, 'Branch is required'),
  quantity: z.coerce.number().min(0, 'Quantity must be a positive number'),
  quantity_reserved: z.coerce.number().min(0, 'Reserved quantity must be a positive number').optional(),

  // Serial numbers for serialized products
  serial_numbers: z.array(z.string()).optional(),

  // Pricing fields
  buying_price: z.coerce.number().min(0, 'Buying price must be a positive number').optional(),
  default_buying_price: z.coerce.number().min(0, 'Default buying price must be a positive number').optional(),
  default_selling_price: z.coerce.number().min(0, 'Default selling price must be a positive number').optional(),
  default_wholesale_price: z.coerce.number().min(0, 'Default wholesale price must be a positive number').optional(),

  // VAT-related fields
  buying_price_including_vat: z.coerce.number().min(0, 'Buying price including VAT must be a positive number').optional(),
  buying_price_excluding_vat: z.coerce.number().min(0, 'Buying price excluding VAT must be a positive number').optional(),
  buying_vat_amount: z.coerce.number().min(0, 'VAT amount must be a positive number').optional(),
  buying_vat_rate: z.coerce.number().min(0, 'VAT rate must be a positive number').optional(),

  // Batch/lot tracking
  batch_number: z.string().optional(),
  expiry_date: z.string().optional(),
  manufacturing_date: z.string().optional(),

  // Inventory management
  reorder_level: z.coerce.number().min(0, 'Reorder level must be a positive number').optional(),
  reorder_quantity: z.coerce.number().min(0, 'Reorder quantity must be a positive number').optional(),
  valuation_method: z.enum(['FIFO', 'LIFO', 'WEIGHTED_AVERAGE']).optional(),
});

type FormValues = z.infer<typeof stockItemSchema>;

interface StockItemFormProps {
  stockItem?: StockItem;
  products: { id: number; name: string }[];
  branches: { id: number; name: string }[];
  locations: { id: number; name: string }[];
  onSubmit: (data: CreateStockItemDto | UpdateStockItemDto) => Promise<void>;
  isSubmitting: boolean;
}

export function StockItemForm({
  stockItem,
  products,
  branches,
  locations,
  onSubmit,
  isSubmitting,
}: StockItemFormProps) {
  // State to store the selected product ID
  const [selectedProductId, setSelectedProductId] = useState<number | null>(stockItem?.product_id || null);
  // State to track if the selected product has serial numbers
  const [hasSerialNumbers, setHasSerialNumbers] = useState<boolean>(false);

  // Fetch product details when a product is selected
  const { data: selectedProduct, isLoading: isLoadingProduct } = useProduct(
    selectedProductId || 0,
    {
      enabled: !!selectedProductId,
    }
  );

  const form = useForm<FormValues>({
    resolver: zodResolver(stockItemSchema),
    defaultValues: {
      product_id: stockItem?.product_id || 0,
      branch_id: stockItem?.branch_id || 1, // Default to headquarters (branch_id 1)
      quantity: stockItem?.quantity || 0,
      quantity_reserved: stockItem?.quantity_reserved || 0,

      // Serial numbers
      serial_numbers: [],

      // Pricing fields
      buying_price: stockItem?.buying_price || 0,
      default_buying_price: stockItem?.default_buying_price || 0,
      default_selling_price: stockItem?.default_selling_price || 0,
      default_wholesale_price: stockItem?.default_wholesale_price || 0,

      // VAT-related fields
      buying_price_including_vat: stockItem?.buying_price_including_vat,
      buying_price_excluding_vat: stockItem?.buying_price_excluding_vat,
      buying_vat_amount: stockItem?.buying_vat_amount,
      buying_vat_rate: stockItem?.buying_vat_rate,

      // Batch/lot tracking
      batch_number: stockItem?.batch_number || '',
      expiry_date: stockItem?.expiry_date ? new Date(stockItem.expiry_date).toISOString().split('T')[0] : '',
      manufacturing_date: stockItem?.manufacturing_date ? new Date(stockItem.manufacturing_date).toISOString().split('T')[0] : '',

      // Inventory management
      reorder_level: stockItem?.reorder_level,
      reorder_quantity: stockItem?.reorder_quantity,
      valuation_method: stockItem?.valuation_method || 'WEIGHTED_AVERAGE',
    },
  });

  // Update form when editing an existing stock item
  useEffect(() => {
    if (stockItem) {
      form.reset({
        product_id: stockItem.product_id,
        branch_id: stockItem.branch_id,
        quantity: stockItem.quantity,
        quantity_reserved: stockItem.quantity_reserved || 0,

        // Pricing fields
        buying_price: stockItem.buying_price,
        default_buying_price: stockItem.default_buying_price || 0,
        default_selling_price: stockItem.default_selling_price || 0,
        default_wholesale_price: stockItem.default_wholesale_price || 0,

        // VAT-related fields
        buying_price_including_vat: stockItem.buying_price_including_vat,
        buying_price_excluding_vat: stockItem.buying_price_excluding_vat,
        buying_vat_amount: stockItem.buying_vat_amount,
        buying_vat_rate: stockItem.buying_vat_rate,

        // Batch/lot tracking
        batch_number: stockItem.batch_number || '',
        expiry_date: stockItem.expiry_date ? new Date(stockItem.expiry_date).toISOString().split('T')[0] : '',
        manufacturing_date: stockItem.manufacturing_date ? new Date(stockItem.manufacturing_date).toISOString().split('T')[0] : '',

        // Inventory management
        reorder_level: stockItem.reorder_level,
        reorder_quantity: stockItem.reorder_quantity,
        valuation_method: stockItem.valuation_method || 'WEIGHTED_AVERAGE',
      });
    }
  }, [stockItem, form]);

  // Prefill form with product data when a product is selected
  useEffect(() => {
    if (!stockItem && selectedProduct) {
      console.log('Prefilling form with product data:', selectedProduct);

      // Check if selectedProduct is null (product not found)
      if (!selectedProduct) {
        console.warn('Selected product not found or could not be loaded');
        toast.error('Product details could not be loaded', {
          description: 'Please try selecting a different product or contact support if the issue persists.'
        });
        return;
      }

      // Check if product has serial numbers
      setHasSerialNumbers(!!selectedProduct.has_serial);
      console.log('Product has serial numbers:', selectedProduct.has_serial);

      // Reset serial numbers when product changes
      form.setValue('serial_numbers', []);

      // Prefill prices from product data
      if (selectedProduct.suggested_buying_price) {
        form.setValue('buying_price', selectedProduct.suggested_buying_price);
        form.setValue('default_buying_price', selectedProduct.suggested_buying_price);
      }

      if (selectedProduct.suggested_selling_price) {
        // Set both default_selling_price and selling_price to the product's suggested_selling_price
        form.setValue('default_selling_price', selectedProduct.suggested_selling_price);
        console.log('Product has suggested selling price:', selectedProduct.suggested_selling_price);
      }

      if (selectedProduct.default_wholesale_price) {
        form.setValue('default_wholesale_price', selectedProduct.default_wholesale_price);
        console.log('Product has default wholesale price:', selectedProduct.default_wholesale_price);
      }

      // Prefill VAT rate if available
      if (selectedProduct.vat_rate) {
        form.setValue('buying_vat_rate', selectedProduct.vat_rate);

        // Calculate VAT values if buying price is already set
        const buyingPrice = form.getValues('buying_price');
        if (buyingPrice && selectedProduct.vat_rate) {
          const vatRate = parseFloat(selectedProduct.vat_rate.toString());
          const buyingPriceValue = parseFloat(buyingPrice.toString());

          if (vatRate > 0 && buyingPriceValue > 0) {
            const vatAmount = (buyingPriceValue * vatRate) / 100;
            const priceExcludingVat = buyingPriceValue;
            const priceIncludingVat = buyingPriceValue + vatAmount;

            form.setValue('buying_vat_amount', vatAmount);
            form.setValue('buying_price_excluding_vat', priceExcludingVat);
            form.setValue('buying_price_including_vat', priceIncludingVat);
          }
        }
      }

      // Prefill reorder levels if available
      if (selectedProduct.reorder_level) {
        form.setValue('reorder_level', selectedProduct.reorder_level);
      }

      if (selectedProduct.reorder_quantity) {
        form.setValue('reorder_quantity', selectedProduct.reorder_quantity);
      }
    }
  }, [selectedProduct, form, stockItem]);

  const handleSubmit = async (data: FormValues) => {
    try {
      // Validate serial numbers if product has serial numbers
      if (hasSerialNumbers && data.quantity > 0) {
        const serialNumbers = data.serial_numbers || [];
        if (serialNumbers.length !== data.quantity) {
          toast.error(`Serial numbers required`, {
            description: `You need to provide ${data.quantity} serial numbers for this product.`
          });
          return;
        }
      }

      // Ensure branch_id is always set to 1 (headquarters)
      const submissionData = {
        ...data,
        branch_id: 1,

        // Ensure all necessary fields are included
        product_id: data.product_id,
        quantity: data.quantity,
        quantity_reserved: data.quantity_reserved || 0,

        // Include serial numbers if product has serial numbers
        serial_numbers: hasSerialNumbers ? data.serial_numbers : undefined,

        // Pricing fields
        buying_price: data.buying_price,
        default_buying_price: data.default_buying_price || data.buying_price,
        default_selling_price: data.default_selling_price || (selectedProduct?.suggested_selling_price || 0),
        default_wholesale_price: data.default_wholesale_price,

        // VAT-related fields
        buying_price_including_vat: data.buying_price_including_vat,
        buying_price_excluding_vat: data.buying_price_excluding_vat,
        buying_vat_amount: data.buying_vat_amount,
        buying_vat_rate: data.buying_vat_rate,

        // Batch/lot tracking
        batch_number: data.batch_number,
        expiry_date: data.expiry_date,
        manufacturing_date: data.manufacturing_date,

        // Inventory management
        reorder_level: data.reorder_level,
        reorder_quantity: data.reorder_quantity,
        valuation_method: data.valuation_method || 'WEIGHTED_AVERAGE',
      };

      console.log('Submitting stock item data:', submissionData);
      await onSubmit(submissionData);
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Failed to save stock item');
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{stockItem ? 'Edit Stock Item' : 'Create Stock Item'}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="product_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Product</FormLabel>
                    <Select
                      onValueChange={(value) => {
                        try {
                          const productId = parseInt(value, 10);
                          field.onChange(productId);
                          setSelectedProductId(productId);
                        } catch (error) {
                          console.error('Error parsing product ID:', error);
                          toast.error('Invalid product selection', {
                            description: 'Please try selecting a different product.'
                          });
                        }
                      }}
                      defaultValue={field.value ? field.value.toString() : undefined}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a product" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {products && products.length > 0 ? (
                          products.map((product) => (
                            <SelectItem key={product.id} value={product.id.toString()}>
                              {product.name}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-products" disabled>
                            No products available
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="branch_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Branch</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(parseInt(value, 10))}
                      defaultValue={field.value ? field.value.toString() : "1"} // Default to headquarters
                      disabled={true} // Make branch selection non-editable
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a branch" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {branches.map((branch) => (
                          <SelectItem key={branch.id} value={branch.id.toString()}>
                            {branch.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Fixed to Headquarters for stock creation
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Location field removed as per requirement */}

            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantity</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      step="1"
                      {...field}
                      onChange={(e) => {
                        field.onChange(e);
                        // Reset serial numbers if quantity changes
                        if (hasSerialNumbers) {
                          form.setValue('serial_numbers', []);
                        }
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Serial Number Input - only shown for products with serial numbers */}
            {hasSerialNumbers && (
              <FormField
                control={form.control}
                name="serial_numbers"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Serial Numbers</FormLabel>
                    <FormControl>
                      <SerialNumberInput
                        quantity={form.getValues('quantity')}
                        value={field.value || []}
                        onChange={field.onChange}
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      {form.getValues('quantity') > 0
                        ? `You need to add ${form.getValues('quantity')} serial numbers for this product.`
                        : 'Set a quantity first, then add serial numbers.'}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="buying_price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Buying Price</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => {
                          field.onChange(e);
                          // Get the current VAT rate
                          const vatRate = parseFloat(form.getValues("buying_vat_rate") || "0");
                          const buyingPrice = parseFloat(e.target.value) || 0;

                          // Update default buying price to match
                          form.setValue("default_buying_price", buyingPrice);

                          // Calculate VAT values if VAT rate is set
                          if (vatRate > 0 && buyingPrice > 0) {
                            const vatAmount = (buyingPrice * vatRate) / 100;
                            const priceExcludingVat = buyingPrice;
                            const priceIncludingVat = buyingPrice + vatAmount;

                            // Update form values
                            form.setValue("buying_vat_amount", vatAmount);
                            form.setValue("buying_price_excluding_vat", priceExcludingVat);
                            form.setValue("buying_price_including_vat", priceIncludingVat);
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="buying_vat_rate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>VAT Rate (%)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="16.00"
                        {...field}
                        value={field.value || ''}
                        onChange={(e) => {
                          field.onChange(e);
                          // Get the current buying price
                          const buyingPrice = parseFloat(form.getValues("buying_price") || "0");
                          const vatRate = parseFloat(e.target.value) || 0;

                          // Calculate VAT values
                          if (vatRate > 0 && buyingPrice > 0) {
                            const vatAmount = (buyingPrice * vatRate) / 100;
                            const priceExcludingVat = buyingPrice;
                            const priceIncludingVat = buyingPrice + vatAmount;

                            // Update form values
                            form.setValue("buying_vat_amount", vatAmount);
                            form.setValue("buying_price_excluding_vat", priceExcludingVat);
                            form.setValue("buying_price_including_vat", priceIncludingVat);
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="default_selling_price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Default Selling Price</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>Default selling price for this item</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="default_wholesale_price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Default Wholesale Price</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>Default wholesale price for this item</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="batch_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Order/Lot Number</FormLabel>
                    <FormControl>
                      <Input placeholder="ORDER-001" {...field} value={field.value || ''} />
                    </FormControl>
                    <FormDescription>Optional order or lot number</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expiry_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Expiry Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} value={field.value || ''} />
                    </FormControl>
                    <FormDescription>Optional expiry date</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="manufacturing_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Manufacturing Date</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} value={field.value || ''} />
                  </FormControl>
                  <FormDescription>Optional manufacturing date</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="reorder_level"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reorder Level</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="1"
                        placeholder="10"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>Quantity at which to reorder</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="reorder_quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reorder Quantity</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        step="1"
                        placeholder="20"
                        {...field}
                        value={field.value || ''}
                      />
                    </FormControl>
                    <FormDescription>Quantity to order when reordering</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="valuation_method"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Valuation Method</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="FIFO" />
                        </FormControl>
                        <FormLabel className="font-normal">First In, First Out (FIFO)</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="LIFO" />
                        </FormControl>
                        <FormLabel className="font-normal">Last In, First Out (LIFO)</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="WEIGHTED_AVERAGE" />
                        </FormControl>
                        <FormLabel className="font-normal">Weighted Average</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : stockItem ? 'Update Stock Item' : 'Create Stock Item'}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
