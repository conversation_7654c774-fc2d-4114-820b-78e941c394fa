import { api } from '@/lib/api';

export interface CashStatusReportParams {
  start_date: string;
  end_date: string;
  region_id?: number;
  branch_id?: number;
  include_breakdown?: boolean;
  page?: number;
  limit?: number;
}

export interface CashStatusReportResponse {
  status: string;
  data: {
    summary: {
      totalOpeningCash: number;
      totalClosingCash: number;
      totalInflows: number;
      totalOutflows: number;
      netCashChange: number;
    };
    cashPosition: Array<{
      branch_id: number;
      branch_name: string;
      region_id: number;
      region_name: string;
      total_opening_cash: number;
      total_closing_cash: number;
      net_cash_change: number;
    }>;
    cashInflows: {
      cashSales: {
        data: Array<{
          branch_id: number;
          branch_name: string;
          region_id: number;
          region_name: string;
          amount: number;
          transaction_count: number;
        }>;
        total: number;
      };
      dsaReconciliations: {
        data: Array<{
          branch_id: number;
          branch_name: string;
          region_id: number;
          region_name: string;
          amount: number;
          transaction_count: number;
        }>;
        total: number;
      };
      floatReturns: {
        data: Array<{
          branch_id: number;
          branch_name: string;
          region_id: number;
          region_name: string;
          amount: number;
          transaction_count: number;
        }>;
        total: number;
      };
      total: number;
    };
    cashOutflows: {
      expenses: {
        data: Array<{
          branch_id: number;
          branch_name: string;
          region_id: number;
          region_name: string;
          amount: number;
          transaction_count: number;
        }>;
        total: number;
      };
      bankingDeposits: {
        data: Array<{
          branch_id: number;
          branch_name: string;
          region_id: number;
          region_name: string;
          amount: number;
          transaction_count: number;
        }>;
        total: number;
      };
      floatAllocations: {
        data: Array<{
          branch_id: number;
          branch_name: string;
          region_id: number;
          region_name: string;
          amount: number;
          transaction_count: number;
        }>;
        total: number;
      };
      total: number;
    };
    byRegion?: Array<{
      region_id: number;
      region_name: string;
      summary: {
        totalOpeningCash: number;
        totalClosingCash: number;
        totalInflows: number;
        totalOutflows: number;
        netCashChange: number;
      };
    }>;
    byBranch?: Array<{
      branch_id: number;
      branch_name: string;
      region_id: number;
      region_name: string;
      summary: {
        totalOpeningCash: number;
        totalClosingCash: number;
        totalInflows: number;
        totalOutflows: number;
        netCashChange: number;
      };
    }>;
    branchPagination?: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
    regionPagination?: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface RunningBalancesReportParams {
  start_date: string;
  end_date: string;
  region_id?: number;
  branch_id?: number;
}

export interface RunningBalancesReportResponse {
  status: string;
  data: {
    summary: {
      total_cash_balance: number;
      total_mpesa_float_balance: number;
      total_banking_balance: number;
      total_sales: number;
      total_expenses: number;
      net_position: number;
    };
    dailyBalances: Array<{
      date: string;
      cash_balance: number;
      mpesa_float_balance: number;
      banking_balance: number;
      sales_total: number;
      expenses_total: number;
      net_position: number;
    }>;
    cashBalances: Array<any>;
    mpesaFloatBalances: Array<any>;
    bankingBalances: Array<any>;
    salesByPaymentMethod: Array<any>;
    expenses: Array<any>;
  };
}

export const reportService = {
  getRunningBalancesReport: async (params: RunningBalancesReportParams): Promise<RunningBalancesReportResponse> => {
    try {
      // Log the full URL being requested
      const url = `/reports/running-balances`;
      console.log(`Making request to ${url} with params:`, JSON.stringify(params, null, 2));

      // Validate required parameters
      if (!params.start_date || !params.end_date) {
        console.error('Missing required date parameters:', params);
        throw new Error('Start date and end date are required for running balances report');
      }

      // Ensure dates are properly formatted
      const formattedParams = {
        ...params,
        start_date: params.start_date.split('T')[0], // Ensure we only send the date part
        end_date: params.end_date.split('T')[0]
      };

      console.log(`Making request with formatted params:`, JSON.stringify(formattedParams, null, 2));

      const response = await api.get(url, {
        params: formattedParams,
        // Add timeout to prevent hanging requests
        timeout: 60000, // Increased timeout to 60 seconds
        headers: {
          'Accept': 'application/json'
        }
      });

      // Validate the response
      if (!response.data || !response.data.data) {
        console.error('Invalid response format:', response.data);
        throw new Error('Invalid response format from server');
      }

      return response.data;
    } catch (error) {
      console.error('Error fetching running balances report:', error);
      throw error;
    }
  },

  getCashStatusReport: async (params: CashStatusReportParams): Promise<CashStatusReportResponse> => {
    try {
      // Note: We don't need to add cache-busting here anymore
      // It's now handled globally in the API interceptor

      // Log the full URL being requested
      const url = `/reports/cash-status`;
      console.log(`Making request to ${url} with params:`, JSON.stringify(params, null, 2));

      // Validate required parameters
      if (!params.start_date || !params.end_date) {
        console.error('Missing required date parameters:', params);
        throw new Error('Start date and end date are required for cash status report');
      }

      // Ensure dates are properly formatted
      const formattedParams = {
        ...params,
        start_date: params.start_date.split('T')[0], // Ensure we only send the date part
        end_date: params.end_date.split('T')[0]
      };

      console.log(`Making request with formatted params:`, JSON.stringify(formattedParams, null, 2));

      const response = await api.get(url, {
        params: formattedParams,
        // Add timeout to prevent hanging requests
        timeout: 60000, // Increased timeout to 60 seconds
        headers: {
          'Accept': 'application/json'
        }
      });

      // Validate the response
      if (!response.data || !response.data.data) {
        console.error('Invalid response format:', response.data);
        throw new Error('Invalid response format from server');
      }

      // Check if summary contains all zeros
      const summary = response.data.data.summary;
      if (summary &&
          summary.totalOpeningCash === 0 &&
          summary.totalClosingCash === 0 &&
          summary.totalInflows === 0 &&
          summary.totalOutflows === 0) {
        console.warn('Cash status report returned all zeros. This might indicate no data for the selected period.');
      }

      return response.data;
    } catch (error) {
      console.error('Error in getCashStatusReport service:', error);

      // Enhanced error logging
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Server responded with error:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.headers
        });

        // If we have a more specific error message from the server, use it
        if (error.response.data && error.response.data.message) {
          throw new Error(`Server error: ${error.response.data.message}`);
        }
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received from server:', error.request);
        throw new Error('No response received from server. Please check your network connection.');
      }

      throw error;
    }
  }
};
