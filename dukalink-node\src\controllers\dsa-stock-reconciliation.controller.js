const { DsaStockReconciliation, Tenant, Branch, User, Product, DsaStockAssignment, StockItem, Role, Sale, SaleItem, Customer } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../../config/database');
const AppError = require('../utils/error');
const logger = require('../utils/logger');

/**
 * Get all DSA stock reconciliations
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllDsaStockReconciliations = async (req, res, next) => {
  try {
    const { customer_id, start_date, end_date } = req.query;

    const whereClause = {};

    if (customer_id) {
      whereClause.customer_id = customer_id;
    }

    if (start_date && end_date) {
      whereClause.reconciled_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      whereClause.reconciled_at = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      whereClause.reconciled_at = {
        [Op.lte]: new Date(end_date)
      };
    }

    const reconciliations = await DsaStockReconciliation.findAll({
      where: whereClause,
      include: [
        {
          model: User,
          as: 'ReconciliationCreator',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'ReconciliationUpdater',
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [['reconciled_at', 'DESC']]
    });

    // For each reconciliation, fetch the associated sales from the main sales table
    const reconciliationsWithSales = await Promise.all(reconciliations.map(async (reconciliation) => {
      const sales = await Sale.findAll({
        where: {
          is_dsa: true,
          payment_reference: `DSA Reconciliation #${reconciliation.id}`
        },
        include: [{
          model: SaleItem,
          include: [{
            model: Product,
            attributes: ['id', 'name', 'sku']
          }]
        }]
      });

      // Add sales to the reconciliation
      const reconciliationData = reconciliation.toJSON();
      reconciliationData.sales = sales;
      return reconciliationData;
    }));

    return res.status(200).json(reconciliationsWithSales);
  } catch (error) {
    logger.error(`Error fetching DSA stock reconciliations: ${error.message}`);
    next(error);
  }
};

/**
 * Get DSA stock reconciliation by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getDsaStockReconciliationById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const reconciliation = await DsaStockReconciliation.findByPk(id, {
      include: [
        {
          model: User,
          as: 'ReconciliationCreator',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'ReconciliationUpdater',
          attributes: ['id', 'name', 'email']
        }
      ]
    });

    // Fetch the sales created during reconciliation
    const sales = await Sale.findAll({
      where: {
        is_dsa: true,
        payment_reference: `DSA Reconciliation #${id}`
      },
      include: [
        {
          model: User,
          attributes: ['id', 'name', 'phone', 'email']
        },
        {
          model: SaleItem,
          include: [{
            model: Product,
            attributes: ['id', 'name', 'sku']
          }]
        }
      ]
    });

    // Add sales to the response
    reconciliation.dataValues.sales = sales;

    if (!reconciliation) {
      return next(new AppError('DSA stock reconciliation not found', 404));
    }

    // Add payment information
    reconciliation.dataValues.payment_info = {
      total_amount: parseFloat(reconciliation.total_amount || 0),
      amount_paid: parseFloat(reconciliation.amount_paid || 0),
      balance: parseFloat(reconciliation.balance || 0),
      payment_status: reconciliation.payment_status,
      last_payment_date: reconciliation.last_payment_date
    };

    return res.status(200).json({
      success: true,
      data: reconciliation
    });
  } catch (error) {
    logger.error(`Error fetching DSA stock reconciliation: ${error.message}`);
    next(error);
  }
};

/**
 * Get all reconciliations for a specific DSA user
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getReconciliationsByUserId = async (req, res, next) => {
  try {
    const { userId } = req.params;

    // Find DSA agent role
    const dsaRole = await Role.findOne({
      where: { name: 'dsa_agent' }
    });

    if (!dsaRole) {
      return next(new AppError('DSA agent role not found', 404));
    }

    // Check if DSA user exists
    const dsaUser = await User.findOne({
      where: {
        id: userId,
        role_id: dsaRole.id,
        is_dsa: true,
        deleted_at: null
      }
    });

    if (!dsaUser) {
      return next(new AppError('DSA user not found', 404));
    }

    const reconciliations = await DsaStockReconciliation.findAll({
      where: { user_id: userId },
      order: [['reconciled_at', 'DESC']]
    });

    // For each reconciliation, fetch the associated sales from the main sales table
    const reconciliationsWithSales = await Promise.all(reconciliations.map(async (reconciliation) => {
      const sales = await Sale.findAll({
        where: {
          is_dsa: true,
          payment_reference: `DSA Reconciliation #${reconciliation.id}`
        },
        include: [{
          model: SaleItem,
          include: [{
            model: Product,
            attributes: ['id', 'name', 'sku']
          }]
        }]
      });

      // Add sales to the reconciliation
      const reconciliationData = reconciliation.toJSON();
      reconciliationData.sales = sales;
      return reconciliationData;
    }));

    return res.status(200).json(reconciliationsWithSales);
  } catch (error) {
    logger.error(`Error fetching reconciliations for agent: ${error.message}`);
    next(error);
  }
};

/**
 * Create a new DSA stock reconciliation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createDsaStockReconciliation = async (req, res, next) => {
  try {
    const {
      customer_id,
      total_assigned,
      total_sold,
      total_returned,
      cash_received,
      paybill_amount,
      notes,
      products,
      assignment_identifier // Add support for assignment_identifier
    } = req.body;

    // Check if DSA customer exists
    const dsaCustomer = await Customer.findOne({
      where: {
        id: customer_id,
        is_dsa: true,
        deleted_at: null
      }
    });

    if (!dsaCustomer) {
      return next(new AppError('DSA customer not found', 404));
    }

    // Start a transaction to ensure data consistency
    const transaction = await sequelize.transaction();

    try {
      // Calculate total amount based on products
      let totalAmount = 0;
      if (products && products.length > 0) {
        for (const product of products) {
          const remainingQuantity = product.quantity_assigned - product.quantity_returned;
          const productValue = remainingQuantity * product.default_wholesale_price;
          totalAmount += productValue;
        }
      }

      // Calculate initial payment from cash and paybill
      const initialPayment = parseFloat(cash_received || 0) + parseFloat(paybill_amount || 0);

      // Calculate balance
      const balance = Math.max(0, totalAmount - initialPayment);

      // Determine payment status
      let paymentStatus = 'UNPAID';
      if (initialPayment > 0) {
        paymentStatus = balance > 0 ? 'PARTIALLY_PAID' : 'FULLY_PAID';
      }

      // Create reconciliation record
      const reconciliation = await DsaStockReconciliation.create({
        customer_id,
        total_assigned,
        total_sold,
        total_returned,
        cash_received: cash_received || 0,
        paybill_amount: paybill_amount || 0,
        total_amount: totalAmount,
        amount_paid: initialPayment,
        balance,
        payment_status: paymentStatus,
        last_payment_date: initialPayment > 0 ? new Date() : null,
        notes,
        reconciled_at: new Date(),
        created_by: req.user?.id,
        last_updated_by: req.user?.id,
        assignment_identifier: assignment_identifier // Store the assignment identifier
      }, { transaction });

      // If products array is provided, process the reconciliation
      if (products && Array.isArray(products) && products.length > 0) {
        // Get all product details for pricing information
        const productIds = products.map(p => p.product_id);

        // Get current stock assignments for this customer
        const stockAssignments = await DsaStockAssignment.findAll({
          where: {
            customer_id,
            product_id: { [Op.in]: productIds }
          }
        }, { transaction });

        // Create a map of product ID to stock assignment for easy lookup
        const assignmentMap = {};
        stockAssignments.forEach(assignment => {
          assignmentMap[assignment.product_id] = assignment;
        });

        // Get existing DSA sales for this customer that haven't been reconciled yet
        const existingSales = await Sale.findAll({
          where: {
            customer_id,
            is_dsa: true,
            payment_reference: { [Op.notLike]: '%Reconciliation%' } // Exclude already reconciled sales
          },
          include: [{
            model: SaleItem,
            include: [{
              model: Product,
              attributes: ['id', 'name', 'sku']
            }]
          }]
        }, { transaction });

        // Mark these sales as reconciled
        for (const sale of existingSales) {
          await sale.update({
            payment_reference: `DSA Reconciliation #${reconciliation.id}`,
            notes: sale.notes ? `${sale.notes} (Reconciled on ${new Date().toISOString()})` : `Reconciled on ${new Date().toISOString()}`,
            last_updated_by: req.user?.id
          }, { transaction });
        }

        // Process each product in the reconciliation
        for (const product of products) {
          const { product_id, quantity_sold, quantity_returned } = product;

          // Update stock assignment if it exists
          const assignment = assignmentMap[product_id];
          if (assignment) {
            await assignment.update({
              quantity_returned: assignment.quantity_returned + quantity_returned,
              reconciled_at: new Date(),
              // Reset quantity_sold to 0 since we're reconciling
              quantity_sold: 0,
              // Mark as reconciled
              reconciled: true,
              reconciliation_id: reconciliation.id
            }, { transaction });

            // Update branch stock for reconciliation
            const branchStock = await StockItem.findOne({
              where: {
                branch_id: dsaCustomer.branch_id,
                product_id
              }
            }, { transaction });

            if (branchStock) {
              // For DSA reconciliation, we only subtract sold items from stock
              // Returned items are NOT added back to stock (considered damaged/unsellable)
              await branchStock.update({
                quantity: branchStock.quantity - quantity_sold
              }, { transaction });
            } else {
              // If no stock item exists and there were sales, create with negative quantity
              // This indicates stock deficit that needs to be addressed
              if (quantity_sold > 0) {
                await StockItem.create({
                  branch_id: dsaCustomer.branch_id,
                  product_id,
                  quantity: -quantity_sold
                }, { transaction });
              }
            }

            // Log the reconciliation for audit purposes
            logger.info(`DSA reconciliation: Product ${product_id}, Sold: ${quantity_sold}, Returned: ${quantity_returned} (not added to stock)`);
          }
        }
      }

      // Fetch the complete reconciliation within the transaction
      const completeReconciliation = await DsaStockReconciliation.findByPk(reconciliation.id, { transaction });

      // Fetch the sales associated with this reconciliation within the transaction
      const sales = await Sale.findAll({
        where: {
          is_dsa: true,
          payment_reference: `DSA Reconciliation #${reconciliation.id}`
        },
        include: [{
          model: SaleItem,
          include: [{
            model: Product,
            attributes: ['id', 'name', 'sku']
          }]
        }],
        transaction
      });

      // Add sales to the response
      completeReconciliation.dataValues.sales = sales;

      // Add payment information
      completeReconciliation.dataValues.payment_info = {
        total_amount: parseFloat(completeReconciliation.total_amount || 0),
        amount_paid: parseFloat(completeReconciliation.amount_paid || 0),
        balance: parseFloat(completeReconciliation.balance || 0),
        payment_status: completeReconciliation.payment_status,
        last_payment_date: completeReconciliation.last_payment_date
      };

      // Commit the transaction only after all operations are successful
      await transaction.commit();

      return res.status(201).json({
        success: true,
        message: 'DSA stock reconciliation created successfully',
        data: completeReconciliation
      });
    } catch (error) {
      // Rollback transaction in case of error (only if not already committed)
      if (!transaction.finished) {
        await transaction.rollback();
      }
      throw error;
    }
  } catch (error) {
    logger.error(`Error creating DSA stock reconciliation: ${error.message}`);
    next(error);
  }
};

/**
 * Update a DSA stock reconciliation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateDsaStockReconciliation = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { total_assigned, total_sold, total_returned, cash_received, paybill_amount, notes } = req.body;

    // Check if reconciliation exists
    const reconciliation = await DsaStockReconciliation.findByPk(id);
    if (!reconciliation) {
      return next(new AppError('DSA stock reconciliation not found', 404));
    }

    // Update reconciliation
    await reconciliation.update({
      total_assigned: total_assigned !== undefined ? total_assigned : reconciliation.total_assigned,
      total_sold: total_sold !== undefined ? total_sold : reconciliation.total_sold,
      total_returned: total_returned !== undefined ? total_returned : reconciliation.total_returned,
      cash_received: cash_received !== undefined ? cash_received : reconciliation.cash_received,
      paybill_amount: paybill_amount !== undefined ? paybill_amount : reconciliation.paybill_amount,
      notes: notes !== undefined ? notes : reconciliation.notes,
      last_updated_by: req.user?.id
    });

    return res.status(200).json(reconciliation);
  } catch (error) {
    logger.error(`Error updating DSA stock reconciliation: ${error.message}`);
    next(error);
  }
};

/**
 * Delete a DSA stock reconciliation
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteDsaStockReconciliation = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Check if reconciliation exists
    const reconciliation = await DsaStockReconciliation.findByPk(id);
    if (!reconciliation) {
      return next(new AppError('DSA stock reconciliation not found', 404));
    }

    // Start a transaction
    const transaction = await sequelize.transaction();

    try {
      // Delete associated sales in the main sales table
      const sales = await Sale.findAll({
        where: {
          is_dsa: true,
          payment_reference: `DSA Reconciliation #${id}`
        }
      }, { transaction });

      // Delete sale items first
      for (const sale of sales) {
        await SaleItem.destroy({
          where: { sale_id: sale.id },
          transaction
        });

        // Delete the sale
        await sale.destroy({ transaction });
      }

      // Delete reconciliation
      await reconciliation.destroy({ transaction });

      await transaction.commit();

      return res.status(200).json({
        message: 'DSA stock reconciliation deleted successfully',
        salesDeleted: sales.length
      });
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    logger.error(`Error deleting DSA stock reconciliation: ${error.message}`);
    next(error);
  }
};

/**
 * Get all reconciliations for a specific DSA customer
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getReconciliationsByCustomerId = async (req, res, next) => {
  try {
    const { customerId } = req.params;

    // Check if DSA customer exists
    const dsaCustomer = await Customer.findOne({
      where: {
        id: customerId,
        is_dsa: true,
        deleted_at: null
      }
    });

    if (!dsaCustomer) {
      return next(new AppError('DSA customer not found', 404));
    }

    const reconciliations = await DsaStockReconciliation.findAll({
      where: { customer_id: customerId },
      order: [['reconciled_at', 'DESC']]
    });

    // For each reconciliation, fetch the associated sales from the main sales table
    const reconciliationsWithSales = await Promise.all(reconciliations.map(async (reconciliation) => {
      const sales = await Sale.findAll({
        where: {
          is_dsa: true,
          payment_reference: `DSA Reconciliation #${reconciliation.id}`
        },
        include: [{
          model: SaleItem,
          include: [{
            model: Product,
            attributes: ['id', 'name', 'sku']
          }]
        }]
      });

      // Add sales to the reconciliation
      const reconciliationData = reconciliation.toJSON();
      reconciliationData.sales = sales;

      // Add payment information
      reconciliationData.payment_info = {
        total_amount: parseFloat(reconciliation.total_amount || 0),
        amount_paid: parseFloat(reconciliation.amount_paid || 0),
        balance: parseFloat(reconciliation.balance || 0),
        payment_status: reconciliation.payment_status,
        last_payment_date: reconciliation.last_payment_date
      };

      return reconciliationData;
    }));

    return res.status(200).json(reconciliationsWithSales);
  } catch (error) {
    logger.error(`Error fetching reconciliations for DSA customer: ${error.message}`);
    next(error);
  }
};

module.exports = {
  getAllDsaStockReconciliations,
  getDsaStockReconciliationById,
  getReconciliationsByUserId,
  getReconciliationsByCustomerId,
  createDsaStockReconciliation,
  updateDsaStockReconciliation,
  deleteDsaStockReconciliation
};
