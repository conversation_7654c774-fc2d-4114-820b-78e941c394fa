const db = require('../../models');
const { Branch, Region, User } = require('../../models');
const AppError = require('../../utils/error');
const { Op } = require('sequelize');
const moment = require('moment');
const sequelizeConnection = require('../../../config/database');

// Get sequelize instance from the direct connection
// This ensures we're using the properly initialized connection
const sequelize = sequelizeConnection;

// Log warning if sequelize is not available
if (!sequelize) {
  console.error('WARNING: Database connection not properly initialized. Some features may not work correctly.');
}

/**
 * Get cash status report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getCashStatusReport = async (req, res, next) => {
  try {
    // Check if database connection is available
    if (!sequelize) {
      console.error('Database connection is not available in getCashStatusReport');
      return next(new AppError('Database connection is not available. Please check server configuration.', 500));
    }

    // Verify that sequelize has the query method
    if (typeof sequelize.query !== 'function') {
      console.error('Sequelize query method is not available');
      return next(new AppError('Database connection is improperly configured. Missing query method.', 500));
    }

    // Test the database connection
    try {
      await sequelize.authenticate();
      console.log('Database connection is working properly');
    } catch (dbError) {
      console.error('Database connection test failed:', dbError);
      return next(new AppError(`Database connection test failed: ${dbError.message}`, 500));
    }

    // Ensure req and req.query exist
    if (!req) {
      return next(new AppError('Request object is undefined', 500));
    }

    // Initialize query if it doesn't exist
    if (!req.query) {
      req.query = {};
    }

    console.log('Raw query parameters:', req.query);

    // Handle both camelCase and snake_case parameter names
    // Default to a 30-day range if no dates are provided
    const start_date = req.query.start_date || req.query.startDate || moment().subtract(30, 'days').startOf('day').format('YYYY-MM-DD HH:mm:ss');
    const end_date = req.query.end_date || req.query.endDate || moment().endOf('day').format('YYYY-MM-DD HH:mm:ss');

    console.log('Date range for report:', {
      start_date_raw: start_date,
      end_date_raw: end_date,
      start_date_parsed: moment(start_date).format('YYYY-MM-DD HH:mm:ss'),
      end_date_parsed: moment(end_date).format('YYYY-MM-DD HH:mm:ss'),
      days_difference: moment(end_date).diff(moment(start_date), 'days')
    });

    // DIAGNOSTIC: Direct query to check all sales with their payment methods
    try {
      console.log('Running diagnostic query to check all sales...');

      const diagnosticQuery = `
        SELECT
          s.id,
          s.total_amount,
          s.payment_method_id,
          pm.name AS payment_method_name,
          pm.code AS payment_method_code,
          s.created_at,
          s.status
        FROM sales s
        LEFT JOIN payment_methods pm ON s.payment_method_id = pm.id
        WHERE s.tenant_id = :tenantId
          AND s.created_at BETWEEN :startDate AND :endDate
        ORDER BY s.created_at DESC
        LIMIT 20`;

      const salesDiagnostic = await sequelize.query(diagnosticQuery, {
        replacements: {
          tenantId,
          startDate: start_date,
          endDate: end_date
        },
        type: sequelize.QueryTypes.SELECT
      });

      console.log(`Found ${salesDiagnostic.length} recent sales:`, salesDiagnostic);

      // Check payment methods
      const paymentMethodsQuery = `SELECT id, name, code FROM payment_methods`;
      const paymentMethods = await sequelize.query(paymentMethodsQuery, {
        type: sequelize.QueryTypes.SELECT
      });

      console.log('All payment methods:', paymentMethods);

      // Count sales by payment method
      const salesByPaymentMethodQuery = `
        SELECT
          pm.id AS payment_method_id,
          pm.name AS payment_method_name,
          pm.code AS payment_method_code,
          COUNT(s.id) AS sales_count,
          SUM(s.total_amount) AS total_amount
        FROM sales s
        JOIN payment_methods pm ON s.payment_method_id = pm.id
        WHERE s.tenant_id = :tenantId
          AND s.created_at BETWEEN :startDate AND :endDate
          AND s.status = 'completed'
        GROUP BY pm.id
        ORDER BY total_amount DESC`;

      const salesByPaymentMethod = await sequelize.query(salesByPaymentMethodQuery, {
        replacements: {
          tenantId,
          startDate: start_date,
          endDate: end_date
        },
        type: sequelize.QueryTypes.SELECT
      });

      console.log('Sales by payment method:', salesByPaymentMethod);

    } catch (diagnosticError) {
      console.error('Error running diagnostic queries:', diagnosticError);
    }
    const region_id = req.query.region_id || req.query.regionId;
    const branch_id = req.query.branch_id || req.query.branchId;
    const include_breakdown = req.query.include_breakdown || req.query.includeBreakdown || 'true';
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 50;

    // Parse boolean
    const includeBreakdown = include_breakdown === 'true';

    // Get tenant ID from authenticated user
    const tenantId = req.user?.tenant_id;

    if (!tenantId) {
      return next(new AppError('Tenant ID not found in user data', 400));
    }

    // Log the parameters for debugging
    console.log('Cash Status Report Parameters:', {
      tenantId,
      start_date,
      end_date,
      region_id,
      branch_id,
      includeBreakdown
    });

    // Get base cash position
    const cashPosition = await getCashPosition(tenantId, start_date, end_date, region_id, branch_id);

    // Get cash inflows
    const cashInflows = await getCashInflows(tenantId, start_date, end_date, region_id, branch_id);

    // Get cash outflows
    const cashOutflows = await getCashOutflows(tenantId, start_date, end_date, region_id, branch_id);

    // Get detailed sales information
    const salesBreakdown = await getSalesBreakdown(tenantId, start_date, end_date, region_id, branch_id);

    // Log the data for debugging
    console.log('Cash Position:', {
      count: cashPosition.length,
      sample: cashPosition.slice(0, 2)
    });
    console.log('Cash Inflows:', {
      total: cashInflows.total,
      cashSales: cashInflows.cashSales?.total,
      mpesaDeposits: cashInflows.mpesaDeposits?.total,
      otherSales: cashInflows.otherSales?.total
    });
    console.log('Cash Outflows:', {
      total: cashOutflows.total,
      expenses: cashOutflows.expenses?.total,
      bankingDeposits: cashOutflows.bankingDeposits?.total
    });
    console.log('Sales Breakdown:', {
      total: salesBreakdown.total,
      paymentMethodCount: salesBreakdown.byPaymentMethod.length,
      branchCount: salesBreakdown.byBranch.length,
      regionCount: salesBreakdown.byRegion.length,
      dateCount: salesBreakdown.byDate.length
    });

    // Calculate summary
    const summary = calculateSummary(cashPosition, cashInflows, cashOutflows);

    // Log the summary for debugging
    console.log('Summary:', {
      totalOpeningCash: summary.totalOpeningCash,
      totalClosingCash: summary.totalClosingCash,
      totalInflows: summary.totalInflows,
      totalOutflows: summary.totalOutflows,
      netCashChange: summary.netCashChange,
      inflowBreakdown: summary.inflowBreakdown
    });

    // Prepare response data
    const responseData = {
      summary,
      cashPosition,
      cashInflows,
      cashOutflows,
      salesBreakdown
    };

    // Add breakdowns if requested
    if (includeBreakdown) {
      if (!branch_id) {
        const branchBreakdown = await getBranchBreakdown(tenantId, start_date, end_date, region_id, page, limit);
        responseData.byBranch = branchBreakdown.data;
        responseData.branchPagination = branchBreakdown.pagination;
      }

      if (!region_id) {
        const regionBreakdown = await getRegionalBreakdown(tenantId, start_date, end_date, page, limit);
        responseData.byRegion = regionBreakdown.data;
        responseData.regionPagination = regionBreakdown.pagination;
      }
    }

    // Return report data
    res.status(200).json({
      status: 'success',
      data: responseData
    });
  } catch (error) {
    console.error('Error generating cash status report:', error);
    console.error('Error stack:', error.stack);

    // Log database connection status
    try {
      if (sequelize) {
        console.log('Sequelize instance exists, checking connection...');
        sequelize.authenticate()
          .then(() => console.log('Database connection is still active'))
          .catch(err => console.error('Database connection test failed in error handler:', err));
      } else {
        console.error('Sequelize instance is not available in error handler');
      }
    } catch (connError) {
      console.error('Error checking database connection in error handler:', connError);
    }

    // Provide more specific error messages based on the error type
    if (error.name === 'SequelizeDatabaseError') {
      return next(new AppError(`Database error: ${error.message}`, 500));
    } else if (error.name === 'SequelizeConnectionError' || error.name === 'SequelizeConnectionRefusedError') {
      return next(new AppError(`Database connection error: ${error.message}. Please check that the database server is running.`, 500));
    } else if (error.name === 'TypeError') {
      // Handle the specific case of sequelize not being available
      if (error.message.includes('Cannot read properties of undefined')) {
        return next(new AppError('Database connection error: The database connection is not properly initialized. Please restart the server.', 500));
      }
      return next(new AppError(`Type error: ${error.message}`, 500));
    } else if (error.name === 'ReferenceError') {
      return next(new AppError(`Reference error: ${error.message}`, 500));
    } else if (error.name === 'SequelizeTimeoutError') {
      return next(new AppError('Database query timed out. The report may be too complex or the database is under heavy load.', 500));
    }

    // For other errors, provide a more detailed error message
    next(new AppError(`Error generating cash status report: ${error.message}`, 500));
  }
};

/**
 * Get cash position from POS sessions
 */
async function getCashPosition(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!startDate || !endDate) {
      console.error('getCashPosition: Missing date range parameters');
      return [];
    }

    // Check if PosSession model has tenant_id column
    const PosSession = require('../../models').PosSession;
    const posSessionAttributes = Object.keys(PosSession.rawAttributes);
    const hasTenantId = posSessionAttributes.includes('tenant_id');

    // Build the query string without tenant_id if it doesn't exist
    let query = `
      SELECT
        b.id AS branch_id,
        b.name AS branch_name,
        r.id AS region_id,
        r.name AS region_name,
        SUM(ps.opening_cash_balance) AS total_opening_cash,
        COALESCE(SUM(psr.closing_cash_balance), 0) AS total_closing_cash,
        COALESCE(SUM(psr.closing_cash_balance - ps.opening_cash_balance), 0) AS net_cash_change
      FROM pos_sessions ps
      JOIN branches b ON ps.branch_id = b.id
      LEFT JOIN regions r ON b.region_id = r.id
      LEFT JOIN pos_session_reconciliations psr ON ps.id = psr.pos_session_id
      WHERE ps.created_at BETWEEN :startDate AND :endDate`;

    // Add tenant_id condition only if the column exists
    if (hasTenantId && tenantId) {
      query += ' AND ps.tenant_id = :tenantId';
    }

    // Add conditional clauses
    if (branchId) {
      query += ' AND ps.branch_id = :branchId';
    }

    if (regionId) {
      query += ' AND b.region_id = :regionId';
    }

    // Add grouping and ordering
    query += `
      GROUP BY b.id, r.id
      ORDER BY r.name, b.name
    `;

    // Check if sequelize is properly initialized
    if (!sequelize || !sequelize.query) {
      console.error('Sequelize not properly initialized in getCashPosition');
      return [];
    }

    try {
      // Removed SQL query logging

      // Build replacements object conditionally
      const replacements = {
        startDate,
        endDate,
        branchId,
        regionId
      };

      // Only add tenantId if the column exists
      if (hasTenantId && tenantId) {
        replacements.tenantId = tenantId;
      }

      const result = await sequelize.query(query, {
        replacements,
        type: sequelize.QueryTypes.SELECT
      });

      // Removed SQL query result logging
      return result;
    } catch (queryError) {
      console.error('Error executing query in getCashPosition:', queryError);
      console.error('Query error stack:', queryError.stack);

      // Try a simpler query to test the connection
      try {
        console.log('Attempting a simple test query to verify connection...');
        const testResult = await sequelize.query('SELECT 1 as test', {
          type: sequelize.QueryTypes.SELECT
        });
        console.log('Test query result:', testResult);
      } catch (testError) {
        console.error('Test query also failed, database connection may be unstable:', testError);
      }

      return [];
    }
  } catch (error) {
    console.error('Error in getCashPosition:', error);
    // Return an empty array instead of throwing an error
    return [];
  }
}

/**
 * Get cash inflows from sales, MPESA deposits, etc.
 */
async function getCashInflows(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!tenantId) {
      console.error('getCashInflows: Missing tenantId parameter');
      return {
        cashSales: { data: [], total: 0 },
        mpesaDeposits: { data: [], total: 0 },
        otherSales: { data: [], total: 0 },
        total: 0
      };
    }

    if (!startDate || !endDate) {
      console.error('getCashInflows: Missing date range parameters');
      return {
        cashSales: { data: [], total: 0 },
        mpesaDeposits: { data: [], total: 0 },
        otherSales: { data: [], total: 0 },
        total: 0
      };
    }

    // Get cash sales
    const cashSales = await getCashSales(tenantId, startDate, endDate, regionId, branchId);

    // Get MPESA deposits (cash coming into the business)
    const mpesaDeposits = await getMpesaDeposits(tenantId, startDate, endDate, regionId, branchId);

    // Get sales by other payment methods (for reporting purposes)
    const otherSales = await getOtherSales(tenantId, startDate, endDate, regionId, branchId);

    // Ensure each object has the correct structure
    const formattedCashSales = {
      data: cashSales && cashSales.data ? cashSales.data : [],
      total: cashSales && cashSales.total ? cashSales.total : 0
    };

    const formattedMpesaDeposits = {
      data: mpesaDeposits && mpesaDeposits.data ? mpesaDeposits.data : [],
      total: mpesaDeposits && mpesaDeposits.total ? mpesaDeposits.total : 0
    };

    const formattedOtherSales = {
      data: otherSales && otherSales.data ? otherSales.data : [],
      total: otherSales && otherSales.total ? otherSales.total : 0
    };

    // Calculate total (only include cash-related items in the total)
    const total = calculateTotal([
      formattedCashSales,
      formattedMpesaDeposits
    ]);

    // Combine all inflows
    return {
      cashSales: formattedCashSales,
      mpesaDeposits: formattedMpesaDeposits,
      otherSales: formattedOtherSales, // Not included in total, just for reporting
      total: total
    };
  } catch (error) {
    console.error('Error in getCashInflows:', error);
    // Return default object with zero values
    return {
      cashSales: { data: [], total: 0 },
      mpesaDeposits: { data: [], total: 0 },
      otherSales: { data: [], total: 0 },
      total: 0
    };
  }
}

/**
 * Get cash outflows from expenses, banking deposits, etc.
 */
async function getCashOutflows(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!tenantId) {
      console.error('getCashOutflows: Missing tenantId parameter');
      return {
        expenses: { data: [], total: 0 },
        bankingDeposits: { data: [], total: 0 },
        total: 0
      };
    }

    if (!startDate || !endDate) {
      console.error('getCashOutflows: Missing date range parameters');
      return {
        expenses: { data: [], total: 0 },
        bankingDeposits: { data: [], total: 0 },
        total: 0
      };
    }

    // Get expenses
    const expenses = await getExpenses(tenantId, startDate, endDate, regionId, branchId);

    // Get banking deposits
    const bankingDeposits = await getBankingDeposits(tenantId, startDate, endDate, regionId, branchId);

    // Ensure each object has the correct structure
    const formattedExpenses = {
      data: expenses && expenses.data ? expenses.data : [],
      total: expenses && expenses.total ? expenses.total : 0
    };

    const formattedBankingDeposits = {
      data: bankingDeposits && bankingDeposits.data ? bankingDeposits.data : [],
      total: bankingDeposits && bankingDeposits.total ? bankingDeposits.total : 0
    };

    // Calculate total
    const total = calculateTotal([
      formattedExpenses,
      formattedBankingDeposits
    ]);

    // Combine all outflows
    return {
      expenses: formattedExpenses,
      bankingDeposits: formattedBankingDeposits,
      total: total
    };
  } catch (error) {
    console.error('Error in getCashOutflows:', error);
    // Return default object with zero values
    return {
      expenses: { data: [], total: 0 },
      bankingDeposits: { data: [], total: 0 },
      total: 0
    };
  }
}

/**
 * Calculate summary from all data
 */
function calculateSummary(cashPosition, cashInflows, cashOutflows) {
  // Ensure all inputs are properly defined
  const positionArray = Array.isArray(cashPosition) ? cashPosition : [];
  const inflows = cashInflows || { total: 0, cashSales: { total: 0 }, mpesaWithdrawals: { total: 0 } };
  const outflows = cashOutflows || { total: 0 };

  // Calculate totals with safe parsing
  const totalOpeningCash = positionArray.reduce((sum, item) => {
    const value = item && item.total_opening_cash ? parseFloat(item.total_opening_cash) : 0;
    return sum + (isNaN(value) ? 0 : value);
  }, 0);

  const totalClosingCash = positionArray.reduce((sum, item) => {
    const value = item && item.total_closing_cash ? parseFloat(item.total_closing_cash) : 0;
    return sum + (isNaN(value) ? 0 : value);
  }, 0);

  // Get cash sales total
  const cashSalesTotal = inflows.cashSales && typeof inflows.cashSales.total === 'number' ?
                         inflows.cashSales.total :
                         (inflows.cashSales && inflows.cashSales.total ? parseFloat(inflows.cashSales.total) : 0);

  // Get MPESA deposits total
  const mpesaDepositsTotal = inflows.mpesaDeposits && typeof inflows.mpesaDeposits.total === 'number' ?
                             inflows.mpesaDeposits.total :
                             (inflows.mpesaDeposits && inflows.mpesaDeposits.total ? parseFloat(inflows.mpesaDeposits.total) : 0);

  // Calculate total inflows as cash sales + MPESA deposits + opening cash
  const totalInflows = cashSalesTotal + mpesaDepositsTotal + totalOpeningCash;

  // Get total outflows
  const totalOutflows = typeof outflows.total === 'number' ? outflows.total :
                        (outflows.total ? parseFloat(outflows.total) : 0);

  // Calculate net position
  const netCashChange = totalInflows - totalOutflows;

  // Log the calculation for debugging
  console.log('Cash Inflow Calculation:', {
    cashSalesTotal,
    mpesaDepositsTotal,
    totalOpeningCash,
    totalInflows,
    calculation: `${cashSalesTotal} + ${mpesaDepositsTotal} + ${totalOpeningCash} = ${totalInflows}`
  });

  return {
    totalOpeningCash: isNaN(totalOpeningCash) ? 0 : totalOpeningCash,
    totalClosingCash: isNaN(totalClosingCash) ? 0 : totalClosingCash,
    totalInflows: isNaN(totalInflows) ? 0 : totalInflows,
    totalOutflows: isNaN(totalOutflows) ? 0 : totalOutflows,
    netCashChange: isNaN(netCashChange) ? 0 : netCashChange,
    // Add detailed breakdown for transparency
    inflowBreakdown: {
      cashSales: isNaN(cashSalesTotal) ? 0 : cashSalesTotal,
      mpesaDeposits: isNaN(mpesaDepositsTotal) ? 0 : mpesaDepositsTotal,
      openingCash: isNaN(totalOpeningCash) ? 0 : totalOpeningCash
    }
  };
}

/**
 * Get branch breakdown
 */
async function getBranchBreakdown(tenantId, startDate, endDate, regionId, page = 1, limit = 50) {
  try {
    const whereClause = {
      tenant_id: tenantId,
      deleted_at: null
    };

    if (regionId) {
      whereClause.region_id = regionId;
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await Branch.count({
      where: whereClause
    });

    // Get paginated branches
    const branches = await Branch.findAll({
      where: whereClause,
      include: [
        {
          model: Region,
          as: 'Region',
          attributes: ['id', 'name']
        }
      ],
      order: [['name', 'ASC']],
      limit: limit,
      offset: offset
    });

    if (!branches || branches.length === 0) {
      console.log('No branches found');
      return {
        data: [],
        pagination: {
          page: page,
          limit: limit,
          total: totalCount,
          pages: Math.ceil(totalCount / limit)
        }
      };
    }

    const branchIds = branches.map(branch => branch.id);

    // Get data for each branch
    const branchData = await Promise.all(
      branchIds.map(async (branchId) => {
        try {
          const cashPosition = await getCashPosition(tenantId, startDate, endDate, null, branchId);
          const cashInflows = await getCashInflows(tenantId, startDate, endDate, null, branchId);
          const cashOutflows = await getCashOutflows(tenantId, startDate, endDate, null, branchId);
          const summary = calculateSummary(cashPosition, cashInflows, cashOutflows);

          const branch = branches.find(b => b.id === branchId);

          return {
            branch_id: branchId,
            branch_name: branch.name,
            region_id: branch.Region?.id,
            region_name: branch.Region?.name,
            summary
          };
        } catch (error) {
          console.error(`Error processing branch ${branchId}:`, error);
          return {
            branch_id: branchId,
            branch_name: branches.find(b => b.id === branchId)?.name || 'Unknown',
            region_id: branches.find(b => b.id === branchId)?.Region?.id,
            region_name: branches.find(b => b.id === branchId)?.Region?.name || 'Unknown',
            summary: {
              totalOpeningCash: 0,
              totalClosingCash: 0,
              totalInflows: 0,
              totalOutflows: 0,
              netCashChange: 0
            }
          };
        }
      })
    );

    return {
      data: branchData,
      pagination: {
        page: page,
        limit: limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    };
  } catch (error) {
    console.error('Error in getBranchBreakdown:', error);
    return {
      data: [],
      pagination: {
        page: page,
        limit: limit,
        total: 0,
        pages: 0
      }
    };
  }
}

/**
 * Get regional breakdown
 */
async function getRegionalBreakdown(tenantId, startDate, endDate, page = 1, limit = 50) {
  try {
    // Check if the Region model has a tenant_id column
    const regionAttributes = Object.keys(Region.rawAttributes);
    const hasTenantId = regionAttributes.includes('tenant_id');

    let whereClause;
    if (hasTenantId) {
      whereClause = {
        tenant_id: tenantId,
        deleted_at: null
      };
    } else {
      whereClause = {
        deleted_at: null
      };
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await Region.count({
      where: whereClause
    });

    // Get paginated regions
    const regions = await Region.findAll({
      where: whereClause,
      order: [['name', 'ASC']],
      limit: limit,
      offset: offset
    });

    if (!regions || regions.length === 0) {
      console.log('No regions found');
      return {
        data: [],
        pagination: {
          page: page,
          limit: limit,
          total: totalCount,
          pages: Math.ceil(totalCount / limit)
        }
      };
    }

    const regionIds = regions.map(region => region.id);

    // Get data for each region
    const regionData = await Promise.all(
      regionIds.map(async (regionId) => {
        try {
          const cashPosition = await getCashPosition(tenantId, startDate, endDate, regionId, null);
          const cashInflows = await getCashInflows(tenantId, startDate, endDate, regionId, null);
          const cashOutflows = await getCashOutflows(tenantId, startDate, endDate, regionId, null);
          const summary = calculateSummary(cashPosition, cashInflows, cashOutflows);

          const region = regions.find(r => r.id === regionId);

          return {
            region_id: regionId,
            region_name: region.name,
            summary
          };
        } catch (error) {
          console.error(`Error processing region ${regionId}:`, error);
          return {
            region_id: regionId,
            region_name: regions.find(r => r.id === regionId)?.name || 'Unknown',
            summary: {
              totalOpeningCash: 0,
              totalClosingCash: 0,
              totalInflows: 0,
              totalOutflows: 0,
              netCashChange: 0
            }
          };
        }
      })
    );

    return {
      data: regionData,
      pagination: {
        page: page,
        limit: limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    };
  } catch (error) {
    console.error('Error in getRegionalBreakdown:', error);
    return {
      data: [],
      pagination: {
        page: page,
        limit: limit,
        total: 0,
        pages: 0
      }
    };
  }
}

/**
 * Helper function to calculate total from array of objects with total property
 */
function calculateTotal(items) {
  // Handle case where items is undefined or null
  if (!items) {
    return 0;
  }

  // Ensure items is an array
  const itemsArray = Array.isArray(items) ? items : [items];

  return itemsArray.reduce((sum, item) => {
    // Handle null or undefined item
    if (!item) {
      return sum;
    }

    // Handle array items (like result sets)
    if (Array.isArray(item)) {
      return sum + item.reduce((s, i) => {
        const amount = i && i.amount ? parseFloat(i.amount) : 0;
        return s + (isNaN(amount) ? 0 : amount);
      }, 0);
    }

    // Handle object with total property
    const total = item.total ? parseFloat(item.total) : 0;
    return sum + (isNaN(total) ? 0 : total);
  }, 0);
}

// Placeholder functions for specific data queries
// These would be implemented with actual database queries

async function getCashSales(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!startDate || !endDate) {
      console.log('getCashSales: Missing required parameters', { startDate, endDate });
      return {
        data: [],
        total: 0
      };
    }

    console.log('getCashSales called with parameters:', {
      tenantId, // Not used in query
      startDate,
      endDate,
      regionId: regionId || 'null',
      branchId: branchId || 'null'
    });

    // Build the query string - we'll update it after finding the cash payment method ID
    let query = `
      SELECT
        b.id AS branch_id,
        b.name AS branch_name,
        r.id AS region_id,
        r.name AS region_name,
        SUM(s.total_amount) AS amount,
        COUNT(s.id) AS transaction_count
      FROM sales s
      JOIN branches b ON s.branch_id = b.id
      LEFT JOIN regions r ON b.region_id = r.id
      WHERE s.created_at BETWEEN :startDate AND :endDate
        AND s.status = 'completed'`;

    // Add conditional clauses
    if (branchId) {
      query += ' AND s.branch_id = :branchId';
    }

    if (regionId) {
      query += ' AND b.region_id = :regionId';
    }

    // Add grouping and ordering
    query += `
      GROUP BY b.id, r.id
      ORDER BY r.name, b.name
    `;

    // Check if sequelize is properly initialized
    if (!sequelize || !sequelize.query) {
      console.error('Sequelize not properly initialized in getCashSales');
      return { data: [], total: 0 };
    }

    // Log the query for debugging
    console.log('Executing getCashSales query:', query.replace(/\s+/g, ' ').trim());

    try {
      // First, check all payment methods in the system
      const paymentMethodsQuery = `SELECT id, name, code FROM payment_methods`;
      const paymentMethods = await sequelize.query(paymentMethodsQuery, {
        type: sequelize.QueryTypes.SELECT
      });

      console.log('All payment methods:', paymentMethods);

      // Since we know cash sales have payment method ID 1, we'll use that directly
      const cashPaymentMethodId = 1;
      console.log(`Using payment method ID ${cashPaymentMethodId} for cash sales`);

      // For debugging, check if there's a payment method with ID 1
      const paymentMethodWithId1 = paymentMethods.find(pm => pm.id === 1);
      if (paymentMethodWithId1) {
        console.log(`Payment method with ID 1: ${JSON.stringify(paymentMethodWithId1)}`);
      } else {
        console.log('Warning: No payment method found with ID 1');
      }

      // Then check if there are any sales with cash payment method
      const salesCountQuery = `
        SELECT COUNT(*) as count,
               SUM(total_amount) as total_amount
        FROM sales s
        WHERE s.created_at BETWEEN :startDate AND :endDate
          AND s.payment_method_id = :cashPaymentMethodId
          AND s.status = 'completed'`;

      console.log('Executing sales count query with parameters:', {
        startDate,
        endDate,
        cashPaymentMethodId
      });

      const salesCount = await sequelize.query(salesCountQuery, {
        replacements: {
          startDate,
          endDate,
          cashPaymentMethodId
        },
        type: sequelize.QueryTypes.SELECT
      });

      console.log('Total cash sales count:', salesCount);

      // If no cash sales found, try a more direct query without tenant_id
      if (!salesCount[0].count || salesCount[0].count === '0') {
        console.log('No cash sales found with tenant_id filter. Trying without tenant_id...');

        const directSalesCountQuery = `
          SELECT COUNT(*) as count,
                 SUM(total_amount) as total_amount
          FROM sales
          WHERE payment_method_id = :cashPaymentMethodId
            AND created_at BETWEEN :startDate AND :endDate
            AND status = 'completed'`;

        const directSalesCount = await sequelize.query(directSalesCountQuery, {
          replacements: {
            startDate,
            endDate,
            cashPaymentMethodId
          },
          type: sequelize.QueryTypes.SELECT
        });

        console.log('Direct cash sales count (without tenant_id):', directSalesCount);
      }

      // Also check all sales in the period
      const allSalesCountQuery = `
        SELECT COUNT(*) as count,
               SUM(CASE WHEN payment_method_id = :cashPaymentMethodId THEN 1 ELSE 0 END) as cash_sales_count,
               SUM(CASE WHEN payment_method_id = :cashPaymentMethodId THEN total_amount ELSE 0 END) as cash_sales_amount
        FROM sales
        WHERE created_at BETWEEN :startDate AND :endDate`;

      const allSalesStats = await sequelize.query(allSalesCountQuery, {
        replacements: {
          startDate,
          endDate,
          cashPaymentMethodId
        },
        type: sequelize.QueryTypes.SELECT
      });

      console.log('Sales statistics:', allSalesStats);

      // Check if we need to use a direct query without tenant_id
      let useDirect = false;
      if (salesCount[0].count === '0' || parseInt(salesCount[0].count) === 0) {
        console.log('No cash sales found with tenant_id filter. Will use direct query for main results.');
        useDirect = true;
      }

      let result;

      if (useDirect) {
        // Use a direct query without tenant_id
        const directQuery = `
          SELECT
            b.id AS branch_id,
            b.name AS branch_name,
            r.id AS region_id,
            r.name AS region_name,
            SUM(s.total_amount) AS amount,
            COUNT(s.id) AS transaction_count
          FROM sales s
          JOIN branches b ON s.branch_id = b.id
          LEFT JOIN regions r ON b.region_id = r.id
          WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.payment_method_id = :cashPaymentMethodId
            AND s.status = 'completed'`;

        // Add conditional clauses
        let finalDirectQuery = directQuery;

        if (branchId) {
          finalDirectQuery += ' AND s.branch_id = :branchId';
        }

        if (regionId) {
          finalDirectQuery += ' AND b.region_id = :regionId';
        }

        // Add grouping and ordering
        finalDirectQuery += `
          GROUP BY b.id, r.id
          ORDER BY r.name, b.name
        `;

        console.log('Executing direct query:', finalDirectQuery.replace(/\s+/g, ' ').trim());

        result = await sequelize.query(finalDirectQuery, {
          replacements: {
            startDate,
            endDate,
            branchId,
            regionId,
            cashPaymentMethodId
          },
          type: sequelize.QueryTypes.SELECT
        });
      } else {
        // Update the query to filter by the cash payment method ID
        query += ' AND s.payment_method_id = :cashPaymentMethodId';

        // Log the updated query
        console.log('Updated query with payment method ID:', query.replace(/\s+/g, ' ').trim());

        // Execute the original query
        result = await sequelize.query(query, {
          replacements: {
            startDate,
            endDate,
            branchId,
            regionId,
            cashPaymentMethodId
          },
          type: sequelize.QueryTypes.SELECT
        });
      }

      console.log(`getCashSales query returned ${result.length} rows:`, result);

      const total = result.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);
      console.log(`Total cash sales amount: ${total}`);

      return {
        data: result,
        total: total
      };
    } catch (queryError) {
      console.error('Error executing query in getCashSales:', queryError);
      console.error('Query error stack:', queryError.stack);

      // Try a simpler query to test the connection
      try {
        console.log('Attempting a simple test query to verify sales and payment_methods tables...');
        const salesColumns = await sequelize.query('DESCRIBE sales', {
          type: sequelize.QueryTypes.SELECT
        });
        console.log('Sales table columns:', salesColumns);

        const pmColumns = await sequelize.query('DESCRIBE payment_methods', {
          type: sequelize.QueryTypes.SELECT
        });
        console.log('Payment methods table columns:', pmColumns);
      } catch (testError) {
        console.error('Test query also failed:', testError);
      }

      return { data: [], total: 0 };
    }
  } catch (error) {
    console.error('Error in getCashSales:', error);
    return {
      data: [],
      total: 0
    };
  }
}

async function getDSAReconciliations(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!startDate || !endDate) {
      console.log('getDSAReconciliations: Missing required parameters', { startDate, endDate });
      return {
        data: [],
        total: 0
      };
    }

    // Check if DSAReconciliation model has tenant_id column
    const DSAReconciliation = require('../../models').DSAReconciliation;
    let hasTenantId = false;

    try {
      const dsaReconciliationAttributes = Object.keys(DSAReconciliation.rawAttributes);
      hasTenantId = dsaReconciliationAttributes.includes('tenant_id');
    } catch (modelError) {
      console.log('DSAReconciliation model not found, skipping DSA reconciliations');
      return { data: [], total: 0 };
    }

    // Build the query string
    let query = `
      SELECT
        b.id AS branch_id,
        b.name AS branch_name,
        r.id AS region_id,
        r.name AS region_name,
        SUM(dr.cash_amount) AS amount,
        COUNT(dr.id) AS transaction_count
      FROM dsa_reconciliations dr
      JOIN branches b ON dr.branch_id = b.id
      LEFT JOIN regions r ON b.region_id = r.id
      WHERE dr.created_at BETWEEN :startDate AND :endDate`;

    // Add tenant_id condition only if the column exists
    if (hasTenantId && tenantId) {
      query += ' AND dr.tenant_id = :tenantId';
    }

    // Add conditional clauses
    if (branchId) {
      query += ' AND dr.branch_id = :branchId';
    }

    if (regionId) {
      query += ' AND b.region_id = :regionId';
    }

    // Add grouping and ordering
    query += `
      GROUP BY b.id, r.id
      ORDER BY r.name, b.name
    `;

    // Check if sequelize is properly initialized
    if (!sequelize || !sequelize.query) {
      console.error('Sequelize not properly initialized in getDSAReconciliations');
      return { data: [], total: 0 };
    }

    // Build replacements object conditionally
    const replacements = {
      startDate,
      endDate,
      branchId,
      regionId
    };

    // Only add tenantId if the column exists
    if (hasTenantId && tenantId) {
      replacements.tenantId = tenantId;
    }

    const result = await sequelize.query(query, {
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    return {
      data: result,
      total: result.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0)
    };
  } catch (error) {
    console.error('Error in getDSAReconciliations:', error);
    return {
      data: [],
      total: 0
    };
  }
}

async function getFloatReturns(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!startDate || !endDate) {
      console.log('getFloatReturns: Missing required parameters', { startDate, endDate });
      return {
        data: [],
        total: 0
      };
    }

    // Check if MpesaFloatMovement model has tenant_id column
    const MpesaFloatMovement = require('../../models').MpesaFloatMovement;
    let hasTenantId = false;

    try {
      const mpesaFloatMovementAttributes = Object.keys(MpesaFloatMovement.rawAttributes);
      hasTenantId = mpesaFloatMovementAttributes.includes('tenant_id');
    } catch (modelError) {
      console.log('MpesaFloatMovement model not found, skipping float returns');
      return { data: [], total: 0 };
    }

    // Build the query string
    let query = `
      SELECT
        b.id AS branch_id,
        b.name AS branch_name,
        r.id AS region_id,
        r.name AS region_name,
        SUM(fm.amount) AS amount,
        COUNT(fm.id) AS transaction_count
      FROM mpesa_float_movements fm
      JOIN branches b ON fm.to_branch_id = b.id
      LEFT JOIN regions r ON b.region_id = r.id
      WHERE fm.created_at BETWEEN :startDate AND :endDate
        AND fm.status = 'received'`;

    // Add tenant_id condition only if the column exists
    if (hasTenantId && tenantId) {
      query += ' AND fm.tenant_id = :tenantId';
    }

    // Add conditional clauses
    if (branchId) {
      query += ' AND fm.to_branch_id = :branchId';
    }

    if (regionId) {
      query += ' AND b.region_id = :regionId';
    }

    // Add grouping and ordering
    query += `
      GROUP BY b.id, r.id
      ORDER BY r.name, b.name
    `;

    // Check if sequelize is properly initialized
    if (!sequelize || !sequelize.query) {
      console.error('Sequelize not properly initialized in getFloatReturns');
      return { data: [], total: 0 };
    }

    // Build replacements object conditionally
    const replacements = {
      startDate,
      endDate,
      branchId,
      regionId
    };

    // Only add tenantId if the column exists
    if (hasTenantId && tenantId) {
      replacements.tenantId = tenantId;
    }

    const result = await sequelize.query(query, {
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    return {
      data: result,
      total: result.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0)
    };
  } catch (error) {
    console.error('Error in getFloatReturns:', error);
    return {
      data: [],
      total: 0
    };
  }
}

async function getExpenses(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!tenantId || !startDate || !endDate) {
      console.log('getExpenses: Missing required parameters', { tenantId, startDate, endDate });
      return {
        data: [],
        total: 0
      };
    }

    // Note: The expenses table doesn't have a tenant_id column
    // We'll filter by branch_id instead, as branches are associated with tenants
    const whereClause = {
      created_at: {
        [Op.between]: [startDate, endDate]
      },
      status: 'approved' // Status is lowercase in the database
    };

    if (branchId) {
      whereClause.branch_id = branchId;
    }

    // Build the query string
    let query = `
      SELECT
        b.id AS branch_id,
        b.name AS branch_name,
        r.id AS region_id,
        r.name AS region_name,
        SUM(e.amount) AS amount,
        COUNT(e.id) AS transaction_count
      FROM expenses e
      JOIN branches b ON e.branch_id = b.id
      LEFT JOIN regions r ON b.region_id = r.id
      WHERE e.created_at BETWEEN :startDate AND :endDate
        AND e.status = 'approved'`;

    // Add conditional clauses
    if (branchId) {
      query += ' AND e.branch_id = :branchId';
    }

    if (regionId) {
      query += ' AND b.region_id = :regionId';
    }

    // Add grouping and ordering
    query += `
      GROUP BY b.id, r.id
      ORDER BY r.name, b.name
    `;

    // Check if sequelize is properly initialized
    if (!sequelize || !sequelize.query) {
      console.error('Sequelize not properly initialized in getExpenses');
      return { data: [], total: 0 };
    }

    // Log the query for debugging
    console.log('Executing getExpenses query with parameters:', {
      startDate,
      endDate,
      branchId: branchId || 'null',
      regionId: regionId || 'null'
    });
    console.log('Query:', query.replace(/\s+/g, ' ').trim());

    try {
      const result = await sequelize.query(query, {
        replacements: {
          startDate,
          endDate,
          branchId,
          regionId
        },
        type: sequelize.QueryTypes.SELECT
      });

      console.log(`getExpenses query returned ${result.length} rows`);
      return {
        data: result,
        total: result.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0)
      };
    } catch (queryError) {
      console.error('Error executing query in getExpenses:', queryError);
      console.error('Query error stack:', queryError.stack);

      // Try a simpler query to test the connection
      try {
        console.log('Attempting a simple test query to verify expenses table...');
        const testResult = await sequelize.query('DESCRIBE expenses', {
          type: sequelize.QueryTypes.SELECT
        });
        console.log('Expenses table columns:', testResult);
      } catch (testError) {
        console.error('Test query also failed:', testError);
      }

      return { data: [], total: 0 };
    }
  } catch (error) {
    console.error('Error in getExpenses:', error);
    return {
      data: [],
      total: 0
    };
  }
}

async function getBankingDeposits(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!startDate || !endDate) {
      console.log('getBankingDeposits: Missing required parameters', { startDate, endDate });
      return {
        data: [],
        total: 0
      };
    }

    // Check if BankingTransaction model has tenant_id column
    const BankingTransaction = require('../../models').BankingTransaction;
    let hasTenantId = false;

    try {
      const bankingTransactionAttributes = Object.keys(BankingTransaction.rawAttributes);
      hasTenantId = bankingTransactionAttributes.includes('tenant_id');
    } catch (modelError) {
      console.log('BankingTransaction model not found, skipping banking deposits');
      return { data: [], total: 0 };
    }

    // Build the query string
    let query = `
      SELECT
        b.id AS branch_id,
        b.name AS branch_name,
        r.id AS region_id,
        r.name AS region_name,
        SUM(bt.amount) AS amount,
        COUNT(bt.id) AS transaction_count
      FROM banking_transactions bt
      JOIN branches b ON bt.branch_id = b.id
      LEFT JOIN regions r ON b.region_id = r.id
      WHERE bt.created_at BETWEEN :startDate AND :endDate
        AND bt.banking_method IN ('bank', 'agent', 'mpesa')
        AND bt.status = 'completed'`;

    // Add tenant_id condition only if the column exists
    if (hasTenantId && tenantId) {
      query += ' AND bt.tenant_id = :tenantId';
    }

    // Add conditional clauses
    if (branchId) {
      query += ' AND bt.branch_id = :branchId';
    }

    if (regionId) {
      query += ' AND b.region_id = :regionId';
    }

    // Add grouping and ordering
    query += `
      GROUP BY b.id, r.id
      ORDER BY r.name, b.name
    `;

    // Check if sequelize is properly initialized
    if (!sequelize || !sequelize.query) {
      console.error('Sequelize not properly initialized in getBankingDeposits');
      return { data: [], total: 0 };
    }

    // Log the query for debugging
    console.log('Executing getBankingDeposits query with parameters:', {
      tenantId: hasTenantId ? tenantId : 'not used',
      startDate,
      endDate,
      branchId: branchId || 'null',
      regionId: regionId || 'null'
    });
    console.log('Query:', query.replace(/\s+/g, ' ').trim());

    try {
      // Build replacements object conditionally
      const replacements = {
        startDate,
        endDate,
        branchId,
        regionId
      };

      // Only add tenantId if the column exists
      if (hasTenantId && tenantId) {
        replacements.tenantId = tenantId;
      }

      const result = await sequelize.query(query, {
        replacements,
        type: sequelize.QueryTypes.SELECT
      });

      console.log(`getBankingDeposits query returned ${result.length} rows`);
      return {
        data: result,
        total: result.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0)
      };
    } catch (queryError) {
      console.error('Error executing query in getBankingDeposits:', queryError);
      console.error('Query error stack:', queryError.stack);

      // Try a simpler query to test the connection
      try {
        console.log('Attempting a simple test query to verify banking_transactions table...');
        const testResult = await sequelize.query('DESCRIBE banking_transactions', {
          type: sequelize.QueryTypes.SELECT
        });
        console.log('Banking transactions table columns:', testResult);
      } catch (testError) {
        console.error('Test query also failed:', testError);
      }

      return { data: [], total: 0 };
    }
  } catch (error) {
    console.error('Error in getBankingDeposits:', error);
    return {
      data: [],
      total: 0
    };
  }
}

async function getFloatAllocations(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!startDate || !endDate) {
      console.log('getFloatAllocations: Missing required parameters', { startDate, endDate });
      return {
        data: [],
        total: 0
      };
    }

    // Check if MpesaFloatMovement model has tenant_id column
    const MpesaFloatMovement = require('../../models').MpesaFloatMovement;
    let hasTenantId = false;

    try {
      const mpesaFloatMovementAttributes = Object.keys(MpesaFloatMovement.rawAttributes);
      hasTenantId = mpesaFloatMovementAttributes.includes('tenant_id');
    } catch (modelError) {
      console.log('MpesaFloatMovement model not found, skipping float allocations');
      return { data: [], total: 0 };
    }

    // Build the query string
    let query = `
      SELECT
        b.id AS branch_id,
        b.name AS branch_name,
        r.id AS region_id,
        r.name AS region_name,
        SUM(fm.amount) AS amount,
        COUNT(fm.id) AS transaction_count
      FROM mpesa_float_movements fm
      JOIN branches b ON fm.to_branch_id = b.id
      LEFT JOIN regions r ON b.region_id = r.id
      WHERE fm.created_at BETWEEN :startDate AND :endDate
        AND fm.status = 'pending'`;

    // Add tenant_id condition only if the column exists
    if (hasTenantId && tenantId) {
      query += ' AND fm.tenant_id = :tenantId';
    }

    // Add conditional clauses
    if (branchId) {
      query += ' AND fm.to_branch_id = :branchId';
    }

    if (regionId) {
      query += ' AND b.region_id = :regionId';
    }

    // Add grouping and ordering
    query += `
      GROUP BY b.id, r.id
      ORDER BY r.name, b.name
    `;

    // Check if sequelize is properly initialized
    if (!sequelize || !sequelize.query) {
      console.error('Sequelize not properly initialized in getFloatAllocations');
      return { data: [], total: 0 };
    }

    // Build replacements object conditionally
    const replacements = {
      startDate,
      endDate,
      branchId,
      regionId
    };

    // Only add tenantId if the column exists
    if (hasTenantId && tenantId) {
      replacements.tenantId = tenantId;
    }

    const result = await sequelize.query(query, {
      replacements,
      type: sequelize.QueryTypes.SELECT
    });

    return {
      data: result,
      total: result.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0)
    };
  } catch (error) {
    console.error('Error in getFloatAllocations:', error);
    return {
      data: [],
      total: 0
    };
  }
}

/**
 * Get MPESA deposits (cash inflows from MPESA deposits)
 */
async function getMpesaDeposits(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!startDate || !endDate) {
      console.log('getMpesaDeposits: Missing required parameters', { startDate, endDate });
      return {
        data: [],
        total: 0
      };
    }

    console.log('getMpesaDeposits called with parameters:', {
      tenantId, // Not used in query
      startDate,
      endDate,
      regionId: regionId || 'null',
      branchId: branchId || 'null'
    });

    const whereClause = {
      created_at: {
        [Op.between]: [startDate, endDate]
      },
      type: 'deposit',
      status: 'completed'
    };

    if (branchId) {
      whereClause.branch_id = branchId;
    }

    // Build the query string
    let query = `
      SELECT
        b.id AS branch_id,
        b.name AS branch_name,
        r.id AS region_id,
        r.name AS region_name,
        SUM(mt.amount) AS amount,
        COUNT(mt.id) AS transaction_count
      FROM mpesa_transactions mt
      JOIN branches b ON mt.branch_id = b.id
      LEFT JOIN regions r ON b.region_id = r.id
      WHERE mt.created_at BETWEEN :startDate AND :endDate
        AND mt.type = 'deposit'
        AND mt.status = 'completed'`;

    // Add conditional clauses
    if (branchId) {
      query += ' AND mt.branch_id = :branchId';
    }

    if (regionId) {
      query += ' AND b.region_id = :regionId';
    }

    // Add grouping and ordering
    query += `
      GROUP BY b.id, r.id
      ORDER BY r.name, b.name
    `;

    // Check if sequelize is properly initialized
    if (!sequelize || !sequelize.query) {
      console.error('Sequelize not properly initialized in getMpesaDeposits');
      return { data: [], total: 0 };
    }

    // Log the query for debugging
    console.log('Executing getMpesaDeposits query:', query.replace(/\s+/g, ' ').trim());

    const result = await sequelize.query(query, {
      replacements: {
        startDate,
        endDate,
        branchId,
        regionId
      },
      type: sequelize.QueryTypes.SELECT
    });

    return {
      data: result,
      total: result.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0)
    };
  } catch (error) {
    console.error('Error in getMpesaDeposits:', error);
    return {
      data: [],
      total: 0
    };
  }
}

/**
 * Get MPESA withdrawals (cash outflows from MPESA withdrawals)
 */
async function getMpesaWithdrawals(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!startDate || !endDate) {
      console.log('getMpesaWithdrawals: Missing required parameters', { startDate, endDate });
      return {
        data: [],
        total: 0
      };
    }

    console.log('getMpesaWithdrawals called with parameters:', {
      tenantId, // Not used in query
      startDate,
      endDate,
      regionId: regionId || 'null',
      branchId: branchId || 'null'
    });

    const whereClause = {
      created_at: {
        [Op.between]: [startDate, endDate]
      },
      type: 'withdrawal',
      status: 'completed'
    };

    if (branchId) {
      whereClause.branch_id = branchId;
    }

    // Build the query string
    let query = `
      SELECT
        b.id AS branch_id,
        b.name AS branch_name,
        r.id AS region_id,
        r.name AS region_name,
        SUM(mt.amount) AS amount,
        COUNT(mt.id) AS transaction_count
      FROM mpesa_transactions mt
      JOIN branches b ON mt.branch_id = b.id
      LEFT JOIN regions r ON b.region_id = r.id
      WHERE mt.created_at BETWEEN :startDate AND :endDate
        AND mt.type = 'withdrawal'
        AND mt.status = 'completed'`;

    // Add conditional clauses
    if (branchId) {
      query += ' AND mt.branch_id = :branchId';
    }

    if (regionId) {
      query += ' AND b.region_id = :regionId';
    }

    // Add grouping and ordering
    query += `
      GROUP BY b.id, r.id
      ORDER BY r.name, b.name
    `;

    // Check if sequelize is properly initialized
    if (!sequelize || !sequelize.query) {
      console.error('Sequelize not properly initialized in getMpesaWithdrawals');
      return { data: [], total: 0 };
    }

    // Log the query for debugging
    console.log('Executing getMpesaWithdrawals query:', query.replace(/\s+/g, ' ').trim());

    const result = await sequelize.query(query, {
      replacements: {
        startDate,
        endDate,
        branchId,
        regionId
      },
      type: sequelize.QueryTypes.SELECT
    });

    return {
      data: result,
      total: result.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0)
    };
  } catch (error) {
    console.error('Error in getMpesaWithdrawals:', error);
    return {
      data: [],
      total: 0
    };
  }
}

/**
 * Get sales by other payment methods (for reporting purposes)
 */
async function getOtherSales(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!startDate || !endDate) {
      console.log('getOtherSales: Missing required parameters', { startDate, endDate });
      return {
        data: [],
        total: 0
      };
    }

    console.log('getOtherSales called with parameters:', {
      tenantId, // Not used in query
      startDate,
      endDate,
      regionId: regionId || 'null',
      branchId: branchId || 'null'
    });

    // Build the query string
    let query = `
      SELECT
        b.id AS branch_id,
        b.name AS branch_name,
        r.id AS region_id,
        r.name AS region_name,
        pm.id AS payment_method_id,
        pm.name AS payment_method_name,
        pm.code AS payment_method_code,
        SUM(s.total_amount) AS amount,
        COUNT(s.id) AS transaction_count
      FROM sales s
      JOIN branches b ON s.branch_id = b.id
      LEFT JOIN regions r ON b.region_id = r.id
      JOIN payment_methods pm ON s.payment_method_id = pm.id
      WHERE s.created_at BETWEEN :startDate AND :endDate
        AND pm.code != 'CASH'
        AND s.status = 'completed'`;

    // Add conditional clauses
    if (branchId) {
      query += ' AND s.branch_id = :branchId';
    }

    if (regionId) {
      query += ' AND b.region_id = :regionId';
    }

    // Add grouping and ordering
    query += `
      GROUP BY b.id, r.id, pm.id
      ORDER BY r.name, b.name, pm.name
    `;

    // Check if sequelize is properly initialized
    if (!sequelize || !sequelize.query) {
      console.error('Sequelize not properly initialized in getOtherSales');
      return { data: [], total: 0 };
    }

    // Log the query for debugging
    console.log('Executing getOtherSales query:', query.replace(/\s+/g, ' ').trim());

    try {
      const result = await sequelize.query(query, {
        replacements: {
          startDate,
          endDate,
          branchId,
          regionId
        },
        type: sequelize.QueryTypes.SELECT
      });

      console.log(`getOtherSales query returned ${result.length} rows`);

      return {
        data: result,
        total: result.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0)
      };
    } catch (queryError) {
      console.error('Error executing query in getOtherSales:', queryError);
      console.error('Query error stack:', queryError.stack);

      // Try a simpler query to test the connection
      try {
        console.log('Attempting a simple test query to verify sales table...');
        const testResult = await sequelize.query('DESCRIBE sales', {
          type: sequelize.QueryTypes.SELECT
        });
        console.log('Sales table columns:', testResult);
      } catch (testError) {
        console.error('Test query also failed:', testError);
      }

      return { data: [], total: 0 };
    }
  } catch (error) {
    console.error('Error in getOtherSales:', error);
    return {
      data: [],
      total: 0
    };
  }
}

/**
 * Get detailed sales information by payment method
 */
async function getSalesBreakdown(tenantId, startDate, endDate, regionId, branchId) {
  try {
    // Validate input parameters
    if (!startDate || !endDate) {
      console.log('getSalesBreakdown: Missing required parameters', { startDate, endDate });
      return {
        byPaymentMethod: [],
        byBranch: [],
        byRegion: [],
        byDate: [],
        topProducts: [],
        total: 0
      };
    }

    console.log('getSalesBreakdown called with parameters:', {
      tenantId, // Not used in query
      startDate,
      endDate,
      regionId: regionId || 'null',
      branchId: branchId || 'null'
    });

    // Build the query for sales by payment method
    let paymentMethodQuery = `
      SELECT
        pm.id AS payment_method_id,
        pm.name AS payment_method_name,
        pm.code AS payment_method_code,
        SUM(s.total_amount) AS amount,
        COUNT(s.id) AS transaction_count
      FROM sales s
      JOIN payment_methods pm ON s.payment_method_id = pm.id
      WHERE s.created_at BETWEEN :startDate AND :endDate
        AND s.status = 'completed'`;

    // Add conditional clauses
    if (branchId) {
      paymentMethodQuery += ' AND s.branch_id = :branchId';
    }

    if (regionId) {
      paymentMethodQuery += ' AND EXISTS (SELECT 1 FROM branches b WHERE b.id = s.branch_id AND b.region_id = :regionId)';
    }

    // Add grouping and ordering
    paymentMethodQuery += `
      GROUP BY pm.id
      ORDER BY amount DESC
    `;

    // Build the query for sales by branch
    let branchQuery = `
      SELECT
        b.id AS branch_id,
        b.name AS branch_name,
        r.id AS region_id,
        r.name AS region_name,
        SUM(s.total_amount) AS amount,
        COUNT(s.id) AS transaction_count
      FROM sales s
      JOIN branches b ON s.branch_id = b.id
      LEFT JOIN regions r ON b.region_id = r.id
      WHERE s.created_at BETWEEN :startDate AND :endDate
        AND s.status = 'completed'`;

    // Add conditional clauses
    if (branchId) {
      branchQuery += ' AND s.branch_id = :branchId';
    }

    if (regionId) {
      branchQuery += ' AND b.region_id = :regionId';
    }

    // Add grouping and ordering
    branchQuery += `
      GROUP BY b.id
      ORDER BY amount DESC
    `;

    // Build the query for sales by region
    let regionQuery = `
      SELECT
        r.id AS region_id,
        r.name AS region_name,
        SUM(s.total_amount) AS amount,
        COUNT(s.id) AS transaction_count
      FROM sales s
      JOIN branches b ON s.branch_id = b.id
      JOIN regions r ON b.region_id = r.id
      WHERE s.created_at BETWEEN :startDate AND :endDate
        AND s.status = 'completed'`;

    // Add conditional clauses
    if (regionId) {
      regionQuery += ' AND r.id = :regionId';
    }

    // Add grouping and ordering
    regionQuery += `
      GROUP BY r.id
      ORDER BY amount DESC
    `;

    // Build the query for sales by date
    let dateQuery = `
      SELECT
        DATE(s.created_at) AS sale_date,
        SUM(s.total_amount) AS amount,
        COUNT(s.id) AS transaction_count
      FROM sales s
      WHERE s.created_at BETWEEN :startDate AND :endDate
        AND s.status = 'completed'`;

    // Add conditional clauses
    if (branchId) {
      dateQuery += ' AND s.branch_id = :branchId';
    }

    if (regionId) {
      dateQuery += ' AND EXISTS (SELECT 1 FROM branches b WHERE b.id = s.branch_id AND b.region_id = :regionId)';
    }

    // Add grouping and ordering
    dateQuery += `
      GROUP BY DATE(s.created_at)
      ORDER BY sale_date ASC
    `;

    // Build the query for top-selling products
    let topProductsQuery = `
      SELECT
        p.id AS product_id,
        p.name AS product_name,
        p.sku AS product_sku,
        c.id AS category_id,
        c.name AS category_name,
        SUM(si.quantity) AS quantity_sold,
        SUM(si.total_price) AS amount,
        COUNT(DISTINCT s.id) AS transaction_count
      FROM sales s
      JOIN sale_items si ON s.id = si.sale_id
      JOIN products p ON si.product_id = p.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE s.created_at BETWEEN :startDate AND :endDate
        AND s.status = 'completed'`;

    // Add conditional clauses
    if (branchId) {
      topProductsQuery += ' AND s.branch_id = :branchId';
    }

    if (regionId) {
      topProductsQuery += ' AND EXISTS (SELECT 1 FROM branches b WHERE b.id = s.branch_id AND b.region_id = :regionId)';
    }

    // Add grouping and ordering
    topProductsQuery += `
      GROUP BY p.id
      ORDER BY quantity_sold DESC
      LIMIT 20
    `;

    // Check if sequelize is properly initialized
    if (!sequelize || !sequelize.query) {
      console.error('Sequelize not properly initialized in getSalesBreakdown');
      return {
        byPaymentMethod: [],
        byBranch: [],
        byRegion: [],
        byDate: [],
        topProducts: [],
        total: 0
      };
    }

    // Execute all queries in parallel
    const [paymentMethodResults, branchResults, regionResults, dateResults, topProductsResults] = await Promise.all([
      sequelize.query(paymentMethodQuery, {
        replacements: { startDate, endDate, branchId, regionId },
        type: sequelize.QueryTypes.SELECT
      }),
      sequelize.query(branchQuery, {
        replacements: { startDate, endDate, branchId, regionId },
        type: sequelize.QueryTypes.SELECT
      }),
      sequelize.query(regionQuery, {
        replacements: { startDate, endDate, regionId },
        type: sequelize.QueryTypes.SELECT
      }),
      sequelize.query(dateQuery, {
        replacements: { startDate, endDate, branchId, regionId },
        type: sequelize.QueryTypes.SELECT
      }),
      sequelize.query(topProductsQuery, {
        replacements: { startDate, endDate, branchId, regionId },
        type: sequelize.QueryTypes.SELECT
      })
    ]);

    // Calculate total sales
    const total = paymentMethodResults.reduce((sum, item) => sum + (parseFloat(item.amount) || 0), 0);

    // Format date results to ensure consistent date format
    const formattedDateResults = dateResults.map(item => ({
      ...item,
      sale_date: item.sale_date ? new Date(item.sale_date).toISOString().split('T')[0] : null
    }));

    // Log the top products for debugging
    console.log(`Found ${topProductsResults.length} top-selling products`);

    return {
      byPaymentMethod: paymentMethodResults,
      byBranch: branchResults,
      byRegion: regionResults,
      byDate: formattedDateResults,
      topProducts: topProductsResults,
      total
    };
  } catch (error) {
    console.error('Error in getSalesBreakdown:', error);
    return {
      byPaymentMethod: [],
      byBranch: [],
      byRegion: [],
      byDate: [],
      topProducts: [],
      total: 0
    };
  }
}
