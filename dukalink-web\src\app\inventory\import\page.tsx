"use client";

import { MainLayout } from "@/components/layouts/main-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { ChevronRight, Download, Upload } from "lucide-react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import * as XLSX from "xlsx";
import { useBranches } from "@/features/branches/hooks/use-branches";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useCreateStockItem } from "@/features/inventory/hooks/use-stock-items";

export default function ImportInventoryItemsPage() {
  const router = useRouter();
  const [file, setFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedBranchId, setSelectedBranchId] = useState<string>();
  const { data: branchesData, isLoading: isLoadingBranches } = useBranches();
  const createStockItem = useCreateStockItem();

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
    }
  };

  // Download template
  const downloadTemplate = () => {
    // Create a template with headers
    const template = [
      ["product_id", "branch_id", "quantity", "buying_price", "selling_price", "reorder_level", "order_number", "expiry_date"]
    ];

    // Create worksheet
    const ws = XLSX.utils.aoa_to_sheet(template);

    // Create workbook
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Template");

    // Generate Excel file and trigger download
    XLSX.writeFile(wb, "stock_items_import_template.xlsx");
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!file) {
      toast.error("Please select a file to import");
      return;
    }

    if (!selectedBranchId || selectedBranchId === "0") {
      toast.error("Please select a branch");
      return;
    }

    setIsUploading(true);

    try {
      // Read the Excel file
      const reader = new FileReader();

      reader.onload = async (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });

          // Get the first worksheet
          const worksheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[worksheetName];

          // Convert to JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet);

          if (jsonData.length === 0) {
            toast.error("The file contains no data");
            setIsUploading(false);
            return;
          }

          // Process each row
          let successCount = 0;
          let errorCount = 0;

          for (const row of jsonData) {
            try {
              // Validate required fields
              if (!row.product_id || !row.quantity) {
                errorCount++;
                continue;
              }

              // Create stock item
              await createStockItem.mutateAsync({
                product_id: Number(row.product_id),
                branch_id: Number(selectedBranchId),
                quantity: Number(row.quantity),
                buying_price: row.buying_price ? Number(row.buying_price) : 0,
                selling_price: row.selling_price ? Number(row.selling_price) : 0,
                reorder_level: row.reorder_level ? Number(row.reorder_level) : undefined,
                batch_number: row.batch_number,
                expiry_date: row.expiry_date,
              });

              successCount++;
            } catch (error) {
              console.error("Error processing row:", row, error);
              errorCount++;
            }
          }

          // Show results
          if (successCount > 0) {
            toast.success(`Successfully imported ${successCount} stock items`);
          }

          if (errorCount > 0) {
            toast.error(`Failed to import ${errorCount} stock items`);
          }

          // Navigate back to inventory page
          if (successCount > 0) {
            router.push("/inventory");
          }
        } catch (error) {
          console.error("Error processing file:", error);
          toast.error("Failed to process the file. Please check the file format.");
        } finally {
          setIsUploading(false);
        }
      };

      reader.readAsArrayBuffer(file);
    } catch (error) {
      console.error("Error importing inventory items:", error);
      toast.error("An error occurred while importing stock items");
      setIsUploading(false);
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/inventory">Inventory</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbLink>Import Stock Items</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Import Stock Items
          </h1>
          <p className="text-muted-foreground">
            Import multiple stock items from an Excel file
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Import Stock Items</CardTitle>
            <CardDescription>
              Upload an Excel file with stock items to import
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="branch">Branch</Label>
                <Select
                  value={selectedBranchId}
                  onValueChange={setSelectedBranchId}
                  disabled={isLoadingBranches}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {branchesData?.data?.map((branch) => (
                      <SelectItem key={branch.id} value={branch.id.toString()}>
                        {branch.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="file">Upload File</Label>
                <Input
                  id="file"
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleFileChange}
                  disabled={isUploading}
                />
                <p className="text-sm text-muted-foreground">
                  Upload an Excel file (.xlsx, .xls) with stock items
                </p>
              </div>

              <div className="flex flex-col sm:flex-row justify-between gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={downloadTemplate}
                  disabled={isUploading}
                >
                  <Download className="mr-2 h-4 w-4" /> Download Template
                </Button>
                <Button type="submit" disabled={!file || isUploading}>
                  {isUploading ? (
                    "Importing..."
                  ) : (
                    <>
                      <Upload className="mr-2 h-4 w-4" /> Import Stock Items
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
