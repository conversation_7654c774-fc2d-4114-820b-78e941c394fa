"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Download,
  FileSpreadsheet,
  Settings,
  Zap,
  Clock,
  AlertCircle,
  BarChart3,
} from "lucide-react";
import {
  useSalesSummaryExportSuite,
  useSalesSummaryExportRecommendation,
} from "../hooks/use-sales-summary-export";
import { SalesSummaryExportParams, CustomSalesSummaryExportParams } from "../api/sales-summary-export-service";

interface SalesSummaryExportDialogProps {
  filters: any; // Current filters from the sales summary page
  totalRecords?: number;
  trigger?: React.ReactNode;
}

export function SalesSummaryExportDialog({
  filters,
  totalRecords = 0,
  trigger,
}: SalesSummaryExportDialogProps) {
  const [open, setOpen] = useState(false);
  const [exportType, setExportType] = useState<"all" | "custom" | "summary" | "lightweight">("lightweight");
  const [customOptions, setCustomOptions] = useState({
    include_summary: true,
    include_sales_details: true,
    include_product_breakdown: true,
    include_branch_breakdown: true,
    include_payment_breakdown: true,
    include_charts: true,
    format_type: "detailed" as "detailed" | "summary",
    columns: "all",
  });

  const exportSuite = useSalesSummaryExportSuite();
  const recommendation = useSalesSummaryExportRecommendation(totalRecords);

  const handleExport = async () => {
    try {
      const baseParams: SalesSummaryExportParams = {
        start_date: filters.start_date,
        end_date: filters.end_date,
        branch_id: filters.branch_id,
        region_id: filters.region_id,
        product_id: filters.product_id,
        category_id: filters.category_id,
        location_id: filters.location_id,
        user_id: filters.user_id,
        payment_method: filters.payment_method,
      };

      switch (exportType) {
        case "all":
          await exportSuite.exportAll({
            ...baseParams,
            include_summary: true,
            include_sales_details: true,
            include_product_breakdown: true,
            include_branch_breakdown: true,
            include_payment_breakdown: true,
            include_charts: true,
          });
          break;

        case "custom":
          const customParams: CustomSalesSummaryExportParams = {
            ...baseParams,
            ...customOptions,
          };
          await exportSuite.exportCustom(customParams);
          break;

        case "summary":
          await exportSuite.exportSummary(baseParams);
          break;

        case "lightweight":
          await exportSuite.exportLightweight(baseParams);
          break;
      }

      setOpen(false);
    } catch (error) {
      // Error handling is done in the hooks
      console.error("Export failed:", error);
    }
  };

  const getExportIcon = (type: string) => {
    switch (type) {
      case "all":
        return <FileSpreadsheet className="h-4 w-4" />;
      case "custom":
        return <Settings className="h-4 w-4" />;
      case "summary":
        return <Zap className="h-4 w-4" />;
      default:
        return <Download className="h-4 w-4" />;
    }
  };

  const getEstimatedTime = () => {
    if (totalRecords <= 1000) return "< 30 seconds";
    if (totalRecords <= 5000) return "30-90 seconds";
    return "1-3 minutes";
  };

  const isExporting = exportSuite.isAnyExporting;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" disabled={isExporting}>
            {isExporting ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export Sales
              </>
            )}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Export Sales Summary
          </DialogTitle>
          <DialogDescription>
            Choose your export format and options. Exporting {totalRecords.toLocaleString()} sales records.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Recommendation Banner */}
          <div className="rounded-lg border bg-blue-50 p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">Export Guidelines</h4>
                <p className="text-sm text-blue-700 mt-1">{recommendation.message}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-blue-700">
                    Estimated time: {recommendation.estimatedTime}
                  </span>
                </div>
                {totalRecords > 3000 && (
                  <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
                    <strong>Large Dataset Notice:</strong> For performance, exports are limited to the most recent 3,000 records.
                    Use date filters to export specific time periods.
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Export Type Selection */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Export Format</Label>
            <div className="grid grid-cols-1 gap-3">
              {/* Quick Export */}
              <div
                className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                  exportType === "lightweight"
                    ? "border-green-500 bg-green-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setExportType("lightweight")}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    <input
                      type="radio"
                      checked={exportType === "lightweight"}
                      onChange={() => setExportType("lightweight")}
                      className="h-4 w-4"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      <span className="font-medium">Quick Export</span>
                      <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                        Recommended
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Basic sales data only - fastest option (500 records max, &lt; 15 seconds)
                    </p>
                  </div>
                </div>
              </div>

              {/* Custom Export */}
              <div
                className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                  exportType === "custom"
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setExportType("custom")}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    <input
                      type="radio"
                      checked={exportType === "custom"}
                      onChange={() => setExportType("custom")}
                      className="h-4 w-4"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      <span className="font-medium">Custom Export</span>
                      {recommendation.recommended === "custom" && (
                        <Badge variant="secondary" className="text-xs">
                          Recommended
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Select specific sheets and data to include
                    </p>
                  </div>
                </div>
              </div>

              {/* Summary Export */}
              <div
                className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                  exportType === "summary"
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setExportType("summary")}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    <input
                      type="radio"
                      checked={exportType === "summary"}
                      onChange={() => setExportType("summary")}
                      className="h-4 w-4"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      <span className="font-medium">Summary Export</span>
                      {recommendation.recommended === "summary" && (
                        <Badge variant="secondary" className="text-xs">
                          Recommended
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Essential sales data only for quick analysis
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Custom Options */}
          {exportType === "custom" && (
            <>
              <Separator />
              <div className="space-y-4">
                <Label className="text-base font-medium">Custom Options</Label>

                {/* Format Type */}
                <div className="space-y-2">
                  <Label className="text-sm">Format Type</Label>
                  <Select
                    value={customOptions.format_type}
                    onValueChange={(value: "detailed" | "summary") =>
                      setCustomOptions({ ...customOptions, format_type: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="detailed">Detailed (All columns)</SelectItem>
                      <SelectItem value="summary">Summary (Essential columns)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Include Sheets */}
                <div className="space-y-3">
                  <Label className="text-sm">Include Sheets</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_summary"
                        checked={customOptions.include_summary}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_summary: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_summary" className="text-sm">
                        Summary Sheet
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_sales_details"
                        checked={customOptions.include_sales_details}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_sales_details: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_sales_details" className="text-sm">
                        Sales Details
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_product_breakdown"
                        checked={customOptions.include_product_breakdown}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_product_breakdown: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_product_breakdown" className="text-sm">
                        Product Breakdown
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_branch_breakdown"
                        checked={customOptions.include_branch_breakdown}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_branch_breakdown: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_branch_breakdown" className="text-sm">
                        Branch Breakdown
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_payment_breakdown"
                        checked={customOptions.include_payment_breakdown}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_payment_breakdown: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_payment_breakdown" className="text-sm">
                        Payment Breakdown
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_charts"
                        checked={customOptions.include_charts}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_charts: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_charts" className="text-sm">
                        Charts & Graphs
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Current Filters Display */}
          {(filters.start_date || filters.end_date || filters.branch_id || filters.product_id || filters.category_id) && (
            <>
              <Separator />
              <div className="space-y-2">
                <Label className="text-sm font-medium">Applied Filters</Label>
                <div className="flex flex-wrap gap-2">
                  {filters.start_date && filters.end_date && (
                    <Badge variant="outline">Date: {filters.start_date} to {filters.end_date}</Badge>
                  )}
                  {filters.branch_id && (
                    <Badge variant="outline">Branch: {filters.branch_id}</Badge>
                  )}
                  {filters.region_id && (
                    <Badge variant="outline">Region: {filters.region_id}</Badge>
                  )}
                  {filters.product_id && (
                    <Badge variant="outline">Product: {filters.product_id}</Badge>
                  )}
                  {filters.category_id && (
                    <Badge variant="outline">Category: {filters.category_id}</Badge>
                  )}
                  {filters.payment_method && (
                    <Badge variant="outline">Payment: {filters.payment_method}</Badge>
                  )}
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)} disabled={isExporting}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                Exporting...
              </>
            ) : (
              <>
                {getExportIcon(exportType)}
                <span className="ml-2">Export ({getEstimatedTime()})</span>
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
