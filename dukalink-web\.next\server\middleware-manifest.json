{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_ebabcf0a._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_5830921f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|_next\\/data|favicon.ico|images|fonts).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|_next/data|favicon.ico|images|fonts).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "+oNudljpj9p7dWGY3XiSV8GquDtokFGZN6RtJ8/C9UU=", "__NEXT_PREVIEW_MODE_ID": "41bee369458acc8934af32a3573f1a37", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e59eb0a11c3b7908a45f6448ce3f0c64fee6e6592f97baa92524610ff7c0d64d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0cda6d466fea767fb8deacf29fff581bb948b2a1ffb1350819d546851123a17b"}}}, "sortedMiddleware": ["/"], "functions": {}}