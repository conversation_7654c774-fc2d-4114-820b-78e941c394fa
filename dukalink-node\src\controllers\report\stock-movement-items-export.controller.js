const ExcelJS = require('exceljs');
const { StockMovementItem, StockMovement, Branch, User, Product } = require('../../models');
const { Op } = require('sequelize');
const logger = require('../../utils/logger');

class StockMovementItemsExportController {
  /**
   * Export all stock movement items with comprehensive data
   */
  async getAllExportData(filters = {}) {
    try {
      const {
        status,
        from_branch_id,
        to_branch_id,
        product_id,
        reference_number,
        date_from,
        date_to,
        search,
        sort_by = 'created_at',
        sort_direction = 'desc'
      } = filters;

      // Build where clause for stock movement items
      const whereClause = {};

      logger.info('Exporting all stock movement items without role-based filtering (skip_role_filter=true)');

      if (status) {
        whereClause.status = status;
      }

      if (product_id) {
        whereClause.product_id = product_id;
      }

      if (date_from && date_to) {
        whereClause.created_at = {
          [Op.between]: [new Date(date_from), new Date(date_to)]
        };
      } else if (date_from) {
        whereClause.created_at = {
          [Op.gte]: new Date(date_from)
        };
      } else if (date_to) {
        whereClause.created_at = {
          [Op.lte]: new Date(date_to)
        };
      }

      // Build include clause with stock movement filters
      const stockMovementWhere = {};

      if (from_branch_id) {
        stockMovementWhere.from_branch_id = from_branch_id;
      }

      if (to_branch_id) {
        stockMovementWhere.to_branch_id = to_branch_id;
      }

      if (reference_number) {
        stockMovementWhere.reference_number = { [Op.like]: `%${reference_number}%` };
      }

      // Add search functionality for product names, SKUs, reference numbers, and notes
      if (search) {
        const searchTerm = search.trim();
        if (Op && typeof Op === 'object' && Op.or && Op.like) {
          // Search in stock movement item notes
          whereClause[Op.or] = [
            { notes: { [Op.like]: `%${searchTerm}%` } }
          ];

          // Also search in stock movement reference and notes
          if (Object.keys(stockMovementWhere).length === 0) {
            stockMovementWhere[Op.or] = [
              { reference_number: { [Op.like]: `%${searchTerm}%` } },
              { notes: { [Op.like]: `%${searchTerm}%` } }
            ];
          }
        }
        console.log(`Searching stock movement items by reference, notes, and product info: "${searchTerm}"`);
      }

      // Fetch stock movement items (all branches, no role-based filtering)
      const items = await StockMovementItem.findAll({
        where: whereClause,
        include: [
          {
            model: StockMovement,
            where: Object.keys(stockMovementWhere).length > 0 ? stockMovementWhere : undefined,
            include: [
              {
                model: Branch,
                as: 'FromBranch',
                attributes: ['id', 'name', 'location']
              },
              {
                model: Branch,
                as: 'ToBranch',
                attributes: ['id', 'name', 'location']
              },
              {
                model: User,
                as: 'RequestedBy',
                attributes: ['id', 'name', 'email']
              }
            ]
          },
          {
            model: Product,
            attributes: ['id', 'name', 'sku', 'has_serial'],
            // Add product search if search term is provided
            where: search ? {
              [Op.or]: [
                { name: { [Op.like]: `%${search.trim()}%` } },
                { sku: { [Op.like]: `%${search.trim()}%` } }
              ]
            } : undefined
          }
        ],
        order: [[sort_by, sort_direction.toUpperCase()]],
        raw: false
      });

      logger.info(`Found ${items.length} stock movement items for export`);
      logger.info(`Export query filters:`, JSON.stringify(filters, null, 2));

      // Transform items to export format
      const transformedItems = items.map((item) => ({
        id: item.id,
        reference_number: item.StockMovement?.reference_number || `SM-${item.stock_movement_id}`,
        product_name: item.Product?.name || 'Unknown Product',
        product_sku: item.Product?.sku || '',
        from_branch: item.StockMovement?.FromBranch?.name || 'HQ',
        to_branch: item.StockMovement?.ToBranch?.name || 'Unknown',
        status: item.status || 'pending',
        requested_quantity: item.requested_quantity || 0,
        dispatched_quantity: item.dispatched_quantity || 0,
        received_quantity: item.received_quantity || 0,
        created_at: item.created_at,
        updated_at: item.updated_at,
        notes: item.notes || '',
        // Additional fields for comprehensive export
        from_branch_location: item.StockMovement?.FromBranch?.location || '',
        to_branch_location: item.StockMovement?.ToBranch?.location || '',
        requested_by: item.StockMovement?.RequestedBy?.name || 'Unknown',
        requested_by_email: item.StockMovement?.RequestedBy?.email || '',
        has_serial: item.Product?.has_serial || false,
        stock_movement_id: item.stock_movement_id,
        product_id: item.product_id,
      }));

      return transformedItems;
    } catch (error) {
      logger.error('Error getting stock movement items export data:', error);
      throw error;
    }
  }

  /**
   * Generate summary statistics
   */
  generateSummaryStats(items, filters) {
    const stats = {
      total_items: items.length,
      by_status: {},
      by_from_branch: {},
      by_to_branch: {},
      by_product: {},
      quantity_totals: {
        total_requested: 0,
        total_dispatched: 0,
        total_received: 0
      },
      date_range: {
        earliest: null,
        latest: null
      },
      filters_applied: filters
    };

    // Count by status and calculate totals
    items.forEach(item => {
      const status = item.status || 'unknown';
      stats.by_status[status] = (stats.by_status[status] || 0) + 1;

      stats.quantity_totals.total_requested += item.requested_quantity || 0;
      stats.quantity_totals.total_dispatched += item.dispatched_quantity || 0;
      stats.quantity_totals.total_received += item.received_quantity || 0;
    });

    // Count by branches
    items.forEach(item => {
      const fromBranch = item.from_branch || 'unknown';
      const toBranch = item.to_branch || 'unknown';
      stats.by_from_branch[fromBranch] = (stats.by_from_branch[fromBranch] || 0) + 1;
      stats.by_to_branch[toBranch] = (stats.by_to_branch[toBranch] || 0) + 1;
    });

    // Count by product
    items.forEach(item => {
      const product = item.product_name || 'unknown';
      stats.by_product[product] = (stats.by_product[product] || 0) + 1;
    });

    // Date range
    if (items.length > 0) {
      const dates = items.map(item => new Date(item.created_at)).sort();
      stats.date_range.earliest = dates[0];
      stats.date_range.latest = dates[dates.length - 1];
    }

    return stats;
  }

  /**
   * Create Excel workbook with multiple sheets
   */
  async createExcelWorkbook(items, options = {}) {
    const {
      include_summary = true,
      include_details = true,
      include_status_breakdown = true,
      include_branch_breakdown = true,
      include_product_breakdown = true,
      filters = {}
    } = options;

    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'DukaLink POS System';
    workbook.created = new Date();

    // Generate summary statistics
    const stats = this.generateSummaryStats(items, filters);

    // Summary Sheet
    if (include_summary) {
      const summarySheet = workbook.addWorksheet('Summary');

      // Title
      summarySheet.addRow(['Stock Movement Items Export Summary']);
      summarySheet.addRow(['Generated on:', new Date().toLocaleString()]);
      summarySheet.addRow([]);

      // Key metrics
      summarySheet.addRow(['Total Items:', stats.total_items]);
      summarySheet.addRow(['Total Requested Quantity:', stats.quantity_totals.total_requested]);
      summarySheet.addRow(['Total Dispatched Quantity:', stats.quantity_totals.total_dispatched]);
      summarySheet.addRow(['Total Received Quantity:', stats.quantity_totals.total_received]);
      summarySheet.addRow(['Date Range:',
        stats.date_range.earliest ? stats.date_range.earliest.toLocaleDateString() : 'N/A',
        'to',
        stats.date_range.latest ? stats.date_range.latest.toLocaleDateString() : 'N/A'
      ]);
      summarySheet.addRow([]);

      // Applied filters
      summarySheet.addRow(['Applied Filters:']);
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          summarySheet.addRow([`${key}:`, value]);
        }
      });

      // Style the summary sheet
      summarySheet.getCell('A1').font = { bold: true, size: 16 };
      summarySheet.getColumn('A').width = 25;
      summarySheet.getColumn('B').width = 20;
    }

    // Items Details Sheet
    if (include_details) {
      const detailsSheet = workbook.addWorksheet('Items');

      // Headers
      const headers = [
        'Reference Number',
        'Product Name',
        'Product SKU',
        'From Branch',
        'To Branch',
        'Status',
        'Requested Qty',
        'Dispatched Qty',
        'Received Qty',
        'Date Created',
        'Requested By',
        'Notes'
      ];

      detailsSheet.addRow(headers);

      // Data rows
      items.forEach(item => {
        detailsSheet.addRow([
          item.reference_number,
          item.product_name,
          item.product_sku,
          item.from_branch,
          item.to_branch,
          item.status,
          item.requested_quantity,
          item.dispatched_quantity,
          item.received_quantity,
          item.created_at ? new Date(item.created_at).toLocaleDateString() : '',
          item.requested_by,
          item.notes
        ]);
      });

      // Style the headers
      const headerRow = detailsSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Auto-fit columns
      detailsSheet.columns.forEach(column => {
        column.width = 15;
      });
    }

    // Status Breakdown Sheet
    if (include_status_breakdown && Object.keys(stats.by_status).length > 0) {
      const statusSheet = workbook.addWorksheet('Status Breakdown');

      statusSheet.addRow(['Status', 'Count', 'Percentage']);

      Object.entries(stats.by_status).forEach(([status, count]) => {
        const percentage = ((count / stats.total_items) * 100).toFixed(1);
        statusSheet.addRow([status, count, `${percentage}%`]);
      });

      // Style headers
      const headerRow = statusSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      statusSheet.columns.forEach(column => {
        column.width = 15;
      });
    }

    // Branch Breakdown Sheet
    if (include_branch_breakdown && Object.keys(stats.by_from_branch).length > 0) {
      const branchSheet = workbook.addWorksheet('Branch Breakdown');

      branchSheet.addRow(['Source Branch Analysis']);
      branchSheet.addRow(['Branch', 'Outgoing Items']);

      Object.entries(stats.by_from_branch).forEach(([branch, count]) => {
        branchSheet.addRow([branch, count]);
      });

      branchSheet.addRow([]);
      branchSheet.addRow(['Destination Branch Analysis']);
      branchSheet.addRow(['Branch', 'Incoming Items']);

      Object.entries(stats.by_to_branch).forEach(([branch, count]) => {
        branchSheet.addRow([branch, count]);
      });

      // Style
      branchSheet.getCell('A1').font = { bold: true };
      branchSheet.getCell('A6').font = { bold: true };
      branchSheet.columns.forEach(column => {
        column.width = 20;
      });
    }

    // Product Breakdown Sheet
    if (include_product_breakdown && Object.keys(stats.by_product).length > 0) {
      const productSheet = workbook.addWorksheet('Product Breakdown');

      productSheet.addRow(['Product Analysis']);
      productSheet.addRow(['Product', 'Transfer Count']);

      // Sort products by count (descending)
      const sortedProducts = Object.entries(stats.by_product)
        .sort(([,a], [,b]) => b - a);

      sortedProducts.forEach(([product, count]) => {
        productSheet.addRow([product, count]);
      });

      // Style
      productSheet.getCell('A1').font = { bold: true };
      productSheet.columns.forEach(column => {
        column.width = 25;
      });
    }

    return workbook;
  }
}

// Route handlers
const exportAllStockMovementItems = async (req, res, next) => {
  try {
    logger.info('Starting comprehensive stock movement items export', {
      filters: req.query,
      user: req.user?.id
    });

    const controller = new StockMovementItemsExportController();
    const items = await controller.getAllExportData(req.query);

    const workbook = await controller.createExcelWorkbook(items, {
      include_summary: true,
      include_details: true,
      include_status_breakdown: true,
      include_branch_breakdown: true,
      include_product_breakdown: true,
      filters: req.query
    });

    // Set response headers for Excel download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=stock-movement-items-export.xlsx');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info('Stock movement items export completed successfully', {
      itemsCount: items.length,
      user: req.user?.id
    });

  } catch (error) {
    logger.error('Error exporting stock movement items:', error);
    next(error);
  }
};

const exportCustomStockMovementItems = async (req, res, next) => {
  try {
    logger.info('Starting custom stock movement items export', {
      filters: req.query,
      user: req.user?.id
    });

    const {
      include_summary = true,
      include_details = true,
      include_status_breakdown = true,
      include_branch_breakdown = true,
      include_product_breakdown = true,
      format_type = 'detailed'
    } = req.query;

    const controller = new StockMovementItemsExportController();
    const items = await controller.getAllExportData(req.query);

    const workbook = await controller.createExcelWorkbook(items, {
      include_summary: include_summary === 'true',
      include_details: include_details === 'true',
      include_status_breakdown: include_status_breakdown === 'true',
      include_branch_breakdown: include_branch_breakdown === 'true',
      include_product_breakdown: include_product_breakdown === 'true',
      filters: req.query
    });

    // Set response headers for Excel download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=stock-movement-items-custom-export.xlsx');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info('Custom stock movement items export completed successfully', {
      itemsCount: items.length,
      user: req.user?.id
    });

  } catch (error) {
    logger.error('Error exporting custom stock movement items:', error);
    next(error);
  }
};

module.exports = {
  StockMovementItemsExportController,
  exportAllStockMovementItems,
  exportCustomStockMovementItems
};
