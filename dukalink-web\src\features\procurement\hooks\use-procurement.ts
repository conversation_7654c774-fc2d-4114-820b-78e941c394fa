import { useToast } from "@/components/ui/use-toast";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
import { parseProcurementError } from "@/lib/error-utils";
import {
  CreateProcurementReceipt,
  CreateProcurementRequest,
  procurementApi,
} from "../api/procurement-api";

// Procurement Requests Hooks
export const useProcurementRequests = (params?: {
  page?: number;
  limit?: number;
  status?: string;
  requesting_branch_id?: number;
  delivery_branch_id?: number;
  supplier_id?: number;
  from_date?: string;
  to_date?: string;
}) => {
  return useQuery({
    queryKey: ["procurement-requests", params],
    queryFn: async () => {
      console.log("Fetching procurement requests with params:", params);
      try {
        const response = await procurementApi.getProcurementRequests(params);
        console.log("Procurement requests API response:", response);
        // If the response is an array, normalize and return it
        // If it has a data property, normalize and return the data
        // Otherwise, return an empty array
        if (Array.isArray(response)) {
          // Normalize each item in the array
          return response.map(item => {
            // Handle both supplier and Supplier
            if (item.Supplier && !item.supplier) {
              item.supplier = item.Supplier;
            }

            // Handle both items and Items
            if (item.Items && !item.items) {
              item.items = item.Items;

              // Also normalize product property in each item
              if (Array.isArray(item.items)) {
                item.items = item.items.map(subItem => {
                  if (subItem.Product && !subItem.product) {
                    subItem.product = subItem.Product;
                  }
                  return subItem;
                });
              }
            }

            return item;
          });
        } else if (response && response.data) {
          // Normalize each item in the data array
          if (Array.isArray(response.data)) {
            response.data = response.data.map(item => {
              // Handle both supplier and Supplier
              if (item.Supplier && !item.supplier) {
                item.supplier = item.Supplier;
              }

              // Handle both items and Items
              if (item.Items && !item.items) {
                item.items = item.Items;

                // Also normalize product property in each item
                if (Array.isArray(item.items)) {
                  item.items = item.items.map(subItem => {
                    if (subItem.Product && !subItem.product) {
                      subItem.product = subItem.Product;
                    }
                    return subItem;
                  });
                }
              }

              return item;
            });
          }
          return response.data;
        } else {
          return [];
        }
      } catch (error) {
        console.error("Error fetching procurement requests:", error);
        throw error;
      }
    },
  });
};

export const useProcurementRequest = (id: number) => {
  return useQuery({
    queryKey: ["procurement-request", id],
    queryFn: async () => {
      console.log("Fetching procurement request with ID:", id);
      try {
        const response = await procurementApi.getProcurementRequestById(id);
        console.log("Procurement request API response:", response);

        // Handle different response structures
        if (response) {
          // If response has data property, return the response
          if (response.data) {
            // Normalize the supplier property (handle both supplier and Supplier)
            if (response.data.Supplier && !response.data.supplier) {
              response.data.supplier = response.data.Supplier;
            }

            // Normalize the items property (handle both items and Items)
            if (response.data.Items && !response.data.items) {
              response.data.items = response.data.Items;

              // Also normalize product property in each item
              if (Array.isArray(response.data.items)) {
                response.data.items = response.data.items.map(item => {
                  if (item.Product && !item.product) {
                    item.product = item.Product;
                  }
                  return item;
                });
              }
            }

            return response;
          }

          // If response itself is the data (no wrapper object)
          if (response.id) {
            // Normalize the supplier property (handle both supplier and Supplier)
            if (response.Supplier && !response.supplier) {
              response.supplier = response.Supplier;
            }

            // Normalize the items property (handle both items and Items)
            if (response.Items && !response.items) {
              response.items = response.Items;

              // Also normalize product property in each item
              if (Array.isArray(response.items)) {
                response.items = response.items.map(item => {
                  if (item.Product && !item.product) {
                    item.product = item.Product;
                  }
                  return item;
                });
              }
            }

            return { success: true, data: response };
          }

          // If it's an empty object, return a structured error
          if (Object.keys(response).length === 0) {
            console.warn("Empty response received for procurement request");
            return {
              success: false,
              data: null,
              message: "No data found for this procurement request"
            };
          }
        }

        // Fallback for unexpected response structure
        console.error("Unexpected API response structure:", response);
        return {
          success: false,
          data: null,
          message: "Invalid API response structure"
        };
      } catch (error) {
        console.error("Error fetching procurement request:", error);
        throw error;
      }
    },
    enabled: !!id,
    retry: 1, // Only retry once to avoid excessive requests
  });
};

export const useCreateProcurementRequest = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: CreateProcurementRequest) => {
      console.log("Creating procurement request with data:", data);
      return procurementApi.createProcurementRequest(data);
    },
    onSuccess: (response) => {
      console.log("Procurement request created successfully in hook:", response);
      queryClient.invalidateQueries({ queryKey: ["procurement-requests"] });
      // Note: We're not showing the toast here anymore, it's handled in the component
    },
    onError: (error: any) => {
      console.error("Error creating procurement request in hook:", error);
      toast.error(
        "Error",
        {
          description: error.response?.data?.message || "Failed to create procurement request"
        }
      );
    },
  });
};

export const useSubmitProcurementRequest = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: number) => procurementApi.submitProcurementRequest(id),
    onSuccess: (_, id) => {
      queryClient.invalidateQueries({
        queryKey: ["procurement-requests"],
      });
      queryClient.invalidateQueries({
        queryKey: ["procurement-request", id],
      });
      toast.success(
        "Success",
        { description: "Procurement request submitted successfully" }
      );
    },
    onError: (error: any) => {
      toast.error(
        "Error",
        {
          description: error.response?.data?.message || "Failed to submit procurement request"
        }
      );
    },
  });
};

export const useApproveProcurementRequest = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: number;
      data: {
        items?: {
          id: number;
          approved_quantity: number;
          notes?: string;
        }[];
      };
    }) => procurementApi.approveProcurementRequest(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: ["procurement-requests"],
      });
      queryClient.invalidateQueries({
        queryKey: ["procurement-request", id],
      });
      toast({
        title: "Success",
        description: "Procurement request approved successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          "Failed to approve procurement request",
        variant: "destructive",
      });
    },
  });
};

export const useRejectProcurementRequest = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, reason }: { id: number; reason: string }) =>
      procurementApi.rejectProcurementRequest(id, { reason }),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: ["procurement-requests"],
      });
      queryClient.invalidateQueries({
        queryKey: ["procurement-request", id],
      });
      toast({
        title: "Success",
        description: "Procurement request rejected successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          "Failed to reject procurement request",
        variant: "destructive",
      });
    },
  });
};

export const usePrintPurchaseOrder = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: number) => procurementApi.printPurchaseOrder(id),
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Print dialog opened successfully",
      });
    },
    onError: (error: any) => {
      console.error("Print error:", error);
      toast({
        title: "Error",
        description:
          error.response?.data?.message ||
          "Failed to print purchase order",
        variant: "destructive",
      });
    },
  });
};

// Procurement Receipts Hooks
export const useProcurementReceipts = (params?: {
  page?: number;
  limit?: number;
  procurement_request_id?: number;
  receiving_branch_id?: number;
  from_date?: string;
  to_date?: string;
}) => {
  return useQuery({
    queryKey: ["procurement-receipts", params],
    queryFn: () => procurementApi.getProcurementReceipts(params),
  });
};

export const useProcurementReceipt = (id: number) => {
  return useQuery({
    queryKey: ["procurement-receipt", id],
    queryFn: () => procurementApi.getProcurementReceiptById(id),
    enabled: !!id,
  });
};

export const useCreateProcurementReceipt = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: CreateProcurementReceipt) =>
      procurementApi.createProcurementReceipt(data),
    onSuccess: (_, { procurement_request_id }) => {
      queryClient.invalidateQueries({
        queryKey: ["procurement-receipts"],
      });
      queryClient.invalidateQueries({
        queryKey: ["procurement-request", procurement_request_id],
      });
      toast({
        title: "Success",
        description: "Procurement receipt created successfully",
      });
    },
    onError: (error: any) => {
      console.error("Procurement receipt creation error:", error);

      // Parse error using utility function
      const parsedError = parseProcurementError(error);

      toast({
        title: parsedError.title,
        description: parsedError.message,
        variant: "destructive",
      });
    },
  });
};

export const useUploadDeliveryNote = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ id, file }: { id: number; file: File }) =>
      procurementApi.uploadDeliveryNote(id, file),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({
        queryKey: ["procurement-receipts"],
      });
      queryClient.invalidateQueries({
        queryKey: ["procurement-receipt", id],
      });
      toast({
        title: "Success",
        description: "Delivery note uploaded successfully",
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description:
          error.response?.data?.message || "Failed to upload delivery note",
        variant: "destructive",
      });
    },
  });
};
