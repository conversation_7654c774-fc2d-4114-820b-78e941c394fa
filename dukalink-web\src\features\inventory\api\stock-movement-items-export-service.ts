import apiClient from "@/lib/api-client";

/**
 * Enhanced Stock Movement Items Export Service
 * Provides methods for comprehensive Excel export functionality for stock movement items data
 */

export interface StockMovementItemsExportParams {
  status?: string;
  from_branch_id?: number;
  to_branch_id?: number;
  product_id?: number;
  reference_number?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
  sort_by?: string;
  sort_direction?: string;
  include_summary?: boolean;
  include_details?: boolean;
  include_status_breakdown?: boolean;
  include_branch_breakdown?: boolean;
  include_product_breakdown?: boolean;
}

export interface CustomStockMovementItemsExportParams extends StockMovementItemsExportParams {
  columns?: string; // comma-separated list or "all"
  format_type?: 'detailed' | 'summary';
}

const stockMovementItemsExportService = {
  /**
   * Export all stock movement items with comprehensive data
   */
  exportAllItems: async (params?: StockMovementItemsExportParams): Promise<Blob> => {
    try {
      console.log("Starting comprehensive stock movement items export...", params);

      const response: any = await apiClient.get("/stock-movement-items/export/all", {
        params: {
          ...params,
          include_summary: params?.include_summary ?? true,
          include_details: params?.include_details ?? true,
          include_status_breakdown: params?.include_status_breakdown ?? true,
          include_branch_breakdown: params?.include_branch_breakdown ?? true,
          include_product_breakdown: params?.include_product_breakdown ?? true,
        },
        responseType: "blob",
        timeout: 120000, // 2 minutes timeout for large exports
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting all stock movement items:", error);

      // Provide more specific error messages
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export stock movement reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. The dataset might be too large. Try filtering the data.");
      } else {
        throw new Error(error.message || "Failed to export stock movement items");
      }
    }
  },

  /**
   * Export stock movement items with custom options
   */
  exportCustomItems: async (params?: CustomStockMovementItemsExportParams): Promise<Blob> => {
    try {
      console.log("Starting custom stock movement items export...", params);

      const response: any = await apiClient.get("/stock-movement-items/export/custom", {
        params: {
          ...params,
          columns: params?.columns || "all",
          format_type: params?.format_type || "detailed",
        },
        responseType: "blob",
        timeout: 90000, // 1.5 minutes timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting custom stock movement items:", error);

      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export stock movement reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. Try using summary format or filtering the data.");
      } else {
        throw new Error(error.message || "Failed to export custom stock movement items");
      }
    }
  },

  /**
   * Export summary format (lightweight)
   */
  exportSummaryItems: async (params?: StockMovementItemsExportParams): Promise<Blob> => {
    return stockMovementItemsExportService.exportCustomItems({
      ...params,
      format_type: 'summary',
      columns: 'reference,product,from_branch,to_branch,status,quantities,date',
    });
  },

  /**
   * Download blob as file with proper filename
   */
  downloadBlob: (blob: Blob, filename?: string): void => {
    try {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Generate filename if not provided
      if (!filename) {
        const timestamp = new Date().toISOString().split('T')[0];
        filename = `stock-movement-items-export-${timestamp}.xlsx`;
      }

      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log(`File downloaded: ${filename}`);
    } catch (error) {
      console.error('Error downloading file:', error);
      throw new Error('Failed to download the export file');
    }
  },

  /**
   * Generate descriptive filename based on filters
   */
  generateFilename: (params?: StockMovementItemsExportParams, formatType: string = 'comprehensive'): string => {
    const timestamp = new Date().toISOString().split('T')[0];
    const parts = ['stock-movement-items'];

    if (formatType !== 'comprehensive') {
      parts.push(formatType);
    }

    if (params?.status) {
      parts.push(`status-${params.status.toLowerCase()}`);
    }

    if (params?.from_branch_id) {
      parts.push(`from-branch-${params.from_branch_id}`);
    }

    if (params?.to_branch_id) {
      parts.push(`to-branch-${params.to_branch_id}`);
    }

    if (params?.product_id) {
      parts.push(`product-${params.product_id}`);
    }

    if (params?.search) {
      parts.push('filtered');
    }

    if (params?.date_from && params?.date_to) {
      parts.push(`${params.date_from}-to-${params.date_to}`);
    }

    parts.push(timestamp);

    return `${parts.join('-')}.xlsx`;
  },

  /**
   * Estimate export size and provide recommendations
   */
  getExportRecommendation: (estimatedRecords: number): {
    recommended: 'all' | 'custom' | 'summary';
    message: string;
    estimatedTime: string;
  } => {
    if (estimatedRecords <= 1000) {
      return {
        recommended: 'all',
        message: 'Small dataset - comprehensive export recommended',
        estimatedTime: '< 30 seconds'
      };
    } else if (estimatedRecords <= 5000) {
      return {
        recommended: 'custom',
        message: 'Medium dataset - custom export with selected sheets recommended',
        estimatedTime: '30-90 seconds'
      };
    } else {
      return {
        recommended: 'summary',
        message: 'Large dataset - summary export recommended for faster processing',
        estimatedTime: '1-3 minutes'
      };
    }
  },

  /**
   * Validate export parameters
   */
  validateExportParams: (params?: StockMovementItemsExportParams): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (params?.from_branch_id && (params.from_branch_id < 1 || !Number.isInteger(params.from_branch_id))) {
      errors.push('From branch ID must be a positive integer');
    }

    if (params?.to_branch_id && (params.to_branch_id < 1 || !Number.isInteger(params.to_branch_id))) {
      errors.push('To branch ID must be a positive integer');
    }

    if (params?.product_id && (params.product_id < 1 || !Number.isInteger(params.product_id))) {
      errors.push('Product ID must be a positive integer');
    }

    if (params?.search && params.search.length < 2) {
      errors.push('Search term must be at least 2 characters long');
    }

    if (params?.date_from && params?.date_to) {
      const fromDate = new Date(params.date_from);
      const toDate = new Date(params.date_to);
      if (fromDate > toDate) {
        errors.push('From date must be before to date');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Get export format options for UI
   */
  getExportFormatOptions: () => [
    {
      value: 'all',
      label: 'Comprehensive Export',
      description: 'All stock movement items with summary, details, status breakdown, and analysis',
      icon: 'FileSpreadsheet',
      estimatedTime: 'Slower but complete'
    },
    {
      value: 'custom',
      label: 'Custom Export',
      description: 'Select specific sheets and columns to include',
      icon: 'Settings',
      estimatedTime: 'Customizable speed'
    },
    {
      value: 'summary',
      label: 'Summary Export',
      description: 'Essential stock movement data only for quick analysis',
      icon: 'Zap',
      estimatedTime: 'Fast and lightweight'
    }
  ],

  /**
   * Get available column options for custom export
   */
  getColumnOptions: () => [
    { value: 'reference', label: 'Reference Number', category: 'basic' },
    { value: 'product', label: 'Product Name', category: 'basic' },
    { value: 'sku', label: 'Product SKU', category: 'basic' },
    { value: 'from_branch', label: 'From Branch', category: 'basic' },
    { value: 'to_branch', label: 'To Branch', category: 'basic' },
    { value: 'status', label: 'Status', category: 'basic' },
    { value: 'quantities', label: 'Quantities (Requested/Dispatched/Received)', category: 'quantities' },
    { value: 'requested_quantity', label: 'Requested Quantity', category: 'quantities' },
    { value: 'dispatched_quantity', label: 'Dispatched Quantity', category: 'quantities' },
    { value: 'received_quantity', label: 'Received Quantity', category: 'quantities' },
    { value: 'date', label: 'Date Created', category: 'dates' },
    { value: 'notes', label: 'Notes', category: 'details' },
  ]
};

export default stockMovementItemsExportService;
