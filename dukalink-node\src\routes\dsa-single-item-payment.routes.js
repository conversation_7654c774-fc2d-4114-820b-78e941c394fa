const express = require('express');
const router = express.Router();
const dsaSingleItemPaymentController = require('../controllers/dsa-single-item-payment.controller');
const { authenticate } = require('../middleware/auth');
const rbac = require('../middleware/rbac.middleware');

/**
 * DSA Single Item Payment Routes
 * Handles payment collection for individual DSA assignment items
 */

/**
 * @route   POST /api/dsa-payments/single-item
 * @desc    Process payment for a single DSA item
 * @access  Private (Branch Manager, Company Admin, Tenant Admin, Super Admin)
 */
router.post(
  '/',
  authenticate,
  rbac.checkPermission('dsa', 'create'),
  dsaSingleItemPaymentController.processSingleItemPayment
);

/**
 * @route   POST /api/dsa-payments/single-item/validate
 * @desc    Validate single item payment before processing
 * @access  Private (Branch Manager, Company Admin, Tenant Admin, Super Admin)
 */
router.post(
  '/validate',
  authenticate,
  rbac.checkPermission('dsa', 'read'),
  dsaSingleItemPaymentController.validateSingleItemPayment
);

/**
 * @route   GET /api/dsa-payments/single-item/unpaid/:assignment_identifier
 * @desc    Get unpaid items for an assignment
 * @access  Private (Branch Manager, Company Admin, Tenant Admin, Super Admin)
 */
router.get(
  '/unpaid/:assignment_identifier',
  authenticate,
  rbac.checkPermission('dsa', 'read'),
  dsaSingleItemPaymentController.getUnpaidItems
);

/**
 * @route   GET /api/dsa-payments/single-item/summary/:assignment_identifier
 * @desc    Get payment summary for an assignment
 * @access  Private (Branch Manager, Company Admin, Tenant Admin, Super Admin)
 */
router.get(
  '/summary/:assignment_identifier',
  authenticate,
  rbac.checkPermission('dsa', 'read'),
  dsaSingleItemPaymentController.getPaymentSummary
);

/**
 * @route   GET /api/dsa-payments/single-item/history/:assignment_identifier/:item_id
 * @desc    Get payment history for a specific item
 * @access  Private (Branch Manager, Company Admin, Tenant Admin, Super Admin)
 */
router.get(
  '/history/:assignment_identifier/:item_id',
  authenticate,
  rbac.checkPermission('dsa', 'read'),
  dsaSingleItemPaymentController.getItemPaymentHistory
);

module.exports = router;
