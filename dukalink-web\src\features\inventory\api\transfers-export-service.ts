import apiClient from "@/lib/api-client";

/**
 * Enhanced Stock Transfers Export Service
 * Provides methods for comprehensive Excel export functionality for stock transfers data
 */

export interface TransfersExportParams {
  status?: string;
  source_branch_id?: number;
  destination_branch_id?: number;
  created_by?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
  sort_by?: string;
  sort_direction?: string;
  include_summary?: boolean;
  include_details?: boolean;
  include_status_breakdown?: boolean;
  include_branch_breakdown?: boolean;
}

export interface CustomTransfersExportParams extends TransfersExportParams {
  columns?: string; // comma-separated list or "all"
  format_type?: 'detailed' | 'summary';
}

const transfersExportService = {
  /**
   * Export all stock transfers with comprehensive data
   */
  exportAllTransfers: async (params?: TransfersExportParams): Promise<Blob> => {
    try {
      console.log("Starting comprehensive transfers export...", params);

      const response: any = await apiClient.get("/stock-movements/export/all", {
        params: {
          ...params,
          include_summary: params?.include_summary ?? true,
          include_details: params?.include_details ?? true,
          include_status_breakdown: params?.include_status_breakdown ?? true,
          include_branch_breakdown: params?.include_branch_breakdown ?? true,
        },
        responseType: "blob",
        timeout: 120000, // 2 minutes timeout for large exports
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting all transfers:", error);
      
      // Provide more specific error messages
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export transfer reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. The dataset might be too large. Try filtering the data.");
      } else {
        throw new Error(error.message || "Failed to export transfers");
      }
    }
  },

  /**
   * Export transfers with custom options
   */
  exportCustomTransfers: async (params?: CustomTransfersExportParams): Promise<Blob> => {
    try {
      console.log("Starting custom transfers export...", params);

      const response: any = await apiClient.get("/stock-movements/export/custom", {
        params: {
          ...params,
          columns: params?.columns || "all",
          format_type: params?.format_type || "detailed",
        },
        responseType: "blob",
        timeout: 90000, // 1.5 minutes timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting custom transfers:", error);
      
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export transfer reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. Try using summary format or filtering the data.");
      } else {
        throw new Error(error.message || "Failed to export custom transfers");
      }
    }
  },

  /**
   * Export summary format (lightweight)
   */
  exportSummaryTransfers: async (params?: TransfersExportParams): Promise<Blob> => {
    return transfersExportService.exportCustomTransfers({
      ...params,
      format_type: 'summary',
      columns: 'reference,source,destination,status,created_by,date',
    });
  },

  /**
   * Download blob as file with proper filename
   */
  downloadBlob: (blob: Blob, filename?: string): void => {
    try {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Generate filename if not provided
      if (!filename) {
        const timestamp = new Date().toISOString().split('T')[0];
        filename = `transfers-export-${timestamp}.xlsx`;
      }
      
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      console.log(`File downloaded: ${filename}`);
    } catch (error) {
      console.error('Error downloading file:', error);
      throw new Error('Failed to download the export file');
    }
  },

  /**
   * Generate descriptive filename based on filters
   */
  generateFilename: (params?: TransfersExportParams, formatType: string = 'comprehensive'): string => {
    const timestamp = new Date().toISOString().split('T')[0];
    const parts = ['transfers'];
    
    if (formatType !== 'comprehensive') {
      parts.push(formatType);
    }
    
    if (params?.status) {
      parts.push(`status-${params.status.toLowerCase()}`);
    }
    
    if (params?.source_branch_id) {
      parts.push(`from-branch-${params.source_branch_id}`);
    }
    
    if (params?.destination_branch_id) {
      parts.push(`to-branch-${params.destination_branch_id}`);
    }
    
    if (params?.search) {
      parts.push('filtered');
    }
    
    if (params?.date_from && params?.date_to) {
      parts.push(`${params.date_from}-to-${params.date_to}`);
    }
    
    parts.push(timestamp);
    
    return `${parts.join('-')}.xlsx`;
  },

  /**
   * Estimate export size and provide recommendations
   */
  getExportRecommendation: (estimatedRecords: number): {
    recommended: 'all' | 'custom' | 'summary';
    message: string;
    estimatedTime: string;
  } => {
    if (estimatedRecords <= 500) {
      return {
        recommended: 'all',
        message: 'Small dataset - comprehensive export recommended',
        estimatedTime: '< 30 seconds'
      };
    } else if (estimatedRecords <= 2000) {
      return {
        recommended: 'custom',
        message: 'Medium dataset - custom export with selected sheets recommended',
        estimatedTime: '30-60 seconds'
      };
    } else {
      return {
        recommended: 'summary',
        message: 'Large dataset - summary export recommended for faster processing',
        estimatedTime: '1-2 minutes'
      };
    }
  },

  /**
   * Validate export parameters
   */
  validateExportParams: (params?: TransfersExportParams): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (params?.source_branch_id && (params.source_branch_id < 1 || !Number.isInteger(params.source_branch_id))) {
      errors.push('Source branch ID must be a positive integer');
    }
    
    if (params?.destination_branch_id && (params.destination_branch_id < 1 || !Number.isInteger(params.destination_branch_id))) {
      errors.push('Destination branch ID must be a positive integer');
    }
    
    if (params?.search && params.search.length < 2) {
      errors.push('Search term must be at least 2 characters long');
    }
    
    if (params?.date_from && params?.date_to) {
      const fromDate = new Date(params.date_from);
      const toDate = new Date(params.date_to);
      if (fromDate > toDate) {
        errors.push('From date must be before to date');
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Get export format options for UI
   */
  getExportFormatOptions: () => [
    {
      value: 'all',
      label: 'Comprehensive Export',
      description: 'All transfer data with summary, details, status breakdown, and branch analysis',
      icon: 'FileSpreadsheet',
      estimatedTime: 'Slower but complete'
    },
    {
      value: 'custom',
      label: 'Custom Export',
      description: 'Select specific sheets and columns to include',
      icon: 'Settings',
      estimatedTime: 'Customizable speed'
    },
    {
      value: 'summary',
      label: 'Summary Export',
      description: 'Essential transfer data only for quick analysis',
      icon: 'Zap',
      estimatedTime: 'Fast and lightweight'
    }
  ],

  /**
   * Get available column options for custom export
   */
  getColumnOptions: () => [
    { value: 'reference', label: 'Reference Number', category: 'basic' },
    { value: 'source', label: 'Source Branch', category: 'basic' },
    { value: 'destination', label: 'Destination Branch', category: 'basic' },
    { value: 'status', label: 'Status', category: 'basic' },
    { value: 'created_by', label: 'Created By', category: 'basic' },
    { value: 'date', label: 'Date Created', category: 'basic' },
    { value: 'items_count', label: 'Items Count', category: 'details' },
    { value: 'total_quantity', label: 'Total Quantity', category: 'details' },
    { value: 'notes', label: 'Notes', category: 'details' },
    { value: 'approved_by', label: 'Approved By', category: 'approval' },
    { value: 'approved_date', label: 'Approval Date', category: 'approval' },
    { value: 'dispatched_date', label: 'Dispatch Date', category: 'tracking' },
    { value: 'received_date', label: 'Received Date', category: 'tracking' },
  ]
};

export default transfersExportService;
