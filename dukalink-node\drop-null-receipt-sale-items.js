const mysql = require('mysql2/promise');
require('dotenv').config();

async function dropNullReceiptSaleItems() {
  let connection;

  try {
    console.log('🗑️  Preparing to drop sale items for sales with null receipts...');
    console.log('📅 Date:', new Date().toISOString().split('T')[0]);
    console.log('⚠️  WARNING: This will permanently delete sale items data!');

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT || 3306,
    });

    console.log('✅ Connected to database successfully');

    // Get today's date in MySQL format
    const today = new Date().toISOString().split('T')[0];

    // First, let's see what we're about to delete
    console.log('🔍 Analyzing sale items to be deleted...');

    const analysisQuery = `
      SELECT
        s.id as sale_id,
        s.customer_id,
        s.total_amount,
        s.payment_method_id,
        s.payment_reference,
        s.created_at,
        c.name as customer_name,
        COUNT(si.id) as item_count,
        SUM(si.total_price) as items_total_value
      FROM sales s
      LEFT JOIN customers c ON s.customer_id = c.id
      LEFT JOIN sale_items si ON s.id = si.sale_id
      WHERE DATE(s.created_at) = ?
        AND s.receipt_number IS NULL
        AND s.is_dsa = 1
      GROUP BY s.id, s.customer_id, s.total_amount, s.payment_method_id, s.payment_reference, s.created_at, c.name
      ORDER BY s.customer_id, s.created_at;
    `;

    const [salesToClean] = await connection.execute(analysisQuery, [today]);

    if (salesToClean.length === 0) {
      console.log('✅ No sales with null receipts found for today!');
      return;
    }

    console.log(`📊 Found ${salesToClean.length} sales with null receipts:`);
    console.log('');

    let totalItemsToDelete = 0;
    let totalValueToDelete = 0;

    salesToClean.forEach((sale, index) => {
      console.log(`${index + 1}. Sale ID ${sale.sale_id} - ${sale.customer_name}`);
      console.log(`   Amount: ${sale.total_amount} KES`);
      console.log(`   Items: ${sale.item_count} (Value: ${sale.items_total_value} KES)`);
      console.log(`   Payment Method: ${sale.payment_method_id}`);
      console.log(`   Reference: ${sale.payment_reference || 'None'}`);
      console.log(`   Created: ${sale.created_at}`);

      totalItemsToDelete += parseInt(sale.item_count);
      totalValueToDelete += parseFloat(sale.items_total_value || 0);
    });

    console.log('');
    console.log(`📈 SUMMARY:`);
    console.log(`   Sales to clean: ${salesToClean.length}`);
    console.log(`   Total sale items to delete: ${totalItemsToDelete}`);
    console.log(`   Total value of items to delete: ${totalValueToDelete} KES`);
    console.log('');

    // Safety check - require manual confirmation
    console.log('⚠️  SAFETY CHECK:');
    console.log('   This operation will permanently delete sale items for null receipt sales.');
    console.log('   The sales records themselves will remain, but will have no items.');
    console.log('   This should clean up the duplicate data from the dual-sale issue.');
    console.log('');
    console.log('🔄 To proceed, you need to modify this script to set CONFIRM_DELETE = true');

    const CONFIRM_DELETE = true; // Set to true to actually perform the deletion

    if (!CONFIRM_DELETE) {
      console.log('❌ Deletion not confirmed. Exiting safely.');
      console.log('   To proceed, set CONFIRM_DELETE = true in the script.');
      return;
    }

    // If we reach here, deletion is confirmed
    console.log('🚀 Proceeding with deletion...');

    // Start transaction for safety
    await connection.beginTransaction();

    try {
      // Get the sale IDs to delete items for
      const saleIdsQuery = `
        SELECT id
        FROM sales
        WHERE DATE(created_at) = ?
          AND receipt_number IS NULL
          AND is_dsa = 1
      `;

      const [saleIds] = await connection.execute(saleIdsQuery, [today]);

      if (saleIds.length === 0) {
        console.log('❌ No sale IDs found for deletion');
        await connection.rollback();
        return;
      }

      const saleIdList = saleIds.map(row => row.id);
      console.log(`🎯 Deleting sale items for sale IDs: ${saleIdList.join(', ')}`);

      // Delete sale items for these sales
      const deleteQuery = `
        DELETE FROM sale_items
        WHERE sale_id IN (${saleIdList.map(() => '?').join(',')})
      `;

      const [deleteResult] = await connection.execute(deleteQuery, saleIdList);

      console.log(`✅ Deleted ${deleteResult.affectedRows} sale items`);

      // Verify deletion
      const verifyQuery = `
        SELECT COUNT(*) as remaining_items
        FROM sale_items si
        JOIN sales s ON si.sale_id = s.id
        WHERE DATE(s.created_at) = ?
          AND s.receipt_number IS NULL
          AND s.is_dsa = 1
      `;

      const [verifyResult] = await connection.execute(verifyQuery, [today]);
      const remainingItems = verifyResult[0].remaining_items;

      if (remainingItems === 0) {
        console.log('✅ Verification passed: No sale items remain for null receipt sales');
        await connection.commit();
        console.log('🎉 Transaction committed successfully!');
      } else {
        console.log(`❌ Verification failed: ${remainingItems} items still remain`);
        await connection.rollback();
        console.log('🔄 Transaction rolled back for safety');
      }

    } catch (deleteError) {
      console.error('❌ Error during deletion:', deleteError.message);
      await connection.rollback();
      console.log('🔄 Transaction rolled back due to error');
      throw deleteError;
    }

    // Final summary
    console.log('');
    console.log('📊 CLEANUP SUMMARY:');
    console.log(`   ✅ Cleaned ${salesToClean.length} sales with null receipts`);
    console.log(`   ✅ Removed ${totalItemsToDelete} duplicate sale items`);
    console.log(`   ✅ Cleaned up ${totalValueToDelete} KES worth of duplicate data`);
    console.log('');
    console.log('🎯 Next steps:');
    console.log('   1. The sales records still exist but have no items');
    console.log('   2. Consider deleting the empty sales records as well');
    console.log('   3. Deploy the DSA fix to prevent future duplicates');
    console.log('   4. Monitor for any remaining duplicate issues');

  } catch (error) {
    console.error('❌ Error dropping null receipt sale items:', error.message);
    console.error('Full error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the cleanup
dropNullReceiptSaleItems();
