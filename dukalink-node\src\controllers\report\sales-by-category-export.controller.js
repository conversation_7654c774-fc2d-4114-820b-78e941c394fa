const ExcelJS = require('exceljs');
const { Sale, SaleItem, Branch, User, Product, Category } = require('../../models');
const { Op } = require('sequelize');
const logger = require('../../utils/logger');

class SalesByCategoryExportController {
  /**
   * Get sales by category data with filtering
   */
  async getSalesByCategoryData(filters = {}) {
    try {
      const {
        start_date,
        end_date,
        branch_id,
        region_id,
        category_id
      } = filters;

      logger.info('Exporting sales by category data with filters:', filters);

      // Build where clause for sales
      const whereClause = {};

      // Default to last 30 days if no date range specified
      if (start_date && end_date) {
        whereClause.created_at = {
          [Op.between]: [new Date(start_date), new Date(end_date)]
        };
      } else if (start_date) {
        whereClause.created_at = {
          [Op.gte]: new Date(start_date)
        };
      } else if (end_date) {
        whereClause.created_at = {
          [Op.lte]: new Date(end_date)
        };
      } else {
        // Default to last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        whereClause.created_at = {
          [Op.gte]: thirtyDaysAgo
        };
        logger.info('No date range specified, defaulting to last 30 days');
      }

      if (branch_id) {
        whereClause.branch_id = branch_id;
      }

      // Fetch sales data with basic includes
      const sales = await Sale.findAll({
        where: whereClause,
        include: [
          {
            model: Branch,
            attributes: ['id', 'name', 'location'],
            required: false
          },
          {
            model: User,
            attributes: ['id', 'name', 'email'],
            required: false
          }
        ],
        order: [['created_at', 'DESC']],
        limit: 2000, // Limit for performance
        raw: false
      });

      logger.info(`Found ${sales.length} sales for category analysis`);

      // Get sale items with categories
      const saleIds = sales.map(sale => sale.id);
      if (saleIds.length === 0) {
        return { sales: [], categoryData: [] };
      }

      // Fetch sale items with products and categories
      const saleItems = await SaleItem.findAll({
        where: {
          sale_id: {
            [Op.in]: saleIds
          }
        },
        include: [
          {
            model: Product,
            attributes: ['id', 'name', 'sku'],
            required: false,
            include: [
              {
                model: Category,
                attributes: ['id', 'name'],
                where: category_id ? { id: category_id } : undefined,
                required: false
              }
            ]
          }
        ],
        raw: false
      });

      logger.info(`Found ${saleItems.length} sale items for category analysis`);

      // Process category data
      const categoryMap = new Map();
      const salesMap = new Map();

      // Map sales for easy lookup
      sales.forEach(sale => {
        salesMap.set(sale.id, sale);
      });

      // Process sale items to build category breakdown
      saleItems.forEach(item => {
        const sale = salesMap.get(item.sale_id);
        if (!sale) return;

        const categoryId = item.Product?.Category?.id || 'uncategorized';
        const categoryName = item.Product?.Category?.name || 'Uncategorized';

        if (!categoryMap.has(categoryId)) {
          categoryMap.set(categoryId, {
            category_id: categoryId,
            category_name: categoryName,
            quantity: 0,
            total_sales: 0,
            total_cost: 0,
            profit: 0,
            sales_count: 0,
            products: new Set()
          });
        }

        const categoryData = categoryMap.get(categoryId);
        categoryData.quantity += parseInt(item.quantity) || 0;
        categoryData.total_sales += parseFloat(item.total_amount) || 0;
        categoryData.total_cost += parseFloat(item.cost_amount) || 0;
        categoryData.profit += (parseFloat(item.total_amount) || 0) - (parseFloat(item.cost_amount) || 0);
        categoryData.sales_count += 1;
        categoryData.products.add(item.Product?.name || 'Unknown Product');
      });

      // Convert to array and calculate percentages
      const categoryData = Array.from(categoryMap.values()).map(category => ({
        ...category,
        products: Array.from(category.products),
        product_count: category.products.size
      }));

      // Calculate total sales for percentage calculation
      const totalSales = categoryData.reduce((sum, cat) => sum + cat.total_sales, 0);

      categoryData.forEach(category => {
        category.percentage = totalSales > 0 ? (category.total_sales / totalSales * 100) : 0;
        category.profit_margin = category.total_sales > 0 ? (category.profit / category.total_sales * 100) : 0;
      });

      // Sort by total sales descending
      categoryData.sort((a, b) => b.total_sales - a.total_sales);

      return {
        sales: sales.map(sale => ({
          id: sale.id,
          receipt_number: sale.receipt_number,
          created_at: sale.created_at,
          branch_name: sale.Branch?.name || 'Unknown',
          branch_location: sale.Branch?.location || '',
          user_name: sale.User?.name || 'Unknown',
          total_amount: parseFloat(sale.total_amount) || 0,
          discount_amount: parseFloat(sale.discount_amount) || 0,
          net_amount: parseFloat(sale.net_amount) || 0,
          payment_method: sale.payment_method || 'Unknown'
        })),
        categoryData
      };
    } catch (error) {
      logger.error('Error getting sales by category data:', error);
      throw error;
    }
  }

  /**
   * Generate summary statistics
   */
  generateSummaryStats(categoryData, filters) {
    const stats = {
      total_categories: categoryData.length,
      total_sales: 0,
      total_profit: 0,
      total_quantity: 0,
      average_profit_margin: 0,
      top_category: null,
      filters_applied: filters
    };

    if (categoryData.length > 0) {
      stats.total_sales = categoryData.reduce((sum, cat) => sum + cat.total_sales, 0);
      stats.total_profit = categoryData.reduce((sum, cat) => sum + cat.profit, 0);
      stats.total_quantity = categoryData.reduce((sum, cat) => sum + cat.quantity, 0);
      stats.average_profit_margin = stats.total_sales > 0 ? (stats.total_profit / stats.total_sales * 100) : 0;
      stats.top_category = categoryData[0]; // Already sorted by total_sales desc
    }

    return stats;
  }

  /**
   * Create Excel workbook with category data
   */
  async createExcelWorkbook(data, options = {}) {
    const {
      include_summary = true,
      include_details = true,
      include_product_breakdown = true,
      include_charts = false,
      filters = {}
    } = options;

    logger.info(`Creating Excel workbook for ${data.categoryData.length} categories`);

    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'DukaLink POS System';
    workbook.created = new Date();

    const stats = this.generateSummaryStats(data.categoryData, filters);

    // Summary Sheet
    if (include_summary) {
      const summarySheet = workbook.addWorksheet('Category Summary');

      // Title
      summarySheet.addRow(['Sales by Category Export']);
      summarySheet.addRow(['Generated on:', new Date().toLocaleString()]);
      summarySheet.addRow([]);

      // Key metrics
      summarySheet.addRow(['Total Categories:', stats.total_categories]);
      summarySheet.addRow(['Total Sales:', stats.total_sales.toFixed(2)]);
      summarySheet.addRow(['Total Profit:', stats.total_profit.toFixed(2)]);
      summarySheet.addRow(['Total Quantity:', stats.total_quantity]);
      summarySheet.addRow(['Average Profit Margin:', `${stats.average_profit_margin.toFixed(2)}%`]);
      if (stats.top_category) {
        summarySheet.addRow(['Top Category:', stats.top_category.category_name]);
        summarySheet.addRow(['Top Category Sales:', stats.top_category.total_sales.toFixed(2)]);
      }
      summarySheet.addRow([]);

      // Applied filters
      summarySheet.addRow(['Applied Filters:']);
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          summarySheet.addRow([`${key}:`, value]);
        }
      });

      // Style the summary sheet
      summarySheet.getCell('A1').font = { bold: true, size: 16 };
      summarySheet.getColumn('A').width = 25;
      summarySheet.getColumn('B').width = 20;
    }

    // Category Details Sheet
    if (include_details) {
      const detailsSheet = workbook.addWorksheet('Category Details');

      // Headers
      const headers = [
        'Category Name',
        'Quantity Sold',
        'Total Sales',
        'Total Profit',
        'Profit Margin %',
        '% of Total Sales',
        'Product Count',
        'Sales Count'
      ];

      detailsSheet.addRow(headers);

      // Data rows
      data.categoryData.forEach(category => {
        detailsSheet.addRow([
          category.category_name,
          category.quantity,
          category.total_sales.toFixed(2),
          category.profit.toFixed(2),
          category.profit_margin.toFixed(2),
          category.percentage.toFixed(2),
          category.product_count,
          category.sales_count
        ]);
      });

      // Style the headers
      const headerRow = detailsSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Auto-fit columns
      detailsSheet.columns.forEach(column => {
        column.width = 15;
      });
    }

    // Product Breakdown Sheet
    if (include_product_breakdown) {
      const productSheet = workbook.addWorksheet('Product Breakdown');

      productSheet.addRow(['Category', 'Products in Category']);

      data.categoryData.forEach(category => {
        productSheet.addRow([category.category_name, '']);
        category.products.forEach(product => {
          productSheet.addRow(['', product]);
        });
        productSheet.addRow(['', '']); // Empty row for separation
      });

      // Style headers
      const headerRow = productSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      productSheet.columns.forEach(column => {
        column.width = 25;
      });
    }

    return workbook;
  }
}

const salesByCategoryExportController = new SalesByCategoryExportController();

/**
 * Export all sales by category data
 */
const exportAllSalesByCategory = async (req, res, next) => {
  const timeout = setTimeout(() => {
    if (!res.headersSent) {
      res.status(408).json({
        error: 'Export timeout',
        message: 'The export is taking too long. Try filtering your data or use lightweight export.'
      });
    }
  }, 45000); // 45 seconds timeout

  try {
    const filters = req.query;
    logger.info('Starting comprehensive sales by category export with filters:', filters);

    // Get category data
    const data = await salesByCategoryExportController.getSalesByCategoryData(filters);

    if (data.categoryData.length === 0) {
      clearTimeout(timeout);
      return res.status(404).json({
        error: 'No category data found for the specified criteria',
        message: 'Try adjusting your date range or removing filters to see more data.'
      });
    }

    logger.info(`Creating Excel workbook for ${data.categoryData.length} categories`);

    // Create Excel workbook
    const workbook = await salesByCategoryExportController.createExcelWorkbook(data, {
      include_summary: true,
      include_details: true,
      include_product_breakdown: true,
      include_charts: true,
      filters
    });

    // Clear timeout since we're about to send response
    clearTimeout(timeout);

    // Set response headers
    const filename = `sales-by-category-comprehensive-${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info(`Sales by category export completed successfully: ${data.categoryData.length} categories exported as ${filename}`);
  } catch (error) {
    clearTimeout(timeout);
    logger.error('Error in exportAllSalesByCategory:', error);

    // Send error response if headers haven't been sent
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to export sales by category',
        message: error.message || 'An unexpected error occurred during export'
      });
    }
  }
};

/**
 * Export custom sales by category data
 */
const exportCustomSalesByCategory = async (req, res, next) => {
  const timeout = setTimeout(() => {
    if (!res.headersSent) {
      res.status(408).json({
        error: 'Export timeout',
        message: 'The export is taking too long. Try using lightweight format or filtering the data.'
      });
    }
  }, 30000); // 30 seconds timeout for custom exports

  try {
    const {
      include_summary = 'true',
      include_details = 'true',
      include_product_breakdown = 'true',
      include_charts = 'true',
      format_type = 'detailed',
      ...filters
    } = req.query;

    logger.info('Starting custom sales by category export with options:', {
      include_summary,
      include_details,
      include_product_breakdown,
      include_charts,
      format_type,
      filters
    });

    // Get category data
    const data = await salesByCategoryExportController.getSalesByCategoryData(filters);

    if (data.categoryData.length === 0) {
      clearTimeout(timeout);
      return res.status(404).json({
        error: 'No category data found for the specified criteria',
        message: 'Try adjusting your date range or removing filters to see more data.'
      });
    }

    // Create Excel workbook with custom options
    const workbook = await salesByCategoryExportController.createExcelWorkbook(data, {
      include_summary: include_summary === 'true',
      include_details: include_details === 'true',
      include_product_breakdown: include_product_breakdown === 'true',
      include_charts: include_charts === 'true',
      filters
    });

    // Clear timeout since we're about to send response
    clearTimeout(timeout);

    // Set response headers
    const formatSuffix = format_type === 'summary' ? 'summary' : 'custom';
    const filename = `sales-by-category-${formatSuffix}-${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info(`Custom sales by category export completed: ${data.categoryData.length} categories exported as ${filename}`);
  } catch (error) {
    clearTimeout(timeout);
    logger.error('Error in exportCustomSalesByCategory:', error);

    // Send error response if headers haven't been sent
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to export custom sales by category',
        message: error.message || 'An unexpected error occurred during export'
      });
    }
  }
};

/**
 * Export lightweight sales by category data (fastest option)
 */
const exportLightweightSalesByCategory = async (req, res, next) => {
  try {
    const filters = req.query;
    logger.info('Starting lightweight sales by category export with filters:', filters);

    // Get category data
    const data = await salesByCategoryExportController.getSalesByCategoryData(filters);

    if (data.categoryData.length === 0) {
      return res.status(404).json({
        error: 'No category data found for the specified criteria',
        message: 'Try adjusting your date range or removing filters to see more data.'
      });
    }

    logger.info(`Creating lightweight Excel workbook for ${data.categoryData.length} categories`);

    // Create simple workbook with just category data
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'DukaLink POS System';
    workbook.created = new Date();

    // Simple category sheet only
    const categorySheet = workbook.addWorksheet('Category Data');

    // Headers
    const headers = [
      'Category Name',
      'Quantity Sold',
      'Total Sales',
      'Total Profit',
      'Profit Margin %',
      '% of Total Sales'
    ];

    categorySheet.addRow(headers);

    // Data rows
    data.categoryData.forEach(category => {
      categorySheet.addRow([
        category.category_name,
        category.quantity,
        category.total_sales.toFixed(2),
        category.profit.toFixed(2),
        category.profit_margin.toFixed(2),
        category.percentage.toFixed(2)
      ]);
    });

    // Style the headers
    const headerRow = categorySheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Auto-fit columns
    categorySheet.columns.forEach(column => {
      column.width = 15;
    });

    // Set response headers
    const filename = `sales-by-category-${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info(`Lightweight sales by category export completed: ${data.categoryData.length} categories exported as ${filename}`);
  } catch (error) {
    logger.error('Error in exportLightweightSalesByCategory:', error);

    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to export category data',
        message: error.message || 'An unexpected error occurred during export'
      });
    }
  }
};

module.exports = {
  salesByCategoryExportController,
  exportAllSalesByCategory,
  exportCustomSalesByCategory,
  exportLightweightSalesByCategory
};
