const express = require('express');
const router = express.Router();
const stockMovementItemsController = require('../controllers/stock-movement-items.controller');
const { exportAllStockMovementItems, exportCustomStockMovementItems } = require('../controllers/report/stock-movement-items-export.controller');
const { authenticate, rbac } = require('../middleware/auth.middleware');

/**
 * @swagger
 * components:
 *   schemas:
 *     StockMovementItem:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The stock movement item ID
 *         stock_movement_id:
 *           type: integer
 *           description: The stock movement ID
 *         reference_number:
 *           type: string
 *           description: The stock movement reference number
 *         product_id:
 *           type: integer
 *           description: The product ID
 *         product_name:
 *           type: string
 *           description: The product name
 *         product_sku:
 *           type: string
 *           description: The product SKU
 *         from_branch_name:
 *           type: string
 *           description: The source branch name
 *         to_branch_name:
 *           type: string
 *           description: The destination branch name
 *         requested_quantity:
 *           type: integer
 *           description: The requested quantity
 *         dispatched_quantity:
 *           type: integer
 *           description: The dispatched quantity
 *         received_quantity:
 *           type: integer
 *           description: The received quantity
 *         status:
 *           type: string
 *           description: The stock movement status
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: Creation timestamp
 *         updated_at:
 *           type: string
 *           format: date-time
 *           description: Last update timestamp
 */

/**
 * @swagger
 * /stock-movement-items:
 *   get:
 *     summary: Get all stock movement items with search and pagination
 *     tags: [Stock Movement Items]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in product names, SKUs, reference numbers, and notes
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status
 *       - in: query
 *         name: from_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by source branch ID
 *       - in: query
 *         name: to_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by destination branch ID
 *       - in: query
 *         name: product_id
 *         schema:
 *           type: integer
 *         description: Filter by product ID
 *       - in: query
 *         name: reference_number
 *         schema:
 *           type: string
 *         description: Filter by reference number
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date filter
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: End date filter
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Items per page
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *           default: created_at
 *         description: Sort field
 *       - in: query
 *         name: sort_direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort direction
 *     responses:
 *       200:
 *         description: List of stock movement items with pagination
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/StockMovementItem'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *                     totalPages:
 *                       type: integer
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authenticate, rbac.checkPermission('stock_movements', 'read'), stockMovementItemsController.getAllStockMovementItems);

/**
 * @swagger
 * /stock-movement-items/{id}:
 *   get:
 *     summary: Get stock movement item by ID
 *     tags: [Stock Movement Items]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Stock movement item ID
 *     responses:
 *       200:
 *         description: Stock movement item details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/StockMovementItem'
 *       404:
 *         description: Stock movement item not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/:id', authenticate, rbac.checkPermission('stock_movements', 'read'), stockMovementItemsController.getStockMovementItemById);

/**
 * @swagger
 * /stock-movement-items/export/all:
 *   get:
 *     summary: Export all stock movement items to Excel
 *     tags: [Stock Movement Items]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in product names, SKUs, reference numbers, and notes
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status
 *       - in: query
 *         name: from_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by source branch ID
 *       - in: query
 *         name: to_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by destination branch ID
 *       - in: query
 *         name: product_id
 *         schema:
 *           type: integer
 *         description: Filter by product ID
 *       - in: query
 *         name: reference_number
 *         schema:
 *           type: string
 *         description: Filter by reference number
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date filter
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: End date filter
 *     responses:
 *       200:
 *         description: Excel file with comprehensive stock movement items data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/export/all', authenticate, rbac.checkPermission('stock_reports', 'read'), exportAllStockMovementItems);

/**
 * @swagger
 * /stock-movement-items/export/custom:
 *   get:
 *     summary: Export custom stock movement items to Excel
 *     tags: [Stock Movement Items]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in product names, SKUs, reference numbers, and notes
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status
 *       - in: query
 *         name: from_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by source branch ID
 *       - in: query
 *         name: to_branch_id
 *         schema:
 *           type: integer
 *         description: Filter by destination branch ID
 *       - in: query
 *         name: product_id
 *         schema:
 *           type: integer
 *         description: Filter by product ID
 *       - in: query
 *         name: reference_number
 *         schema:
 *           type: string
 *         description: Filter by reference number
 *       - in: query
 *         name: date_from
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date filter
 *       - in: query
 *         name: date_to
 *         schema:
 *           type: string
 *           format: date
 *         description: End date filter
 *       - in: query
 *         name: include_summary
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include summary sheet
 *       - in: query
 *         name: include_details
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include details sheet
 *       - in: query
 *         name: include_status_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include status breakdown sheet
 *       - in: query
 *         name: include_branch_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include branch breakdown sheet
 *       - in: query
 *         name: include_product_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include product breakdown sheet
 *       - in: query
 *         name: format_type
 *         schema:
 *           type: string
 *           enum: [detailed, summary]
 *           default: detailed
 *         description: Format type for export
 *     responses:
 *       200:
 *         description: Excel file with custom stock movement items data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/export/custom', authenticate, rbac.checkPermission('stock_reports', 'read'), exportCustomStockMovementItems);

module.exports = router;
