export interface InventoryTransaction {
  id: number;
  transaction_type:
    | "purchase"
    | "sale"
    | "adjustment"
    | "transfer_in"
    | "transfer_out"
    | "return";
  reference_number: string;
  product_id: number;
  branch_id: number;
  quantity: number;
  unit_price?: number;
  total_price?: number;
  notes?: string;
  created_by: number;
  created_at: string;
  updated_at: string;
  product?: {
    id: number;
    name: string;
    sku: string;
    image_url?: string;
  };
  branch?: {
    id: number;
    name: string;
    code: string;
  };
  user?: {
    id: number;
    name: string;
  };
}

export interface StockTransfer {
  id: number;
  tenant_id?: number;
  from_branch_id?: number | null;
  to_branch_id?: number | null;
  status:
    | "pending"
    | "in_transit"
    | "completed"
    | "cancelled"
    | "received"
    | "partially_received"
    | "approved"
    | "rejected";
  initiated_at?: string;
  completed_at?: string | null;
  notes?: string | null;
  created_by?: number | null;
  last_updated_by?: number | null;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;

  // API returns these with capital letters
  FromBranch?: {
    id: number;
    name: string;
    location?: string;
  } | null;
  ToBranch?: {
    id: number;
    name: string;
    location?: string;
  } | null;
  Tenant?: {
    id: number;
    name: string;
  };
  CreatedBy?: {
    id: number;
    name: string;
    email?: string;
  };
  LastUpdatedBy?: {
    id: number;
    name: string;
    email?: string;
  };
  StockMovementItems?: StockTransferItem[];

  // For backward compatibility
  reference_number?: string;
  source_branch_id?: number;
  destination_branch_id?: number;
  cancelled_at?: string;
  source_branch?: {
    id: number;
    name: string;
    code?: string;
  };
  destination_branch?: {
    id: number;
    name: string;
    code?: string;
  };
  user?: {
    id: number;
    name: string;
  };
  items?: StockTransferItem[];
}

export interface StockTransferItem {
  id: number;
  stock_movement_id?: number;
  product_id: number;
  quantity_sent?: number;
  quantity_received?: number | null;
  notes?: string;
  created_by?: number;
  last_updated_by?: number;
  created_at?: string;
  updated_at?: string;
  Product?: {
    id: number;
    name: string;
    sku: string;
    has_serial?: boolean;
    image_url?: string;
  };

  // For backward compatibility
  stock_transfer_id?: number;
  quantity?: number;
  product?: {
    id: number;
    name: string;
    sku?: string | null;
    image_url?: string;
  };
}

export interface InventoryAdjustment {
  id: number;
  reference_number: string;
  branch_id: number;
  adjustment_type: "increase" | "decrease";
  reason: string;
  notes?: string;
  status: "draft" | "pending" | "approved" | "rejected";
  created_by: number;
  approved_by?: number;
  rejected_by?: number;
  created_at: string;
  updated_at: string;
  approved_at?: string;
  rejected_at?: string;
  branch?: {
    id: number;
    name: string;
    code: string;
  };
  user?: {
    id: number;
    name: string;
  };
  approver?: {
    id: number;
    name: string;
  };
  rejecter?: {
    id: number;
    name: string;
  };
  items: InventoryAdjustmentItem[];
}

export interface InventoryAdjustmentItem {
  id: number;
  inventory_adjustment_id: number;
  product_id: number;
  quantity: number;
  reason: string;
  notes?: string;
  product?: {
    id: number;
    name: string;
    sku: string;
    image_url?: string;
  };
}

export interface BranchInventory {
  id: number;
  branch_id: number;
  product_id: number;
  quantity: number;
  quantity_reserved?: number;

  // Pricing fields
  selling_price: string;
  buying_price: string;
  default_selling_price?: string;
  default_buying_price?: string;
  default_wholesale_price?: string;

  // VAT-related fields
  buying_price_including_vat?: string;
  buying_price_excluding_vat?: string;
  buying_vat_amount?: string;
  buying_vat_rate?: string;

  // Order/lot tracking
  batch_number?: string;
  expiry_date?: string;
  manufacturing_date?: string;

  // Inventory management
  reorder_level?: number;
  reorder_quantity?: number;
  valuation_method?: 'FIFO' | 'LIFO' | 'WEIGHTED_AVERAGE';

  // Timestamps
  created_at: string;
  updated_at: string;
  deleted_at: string | null;

  // Relationships
  Branch?: {
    id: number;
    name: string;
    location: string;
  };
  Product?: {
    id: number;
    name: string;
    sku?: string | null;
    barcode?: string | null;
    has_serial?: boolean;
    image_url?: string;
  };

  // For backward compatibility
  last_updated?: string;
  branch?: {
    id: number;
    name: string;
    code: string;
  };
  product?: {
    id: number;
    name: string;
    sku?: string | null;
    barcode?: string | null;
    price?: number;
    image_url?: string;
    category_id?: number;
    category?: {
      id: number;
      name: string;
    };
  };
}

export interface HQStockItem {
  id: number;
  tenant_id: number;
  product_id: number;
  quantity: number;
  buying_price: string;
  selling_price: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  Tenant?: {
    id: number;
    name: string;
  };
  Product?: {
    id: number;
    name: string;
    sku?: string | null;
    barcode?: string | null;
    has_serial?: boolean;
    image_url?: string;
    category_id?: number;
    Category?: {
      id: number;
      name: string;
    };
  };
}

export interface CreateHQStockItemRequest {
  product_id: number;
  quantity: number;
  buying_price: number | string;
  selling_price: number | string;
}

export interface UpdateHQStockItemRequest {
  quantity?: number;
  buying_price?: number | string;
  selling_price?: number | string;
}

export interface CreateInventoryTransactionRequest {
  transaction_type:
    | "purchase"
    | "sale"
    | "adjustment"
    | "transfer_in"
    | "transfer_out"
    | "return";
  reference_number?: string;
  product_id: number;
  branch_id: number;
  quantity: number;
  unit_price?: number;
  notes?: string;
}

export interface CreateStockTransferRequest {
  source_branch_id: number;
  destination_branch_id: number;
  notes?: string;
  items: {
    product_id: number;
    quantity: number;
    notes?: string;
  }[];
}

export interface UpdateStockTransferStatusRequest {
  status: "in_transit" | "completed" | "cancelled";
  notes?: string;
}

export interface StockAdjustmentType {
  id: number;
  name: string;
  description: string;
  affects_inventory: boolean;
  created_at: string;
  updated_at: string;
}

export interface StockItem {
  id: number;
  branch_id: number;
  product_id: number;
  quantity: number;
  Product?: {
    id: number;
    name: string;
    sku: string;
    barcode?: string;
  };
}

export interface CreateInventoryAdjustmentRequest {
  branch_id: number;
  adjustment_type_id: number;
  notes?: string;
  items: {
    stock_item_id: number;
    quantity: number;
    reason: string;
    notes?: string;
  }[];
}

export interface UpdateInventoryAdjustmentStatusRequest {
  status: "pending" | "approved" | "rejected";
  notes?: string;
  processed_by?: number;
}

export interface InventoryReportFilters {
  branch_id?: number;
  category_id?: number;
  product_id?: number;
  start_date?: string;
  end_date?: string;
  transaction_type?: string;
  status?: string;
}

export interface InventoryReportSummary {
  // Fields for both report types
  total_products?: number;
  total_value?: number;
  low_stock_count?: number;
  out_of_stock_count?: number;

  // Fields for inventory value report
  by_category?: {
    category_id: number;
    category_name: string;
    product_count: number;
    total_quantity: number;
    total_value: number;
  }[];
  by_branch?: {
    branch_id: number;
    branch_name: string;
    product_count: number;
    total_quantity: number;
    total_value: number;
  }[];

  // Fields for inventory value top products
  value_top_products?: {
    id: number;
    name: string;
    sku: string;
    quantity: number;
    value: number;
  }[];

  // Fields for stock movements report
  total_movements?: number;
  status_breakdown?: {
    pending: number;
    in_transit: number;
    received: number;
    cancelled: number;
  };
  total_quantity_sent?: number;
  total_quantity_received?: number;
  discrepancy?: number;
  top_branches?: {
    to_branch_id: number;
    movement_count: number;
    ToBranch: {
      id: number;
      name: string;
      location: string;
    };
  }[];

  // Fields for stock movements top products
  top_products?: {
    product_id: number;
    total_quantity: string;
    Product: {
      id: number;
      name: string;
      sku: string;
    };
  }[];
}
