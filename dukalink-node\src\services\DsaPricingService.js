const logger = require('../utils/logger');

/**
 * Centralized DSA Pricing Service
 * Provides consistent pricing logic across all DSA operations
 */
class DsaPricingService {
  /**
   * Get the correct price for a DSA item with clear priority order
   * @param {Object} assignment - DSA stock assignment object
   * @param {Object} product - Product object (optional if included in assignment)
   * @param {Object} stockItem - Stock item object (optional)
   * @returns {number} The wholesale price to use
   */
  static getItemPrice(assignment, product = null, stockItem = null) {
    // Use product from assignment if not provided separately
    const productData = product || assignment.Product;
    
    // Priority order for DSA pricing (always wholesale for DSA operations)
    let price = 0;
    let priceSource = '';
    
    // 1. First priority: Assignment-specific wholesale price
    if (assignment.default_wholesale_price && parseFloat(assignment.default_wholesale_price) > 0) {
      price = parseFloat(assignment.default_wholesale_price);
      priceSource = 'assignment.default_wholesale_price';
    }
    // 2. Second priority: Product's default wholesale price
    else if (productData?.default_wholesale_price && parseFloat(productData.default_wholesale_price) > 0) {
      price = parseFloat(productData.default_wholesale_price);
      priceSource = 'product.default_wholesale_price';
    }
    // 3. Third priority: Stock item wholesale price
    else if (stockItem?.default_wholesale_price && parseFloat(stockItem.default_wholesale_price) > 0) {
      price = parseFloat(stockItem.default_wholesale_price);
      priceSource = 'stockItem.default_wholesale_price';
    }
    // 4. Fallback: Product suggested selling price (treated as wholesale for DSA)
    else if (productData?.suggested_selling_price && parseFloat(productData.suggested_selling_price) > 0) {
      price = parseFloat(productData.suggested_selling_price);
      priceSource = 'product.suggested_selling_price';
    }
    // 5. Last resort: Stock item selling price (treated as wholesale for DSA)
    else if (stockItem?.default_selling_price && parseFloat(stockItem.default_selling_price) > 0) {
      price = parseFloat(stockItem.default_selling_price);
      priceSource = 'stockItem.default_selling_price';
    }
    
    logger.debug(`DSA Pricing: Product ${productData?.id || 'unknown'} - Price: ${price} from ${priceSource}`);
    
    return price;
  }

  /**
   * Calculate the total value for a DSA invoice/assignment
   * @param {Array} assignments - Array of DSA stock assignments
   * @returns {Object} Calculation breakdown
   */
  static calculateInvoiceTotal(assignments) {
    let totalAssignedValue = 0;
    let totalRemainingValue = 0;
    let totalReturnedValue = 0;
    let itemBreakdown = [];

    assignments.forEach(assignment => {
      const price = this.getItemPrice(assignment);
      const quantityAssigned = assignment.quantity_assigned || 0;
      const quantityReturned = assignment.quantity_returned || 0;
      const quantityRemaining = Math.max(0, quantityAssigned - quantityReturned);
      
      const assignedValue = price * quantityAssigned;
      const returnedValue = price * quantityReturned;
      const remainingValue = price * quantityRemaining;
      
      totalAssignedValue += assignedValue;
      totalReturnedValue += returnedValue;
      totalRemainingValue += remainingValue;
      
      itemBreakdown.push({
        assignment_id: assignment.id,
        product_id: assignment.product_id,
        product_name: assignment.Product?.name || 'Unknown Product',
        unit_price: price,
        quantity_assigned: quantityAssigned,
        quantity_returned: quantityReturned,
        quantity_remaining: quantityRemaining,
        assigned_value: assignedValue,
        returned_value: returnedValue,
        remaining_value: remainingValue
      });
    });

    return {
      total_assigned_value: totalAssignedValue,
      total_returned_value: totalReturnedValue,
      total_remaining_value: totalRemainingValue,
      item_breakdown: itemBreakdown
    };
  }

  /**
   * Calculate payment status based on balance and payment amount
   * @param {number} balance - Current balance
   * @param {number} totalPaid - Total amount paid
   * @param {boolean} allItemsReturned - Whether all items have been returned
   * @returns {string} Payment status
   */
  static determinePaymentStatus(balance, totalPaid, allItemsReturned = false) {
    // If all items are returned, consider it fully paid regardless of balance
    if (allItemsReturned) {
      return 'FULLY_PAID';
    }
    
    // If balance is zero or negative, it's fully paid
    if (balance <= 0) {
      return 'FULLY_PAID';
    }
    
    // If some payment has been made but balance remains, it's partially paid
    if (totalPaid > 0) {
      return 'PARTIALLY_PAID';
    }
    
    // No payment made
    return 'UNPAID';
  }

  /**
   * Validate a payment amount against an invoice
   * @param {number} paymentAmount - Amount being paid
   * @param {number} currentBalance - Current outstanding balance
   * @param {Object} options - Validation options
   * @returns {Object} Validation result
   */
  static validatePaymentAmount(paymentAmount, currentBalance, options = {}) {
    const {
      allowOverpayment = true,
      overpaymentTolerance = 0.1, // 10% tolerance
      allowZeroPayment = false
    } = options;

    const errors = [];
    const warnings = [];

    // Check for negative amounts
    if (paymentAmount < 0) {
      errors.push('Payment amount cannot be negative');
    }

    // Check for zero payments
    if (paymentAmount === 0 && !allowZeroPayment) {
      errors.push('Payment amount must be greater than zero');
    }

    // Check for overpayments
    if (paymentAmount > currentBalance) {
      const overpaymentPercentage = (paymentAmount - currentBalance) / currentBalance;
      
      if (!allowOverpayment) {
        errors.push(`Payment amount (${paymentAmount}) cannot exceed balance (${currentBalance})`);
      } else if (overpaymentPercentage > overpaymentTolerance) {
        warnings.push(`Payment amount significantly exceeds balance. Overpayment: ${paymentAmount - currentBalance}`);
      } else {
        warnings.push(`Payment exceeds balance by ${paymentAmount - currentBalance}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      overpayment: Math.max(0, paymentAmount - currentBalance)
    };
  }

  /**
   * Calculate the impact of a return on pricing
   * @param {Object} assignment - DSA stock assignment
   * @param {number} returnQuantity - Quantity being returned
   * @returns {Object} Return impact calculation
   */
  static calculateReturnImpact(assignment, returnQuantity) {
    const price = this.getItemPrice(assignment);
    const returnValue = price * returnQuantity;
    
    const currentQuantityReturned = assignment.quantity_returned || 0;
    const newQuantityReturned = currentQuantityReturned + returnQuantity;
    const remainingQuantity = Math.max(0, assignment.quantity_assigned - newQuantityReturned);
    const newRemainingValue = price * remainingQuantity;
    
    return {
      return_value: returnValue,
      new_quantity_returned: newQuantityReturned,
      remaining_quantity: remainingQuantity,
      new_remaining_value: newRemainingValue,
      unit_price: price
    };
  }

  /**
   * Get pricing summary for debugging/reporting
   * @param {Object} assignment - DSA stock assignment
   * @returns {Object} Pricing summary
   */
  static getPricingSummary(assignment) {
    const productData = assignment.Product;
    
    return {
      assignment_id: assignment.id,
      product_id: assignment.product_id,
      product_name: productData?.name || 'Unknown Product',
      pricing_sources: {
        assignment_wholesale_price: assignment.default_wholesale_price || null,
        product_wholesale_price: productData?.default_wholesale_price || null,
        product_suggested_price: productData?.suggested_selling_price || null,
        stock_item_selling_price: assignment.StockItem?.default_selling_price || null
      },
      selected_price: this.getItemPrice(assignment),
      quantities: {
        assigned: assignment.quantity_assigned || 0,
        returned: assignment.quantity_returned || 0,
        remaining: Math.max(0, (assignment.quantity_assigned || 0) - (assignment.quantity_returned || 0))
      }
    };
  }
}

module.exports = DsaPricingService;
