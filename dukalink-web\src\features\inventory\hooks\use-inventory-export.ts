import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import inventoryExportService, {
  InventoryExportParams,
  CustomInventoryExportParams,
} from "../api/inventory-export-service";

/**
 * Enhanced hook for comprehensive inventory export
 * Exports all data with multiple sheets
 */
export const useInventoryExportAll = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: InventoryExportParams) => {
      // Validate parameters
      const validation = inventoryExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await inventoryExportService.exportAllInventory(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = inventoryExportService.generateFilename(params, 'comprehensive');
        inventoryExportService.downloadBlob(blob, filename);
        
        toast.success("Inventory exported successfully!", {
          description: `Comprehensive report with all data sheets downloaded as ${filename}`,
          duration: 5000,
        });

        // Invalidate related queries to refresh any cached data
        queryClient.invalidateQueries({ queryKey: ["inventory"] });
        queryClient.invalidateQueries({ queryKey: ["branch-inventory"] });
        queryClient.invalidateQueries({ queryKey: ["hq-inventory"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Export error:', error);
      
      let errorMessage = "Failed to export inventory";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export inventory reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try filtering the data or use summary export.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for custom inventory export
 * Allows format and column selection
 */
export const useInventoryExportCustom = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: CustomInventoryExportParams) => {
      // Validate parameters
      const validation = inventoryExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await inventoryExportService.exportCustomInventory(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const formatType = params.format_type || 'detailed';
        const filename = inventoryExportService.generateFilename(params, formatType);
        inventoryExportService.downloadBlob(blob, filename);
        
        toast.success("Custom inventory export completed!", {
          description: `${formatType.charAt(0).toUpperCase() + formatType.slice(1)} report downloaded as ${filename}`,
          duration: 5000,
        });

        queryClient.invalidateQueries({ queryKey: ["inventory"] });
        queryClient.invalidateQueries({ queryKey: ["branch-inventory"] });
        queryClient.invalidateQueries({ queryKey: ["hq-inventory"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Custom export error:', error);
      
      let errorMessage = "Failed to export custom inventory";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export inventory reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try using summary format or filtering the data.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Custom Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for quick summary export
 * Lightweight export with essential data only
 */
export const useInventoryExportSummary = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: InventoryExportParams) => {
      const validation = inventoryExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await inventoryExportService.exportSummaryInventory(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = inventoryExportService.generateFilename(params, 'summary');
        inventoryExportService.downloadBlob(blob, filename);
        
        toast.success("Summary export completed!", {
          description: `Quick summary report downloaded as ${filename}`,
          duration: 4000,
        });

        queryClient.invalidateQueries({ queryKey: ["inventory"] });
        queryClient.invalidateQueries({ queryKey: ["branch-inventory"] });
        queryClient.invalidateQueries({ queryKey: ["hq-inventory"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Summary export error:', error);
      
      let errorMessage = "Failed to export summary inventory";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export inventory reports";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Summary Export Failed", {
        description: errorMessage,
        duration: 6000,
      });
    },
  });
};

/**
 * Hook to get export recommendations based on data size
 */
export const useInventoryExportRecommendation = (estimatedRecords: number) => {
  return inventoryExportService.getExportRecommendation(estimatedRecords);
};

/**
 * Hook to get export format options for UI
 */
export const useInventoryExportFormatOptions = () => {
  return inventoryExportService.getExportFormatOptions();
};

/**
 * Hook to get column options for custom export
 */
export const useInventoryExportColumnOptions = () => {
  return inventoryExportService.getColumnOptions();
};

/**
 * Combined hook that provides all export functionality
 * Convenient single hook for components that need multiple export options
 */
export const useInventoryExportSuite = () => {
  const exportAll = useInventoryExportAll();
  const exportCustom = useInventoryExportCustom();
  const exportSummary = useInventoryExportSummary();

  const isAnyExporting = exportAll.isPending || exportCustom.isPending || exportSummary.isPending;

  return {
    // Individual export methods
    exportAll: exportAll.mutateAsync,
    exportCustom: exportCustom.mutateAsync,
    exportSummary: exportSummary.mutateAsync,
    
    // Loading states
    isExportingAll: exportAll.isPending,
    isExportingCustom: exportCustom.isPending,
    isExportingSummary: exportSummary.isPending,
    isAnyExporting,
    
    // Error states
    exportAllError: exportAll.error,
    exportCustomError: exportCustom.error,
    exportSummaryError: exportSummary.error,
    
    // Utility functions
    getRecommendation: inventoryExportService.getExportRecommendation,
    getFormatOptions: inventoryExportService.getExportFormatOptions,
    getColumnOptions: inventoryExportService.getColumnOptions,
    validateParams: inventoryExportService.validateExportParams,
    generateFilename: inventoryExportService.generateFilename,
    
    // Reset functions
    resetAll: () => {
      exportAll.reset();
      exportCustom.reset();
      exportSummary.reset();
    },
  };
};
