"use client";

import axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from "axios";
import { QueryClient } from "@tanstack/react-query";
import { getCookie, setCookie } from "./cookies";

// Define a redirect callback type
type RedirectCallback = (path: string) => void;

// Define an auth error callback type
type AuthErrorCallback = (message: string) => void;

// Default redirect implementation - will be overridden by client components
let redirectCallback: RedirectCallback = (path: string) => {
  console.warn("Redirect callback not set, attempted to redirect to:", path);
  // Use window.location as a fallback if we're in the browser
  if (typeof window !== "undefined") {
    window.location.href = path;
  }
};

// Default auth error callback - will be overridden by client components
let authErrorCallback: AuthErrorCallback = (message: string) => {
  console.warn("Auth error callback not set, auth error:", message);
};

// Reference to the query client for cache invalidation
let queryClientRef: QueryClient | null = null;

// Use the environment variable for API URL
const API_URL = process.env.NEXT_PUBLIC_API_URL;

class ApiClient {
  // Method to set a custom redirect callback
  public setRedirectCallback(callback: RedirectCallback) {
    redirectCallback = callback;
  }

  // Method to set the query client reference
  public setQueryClient(queryClient: QueryClient) {
    queryClientRef = queryClient;
  }

  // Method to set a custom auth error callback
  public setAuthErrorCallback(callback: AuthErrorCallback) {
    authErrorCallback = callback;
  }

  // Method to handle authentication errors
  public handleAuthError(errorMessage?: string) {
    console.log(
      "Handling auth error - clearing tokens and showing auth error dialog"
    );

    // Get a user-friendly error message
    const message =
      errorMessage || "Your session has expired. Please log in again.";

    // Call the auth error callback with the message
    authErrorCallback(message);

    // We don't clear tokens or redirect here anymore
    // That will be handled by the auth error context when the user confirms the dialog
  }
  private client: AxiosInstance;
  private isRefreshing = false;
  private refreshSubscribers: ((token: string) => void)[] = [];

  constructor() {
    this.client = axios.create({
      baseURL: API_URL,
      headers: {
        "Content-Type": "application/json",
      },
      timeout: 60000, // 60 seconds - increased from 30 seconds to handle larger file uploads
    });

    this.setupInterceptors();
  }

  // Method to set auth token for requests
  public setAuthToken(token: string | null) {
    if (token) {
      this.client.defaults.headers.common["Authorization"] = `Bearer ${token}`;

      // Also store in localStorage as a fallback
      try {
        localStorage.setItem("token", token);
      } catch (e) {
        console.warn("Failed to store token in localStorage:", e);
      }
    } else {
      delete this.client.defaults.headers.common["Authorization"];

      // Also remove from localStorage
      try {
        localStorage.removeItem("token");
      } catch (e) {
        console.warn("Failed to remove token from localStorage:", e);
      }
    }
  }

  private setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      async (config) => {
        // Debug log to check if Authorization header is set
        const hasAuthHeader = config.headers?.Authorization ? true : false;
        console.log(
          `API Request to ${config.url}: Auth header ${
            hasAuthHeader ? "present" : "missing"
          }`
        );

        // If no auth header is set but we have a token in localStorage, set it
        if (!hasAuthHeader && typeof window !== "undefined") {
          const token =
            localStorage.getItem("token") ||
            localStorage.getItem("accessToken");
          if (token && config.headers) {
            console.log(
              `Setting missing auth header for ${config.url} from localStorage`
            );
            config.headers.Authorization = `Bearer ${token}`;
          }
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      async (error: AxiosError) => {
        const originalRequest = error.config as AxiosRequestConfig & {
          _retry?: boolean;
        };

        // If unauthorized error and not retrying
        if (error.response?.status === 401 && !originalRequest._retry) {
          console.log("401 Unauthorized error detected:", error.config?.url);

          // Check if the error message indicates an invalid or expired token
          const errorData = error.response?.data as any;
          const isTokenError =
            errorData?.message === "Invalid or expired token" ||
            errorData?.error === "Unauthorized" ||
            errorData?.error === "Error";

          // If already refreshing, queue this request
          if (this.isRefreshing) {
            console.log("Token refresh already in progress, queuing request");
            return new Promise((resolve) => {
              this.refreshSubscribers.push((token: string) => {
                if (originalRequest.headers) {
                  originalRequest.headers.Authorization = `Bearer ${token}`;
                }
                resolve(this.client(originalRequest));
              });
            });
          }

          // Mark as retrying and refreshing
          originalRequest._retry = true;
          this.isRefreshing = true;

          // Try to get refresh token from cookies or localStorage
          const refreshToken =
            typeof document !== "undefined"
              ? getCookie("refreshToken") ||
                localStorage.getItem("refreshToken")
              : null;

          if (refreshToken) {
            console.log(
              "Refresh token found, attempting to refresh access token"
            );

            try {
              // Call the refresh token endpoint
              const response = await this.client.post("/auth/refresh-token", {
                refreshToken,
              });

              const { accessToken, refreshToken: newRefreshToken } =
                response.data;

              if (accessToken) {
                console.log("Token refresh successful");

                // Update tokens in cookies and localStorage
                if (typeof document !== "undefined") {
                  setCookie("accessToken", accessToken);
                  if (newRefreshToken) {
                    setCookie("refreshToken", newRefreshToken);
                  }
                }

                // Update auth header
                this.setAuthToken(accessToken);

                // Execute all queued requests with new token
                this.refreshSubscribers.forEach((callback) =>
                  callback(accessToken)
                );
                this.refreshSubscribers = [];

                // Reset refreshing flag
                this.isRefreshing = false;

                // Retry the original request
                if (originalRequest.headers) {
                  originalRequest.headers.Authorization = `Bearer ${accessToken}`;
                }

                return this.client(originalRequest);
              }
            } catch (refreshError) {
              console.error("Token refresh failed:", refreshError);
            }
          }

          // If we get here, token refresh failed or no refresh token available
          this.isRefreshing = false;
          this.refreshSubscribers = [];

          // If it's a token error or refresh failed, handle auth error
          console.log(
            "Token refresh failed or not possible, handling auth error"
          );
          const errorMessage =
            errorData?.message ||
            "Your session has expired. Please log in again.";
          this.handleAuthError(errorMessage);
          return Promise.reject(error);
        }

        // Handle other errors
        return Promise.reject(this.handleError(error));
      }
    );
  }

  private handleError(error: AxiosError) {
    // Customize error handling
    if (error.response) {
      // Server responded with error status
      const data = error.response.data as any;

      if (data.message) {
        error.message = data.message;
      }

      // Add custom error properties
      (error as any).isApiError = true;
      (error as any).statusCode = error.response.status;
    } else if (error.request) {
      // Request made but no response received
      error.message = "Network error. Please check your connection.";
      (error as any).isNetworkError = true;
    }

    return error;
  }

  // Public methods
  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      console.log(`Making GET request to ${url}`);
      const response = await this.client.get<T>(url, config);
      return response.data;
    } catch (error: any) {
      // Suppress specific tenant settings errors to avoid console noise
      if (
        url.includes("/tenants/") &&
        url.includes("/settings") &&
        error.response?.status === 404
      ) {
        console.log(`Resource not found for ${url} - suppressing error`);
      } else {
        console.error(`Error in GET request to ${url}:`, error.message);
      }

      // Check if it's a connection error
      if (
        error.code === "ECONNREFUSED" ||
        error.code === "ECONNABORTED" ||
        error.message.includes("Network Error")
      ) {
        console.warn("Connection error detected, trying direct backend API");

        // Try direct backend API as fallback
        try {
          // Get the API URL from environment variable
          const baseUrl = process.env.NEXT_PUBLIC_API_URL;

          // Get token from localStorage
          const token =
            typeof window !== "undefined"
              ? localStorage.getItem("token") ||
                localStorage.getItem("accessToken")
              : null;

          if (!token) {
            throw new Error("No authentication token available");
          }

          // Make direct request to backend
          const directResponse = await fetch(`${baseUrl}${url}`, {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
            ...config,
          });

          if (!directResponse.ok) {
            const errorData = await directResponse.json();
            throw new Error(
              errorData.message || `Backend API error: ${directResponse.status}`
            );
          }

          return await directResponse.json();
        } catch (fallbackError: any) {
          console.error("Fallback request also failed:", fallbackError.message);
          throw fallbackError;
        }
      }

      throw error;
    }
  }

  public async post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    // Special handling for FormData (file uploads)
    if (data instanceof FormData) {
      console.log(`Posting FormData to ${url}`);

      // For FormData, explicitly set the Content-Type header to multipart/form-data
      // but let Axios handle the boundary
      const formDataConfig = {
        ...config,
        headers: {
          ...config?.headers,
          "Content-Type": "multipart/form-data",
        },
        // Increase timeout for file uploads
        timeout: config?.timeout || 180000, // 3 minutes default for file uploads
        // Add onUploadProgress handler if not provided
        onUploadProgress:
          config?.onUploadProgress ||
          ((progressEvent) => {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / (progressEvent.total || 1)
            );
            console.log(`Upload progress: ${percentCompleted}%`);
          }),
      };

      try {
        // Log the FormData contents for debugging
        console.log("FormData contents:");
        for (const pair of data.entries()) {
          if (pair[1] instanceof File) {
            const file = pair[1] as File;
            console.log(
              `${pair[0]}: File(${file.name}, ${file.size} bytes, ${file.type})`
            );
          } else {
            console.log(`${pair[0]}: ${pair[1]}`);
          }
        }

        console.log(
          "Sending file upload request with config:",
          JSON.stringify({
            url,
            method: "POST",
            timeout: formDataConfig.timeout,
            headers: { ...formDataConfig.headers },
          })
        );

        // Use Axios for file uploads with proper CORS settings
        console.log("Using Axios for file upload with FormData");

        // Create a special config for file uploads
        const uploadConfig = {
          ...formDataConfig,
          // Don't set Content-Type header, let Axios set it with the boundary
          headers: {
            ...formDataConfig.headers,
            "Content-Type": undefined, // This lets the browser set the correct Content-Type with boundary
          },
          // Don't use withCredentials for cross-origin requests if you're using '*' in CORS
          withCredentials: false,
        };

        console.log(
          "Upload config:",
          JSON.stringify({
            url,
            method: "POST",
            timeout: uploadConfig.timeout,
            withCredentials: uploadConfig.withCredentials,
            headers: { ...uploadConfig.headers },
          })
        );

        const response = await this.client.post<T>(url, data, uploadConfig);
        return response.data;
      } catch (error: any) {
        console.error(`Error posting FormData to ${url}:`, error.message);

        // Add more detailed error logging
        if (error.code === "ECONNABORTED") {
          console.error(
            "Request timed out. Consider increasing the timeout value."
          );
        } else if (error.response) {
          console.error(
            "Server responded with error:",
            error.response.status,
            error.response.data
          );
        } else if (error.request) {
          console.error(
            "No response received from server. Network issue or CORS problem."
          );
        }

        throw error;
      }
    } else {
      // Regular post request
      try {
        const response = await this.client.post<T>(url, data, config);
        return response.data;
      } catch (error: any) {
        // Check for stock limit exceeded error
        if (
          error.message &&
          error.message.includes(
            "Assignment would exceed DSA user's stock limit"
          )
        ) {
          // Extract the limit information from the error message
          const match = error.message.match(
            /stock limit of (\d+) items\. Current total: (\d+), Requested: (\d+)/
          );
          if (match) {
            const [, limit, current, requested] = match;
            throw new Error(
              `Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`
            );
          } else {
            throw new Error(
              "Cannot assign more stock: The DSA user has reached their stock limit."
            );
          }
        }

        // Check for date validation errors
        if (
          error.message &&
          error.message.includes("Incorrect date value") &&
          error.message.includes("expiry_date")
        ) {
          throw new Error(
            "Invalid expiry date provided. Please enter a valid date or leave the field empty."
          );
        }

        // Check for branch ID validation errors
        if (
          error.response?.data?.message &&
          (error.response.data.message.includes('branch_id cannot be null') ||
           error.response.data.message.includes('Invalid delivery location'))
        ) {
          throw new Error(
            "Invalid delivery location configuration. Please check the procurement request settings."
          );
        }

        // Check for procurement receipt validation errors
        if (
          error.response?.data?.type === 'validation' &&
          error.response?.data?.field
        ) {
          const field = error.response.data.field.replace('_', ' ');
          throw new Error(`${field}: ${error.response.data.message}`);
        }

        // Check for transaction errors
        if (
          error.message &&
          error.message.includes("Transaction cannot be rolled back")
        ) {
          console.error(
            "Database transaction error detected. The operation could not be completed."
          );
          throw new Error(
            "The operation could not be completed due to a database error. Please try again later."
          );
        }

        // Add more detailed error logging for other types of errors
        if (error.code === "ECONNABORTED") {
          console.error(
            "Request timed out. Consider increasing the timeout value."
          );
        } else if (error.response) {
          console.error(
            "Server responded with error:",
            error.response.status,
            error.response.data
          );
        } else if (error.request) {
          console.error(
            "No response received from server. Network issue or CORS problem."
          );
        } else {
          console.error(`Error posting to ${url}:`, error.message);
        }

        throw error;
      }
    }
  }

  // Helper method to get auth headers
  private getAuthHeaders() {
    const token = localStorage.getItem("token");
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  public async put<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    try {
      const response = await this.client.put<T>(url, data, config);
      return response.data;
    } catch (error: any) {
      // Check for stock limit exceeded error
      if (
        error.message &&
        error.message.includes("Assignment would exceed DSA user's stock limit")
      ) {
        // Extract the limit information from the error message
        const match = error.message.match(
          /stock limit of (\d+) items\. Current total: (\d+), Requested: (\d+)/
        );
        if (match) {
          const [, limit, current, requested] = match;
          throw new Error(
            `Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`
          );
        } else {
          throw new Error(
            "Cannot assign more stock: The DSA user has reached their stock limit."
          );
        }
      }

      // Check for transaction errors
      if (
        error.message &&
        error.message.includes("Transaction cannot be rolled back")
      ) {
        console.error(
          "Database transaction error detected. The operation could not be completed."
        );
        throw new Error(
          "The operation could not be completed due to a database error. Please try again later."
        );
      }

      console.error(`Error putting to ${url}:`, error.message);
      throw error;
    }
  }

  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.client.delete<T>(url, config);
      return response.data;
    } catch (error: any) {
      // Check for stock limit exceeded error
      if (
        error.message &&
        error.message.includes("Assignment would exceed DSA user's stock limit")
      ) {
        // Extract the limit information from the error message
        const match = error.message.match(
          /stock limit of (\d+) items\. Current total: (\d+), Requested: (\d+)/
        );
        if (match) {
          const [, limit, current, requested] = match;
          throw new Error(
            `Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`
          );
        } else {
          throw new Error(
            "Cannot assign more stock: The DSA user has reached their stock limit."
          );
        }
      }

      // Check for transaction errors
      if (
        error.message &&
        error.message.includes("Transaction cannot be rolled back")
      ) {
        console.error(
          "Database transaction error detected. The operation could not be completed."
        );
        throw new Error(
          "The operation could not be completed due to a database error. Please try again later."
        );
      }

      console.error(`Error deleting from ${url}:`, error.message);
      throw error;
    }
  }

  public async patch<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    try {
      const response = await this.client.patch<T>(url, data, config);
      return response.data;
    } catch (error: any) {
      // Check for stock limit exceeded error
      if (
        error.message &&
        error.message.includes("Assignment would exceed DSA user's stock limit")
      ) {
        // Extract the limit information from the error message
        const match = error.message.match(
          /stock limit of (\d+) items\. Current total: (\d+), Requested: (\d+)/
        );
        if (match) {
          const [, limit, current, requested] = match;
          throw new Error(
            `Stock limit exceeded: You cannot assign ${requested} more items. The DSA already has ${current} items assigned out of a limit of ${limit}.`
          );
        } else {
          throw new Error(
            "Cannot assign more stock: The DSA user has reached their stock limit."
          );
        }
      }

      // Check for transaction errors
      if (
        error.message &&
        error.message.includes("Transaction cannot be rolled back")
      ) {
        console.error(
          "Database transaction error detected. The operation could not be completed."
        );
        throw new Error(
          "The operation could not be completed due to a database error. Please try again later."
        );
      }

      console.error(`Error patching to ${url}:`, error.message);
      throw error;
    }
  }
}

// Create singleton instance
const apiClient = new ApiClient();
export default apiClient;
