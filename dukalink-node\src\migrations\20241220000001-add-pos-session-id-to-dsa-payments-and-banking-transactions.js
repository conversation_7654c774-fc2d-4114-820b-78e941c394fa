'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add pos_session_id to dsa_payments table
    await queryInterface.addColumn('dsa_payments', 'pos_session_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: 'POS Session ID when payment was recorded',
      references: {
        model: 'pos_sessions',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Add pos_session_id to banking_transactions table
    await queryInterface.addColumn('banking_transactions', 'pos_session_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      comment: 'POS Session ID when banking transaction was recorded',
      references: {
        model: 'pos_sessions',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL'
    });

    // Add indexes for better query performance
    await queryInterface.addIndex('dsa_payments', ['pos_session_id'], {
      name: 'idx_dsa_payments_pos_session_id'
    });

    await queryInterface.addIndex('banking_transactions', ['pos_session_id'], {
      name: 'idx_banking_transactions_pos_session_id'
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove indexes first
    await queryInterface.removeIndex('dsa_payments', 'idx_dsa_payments_pos_session_id');
    await queryInterface.removeIndex('banking_transactions', 'idx_banking_transactions_pos_session_id');

    // Remove columns
    await queryInterface.removeColumn('dsa_payments', 'pos_session_id');
    await queryInterface.removeColumn('banking_transactions', 'pos_session_id');
  }
};
