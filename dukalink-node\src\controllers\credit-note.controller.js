const creditNoteService = require('../services/credit-note.service');
const KraTimsService = require('../services/kra-tims.service');
const pdfGenerator = require('../utils/pdf-generator-pdfkit');
const logger = require('../utils/logger');
const { handleError } = require('../utils/error-handler');
const path = require('path');

/**
 * Create a new credit note
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.createCreditNote = async (req, res) => {
  try {
    const { tenant_id, branch_id, id: created_by } = req.user;
    const creditNoteData = {
      ...req.body,
      tenant_id,
      branch_id,
      created_by
    };

    // Pass through raw KRA data if provided (like sales/invoices)
    if (req.body.kra_items || req.body.kra_customer_info) {
      creditNoteData.kra_items = req.body.kra_items;
      creditNoteData.kra_customer_info = req.body.kra_customer_info;
    }

    // Create credit note (now includes immediate KRA integration like sales)
    const result = await creditNoteService.createCreditNote(creditNoteData);

    // Check if creation was successful
    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.message
      });
    }

    return res.status(201).json({
      success: true,
      data: result.data
    });
  } catch (error) {
    logger.error(`Error creating credit note: ${error.message}`);
    return handleError(res, error);
  }
};

/**
 * Get all credit notes with filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCreditNotes = async (req, res) => {
  try {
    const filters = {
      ...req.query,
      tenant_id: req.user.tenant_id
    };

    // Get credit notes
    const result = await creditNoteService.getCreditNotes(filters);

    return res.status(200).json({
      success: true,
      data: result.data,
      pagination: result.pagination
    });
  } catch (error) {
    logger.error(`Error getting credit notes: ${error.message}`);
    return handleError(res, error);
  }
};

/**
 * Get credit note by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.getCreditNoteById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get credit note
    const creditNote = await creditNoteService.getCreditNoteById(id);

    return res.status(200).json({
      success: true,
      data: creditNote
    });
  } catch (error) {
    logger.error(`Error getting credit note: ${error.message}`);
    return handleError(res, error);
  }
};

/**
 * Update credit note
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.updateCreditNote = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      last_updated_by: req.user.id
    };

    // Update credit note
    const result = await creditNoteService.updateCreditNote(id, updateData);

    // Check if update was successful
    if (!result.success) {
      // Determine appropriate status code based on the error message
      const statusCode = result.message.includes('not found') ? 404 : 400;

      return res.status(statusCode).json({
        success: false,
        message: result.message
      });
    }

    // Get updated credit note
    const creditNote = await creditNoteService.getCreditNoteById(id);

    return res.status(200).json({
      success: true,
      data: creditNote
    });
  } catch (error) {
    logger.error(`Error updating credit note: ${error.message}`);
    return handleError(res, error);
  }
};

/**
 * Issue a credit note (send to KRA)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.issueCreditNote = async (req, res) => {
  try {
    const { id } = req.params;

    // Issue credit note
    const result = await creditNoteService.issueCreditNote(id);

    // Check if issue was successful
    if (!result.success) {
      // Determine appropriate status code based on the error message
      const statusCode = result.message.includes('not found') ? 404 : 400;

      return res.status(statusCode).json({
        success: false,
        message: result.message
      });
    }

    return res.status(200).json({
      success: true,
      message: result.message,
      data: result.data
    });
  } catch (error) {
    logger.error(`Error issuing credit note: ${error.message}`);
    return handleError(res, error);
  }
};

/**
 * Generate PDF for credit note
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.generateCreditNotePdf = async (req, res) => {
  try {
    const { id } = req.params;

    // Get credit note
    const creditNote = await creditNoteService.getCreditNoteById(id);

    // Generate PDF
    const pdfBuffer = await pdfGenerator.generateCreditNotePdf(creditNote);

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="credit-note-${creditNote.credit_note_number}.pdf"`);

    // Send PDF
    return res.send(pdfBuffer);
  } catch (error) {
    logger.error(`Error generating credit note PDF: ${error.message}`);
    return handleError(res, error);
  }
};
