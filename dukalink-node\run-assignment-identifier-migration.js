const { Sequelize } = require('sequelize');
require('dotenv').config();

async function runMigration() {
  let sequelize;
  
  try {
    console.log('🚀 Starting DSA Assignment Identifier Migration...');
    
    // Create database connection
    sequelize = new Sequelize(
      process.env.DB_NAME,
      process.env.DB_USER,
      process.env.DB_PASSWORD,
      {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT || 3306,
        dialect: 'mysql',
        logging: console.log, // Enable logging to see what's happening
        dialectOptions: process.env.DB_USE_SSL === 'true' ? {
          ssl: {
            require: true,
            rejectUnauthorized: false,
          }
        } : {},
        pool: {
          max: 5,
          min: 0,
          acquire: 30000,
          idle: 10000,
        },
        timezone: '+00:00',
      }
    );

    console.log('📡 Testing database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully');

    console.log('📋 Loading migration file...');
    const migration = require('./migrations/20250531-add-assignment-identifier-to-sales.js');

    console.log('🔄 Running migration...');
    await migration.up(sequelize.getQueryInterface(), Sequelize);

    console.log('✅ Migration completed successfully!');
    console.log('');
    console.log('📊 Summary of changes:');
    console.log('  - Added assignment_identifier column to sales table');
    console.log('  - Updated sale_type enum to include dsa_assignment and dsa_customer_sale');
    console.log('  - Added index on assignment_identifier for better performance');
    console.log('');
    console.log('🎯 You can now create DSA assignments with KRA integration!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  } finally {
    if (sequelize) {
      console.log('🔌 Closing database connection...');
      await sequelize.close();
    }
  }
}

// Run the migration
runMigration();
