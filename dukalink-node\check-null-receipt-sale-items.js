const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkNullReceiptSaleItems() {
  let connection;

  try {
    console.log('🔍 Checking sale items for sales with null receipts...');
    console.log('📅 Date:', new Date().toISOString().split('T')[0]);

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT || 3306,
    });

    console.log('✅ Connected to database successfully');

    // Check what columns exist in the sale_items table
    const [saleItemColumns] = await connection.execute(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'sale_items'
      AND TABLE_SCHEMA = DATABASE()
      ORDER BY COLUMN_NAME;
    `);

    console.log('📋 Available columns in sale_items table:');
    saleItemColumns.forEach(col => console.log(`   - ${col.COLUMN_NAME}`));
    console.log('');

    // Get today's date in MySQL format
    const today = new Date().toISOString().split('T')[0];

    // Query to find sales with null receipts today
    const nullReceiptSalesQuery = `
      SELECT
        s.id,
        s.receipt_number,
        s.customer_id,
        s.total_amount,
        s.payment_method_id,
        s.payment_reference,
        s.is_dsa,
        s.sale_type,
        s.created_at,
        c.name as customer_name,
        pm.name as payment_method_name
      FROM sales s
      LEFT JOIN customers c ON s.customer_id = c.id
      LEFT JOIN payment_methods pm ON s.payment_method_id = pm.id
      WHERE DATE(s.created_at) = ?
        AND s.receipt_number IS NULL
        AND s.is_dsa = 1
      ORDER BY s.customer_id, s.created_at;
    `;

    console.log('🔍 Finding sales with null receipts...');
    const [nullReceiptSales] = await connection.execute(nullReceiptSalesQuery, [today]);

    if (nullReceiptSales.length === 0) {
      console.log('✅ No sales with null receipts found for today!');
      return;
    }

    console.log(`⚠️  Found ${nullReceiptSales.length} sales with null receipts:`);
    console.log('');

    // Get sale items for each null receipt sale
    for (const sale of nullReceiptSales) {
      console.log(`📋 Sale ID ${sale.id} - ${sale.customer_name} (${sale.total_amount} KES):`);
      console.log(`   Payment: ${sale.payment_method_name} (ID: ${sale.payment_method_id})`);
      console.log(`   Reference: ${sale.payment_reference || 'None'}`);
      console.log(`   Created: ${sale.created_at}`);

      // Query sale items for this sale (using only existing columns)
      const saleItemsQuery = `
        SELECT
          si.id as item_id,
          si.product_id,
          si.quantity,
          si.unit_price,
          si.total_price,
          p.name as product_name,
          p.sku,
          p.barcode,
          pc.name as category_name
        FROM sale_items si
        LEFT JOIN products p ON si.product_id = p.id
        LEFT JOIN product_categories pc ON p.category_id = pc.id
        WHERE si.sale_id = ?
        ORDER BY si.id;
      `;

      const [saleItems] = await connection.execute(saleItemsQuery, [sale.id]);

      if (saleItems.length === 0) {
        console.log('   ❌ No sale items found for this sale!');
      } else {
        console.log(`   📦 ${saleItems.length} items:`);
        saleItems.forEach((item, index) => {
          console.log(`      ${index + 1}. ${item.product_name} (${item.sku || 'No SKU'})`);
          console.log(`         Category: ${item.category_name || 'Unknown'}`);
          console.log(`         Quantity: ${item.quantity}, Unit Price: ${item.unit_price}, Total: ${item.total_price}`);
        });
      }
      console.log('   ---');
    }

    // Summary of products in null receipt sales
    const productSummaryQuery = `
      SELECT
        p.id as product_id,
        p.name as product_name,
        p.sku,
        p.barcode,
        pc.name as category_name,
        COUNT(si.id) as times_sold,
        SUM(si.quantity) as total_quantity,
        SUM(si.total_price) as total_value,
        AVG(si.unit_price) as avg_unit_price
      FROM sale_items si
      JOIN sales s ON si.sale_id = s.id
      LEFT JOIN products p ON si.product_id = p.id
      LEFT JOIN product_categories pc ON p.category_id = pc.id
      WHERE DATE(s.created_at) = ?
        AND s.receipt_number IS NULL
        AND s.is_dsa = 1
      GROUP BY p.id, p.name, p.sku, p.barcode, pc.name
      ORDER BY total_value DESC;
    `;

    console.log('');
    console.log('📊 Product Summary for Null Receipt Sales:');
    const [productSummary] = await connection.execute(productSummaryQuery, [today]);

    if (productSummary.length === 0) {
      console.log('   No products found');
    } else {
      console.log(`   Found ${productSummary.length} unique products:`);
      console.log('');

      productSummary.forEach((product, index) => {
        console.log(`   ${index + 1}. ${product.product_name} (${product.sku || 'No SKU'})`);
        console.log(`      Category: ${product.category_name || 'Unknown'}`);
        console.log(`      Times Sold: ${product.times_sold}`);
        console.log(`      Total Quantity: ${product.total_quantity}`);
        console.log(`      Total Value: ${product.total_value} KES`);
        console.log(`      Avg Unit Price: ${product.avg_unit_price} KES`);
        console.log('      ---');
      });
    }

  } catch (error) {
    console.error('❌ Error checking null receipt sale items:', error.message);
    console.error('Full error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the check
checkNullReceiptSaleItems();
