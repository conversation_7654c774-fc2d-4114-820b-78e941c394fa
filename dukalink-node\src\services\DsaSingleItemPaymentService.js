const { DsaStockAssignment, DsaPayment, Customer, Product, User, PosSession } = require('../models');
const sequelize = require('../../config/database');
const AppError = require('../utils/error');
const logger = require('../utils/logger');
const DsaPricingService = require('./DsaPricingService');
const EnhancedDsaBalanceService = require('./EnhancedDsaBalanceService');

/**
 * DSA Single Item Payment Service
 * Simplified service for processing payments one item at a time
 */
class DsaSingleItemPaymentService {
  /**
   * Process payment for a single item
   * @param {Object} paymentData - Payment data
   * @param {Object} transaction - Database transaction (optional)
   * @returns {Object} Payment processing result
   */
  static async processSingleItemPayment(paymentData, transaction = null) {
    const shouldCommit = !transaction;
    if (!transaction) {
      transaction = await sequelize.transaction();
    }

    try {
      const {
        assignment_identifier,
        dsa_agent_id,
        customer_id,
        branch_id,
        item_id,
        payment_quantity,
        cash_amount,
        paybill_amount,
        notes,
        created_by
      } = paymentData;

      logger.info(`Processing single item payment for assignment: ${assignment_identifier}, item: ${item_id}`);

      // Validate DSA agent
      const dsaAgent = await Customer.findOne({
        where: { id: dsa_agent_id, is_dsa: true },
        transaction
      });

      if (!dsaAgent) {
        throw new AppError(404, 'DSA agent not found');
      }

      // Get the specific assignment item
      const assignment = await DsaStockAssignment.findOne({
        where: {
          id: item_id,
          assignment_identifier,
          customer_id: dsa_agent_id
        },
        include: [
          {
            model: Product,
            attributes: ['id', 'name', 'default_wholesale_price', 'suggested_selling_price']
          }
        ],
        transaction
      });

      if (!assignment) {
        throw new AppError(404, 'Assignment item not found');
      }

      // Validate payment quantity
      const maxPayableQuantity = this.getMaxPayableQuantity(assignment);
      const requestedQuantity = parseInt(payment_quantity);

      if (requestedQuantity <= 0 || requestedQuantity > maxPayableQuantity) {
        throw new AppError(400, `Invalid payment quantity. Maximum payable: ${maxPayableQuantity}`);
      }

      // Calculate payment amounts
      const unitPrice = DsaPricingService.getItemPrice(assignment);
      const expectedPaymentAmount = requestedQuantity * unitPrice;
      const totalPaymentAmount = parseFloat(cash_amount || 0) + parseFloat(paybill_amount || 0);

      // Validate payment amount
      if (Math.abs(totalPaymentAmount - expectedPaymentAmount) > 0.01) {
        throw new AppError(400,
          `Payment amount mismatch. Expected: ${expectedPaymentAmount}, Received: ${totalPaymentAmount}`
        );
      }

      // Get the active POS session for the branch (if any)
      let activePosSession = null;
      try {
        activePosSession = await PosSession.findOne({
          where: {
            branch_id: branch_id,
            status: 'open',
            deleted_at: null
          },
          order: [['start_time', 'DESC']],
          transaction
        });
      } catch (sessionError) {
        logger.warn(`Could not find active POS session for branch ${branch_id}: ${sessionError.message}`);
      }

      // Create payment record
      const payment = await DsaPayment.create({
        assignment_identifier,
        customer_id: dsa_agent_id,
        branch_id,
        pos_session_id: activePosSession?.id || null,
        cash_amount: parseFloat(cash_amount || 0),
        paybill_amount: parseFloat(paybill_amount || 0),
        total_amount: totalPaymentAmount,
        payment_date: new Date(),
        notes: notes || `Single item payment: ${assignment.Product?.name || 'Unknown Product'} (${requestedQuantity} units)`,
        created_by
      }, { transaction });

      // Update assignment record
      const currentQuantitySold = parseInt(assignment.quantity_sold || 0);
      const currentAmountPaid = parseFloat(assignment.amount_paid || 0);
      const newQuantitySold = currentQuantitySold + requestedQuantity;
      const newAmountPaid = currentAmountPaid + totalPaymentAmount;

      // Calculate remaining balance for this item
      const itemTotalValue = this.calculateItemTotalValue(assignment);
      const newBalance = Math.max(0, itemTotalValue - newAmountPaid);

      // Determine payment status for this item
      const paymentStatus = this.determineItemPaymentStatus(newAmountPaid, itemTotalValue, assignment);

      await DsaStockAssignment.update({
        quantity_sold: newQuantitySold,
        amount_paid: newAmountPaid,
        balance: newBalance,
        payment_status: paymentStatus,
        last_payment_date: new Date()
      }, {
        where: { id: assignment.id },
        transaction
      });

      // Recalculate overall assignment balance
      await EnhancedDsaBalanceService.recalculateBalance(assignment_identifier, transaction);

      if (shouldCommit) {
        await transaction.commit();
      }

      const result = {
        success: true,
        payment_id: payment.id,
        assignment_identifier,
        item_details: {
          item_id: assignment.id,
          product_name: assignment.Product?.name || 'Unknown Product',
          quantity_paid: requestedQuantity,
          unit_price: unitPrice,
          payment_amount: totalPaymentAmount,
          new_quantity_sold: newQuantitySold,
          new_amount_paid: newAmountPaid,
          new_balance: newBalance,
          payment_status: paymentStatus
        },
        payment_details: {
          cash_amount: parseFloat(cash_amount || 0),
          paybill_amount: parseFloat(paybill_amount || 0),
          total_amount: totalPaymentAmount,
          payment_date: payment.payment_date
        }
      };

      logger.info(`Single item payment processed successfully: ${JSON.stringify(result.item_details)}`);
      return result;

    } catch (error) {
      if (shouldCommit) {
        await transaction.rollback();
      }
      logger.error(`Error processing single item payment: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get maximum quantity that can be paid for an item
   * @param {Object} assignment - Assignment record
   * @returns {number} Maximum payable quantity
   */
  static getMaxPayableQuantity(assignment) {
    const quantityAssigned = parseInt(assignment.quantity_assigned || 0);
    const quantityReturned = parseInt(assignment.quantity_returned || 0);
    const quantitySold = parseInt(assignment.quantity_sold || 0); // quantity paid
    return Math.max(0, quantityAssigned - quantityReturned - quantitySold);
  }

  /**
   * Calculate total value for an assignment item
   * @param {Object} assignment - Assignment record
   * @returns {number} Total item value
   */
  static calculateItemTotalValue(assignment) {
    const unitPrice = DsaPricingService.getItemPrice(assignment);
    const quantityAssigned = parseInt(assignment.quantity_assigned || 0);
    const quantityReturned = parseInt(assignment.quantity_returned || 0);
    const remainingQuantity = Math.max(0, quantityAssigned - quantityReturned);
    return remainingQuantity * unitPrice;
  }

  /**
   * Determine payment status for an item
   * @param {number} amountPaid - Amount paid for the item
   * @param {number} totalValue - Total value of the item
   * @param {Object} assignment - Assignment record
   * @returns {string} Payment status
   */
  static determineItemPaymentStatus(amountPaid, totalValue, assignment) {
    // Check if all quantity is returned
    const quantityAssigned = parseInt(assignment.quantity_assigned || 0);
    const quantityReturned = parseInt(assignment.quantity_returned || 0);

    if (quantityReturned >= quantityAssigned) {
      return 'FULLY_PAID'; // All items returned, consider as paid
    }

    // Check payment amount vs total value
    if (amountPaid >= totalValue) {
      return 'FULLY_PAID';
    } else if (amountPaid > 0) {
      return 'PARTIALLY_PAID';
    } else {
      return 'UNPAID';
    }
  }

  /**
   * Get unpaid items for an assignment
   * @param {string} assignmentIdentifier - Assignment identifier
   * @param {Object} transaction - Database transaction (optional)
   * @returns {Array} List of unpaid items
   */
  static async getUnpaidItems(assignmentIdentifier, transaction = null) {
    try {
      const assignments = await DsaStockAssignment.findAll({
        where: { assignment_identifier: assignmentIdentifier },
        include: [
          {
            model: Product,
            attributes: ['id', 'name', 'default_wholesale_price', 'suggested_selling_price']
          }
        ],
        transaction
      });

      // Filter items that have unpaid quantities
      const unpaidItems = assignments.filter(assignment => {
        const maxPayable = this.getMaxPayableQuantity(assignment);
        return maxPayable > 0;
      });

      // Add calculated fields
      return unpaidItems.map(assignment => ({
        ...assignment.toJSON(),
        max_payable_quantity: this.getMaxPayableQuantity(assignment),
        unit_price: DsaPricingService.getItemPrice(assignment),
        remaining_value: this.calculateItemTotalValue(assignment) - (parseFloat(assignment.amount_paid || 0))
      }));

    } catch (error) {
      logger.error(`Error getting unpaid items for ${assignmentIdentifier}:`, error);
      throw error;
    }
  }

  /**
   * Get payment summary for an assignment
   * @param {string} assignmentIdentifier - Assignment identifier
   * @param {Object} transaction - Database transaction (optional)
   * @returns {Object} Payment summary
   */
  static async getPaymentSummary(assignmentIdentifier, transaction = null) {
    try {
      // Get current balance
      const balanceInfo = await EnhancedDsaBalanceService.getCurrentBalance(assignmentIdentifier, transaction);

      // Get unpaid items
      const unpaidItems = await this.getUnpaidItems(assignmentIdentifier, transaction);

      // Get recent payments
      const recentPayments = await DsaPayment.findAll({
        where: { assignment_identifier: assignmentIdentifier },
        include: [
          {
            model: User,
            as: 'Creator',
            attributes: ['id', 'name']
          }
        ],
        order: [['payment_date', 'DESC']],
        limit: 5,
        transaction
      });

      return {
        assignment_identifier: assignmentIdentifier,
        balance_info: balanceInfo,
        unpaid_items: unpaidItems,
        unpaid_items_count: unpaidItems.length,
        recent_payments: recentPayments.map(payment => ({
          id: payment.id,
          cash_amount: parseFloat(payment.cash_amount || 0),
          paybill_amount: parseFloat(payment.paybill_amount || 0),
          total_amount: parseFloat(payment.total_amount || 0),
          payment_date: payment.payment_date,
          notes: payment.notes,
          created_by: payment.Creator?.name || 'Unknown'
        }))
      };

    } catch (error) {
      logger.error(`Error getting payment summary for ${assignmentIdentifier}:`, error);
      throw error;
    }
  }

  /**
   * Validate single item payment data
   * @param {Object} paymentData - Payment data to validate
   * @returns {Object} Validation result
   */
  static validatePaymentData(paymentData) {
    const errors = [];
    const warnings = [];

    const {
      assignment_identifier,
      dsa_agent_id,
      item_id,
      payment_quantity,
      cash_amount,
      paybill_amount
    } = paymentData;

    // Required fields
    if (!assignment_identifier) errors.push('Assignment identifier is required');
    if (!dsa_agent_id) errors.push('DSA agent ID is required');
    if (!item_id) errors.push('Item ID is required');
    if (!payment_quantity || parseInt(payment_quantity) <= 0) {
      errors.push('Valid payment quantity is required');
    }

    // Payment amounts
    const totalPayment = parseFloat(cash_amount || 0) + parseFloat(paybill_amount || 0);
    if (totalPayment <= 0) {
      errors.push('Payment amount must be greater than zero');
    }

    // Check for negative amounts
    if (parseFloat(cash_amount || 0) < 0) errors.push('Cash amount cannot be negative');
    if (parseFloat(paybill_amount || 0) < 0) errors.push('Paybill amount cannot be negative');

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      total_payment_amount: totalPayment
    };
  }
}

module.exports = DsaSingleItemPaymentService;
