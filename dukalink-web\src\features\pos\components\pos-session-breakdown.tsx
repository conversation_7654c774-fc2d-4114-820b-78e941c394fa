"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Separator } from "@/components/ui/separator";
import { useEffect, useState } from "react";
import { formatCurrency as formatCurrencyUtil } from "@/lib/utils";
import posSessionService from "../api/pos-session-service";

// Custom formatCurrency function that includes the "KSh" prefix
// so we don't need to add it manually in the UI
const formatCurrency = (value: number | string | null | undefined): string => {
  // Use the utility function but replace "KES" with "KSh"
  const formatted = formatCurrencyUtil(value);
  return formatted.replace("KES", "KSh");
};
import { PosSession } from "@/types";

interface PosSessionBreakdownProps {
  sessionId: number;
  session: PosSession;
  showCloseSummary?: boolean;
}

interface SalesByPaymentMethod {
  [key: string]: number;
}

interface DsaSale {
  id: number;
  name: string;
  total: number;
  sales: {
    id: number;
    amount: number;
    date: string;
    paymentMethod: string;
  }[];
}

interface SessionBreakdownData {
  // Session data
  session: {
    id: number;
    user_id: number;
    branch_id: number;
    start_time: string;
    end_time: string | null;
    opening_cash_balance: string;
    opening_mpesa_balance?: string;
    opening_mpesa_float?: string;
    cash_paid_in: string;
    cash_paid_out: string;
    status: "open" | "closed";
    user_name?: string;
    branch_name?: string;
  };

  // Reconciliation data
  reconciliation?: {
    id: number;
    closing_cash_balance: string;
    closing_mpesa_balance: string;
    closing_mpesa_float?: string;
    cash_payments: string;
    mpesa_payments: string;
    total_sales: string;
    discrepancies: string;
    total_variance: string;
    total_bankings?: string;
    total_dsa_sales?: string;
    notes: string;
    created_at: string;
  };

  // Sales data
  salesByPaymentMethod: SalesByPaymentMethod;
  totalSales: number;
  totalCashSales: number;
  totalNonCashSales: number;

  // MPESA services
  mpesaServices: {
    deposits: number;
    withdrawals: number;
  };

  // Mobile money services
  mobileMoneyServices: {
    deposits: number;
    withdrawals: number;
    float_in: number;
    float_out: number;
  };

  // Banking transactions
  bankingTransactions: any[];

  // Expenses
  totalExpenses: number;

  // DSA sales
  dsaSales: {
    dsaSalesByUser: DsaSale[];
    totalDsaSales: number;
  };
}

export function PosSessionBreakdown({
  sessionId,
  session,
  showCloseSummary = false,
}: PosSessionBreakdownProps) {
  const [loading, setLoading] = useState(true);
  const [breakdownData, setBreakdownData] =
    useState<SessionBreakdownData | null>(null);
  const [shiftClosingData, setShiftClosingData] = useState<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch regular breakdown data
        const breakdownResult = await posSessionService.getSessionBreakdown(
          sessionId
        );
        setBreakdownData(breakdownResult);

        // Fetch shift closing data if session is closed
        if (session.status === "closed") {
          try {
            const closingData = await posSessionService.getShiftClosingData(
              sessionId
            );
            console.log("Shift closing data:", closingData);
            setShiftClosingData(closingData);
          } catch (closingError) {
            console.error("Error fetching shift closing data:", closingError);
          }
        }
      } catch (error) {
        console.error("Error fetching session breakdown data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [sessionId, session.status]);

  if (loading) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-8 w-1/3" />
        <Skeleton className="h-64" />
      </div>
    );
  }

  if (!breakdownData) {
    return <div>Failed to load breakdown data</div>;
  }

  // Check if we have any data to display
  const hasData = true; // Always show data if we have a session

  if (!hasData) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <p className="text-muted-foreground mb-2">
          No data available for this session
        </p>
        <p className="text-sm text-muted-foreground">
          This could be because the session is still open or no transactions
          were recorded.
        </p>

        {/* Debug information */}
        <div className="mt-8 p-4 border border-dashed border-red-300 rounded-md">
          <h3 className="font-bold text-red-500 mb-2">Debug Information</h3>
          <div className="space-y-2 text-sm">
            <p>
              <strong>Session ID:</strong> {sessionId}
            </p>
            <p>
              <strong>Session Status:</strong> {breakdownData.session.status}
            </p>
            <p>
              <strong>Has Reconciliation Data:</strong>{" "}
              {breakdownData.reconciliation ? "Yes" : "No"}
            </p>
            <p>
              <strong>Original Session Status:</strong> {session.status}
            </p>
            <p>
              <strong>Original Has Reconciliation:</strong>{" "}
              {session.PosSessionReconciliation ? "Yes" : "No"}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Determine what content to show based on the showCloseSummary prop
  if (showCloseSummary) {
    // Only show the Shift Closing Summary when showCloseSummary is true
    return (
      <div className="space-y-6">
        {/* Shift Closing Data - Using the new endpoint */}
        {shiftClosingData && breakdownData.session.status === "closed" ? (
          <Card>
            <CardHeader>
              <CardTitle>Shift Closing Summary</CardTitle>
            </CardHeader>
            <CardContent>
              {/* Session Info at the top */}
              <div className="mb-6">
                <h3 className="text-sm font-medium mb-2">
                  Session Information
                </h3>
                <div className="bg-muted p-3 rounded-md">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Shop
                      </span>
                      <span className="text-sm font-medium">
                        {shiftClosingData.session.branch.name}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Closed By
                      </span>
                      <span className="text-sm font-medium">
                        {shiftClosingData.session.user.name}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Main content in columns */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Column 1: Sales Summary */}
                <div>
                  <h3 className="text-sm font-medium mb-2">SALES SUMMARY</h3>
                  <div className="space-y-2 bg-muted p-3 rounded-md">
                    {Object.entries(shiftClosingData.sales.by_payment_method)
                      .filter(
                        ([key]) =>
                          !key.includes("CASH") && !key.includes("MPESA")
                      )
                      .map(([key, value]: [string, any]) => (
                        <div
                          key={key}
                          className="flex justify-between items-center"
                        >
                          <span className="text-sm">{value.name}</span>
                          <span className="text-sm font-medium">
                            {formatCurrency(value.amount)}
                          </span>
                        </div>
                      ))}
                    <Separator className="my-2" />
                    <div className="flex justify-between items-center font-medium">
                      <span>Total Sales</span>
                      <span>
                        {formatCurrency(shiftClosingData.sales.total)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Column 2: Mobile Money */}
                <div>
                  <h3 className="text-sm font-medium mb-2">MOBILE MONEY</h3>
                  <div className="space-y-2 bg-muted p-3 rounded-md">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Opening Float</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          shiftClosingData.mobile_money_services.opening_float
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">+ Float In</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          shiftClosingData.mobile_money_services.float_in
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">- Float Out</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          shiftClosingData.mobile_money_services.float_out
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">- Deposits</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          shiftClosingData.mobile_money_services.total_deposits
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">+ Withdrawals</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          shiftClosingData.mobile_money_services
                            .total_withdrawals
                        )}
                      </span>
                    </div>
                    <Separator className="my-2" />
                    <div className="flex justify-between items-center font-medium">
                      <span>Expected Float</span>
                      <span>
                        {formatCurrency(
                          shiftClosingData.mobile_money_services.expected_float
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Actual Float</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          parseFloat(
                            breakdownData.reconciliation
                              ?.closing_mpesa_balance || "0"
                          )
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Variance</span>
                      <span
                        className={`text-sm font-medium ${
                          parseFloat(
                            breakdownData.reconciliation
                              ?.closing_mpesa_balance || "0"
                          ) -
                            shiftClosingData.mobile_money_services
                              .expected_float !==
                          0
                            ? "text-destructive"
                            : "text-green-600"
                        }`}
                      >
                        {formatCurrency(
                          parseFloat(
                            breakdownData.reconciliation
                              ?.closing_mpesa_balance || "0"
                          ) -
                            shiftClosingData.mobile_money_services
                              .expected_float
                        )}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Column 3: Cash Status */}
                <div>
                  <h3 className="text-sm font-medium mb-2">CASH STATUS</h3>
                  <div className="space-y-2 bg-muted p-3 rounded-md">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Opening Balance</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          shiftClosingData.session.opening_cash_balance
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">+ Cash Sales</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(shiftClosingData.sales.cash_sales)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">+ Bankings</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(shiftClosingData.banking.total)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">+ Cash In</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          parseFloat(breakdownData.session.cash_paid_in || "0")
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">- Cash Out</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          parseFloat(breakdownData.session.cash_paid_out || "0")
                        )}
                      </span>
                    </div>
                    <Separator className="my-2" />
                    <div className="flex justify-between items-center font-medium">
                      <span>Expected Cash</span>
                      <span>
                        {formatCurrency(
                          shiftClosingData.session.opening_cash_balance +
                            shiftClosingData.sales.cash_sales +
                            (shiftClosingData.session.total_deposits || 0) -
                            (shiftClosingData.expenses?.total || 0) -
                            (shiftClosingData.session.total_withdrawals || 0)
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Actual Cash</span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          parseFloat(
                            breakdownData.reconciliation
                              ?.closing_cash_balance || "0"
                          )
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Variance</span>
                      <span
                        className={`text-sm font-medium ${
                          parseFloat(
                            breakdownData.reconciliation?.total_variance || "0"
                          ) !== 0
                            ? "text-destructive"
                            : "text-green-600"
                        }`}
                      >
                        {formatCurrency(
                          parseFloat(
                            breakdownData.reconciliation?.total_variance || "0"
                          )
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Total Variance at the bottom */}
              <div className="mt-6">
                <Card className="border border-muted">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center font-medium">
                      <span className="text-base">TOTAL VARIANCE</span>
                      <span
                        className={`text-base font-bold ${
                          parseFloat(
                            breakdownData.reconciliation?.total_variance || "0"
                          ) !== 0
                            ? "text-destructive"
                            : "text-green-600"
                        }`}
                      >
                        {formatCurrency(
                          parseFloat(
                            breakdownData.reconciliation?.total_variance || "0"
                          )
                        )}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Shift Closing Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="p-4 bg-amber-50 border border-amber-200 rounded-md">
                <p className="text-amber-800 text-sm">
                  {breakdownData.session.status !== "closed"
                    ? "This session is not closed yet. Close the session to view the shift closing summary."
                    : "Shift closing data is not available for this session."}
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  // Show the regular breakdown when showCloseSummary is false
  return (
    <div className="space-y-6">
      {/* Session Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Session Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Session Details</h3>
              <div className="space-y-2 bg-muted p-3 rounded-md">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">User</span>
                  <span className="text-sm font-medium">
                    {breakdownData.session.user_name}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Branch</span>
                  <span className="text-sm font-medium">
                    {breakdownData.session.branch_name}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Start Time
                  </span>
                  <span className="text-sm font-medium">
                    {new Date(
                      breakdownData.session.start_time
                    ).toLocaleString()}
                  </span>
                </div>
                {breakdownData.session.end_time && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      End Time
                    </span>
                    <span className="text-sm font-medium">
                      {new Date(
                        breakdownData.session.end_time
                      ).toLocaleString()}
                    </span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Status</span>
                  <span className="text-sm font-medium capitalize">
                    {breakdownData.session.status}
                  </span>
                </div>

                {/* Add closing balances to session details if session is closed */}
                {breakdownData.session.status === "closed" &&
                  breakdownData.reconciliation && (
                    <>
                      <div className="border-t my-2 pt-2"></div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Closing Cash Balance
                        </span>
                        <span className="text-sm font-medium">
                          {formatCurrency(
                            parseFloat(
                              breakdownData.reconciliation.closing_cash_balance
                            )
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Closing M-PESA Balance
                        </span>
                        <span className="text-sm font-medium">
                          {formatCurrency(
                            parseFloat(
                              breakdownData.reconciliation
                                .closing_mpesa_balance || "0"
                            )
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Closing M-PESA Float
                        </span>
                        <span className="text-sm font-medium">
                          {formatCurrency(
                            parseFloat(
                              breakdownData.reconciliation
                                .closing_mpesa_float || "0"
                            )
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Total Bankings
                        </span>
                        <span className="text-sm font-medium">
                          {formatCurrency(
                            parseFloat(
                              breakdownData.reconciliation.total_bankings || "0"
                            )
                          )}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          Total DSA Sales
                        </span>
                        <span className="text-sm font-medium">
                          {formatCurrency(
                            parseFloat(
                              breakdownData.reconciliation.total_dsa_sales ||
                                "0"
                            )
                          )}
                        </span>
                      </div>
                    </>
                  )}
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2">Opening Balances</h3>
              <div className="space-y-2 bg-muted p-3 rounded-md">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    Cash Balance
                  </span>
                  <span className="text-sm font-medium">
                    {formatCurrency(
                      parseFloat(breakdownData.session.opening_cash_balance)
                    )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    M-PESA Balance
                  </span>
                  <span className="text-sm font-medium">
                    {formatCurrency(
                      parseFloat(
                        breakdownData.session.opening_mpesa_balance || "0"
                      )
                    )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">
                    M-PESA Float
                  </span>
                  <span className="text-sm font-medium">
                    {formatCurrency(
                      parseFloat(
                        breakdownData.session.opening_mpesa_float || "0"
                      )
                    )}
                  </span>
                </div>
              </div>
            </div>

            {breakdownData.reconciliation && (
              <>
                <div>
                  <h3 className="text-sm font-medium mb-2">Closing Balances</h3>
                  <div className="space-y-2 bg-muted p-3 rounded-md">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Cash Balance
                      </span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          parseFloat(
                            breakdownData.reconciliation.closing_cash_balance
                          )
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        M-PESA Balance
                      </span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          parseFloat(
                            breakdownData.reconciliation.closing_mpesa_balance
                          )
                        )}
                      </span>
                    </div>
                    {breakdownData.reconciliation.closing_mpesa_float && (
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">
                          M-PESA Float
                        </span>
                        <span className="text-sm font-medium">
                          {formatCurrency(
                            parseFloat(
                              breakdownData.reconciliation.closing_mpesa_float
                            )
                          )}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium mb-2">Reconciliation</h3>
                  <div className="space-y-2 bg-muted p-3 rounded-md">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Cash Payments
                      </span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          parseFloat(breakdownData.reconciliation.cash_payments)
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        M-PESA Payments
                      </span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          parseFloat(
                            breakdownData.reconciliation.mpesa_payments
                          )
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Total Sales
                      </span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          parseFloat(breakdownData.reconciliation.total_sales)
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Total Bankings
                      </span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          parseFloat(
                            breakdownData.reconciliation.total_bankings || "0"
                          )
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Total DSA Sales
                      </span>
                      <span className="text-sm font-medium">
                        {formatCurrency(
                          parseFloat(
                            breakdownData.reconciliation.total_dsa_sales || "0"
                          )
                        )}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">
                        Variance
                      </span>
                      <span
                        className={`text-sm font-medium ${
                          parseFloat(
                            breakdownData.reconciliation.total_variance
                          ) !== 0
                            ? "text-destructive"
                            : "text-green-600"
                        }`}
                      >
                        {formatCurrency(
                          parseFloat(
                            breakdownData.reconciliation.total_variance
                          )
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>

          {breakdownData.reconciliation?.notes && (
            <div className="mt-4">
              <h3 className="text-sm font-medium mb-2">Notes</h3>
              <p className="text-sm bg-muted p-3 rounded-md">
                {breakdownData.reconciliation.notes}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* DSA Sales */}
      {breakdownData.dsaSales.dsaSalesByUser.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>DSA Sales</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {breakdownData.dsaSales.dsaSalesByUser.map((dsa) => (
                <div key={dsa.id} className="flex justify-between items-center">
                  <span className="text-sm">{dsa.name}</span>
                  <span className="text-sm font-medium">
                    KSh {formatCurrency(dsa.total)}
                  </span>
                </div>
              ))}
              <Separator className="my-2" />
              <div className="flex justify-between items-center font-medium">
                <span>Total DSA Sales</span>
                <span>
                  KSh {formatCurrency(breakdownData.dsaSales.totalDsaSales)}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* MPESA Services */}
      {(breakdownData.mpesaServices.deposits > 0 ||
        breakdownData.mpesaServices.withdrawals > 0) && (
        <Card>
          <CardHeader>
            <CardTitle>MPESA Services</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {breakdownData.mpesaServices.deposits > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm">Deposits</span>
                  <span className="text-sm font-medium">
                    KSh {formatCurrency(breakdownData.mpesaServices.deposits)}
                  </span>
                </div>
              )}
              {breakdownData.mpesaServices.withdrawals > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm">Withdrawals</span>
                  <span className="text-sm font-medium">
                    {formatCurrency(breakdownData.mpesaServices.withdrawals)}
                  </span>
                </div>
              )}
              {breakdownData.mpesaServices.deposits === 0 &&
                breakdownData.mpesaServices.withdrawals === 0 && (
                  <div className="text-sm text-muted-foreground">
                    No MPESA transactions
                  </div>
                )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Mobile Money Services */}
      {(breakdownData.mobileMoneyServices.deposits > 0 ||
        breakdownData.mobileMoneyServices.withdrawals > 0 ||
        breakdownData.mobileMoneyServices.float_in > 0 ||
        breakdownData.mobileMoneyServices.float_out > 0) && (
        <Card>
          <CardHeader>
            <CardTitle>Mobile Money Services</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {breakdownData.mobileMoneyServices.deposits > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm">Deposits</span>
                  <span className="text-sm font-medium">
                    {formatCurrency(breakdownData.mobileMoneyServices.deposits)}
                  </span>
                </div>
              )}
              {breakdownData.mobileMoneyServices.withdrawals > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm">Withdrawals</span>
                  <span className="text-sm font-medium">
                    {formatCurrency(
                      breakdownData.mobileMoneyServices.withdrawals
                    )}
                  </span>
                </div>
              )}
              {breakdownData.mobileMoneyServices.float_in > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm">Float In</span>
                  <span className="text-sm font-medium">
                    {formatCurrency(breakdownData.mobileMoneyServices.float_in)}
                  </span>
                </div>
              )}
              {breakdownData.mobileMoneyServices.float_out > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm">Float Out</span>
                  <span className="text-sm font-medium">
                    {formatCurrency(
                      breakdownData.mobileMoneyServices.float_out
                    )}
                  </span>
                </div>
              )}
              {breakdownData.mobileMoneyServices.deposits === 0 &&
                breakdownData.mobileMoneyServices.withdrawals === 0 &&
                breakdownData.mobileMoneyServices.float_in === 0 &&
                breakdownData.mobileMoneyServices.float_out === 0 && (
                  <div className="text-sm text-muted-foreground">
                    No mobile money transactions
                  </div>
                )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Expenses */}
      {breakdownData.totalExpenses > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex justify-between items-center font-medium">
              <span>Total Expenses</span>
              <span>KSh {formatCurrency(breakdownData.totalExpenses)}</span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
