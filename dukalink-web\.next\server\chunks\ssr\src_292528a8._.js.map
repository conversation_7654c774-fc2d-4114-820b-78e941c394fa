{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/hooks/use-permissions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCurrentUser } from \"./use-auth\";\r\nimport { usePermissionContext } from \"../context/permission-context\";\r\n\r\n// Define constants for resources and actions\r\nexport const RESOURCES = {\r\n  USERS: \"users\",\r\n  ROLES: \"roles\",\r\n  PERMISSIONS: \"permissions\",\r\n  PRODUCTS: \"products\",\r\n  CATEGORIES: \"categories\",\r\n  BRANDS: \"brands\",\r\n  INVENTORY: \"inventory\",\r\n  STOCK_ITEMS: \"stock_items\",\r\n  BRANCHES: \"branches\",\r\n  EMPLOYEES: \"employees\",\r\n  BANKING: \"banking\",\r\n  EXPENSES: \"expenses\",\r\n  EXPENSE_FIRST_APPROVAL: \"expense_first_approval\",\r\n  EXPENSE_SECOND_APPROVAL: \"expense_second_approval\",\r\n  SALES: \"sales\",\r\n  POS_SESSIONS: \"pos_sessions\",\r\n  CUSTOMERS: \"customers\",\r\n  PROFILE: \"profile\",\r\n  STOCK_LOCATIONS: \"stock_locations\",\r\n};\r\n\r\nexport const ACTIONS = {\r\n  CREATE: \"create\",\r\n  READ: \"read\",\r\n  UPDATE: \"update\",\r\n  DELETE: \"delete\",\r\n};\r\n\r\nexport const SCOPE = {\r\n  ANY: \"any\",\r\n  OWN: \"own\",\r\n};\r\n\r\nexport function usePermissions() {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const {\r\n    permissions,\r\n    isLoading: isPermissionsLoading,\r\n    hasPermission,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n  } = usePermissionContext();\r\n\r\n  // Check if the user is a company admin\r\n  const isCompanyAdmin = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the company_admin role\r\n    return (\r\n      user.role_name === \"company_admin\" ||\r\n      user.role_name === \"tenant_admin\" ||\r\n      user.role_name === \"super_admin\"\r\n    );\r\n  };\r\n\r\n  // Check if the user is a branch manager\r\n  const isBranchManager = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the branch_manager role\r\n    return user.role_name === \"branch_manager\";\r\n  };\r\n\r\n  // Check if the user can manage stock locations (create, update, delete)\r\n  const canManageStockLocations = (): boolean => {\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.CREATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.UPDATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.DELETE, SCOPE.ANY) ||\r\n      isCompanyAdmin()\r\n    );\r\n  };\r\n\r\n  // Check if the user can view stock locations\r\n  const canViewStockLocations = (): boolean => {\r\n    // Check for explicit permission or fallback to any authenticated user\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.READ, SCOPE.ANY) ||\r\n      !!user\r\n    );\r\n  };\r\n\r\n  // Check if the user can perform an action on a resource\r\n  const can = (\r\n    resource: string,\r\n    action: string,\r\n    scope: \"any\" | \"own\" = \"any\"\r\n  ): boolean => {\r\n    return hasPermission(resource, action, scope);\r\n  };\r\n\r\n  // Check if the user has any of the specified permissions\r\n  const hasAnyPermission = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.some(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  // Check if the user has all of the specified permissions\r\n  const hasAllPermissions = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.every(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  return {\r\n    isCompanyAdmin,\r\n    isBranchManager,\r\n    canManageStockLocations,\r\n    canViewStockLocations,\r\n    can,\r\n    hasPermission,\r\n    hasAnyPermission,\r\n    hasAllPermissions,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n    permissions, // Expose the raw permissions for debugging\r\n    isLoading: isUserLoading || isPermissionsLoading,\r\n    RESOURCES,\r\n    ACTIONS,\r\n    SCOPE,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;AAMO,MAAM,YAAY;IACvB,OAAO;IACP,OAAO;IACP,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,aAAa;IACb,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;IACV,wBAAwB;IACxB,yBAAyB;IACzB,OAAO;IACP,cAAc;IACd,WAAW;IACX,SAAS;IACT,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,QAAQ;IACnB,KAAK;IACL,KAAK;AACP;AAEO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EACJ,WAAW,EACX,WAAW,oBAAoB,EAC/B,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,4JAAA,CAAA,uBAAoB,AAAD;IAEvB,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,+CAA+C;QAC/C,OACE,KAAK,SAAS,KAAK,mBACnB,KAAK,SAAS,KAAK,kBACnB,KAAK,SAAS,KAAK;IAEvB;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAElB,gDAAgD;QAChD,OAAO,KAAK,SAAS,KAAK;IAC5B;IAEA,wEAAwE;IACxE,MAAM,0BAA0B;QAC9B,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE;IAEJ;IAEA,6CAA6C;IAC7C,MAAM,wBAAwB;QAC5B,sEAAsE;QACtE,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,KAChE,CAAC,CAAC;IAEN;IAEA,wDAAwD;IACxD,MAAM,MAAM,CACV,UACA,QACA,QAAuB,KAAK;QAE5B,OAAO,cAAc,UAAU,QAAQ;IACzC;IAEA,yDAAyD;IACzD,MAAM,mBAAmB,CACvB;QAMA,OAAO,YAAY,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC1D,cAAc,UAAU,QAAQ;IAEpC;IAEA,yDAAyD;IACzD,MAAM,oBAAoB,CACxB;QAMA,OAAO,YAAY,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC3D,cAAc,UAAU,QAAQ;IAEpC;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,iBAAiB;QAC5B;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/route-permissions.ts"], "sourcesContent": ["/**\r\n * Route Permission Mapping\r\n *\r\n * This file maps routes to the permissions required to access them.\r\n * Each key represents a route pattern, and the value is the permission required.\r\n */\r\n\r\nimport { NavigationPermission } from \"./navigation-permissions\";\r\n\r\nexport const routePermissions: Record<string, NavigationPermission> = {\r\n  // Dashboard routes\r\n  \"/dashboard\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/company\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/branch\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/finance\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/operations\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/stock\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/float\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/pos\": { resource: \"dashboard\", action: \"view\" },\r\n\r\n  // User management routes\r\n  \"/users\": { resource: \"users\", action: \"view\" },\r\n  \"/users/create\": { resource: \"users\", action: \"create\" },\r\n  \"/users/[id]\": { resource: \"users\", action: \"view\" },\r\n  \"/users/[id]/edit\": { resource: \"users\", action: \"update\" },\r\n\r\n  // Role management routes\r\n  \"/roles\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/create\": { resource: \"roles\", action: \"create\" },\r\n  \"/roles/[id]\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/[id]/edit\": { resource: \"roles\", action: \"update\" },\r\n\r\n  // RBAC routes\r\n  \"/rbac\": { resource: \"permissions\", action: \"manage\" },\r\n\r\n  // Branch management routes\r\n  \"/branches\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/create\": { resource: \"branches\", action: \"create\" },\r\n  \"/branches/[id]\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/[id]/edit\": { resource: \"branches\", action: \"update\" },\r\n\r\n  // Location management routes\r\n  \"/locations\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/locations/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n  \"/location-guides\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/location-guides/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n\r\n  // Employee management routes\r\n  \"/employees\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/create\": { resource: \"employees\", action: \"create\" },\r\n  \"/employees/[id]\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/[id]/edit\": { resource: \"employees\", action: \"update\" },\r\n\r\n  // Product management routes\r\n  \"/products\": { resource: \"products\", action: \"view\" },\r\n  \"/products/create\": { resource: \"products\", action: \"create\" },\r\n  \"/products/[id]\": { resource: \"products\", action: \"view\" },\r\n  \"/products/[id]/edit\": { resource: \"products\", action: \"update\" },\r\n\r\n  // Category management routes\r\n  \"/categories\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/create\": { resource: \"categories\", action: \"create\" },\r\n  \"/categories/[id]\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/[id]/edit\": { resource: \"categories\", action: \"update\" },\r\n\r\n  // Brand management routes\r\n  \"/brands\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/create\": { resource: \"brands\", action: \"create\" },\r\n  \"/brands/[id]\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/[id]/edit\": { resource: \"brands\", action: \"update\" },\r\n\r\n  // Inventory management routes\r\n  \"/inventory\": { resource: \"inventory\", action: \"view\" },\r\n  \"/inventory/transfers\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/bulk-transfer\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/reports\": { resource: \"inventory\", action: \"report\" },\r\n  \"/inventory/stock-cards\": { resource: \"inventory\", action: \"view\" },\r\n\r\n  // Banking routes\r\n  \"/banking\": { resource: \"banking\", action: \"view\" },\r\n  \"/banking/summary\": { resource: \"banking\", action: \"view\" },\r\n\r\n  // MPESA routes\r\n  \"/mpesa\": { resource: \"mpesa\", action: \"view\" },\r\n  \"/mpesa/transactions\": { resource: \"mpesa\", action: \"view\" },\r\n\r\n  // Float management routes\r\n  \"/float\": { resource: \"float\", action: \"view\" },\r\n  \"/float/movements\": { resource: \"float\", action: \"view\" },\r\n  \"/float/reconciliations\": { resource: \"float\", action: \"view\" },\r\n  \"/float/topups\": { resource: \"float\", action: \"view\" },\r\n\r\n  // DSA routes\r\n  \"/dsa\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/agents\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/assignments\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/reconciliations\": { resource: \"dsa\", action: \"view\" },\r\n\r\n  // Phone repairs routes\r\n  \"/phone-repairs\": { resource: \"phone_repairs\", action: \"view\" },\r\n  \"/phone-repairs/[id]\": { resource: \"phone_repairs\", action: \"view\" },\r\n\r\n  // Expense management routes\r\n  \"/expenses\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/create\": { resource: \"expenses\", action: \"create\" },\r\n  \"/expenses/[id]\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/[id]/edit\": { resource: \"expenses\", action: \"update\" },\r\n  \"/expense-analytics\": { resource: \"expenses\", action: \"view\" },\r\n\r\n  // Credit Note routes\r\n  \"/credit-notes\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/new\": { resource: \"invoices\", action: \"create\" },\r\n  \"/credit-notes/[id]\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/[id]/edit\": { resource: \"invoices\", action: \"update\" },\r\n\r\n  // Report routes\r\n  \"/reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-summary\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-item\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-category\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-employee\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-payment-type\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-banking\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/cash-status\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/running-balances\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-transactions\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/phone-repairs\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/expense-reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/dsa-sales\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/receipts\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/shifts\": { resource: \"reports\", action: \"view\" },\r\n\r\n  // Settings routes\r\n  \"/settings\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/system\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/company\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/payment-methods\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/health\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // Profile route\r\n  \"/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // POS routes\r\n  \"/pos\": { resource: \"pos\", action: \"view\" },\r\n  \"/pos/sessions\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]/edit\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/close\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/shift-closing\": {\r\n    resource: \"pos_sessions\",\r\n    action: \"view\",\r\n  },\r\n\r\n  // Sales routes\r\n  \"/sales\": { resource: \"sales\", action: \"view\" },\r\n  \"/sales/[id]\": { resource: \"sales\", action: \"view\" },\r\n\r\n  // Customer routes\r\n  \"/customers\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/create\": { resource: \"customers\", action: \"create\" },\r\n  \"/customers/[id]\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/[id]/edit\": { resource: \"customers\", action: \"update\" },\r\n\r\n  // Procurement routes\r\n  \"/procurement\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests/new\": { resource: \"procurement\", action: \"create\" },\r\n  \"/procurement/receipts\": { resource: \"procurement\", action: \"view\" },\r\n\r\n  // Supplier routes\r\n  \"/suppliers\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/create\": { resource: \"suppliers\", action: \"create\" },\r\n  \"/suppliers/[id]\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/[id]/edit\": { resource: \"suppliers\", action: \"update\" },\r\n\r\n  // Tenant routes (Super Admin only)\r\n  \"/tenants\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/create\": { resource: \"tenants\", action: \"create\" },\r\n  \"/tenants/[id]\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/[id]/edit\": { resource: \"tenants\", action: \"update\" },\r\n\r\n  // Super Admin routes\r\n  \"/super-admin\": { resource: \"tenants\", action: \"view\" },\r\n\r\n  // Debug routes\r\n  \"/permission-debug\": { resource: \"settings\", action: \"manage\" },\r\n};\r\n\r\n/**\r\n * Get the permission required for a route\r\n * @param route The route to check\r\n * @returns The permission required for the route, or null if no permission is required\r\n */\r\nexport function getRoutePermission(route: string): NavigationPermission | null {\r\n  // First try exact match\r\n  if (routePermissions[route]) {\r\n    return routePermissions[route];\r\n  }\r\n\r\n  // Then try dynamic routes\r\n  for (const [pattern, permission] of Object.entries(routePermissions)) {\r\n    if (pattern.includes(\"[\") && matchDynamicRoute(route, pattern)) {\r\n      return permission;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Check if a route matches a dynamic route pattern\r\n * @param route The route to check\r\n * @param pattern The pattern to match against\r\n * @returns Whether the route matches the pattern\r\n */\r\nfunction matchDynamicRoute(route: string, pattern: string): boolean {\r\n  const routeParts = route.split(\"/\");\r\n  const patternParts = pattern.split(\"/\");\r\n\r\n  if (routeParts.length !== patternParts.length) {\r\n    return false;\r\n  }\r\n\r\n  for (let i = 0; i < patternParts.length; i++) {\r\n    if (patternParts[i].startsWith(\"[\") && patternParts[i].endsWith(\"]\")) {\r\n      // This is a dynamic part, so it matches anything\r\n      continue;\r\n    }\r\n\r\n    if (patternParts[i] !== routeParts[i]) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAIM,MAAM,mBAAyD;IACpE,mBAAmB;IACnB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC7D,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,kBAAkB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,cAAc;IACd,SAAS;QAAE,UAAU;QAAe,QAAQ;IAAS;IAErD,2BAA2B;IAC3B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAClE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,2BAA2B;QAAE,UAAU;QAAa,QAAQ;IAAS;IACrE,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,8BAA8B;QAAE,UAAU;QAAa,QAAQ;IAAS;IAExE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,eAAe;QAAE,UAAU;QAAc,QAAQ;IAAO;IACxD,sBAAsB;QAAE,UAAU;QAAc,QAAQ;IAAS;IACjE,oBAAoB;QAAE,UAAU;QAAc,QAAQ;IAAO;IAC7D,yBAAyB;QAAE,UAAU;QAAc,QAAQ;IAAS;IAEpE,0BAA0B;IAC1B,WAAW;QAAE,UAAU;QAAU,QAAQ;IAAO;IAChD,kBAAkB;QAAE,UAAU;QAAU,QAAQ;IAAS;IACzD,gBAAgB;QAAE,UAAU;QAAU,QAAQ;IAAO;IACrD,qBAAqB;QAAE,UAAU;QAAU,QAAQ;IAAS;IAE5D,8BAA8B;IAC9B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAW;IACpE,4BAA4B;QAAE,UAAU;QAAa,QAAQ;IAAW;IACxE,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAChE,0BAA0B;QAAE,UAAU;QAAa,QAAQ;IAAO;IAElE,iBAAiB;IACjB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,oBAAoB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE1D,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,uBAAuB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAE3D,0BAA0B;IAC1B,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAO;IACxD,0BAA0B;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9D,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAErD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,eAAe;QAAE,UAAU;QAAO,QAAQ;IAAO;IACjD,oBAAoB;QAAE,UAAU;QAAO,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAO,QAAQ;IAAO;IAE1D,uBAAuB;IACvB,kBAAkB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAC9D,uBAAuB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAEnE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAChE,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAE7D,qBAAqB;IACrB,iBAAiB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACxD,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC7D,2BAA2B;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEpE,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,kCAAkC;QAAE,UAAU;QAAW,QAAQ;IAAO;IACxE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,wBAAwB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC9D,6BAA6B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACnE,+BAA+B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACrE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,4BAA4B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClE,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC5D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC3D,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEzD,kBAAkB;IAClB,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,6BAA6B;QAAE,UAAU;QAAY,QAAQ;IAAS;IACtE,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC3D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE3D,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAElD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,iBAAiB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IAC5D,sBAAsB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IACjE,2BAA2B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACxE,4BAA4B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACzE,oCAAoC;QAClC,UAAU;QACV,QAAQ;IACV;IAEA,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IAEnD,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAC1D,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IACnE,6BAA6B;QAAE,UAAU;QAAe,QAAQ;IAAS;IACzE,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAEnE,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,mCAAmC;IACnC,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAC3D,iBAAiB;QAAE,UAAU;QAAW,QAAQ;IAAO;IACvD,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAE9D,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEtD,eAAe;IACf,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;AAChE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,wBAAwB;IACxB,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,OAAO,gBAAgB,CAAC,MAAM;IAChC;IAEA,0BAA0B;IAC1B,KAAK,MAAM,CAAC,SAAS,WAAW,IAAI,OAAO,OAAO,CAAC,kBAAmB;QACpE,IAAI,QAAQ,QAAQ,CAAC,QAAQ,kBAAkB,OAAO,UAAU;YAC9D,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,KAAa,EAAE,OAAe;IACvD,MAAM,aAAa,MAAM,KAAK,CAAC;IAC/B,MAAM,eAAe,QAAQ,KAAK,CAAC;IAEnC,IAAI,WAAW,MAAM,KAAK,aAAa,MAAM,EAAE;QAC7C,OAAO;IACT;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,IAAI,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM;YAEpE;QACF;QAEA,IAAI,YAAY,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/role-guard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect, useState } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\nimport { getRoutePermission } from \"@/lib/route-permissions\";\r\nimport { hasRouteAccess, getDashboardByRole } from \"@/lib/rbac-config\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\nfunction mapActionToGrantAction(action: string): string {\r\n  switch (action) {\r\n    case \"view\":\r\n      return \"read:any\";\r\n    case \"create\":\r\n      return \"create:any\";\r\n    case \"update\":\r\n      return \"update:any\";\r\n    case \"delete\":\r\n      return \"delete:any\";\r\n    case \"manage\":\r\n      return \"update:any\"; // Manage maps to update:any\r\n    case \"transfer\":\r\n      return \"update:any\"; // Transfer maps to update:any\r\n    case \"report\":\r\n      return \"read:any\"; // Report maps to read:any\r\n    default:\r\n      return `${action}:any`;\r\n  }\r\n}\r\n\r\ninterface RoleGuardProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function RoleGuard({ children }: RoleGuardProps) {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const { hasPermission, isLoading: isPermissionsLoading } = usePermissions();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Track if this is the initial mount\r\n  const [isInitialMount, setIsInitialMount] = useState(true);\r\n\r\n  // Effect to mark initial mount as complete\r\n  useEffect(() => {\r\n    setIsInitialMount(false);\r\n  }, []);\r\n\r\n  // Effect to check access rights\r\n  useEffect(() => {\r\n    // Skip check if on auth pages\r\n    if (\r\n      pathname.startsWith(\"/login\") ||\r\n      pathname.startsWith(\"/forgot-password\") ||\r\n      pathname.startsWith(\"/reset-password\")\r\n    ) {\r\n      setLoading(\"role\", false);\r\n      return;\r\n    }\r\n\r\n    // Only show loading indicator on initial mount or when user data is loading\r\n    // and we haven't shown the loading screen before in this session\r\n    if (isInitialMount && isUserLoading && !hasShownLoading.role) {\r\n      setLoading(\"role\", true);\r\n      return;\r\n    } else {\r\n      setLoading(\"role\", false);\r\n    }\r\n\r\n    // If user data and permissions are loaded, check access rights\r\n    if (!isUserLoading && !isPermissionsLoading && user) {\r\n      // Safely access role_name with type checking\r\n      const roleName =\r\n        user && typeof user === \"object\" && \"role_name\" in user\r\n          ? user.role_name\r\n          : \"\";\r\n\r\n      // First try permission-based access control\r\n      const routePermission = getRoutePermission(pathname);\r\n\r\n      let hasAccess = true;\r\n      let accessMethod = \"default\";\r\n\r\n      // Use RBAC-based access control\r\n      hasAccess = hasRouteAccess(pathname, roleName);\r\n      accessMethod = \"rbac\";\r\n\r\n      // Add detailed debug logging\r\n      console.log(`[RoleGuard] Route: ${pathname}`);\r\n      console.log(`[RoleGuard] User role: ${roleName}`);\r\n      console.log(`[RoleGuard] Using RBAC-based access control`);\r\n      console.log(\r\n        `[RoleGuard] Access ${hasAccess ? \"GRANTED\" : \"DENIED\"} by RBAC check`\r\n      );\r\n\r\n      // If we have a permission mapping, check it too (for debugging purposes only)\r\n      if (routePermission) {\r\n        // Map the action to the format used in grants (e.g., \"view\" -> \"read:any\")\r\n        const grantAction = mapActionToGrantAction(routePermission.action);\r\n\r\n        // Check if user has the required permission (but don't use the result)\r\n        const permissionAccess = hasPermission(\r\n          routePermission.resource,\r\n          grantAction,\r\n          routePermission.scope\r\n        );\r\n\r\n        // Log the permission check result for debugging\r\n        console.log(\r\n          `[RoleGuard] Permission check (not used): ${\r\n            routePermission.resource\r\n          }:${routePermission.action}:${routePermission.scope || \"any\"}`\r\n        );\r\n        console.log(`[RoleGuard] Mapped to grant action: ${grantAction}`);\r\n        console.log(\r\n          `[RoleGuard] Permission would be ${\r\n            permissionAccess ? \"GRANTED\" : \"DENIED\"\r\n          } (for future reference)`\r\n        );\r\n      }\r\n\r\n      if (!hasAccess) {\r\n        console.log(\r\n          `[RoleGuard] Access denied for ${pathname}, redirecting to dashboard (method: ${accessMethod})`\r\n        );\r\n\r\n        // Redirect to the appropriate dashboard based on role using RBAC configuration\r\n        const dashboardRoute = getDashboardByRole(roleName);\r\n        console.log(\r\n          `[RoleGuard] Redirecting to ${dashboardRoute} for role ${roleName}`\r\n        );\r\n        router.replace(dashboardRoute);\r\n      }\r\n\r\n      // User data is loaded, we can stop checking and mark as shown\r\n      setLoading(\"role\", false);\r\n      markLoadingShown(\"role\");\r\n    } else if (!isUserLoading && !isPermissionsLoading) {\r\n      // No user data and not loading, stop checking\r\n      setLoading(\"role\", false);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    user,\r\n    isUserLoading,\r\n    isPermissionsLoading,\r\n    pathname,\r\n    router,\r\n    isInitialMount,\r\n    hasShownLoading.role,\r\n    hasPermission,\r\n  ]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in checking state\r\n  useEffect(() => {\r\n    let timeoutId: NodeJS.Timeout | null = null;\r\n\r\n    if (isLoading.role) {\r\n      timeoutId = setTimeout(() => {\r\n        setLoading(\"role\", false);\r\n        markLoadingShown(\"role\");\r\n      }, 1500); // 1.5 second timeout for better UX\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.role]);\r\n\r\n  // Show loading state only when actively checking role access\r\n  if (isLoading.role) {\r\n    return <LoadingScreen message=\"Checking access...\" />;\r\n  }\r\n\r\n  // Don't block rendering, the useEffect will handle redirects\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAaA;;;;CAIC,GACD,SAAS,uBAAuB,MAAc;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,cAAc,4BAA4B;QACnD,KAAK;YACH,OAAO,cAAc,8BAA8B;QACrD,KAAK;YACH,OAAO,YAAY,0BAA0B;QAC/C;YACE,OAAO,GAAG,OAAO,IAAI,CAAC;IAC1B;AACF;AAMO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EAAE,aAAa,EAAE,WAAW,oBAAoB,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD;IAEX,qCAAqC;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;IACpB,GAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,uBACpB,SAAS,UAAU,CAAC,oBACpB;YACA,WAAW,QAAQ;YACnB;QACF;QAEA,4EAA4E;QAC5E,iEAAiE;QACjE,IAAI,kBAAkB,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;YAC5D,WAAW,QAAQ;YACnB;QACF,OAAO;YACL,WAAW,QAAQ;QACrB;QAEA,+DAA+D;QAC/D,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,MAAM;YACnD,6CAA6C;YAC7C,MAAM,WACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;YAEN,4CAA4C;YAC5C,MAAM,kBAAkB,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;YAE3C,IAAI,YAAY;YAChB,IAAI,eAAe;YAEnB,gCAAgC;YAChC,YAAY,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;YACrC,eAAe;YAEf,6BAA6B;YAC7B,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;YAC5C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU;YAChD,QAAQ,GAAG,CAAC,CAAC,2CAA2C,CAAC;YACzD,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,YAAY,YAAY,SAAS,cAAc,CAAC;YAGxE,8EAA8E;YAC9E,IAAI,iBAAiB;gBACnB,2EAA2E;gBAC3E,MAAM,cAAc,uBAAuB,gBAAgB,MAAM;gBAEjE,uEAAuE;gBACvE,MAAM,mBAAmB,cACvB,gBAAgB,QAAQ,EACxB,aACA,gBAAgB,KAAK;gBAGvB,gDAAgD;gBAChD,QAAQ,GAAG,CACT,CAAC,yCAAyC,EACxC,gBAAgB,QAAQ,CACzB,CAAC,EAAE,gBAAgB,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,IAAI,OAAO;gBAEhE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,aAAa;gBAChE,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAC/B,mBAAmB,YAAY,SAChC,uBAAuB,CAAC;YAE7B;YAEA,IAAI,CAAC,WAAW;gBACd,QAAQ,GAAG,CACT,CAAC,8BAA8B,EAAE,SAAS,oCAAoC,EAAE,aAAa,CAAC,CAAC;gBAGjG,+EAA+E;gBAC/E,MAAM,iBAAiB,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE;gBAC1C,QAAQ,GAAG,CACT,CAAC,2BAA2B,EAAE,eAAe,UAAU,EAAE,UAAU;gBAErE,OAAO,OAAO,CAAC;YACjB;YAEA,8DAA8D;YAC9D,WAAW,QAAQ;YACnB,iBAAiB;QACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;YAClD,8CAA8C;YAC9C,WAAW,QAAQ;QACrB;IACA,uDAAuD;IACzD,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,IAAI;QACpB;KACD;IAED,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAmC;QAEvC,IAAI,UAAU,IAAI,EAAE;YAClB,YAAY,WAAW;gBACrB,WAAW,QAAQ;gBACnB,iBAAiB;YACnB,GAAG,OAAO,mCAAmC;QAC/C;QAEA,OAAO;YACL,IAAI,WAAW,aAAa;QAC9B;IACA,uDAAuD;IACzD,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6DAA6D;IAC7D,IAAI,UAAU,IAAI,EAAE;QAClB,qBAAO,8OAAC,sJAAA,CAAA,gBAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,6DAA6D;IAC7D,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { VariantProps, cva } from \"class-variance-authority\";\r\nimport { PanelLeftIcon } from \"lucide-react\";\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = \"16rem\";\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\";\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\";\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\";\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\";\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\";\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  // Use useMemo to prevent recreating the button on every render\r\n  const button = React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, variant, className, props]);\r\n\r\n  // Use useMemo for tooltipContent to prevent recalculation on every render\r\n  const tooltipContent = React.useMemo(() => {\r\n    if (!tooltip) return null;\r\n    return typeof tooltip === \"string\" ? { children: tooltip } : tooltip;\r\n  }, [tooltip]);\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  // Use useMemo for the hidden prop to prevent recalculation on every render\r\n  const isHidden = React.useMemo(() => {\r\n    return state !== \"collapsed\" || isMobile;\r\n  }, [state, isMobile]);\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={isHidden}\r\n        {...tooltipContent}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, className, showOnHover, props]);\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [className, props]);\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean;\r\n  size?: \"sm\" | \"md\";\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, className, props]);\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AApBA;;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,8OAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,+rBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,+DAA+D;IAC/D,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBAC3B,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,aAAW;YACX,eAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;gBAAE;gBAAS;YAAK,IAAI;YAC3D,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAM;QAAU;QAAS;QAAW;KAAM;IAEpD,0EAA0E;IAC1E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACnC,IAAI,CAAC,SAAS,OAAO;QACrB,OAAO,OAAO,YAAY,WAAW;YAAE,UAAU;QAAQ,IAAI;IAC/D,GAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,2EAA2E;IAC3E,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC7B,OAAO,UAAU,eAAe;IAClC,GAAG;QAAC;QAAO;KAAS;IAEpB,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ;gBACP,GAAG,cAAc;;;;;;;;;;;;AAI1B;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;YAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAW;QAAa;KAAM;AAC1C;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAW;KAAM;AACvB;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,aAAW;YACX,eAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAM;QAAU;QAAW;KAAM;AAC7C", "debugId": null}}, {"offset": {"line": 1881, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/navigation-link.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { ReactNode, MouseEvent, useMemo } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface NavigationLinkProps {\r\n  href: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;\r\n  replace?: boolean;\r\n  prefetch?: boolean;\r\n  scroll?: boolean;\r\n  exact?: boolean;\r\n}\r\n\r\n/**\r\n * NavigationLink component that ensures client-side navigation\r\n * This component wraps Next.js Link component and provides a consistent way to navigate\r\n */\r\nexport function NavigationLink({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(className)}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n      aria-current={isActive ? \"page\" : undefined}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n\r\n/**\r\n * NavigationButton component that looks like a button but navigates like a link\r\n */\r\nexport function NavigationButton({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(\r\n        \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAsBO,SAAS,eAAe,EAC7B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;IACzD,GAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;QACb,gBAAc,WAAW,SAAS;kBAEjC;;;;;;AAGP;AAKO,SAAS,iBAAiB,EAC/B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;IACzD,GAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;QAEF,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;kBAEZ;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,uKAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2022, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-main.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\nimport { ChevronRight, CircleIcon, type LucideIcon } from \"lucide-react\";\r\n\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: {\r\n    title: string;\r\n    url: string;\r\n    icon?: LucideIcon;\r\n    isActive?: boolean;\r\n    items?: {\r\n      title: string;\r\n      url: string;\r\n    }[];\r\n  }[];\r\n}) {\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          // Special case for Dashboard - make it a direct link\r\n          if (item.title === \"Dashboard\") {\r\n            return (\r\n              <SidebarMenuItem key={item.title}>\r\n                <SidebarMenuButton\r\n                  asChild\r\n                  tooltip={item.title}\r\n                  isActive={item.isActive}\r\n                  className=\"nav-main-item\"\r\n                >\r\n                  <NavigationLink href={item.url}>\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                  </NavigationLink>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          }\r\n\r\n          // For all other items, use the collapsible dropdown\r\n          return (\r\n            <Collapsible\r\n              key={item.title}\r\n              asChild\r\n              defaultOpen={item.isActive}\r\n              className=\"group/collapsible mb-2\"\r\n            >\r\n              <SidebarMenuItem>\r\n                <CollapsibleTrigger asChild>\r\n                  <SidebarMenuButton\r\n                    tooltip={item.title}\r\n                    isActive={item.isActive}\r\n                    className=\"nav-main-item\"\r\n                  >\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                    <ChevronRight className=\"ml-auto h-2 w-2 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 nav-main-item-chevron\" />\r\n                  </SidebarMenuButton>\r\n                </CollapsibleTrigger>\r\n                <CollapsibleContent>\r\n                  <SidebarMenuSub className=\"nav-main-sub mt-2\">\r\n                    {item.items?.map((subItem) => (\r\n                      <SidebarMenuSubItem key={subItem.title}>\r\n                        <SidebarMenuSubButton asChild>\r\n                          <NavigationLink href={subItem.url} exact={true}>\r\n                            {/* No circle icon for child items */}\r\n                            <span className=\"text-xs\">{subItem.title}</span>\r\n                          </NavigationLink>\r\n                        </SidebarMenuSubButton>\r\n                      </SidebarMenuSubItem>\r\n                    ))}\r\n                  </SidebarMenuSub>\r\n                </CollapsibleContent>\r\n              </SidebarMenuItem>\r\n            </Collapsible>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAKA;AAVA;;;;;;AAoBO,SAAS,QAAQ,EACtB,KAAK,EAYN;IACC,qBACE,8OAAC,mIAAA,CAAA,eAAY;kBACX,cAAA,8OAAC,mIAAA,CAAA,cAAW;sBACT,MAAM,GAAG,CAAC,CAAC;gBACV,qDAAqD;gBACrD,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,qBACE,8OAAC,mIAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,SAAS,KAAK,KAAK;4BACnB,UAAU,KAAK,QAAQ;4BACvB,WAAU;sCAEV,cAAA,8OAAC,8IAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,GAAG;;kDAC5B,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CAClC,KAAK,QAAQ,kBACZ,8OAAC,0MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAG1B,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAfG,KAAK,KAAK;;;;;gBAqBpC;gBAEA,oDAAoD;gBACpD,qBACE,8OAAC,uIAAA,CAAA,cAAW;oBAEV,OAAO;oBACP,aAAa,KAAK,QAAQ;oBAC1B,WAAU;8BAEV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;;0CACd,8OAAC,uIAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;oCAChB,SAAS,KAAK,KAAK;oCACnB,UAAU,KAAK,QAAQ;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDAClC,KAAK,QAAQ,kBACZ,8OAAC,0MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAG1B,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;sDAEb,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG5B,8OAAC,uIAAA,CAAA,qBAAkB;0CACjB,cAAA,8OAAC,mIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,8OAAC,mIAAA,CAAA,qBAAkB;sDACjB,cAAA,8OAAC,mIAAA,CAAA,uBAAoB;gDAAC,OAAO;0DAC3B,cAAA,8OAAC,8IAAA,CAAA,iBAAc;oDAAC,MAAM,QAAQ,GAAG;oDAAE,OAAO;8DAExC,cAAA,8OAAC;wDAAK,WAAU;kEAAW,QAAQ,KAAK;;;;;;;;;;;;;;;;2CAJrB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;mBA3BzC,KAAK,KAAK;;;;;YAyCrB;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 2234, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2286, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2548, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-user.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Chev<PERSON>UpDown, LogOut } from \"lucide-react\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavUser({\r\n  user,\r\n}: {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n  };\r\n}) {\r\n  const { isMobile } = useSidebar();\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground nav-user\"\r\n            >\r\n              <Avatar className=\"h-6 w-6 rounded-lg nav-user-avatar\">\r\n                <AvatarImage src={user.avatar} alt={user.name} />\r\n                <AvatarFallback className=\"rounded-lg text-xs\">\r\n                  {user.name.charAt(0)}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left leading-tight nav-user-details\">\r\n                <span className=\"truncate text-xs font-medium\">\r\n                  {user.name}\r\n                </span>\r\n                <span className=\"truncate text-[10px]\">{user.email}</span>\r\n              </div>\r\n              <ChevronsUpDown className=\"ml-auto h-3 w-3 nav-user-chevron\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar} alt={user.name} />\r\n                  <AvatarFallback className=\"rounded-lg\">CN</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name}</span>\r\n                  <span className=\"truncate text-xs\">{user.email}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem>\r\n                <BadgeCheck className=\"mr-2 h-3.5 w-3.5\" />\r\n                <span className=\"text-xs\">Account</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem>\r\n              <LogOut className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"text-xs\">Log out</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AASA;AAdA;;;;;;AAqBO,SAAS,QAAQ,EACtB,IAAI,EAOL;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAE9B,qBACE,8OAAC,mIAAA,CAAA,cAAW;kBACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAAwB,KAAK,KAAK;;;;;;;;;;;;8CAEpD,8OAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,8OAAC,4IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,8OAAC;oDAAK,WAAU;8DAAoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIpD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,oBAAiB;0CAChB,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;;sDACf,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,mBAAgB;;kDACf,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 2807, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport \"@/components/ui/sidebar-collapsed.css\";\r\nimport \"@/components/ui/sidebar-compact.css\";\r\nimport {\r\n  Banknote,\r\n  BarChart3 as BarChart3Icon,\r\n  Building as BuildingIcon,\r\n  FileText,\r\n  GalleryVerticalEnd,\r\n  Home as HomeIcon,\r\n  Landmark,\r\n  Package,\r\n  Smartphone,\r\n  UserCheck,\r\n} from \"lucide-react\";\r\n\r\nimport { NavMain } from \"@/components/nav-main\";\r\nimport { NavUser } from \"@/components/nav-user\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n// Import usePermissions for future use\r\n// import { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\n// import { getRoutePermission } from \"@/lib/route-permissions\";\r\n// import { navigationPermissions } from \"@/lib/navigation-permissions\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  hasRouteAccess,\r\n  isNavi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n} from \"@/lib/rbac-config\";\r\n\r\ntype NavItem = {\r\n  title: string;\r\n  url: string;\r\n  icon?: any;\r\n  isActive?: boolean;\r\n  items?: { title: string; url: string }[];\r\n};\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * This function is commented out for MVP, will be used in the future\r\n * when the permission system is fully implemented\r\n *\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\n// function mapActionToGrantAction(action: string): string {\r\n//   switch (action) {\r\n//     case \"view\":\r\n//       return \"read:any\";\r\n//     case \"create\":\r\n//       return \"create:any\";\r\n//     case \"update\":\r\n//       return \"update:any\";\r\n//     case \"delete\":\r\n//       return \"delete:any\";\r\n//     case \"manage\":\r\n//       return \"update:any\"; // Manage maps to update:any\r\n//     case \"transfer\":\r\n//       return \"update:any\"; // Transfer maps to update:any\r\n//     case \"report\":\r\n//       return \"read:any\"; // Report maps to read:any\r\n//     default:\r\n//       return `${action}:any`;\r\n//   }\r\n// }\r\n\r\nexport function AppSidebar() {\r\n  const { data: user } = useCurrentUser();\r\n  const pathname = usePathname();\r\n  // Permissions will be used in the future\r\n  // const { hasPermission, permissions } = usePermissions();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  // Define all navigation items\r\n  const getAllNavigationItems = (): NavItem[] => [\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/dashboard\",\r\n      icon: HomeIcon,\r\n      isActive: pathname === \"/dashboard\" || pathname === \"/\",\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      url: \"/reports/sales-summary\",\r\n      icon: BarChart3Icon,\r\n      isActive:\r\n        pathname.startsWith(\"/reports\") ||\r\n        pathname.startsWith(\"/expense-analytics\"),\r\n      items: [\r\n        {\r\n          title: \"Sales Summary\",\r\n          url: \"/reports/sales-summary\",\r\n        },\r\n        {\r\n          title: \"Sales by Item\",\r\n          url: \"/reports/sales-by-item\",\r\n        },\r\n        {\r\n          title: \"Sales by Category\",\r\n          url: \"/reports/sales-by-category\",\r\n        },\r\n        {\r\n          title: \"Current Stock Levels\",\r\n          url: \"/reports/current-stock-levels\",\r\n        },\r\n        {\r\n          title: \"Stock History\",\r\n          url: \"/reports/stock-history\",\r\n        },\r\n        {\r\n          title: \"Banking Transactions\",\r\n          url: \"/reports/banking-transactions\",\r\n        },\r\n        {\r\n          title: \"Tax Report\",\r\n          url: \"/reports/tax-report\",\r\n        },\r\n        {\r\n          title: \"Banking Summary\",\r\n          url: \"/banking?tab=summary\",\r\n        },\r\n        {\r\n          title: \"M-Pesa Transactions\",\r\n          url: \"/reports/mpesa-transactions\",\r\n        },\r\n        {\r\n          title: \"Running Balances\",\r\n          url: \"/reports/running-balances\",\r\n        },\r\n        {\r\n          title: \"Cash Status\",\r\n          url: \"/reports/cash-status\",\r\n        },\r\n        {\r\n          title: \"Cash Float\",\r\n          url: \"/reports/cash-float\",\r\n        },\r\n        {\r\n          title: \"DSA Sales\",\r\n          url: \"/reports/dsa-sales\",\r\n        },\r\n        {\r\n          title: \"Receipts\",\r\n          url: \"/reports/receipts\",\r\n        },\r\n        {\r\n          title: \"Shifts\",\r\n          url: \"/reports/shifts\",\r\n        },\r\n        {\r\n          title: \"Phone Repairs\",\r\n          url: \"/reports/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"Stock Valuation\",\r\n          url: \"/inventory/valuation\",\r\n        },\r\n        {\r\n          title: \"Stock Aging\",\r\n          url: \"/reports/stock-aging\",\r\n        },\r\n        {\r\n          title: \"Stock Movement\",\r\n          url: \"/reports/stock-movement\",\r\n        },\r\n        {\r\n          title: \"Expense Analytics\",\r\n          url: \"/expense-analytics\",\r\n        },\r\n        {\r\n          title: \"Customers\",\r\n          url: \"/customers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Administration\",\r\n      url: \"/users\",\r\n      icon: BuildingIcon,\r\n      isActive:\r\n        pathname.startsWith(\"/users\") ||\r\n        pathname.startsWith(\"/roles\") ||\r\n        pathname.startsWith(\"/rbac\") ||\r\n        pathname.startsWith(\"/branches\") ||\r\n        pathname.startsWith(\"/locations\") ||\r\n        pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        // Tenants menu item removed\r\n        {\r\n          title: \"Users\",\r\n          url: \"/users\",\r\n        },\r\n        {\r\n          title: \"Roles\",\r\n          url: \"/roles\",\r\n        },\r\n        {\r\n          title: \"RBAC\",\r\n          url: \"/rbac\",\r\n        },\r\n        {\r\n          title: \"Branches\",\r\n          url: \"/branches\",\r\n        },\r\n        {\r\n          title: \"Locations\",\r\n          url: \"/locations\",\r\n        },\r\n        {\r\n          title: \"Employees\",\r\n          url: \"/employees\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Stock & Inventory\",\r\n      url: \"/products\",\r\n      icon: Package,\r\n      isActive:\r\n        pathname.startsWith(\"/products\") ||\r\n        pathname.startsWith(\"/categories\") ||\r\n        pathname.startsWith(\"/brands\") ||\r\n        pathname.startsWith(\"/inventory\") ||\r\n        pathname.includes(\"/inventory/stock-cards\") ||\r\n        pathname.includes(\"/inventory/purchases\"),\r\n      items: [\r\n        {\r\n          title: \"Products\",\r\n          url: \"/products\",\r\n        },\r\n        {\r\n          title: \"Categories\",\r\n          url: \"/categories\",\r\n        },\r\n        {\r\n          title: \"Brands\",\r\n          url: \"/brands\",\r\n        },\r\n        {\r\n          title: \"Stock levels\",\r\n          url: \"/inventory\",\r\n        },\r\n        // Commented out Stock Locations as requested\r\n        // {\r\n        //   title: \"Stock Locations\",\r\n        //   url: \"/inventory/locations\",\r\n        // },\r\n        {\r\n          title: \"Purchases\",\r\n          url: \"/inventory/purchases\",\r\n        },\r\n        {\r\n          title: \"Create Purchase\",\r\n          url: \"/inventory/purchases/new\",\r\n        },\r\n        {\r\n          title: \"Stock Requests\",\r\n          url: \"/inventory/stock-requests\",\r\n        },\r\n        {\r\n          title: \"Stock Transfers\",\r\n          url: \"/inventory/transfers\",\r\n        },\r\n        {\r\n          title: \"Make Stock Transfer\",\r\n          url: \"/inventory/bulk-transfer\",\r\n        },\r\n        {\r\n          title: \"Stock Transfer History\",\r\n          url: \"/inventory/bulk-transfers\",\r\n        },\r\n        // {\r\n        //   title: \"Stock Cards\",\r\n        //   url: \"/inventory/stock-cards\",\r\n        // },\r\n        {\r\n          title: \"Inventory Reports\",\r\n          url: \"/inventory/reports\",\r\n        },\r\n        {\r\n          title: \"Excel Import/Export\",\r\n          url: \"/inventory/excel\",\r\n        },\r\n        {\r\n          title: \"Price List\",\r\n          url: \"/inventory/price-list\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Float Management\",\r\n      url: \"/float\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/float\"),\r\n      items: [\r\n        {\r\n          title: \"Float Dashboard\",\r\n          url: \"/float\",\r\n        },\r\n        {\r\n          title: \"Float Movements\",\r\n          url: \"/float/movements\",\r\n        },\r\n        {\r\n          title: \"Float Reconciliations\",\r\n          url: \"/float/reconciliations\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Banking Management\",\r\n      url: \"/banking\",\r\n      icon: Landmark,\r\n      isActive: pathname.startsWith(\"/banking\"),\r\n      items: [\r\n        {\r\n          title: \"Banking Records\",\r\n          url: \"/banking\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Expenses Management\",\r\n      url: \"/expenses\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/expenses\"),\r\n      items: [\r\n        {\r\n          title: \"Expenses\",\r\n          url: \"/expenses\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Invoice Management\",\r\n      url: \"/invoices\",\r\n      icon: FileText,\r\n      isActive:\r\n        pathname.startsWith(\"/invoices\") ||\r\n        pathname.startsWith(\"/credit-notes\"),\r\n      items: [\r\n        {\r\n          title: \"All Invoices\",\r\n          url: \"/invoices\",\r\n        },\r\n        {\r\n          title: \"Credit Notes\",\r\n          url: \"/credit-notes\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"DSA Management\",\r\n      url: \"/dsa\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/dsa\"),\r\n      items: [\r\n        {\r\n          title: \"DSA Dashboard\",\r\n          url: \"/dsa\",\r\n        },\r\n        {\r\n          title: \"DSA Agents\",\r\n          url: \"/dsa/customers\",\r\n        },\r\n        {\r\n          title: \"Stock Assignments\",\r\n          url: \"/dsa/assignments\",\r\n        },\r\n        // Commented out DSA Reconciliations as requested\r\n        // {\r\n        //   title: \"DSA Reconciliations\",\r\n        //   url: \"/dsa/reconciliations\",\r\n        // },\r\n      ],\r\n    },\r\n    // {\r\n    //   title: \"Procurement\",\r\n    //   url: \"/procurement\",\r\n    //   icon: ShoppingCart,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/procurement\") ||\r\n    //     pathname.startsWith(\"/suppliers\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"Procurement Dashboard\",\r\n    //       url: \"/procurement\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Procurement Requests\",\r\n    //       url: \"/procurement/requests\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Create Request\",\r\n    //       url: \"/procurement/requests/new\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Procurement Receipts\",\r\n    //       url: \"/procurement/receipts\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Suppliers\",\r\n    //       url: \"/suppliers\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    {\r\n      title: \"Phone Repairs\",\r\n      url: \"/phone-repairs\",\r\n      icon: Smartphone,\r\n      isActive:\r\n        pathname === \"/phone-repairs\" || pathname.startsWith(\"/phone-repairs/\")\r\n          ? true\r\n          : false,\r\n      items: [\r\n        {\r\n          title: \"All Repairs\",\r\n          url: \"/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"New Repair\",\r\n          url: \"/phone-repairs/new\",\r\n        },\r\n      ],\r\n    },\r\n    // Settings section completely hidden for production - not implemented\r\n    // {\r\n    //   title: \"Settings\",\r\n    //   url: \"/settings\",\r\n    //   icon: Settings,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/settings\") || pathname.startsWith(\"/profile\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"System Settings\",\r\n    //       url: \"/settings/system\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Company Settings\",\r\n    //       url: \"/settings/company\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Payment Methods\",\r\n    //       url: \"/settings/payment-methods\",\r\n    //     },\r\n    //     {\r\n    //       title: \"System Health\",\r\n    //       url: \"/settings/health\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Profile\",\r\n    //       url: \"/profile\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    // Permissions Demo section removed\r\n  ];\r\n\r\n  // Filter navigation items based on RBAC configuration\r\n  const getFilteredNavigationItemsByRole = (): NavItem[] => {\r\n    const allItems = getAllNavigationItems();\r\n\r\n    console.log(\r\n      `[Navigation] ${userRoleName} role detected - using RBAC-based filtering`\r\n    );\r\n\r\n    // Filter navigation items based on RBAC configuration\r\n    const filteredItems = allItems.filter((item) => {\r\n      // Check if the entire navigation group should be hidden for this role\r\n      if (isNavigationItemHidden(item.title, userRoleName)) {\r\n        console.log(\r\n          `[Navigation] Hiding section \"${item.title}\" for role ${userRoleName}`\r\n        );\r\n        return false;\r\n      }\r\n\r\n      // Check if user has access to the main route of this navigation item\r\n      if (!hasRouteAccess(item.url, userRoleName)) {\r\n        console.log(\r\n          `[Navigation] No route access to \"${item.url}\" for role ${userRoleName}`\r\n        );\r\n        return false;\r\n      }\r\n\r\n      console.log(\r\n        `[Navigation] Allowing section \"${item.title}\" for role ${userRoleName}`\r\n      );\r\n      return true;\r\n    });\r\n\r\n    // Filter subitems based on RBAC configuration\r\n    const itemsWithFilteredSubitems = filteredItems.map((item) => {\r\n      // If the item has subitems, filter them based on RBAC\r\n      if (item.items && item.items.length > 0) {\r\n        console.log(\r\n          `[Navigation] Checking subitems for \"${item.title}\" section`\r\n        );\r\n\r\n        const filteredSubItems = item.items.filter((subItem) => {\r\n          // Check if user has route access for this subitem\r\n          if (!hasRouteAccess(subItem.url, userRoleName)) {\r\n            console.log(\r\n              `[Navigation] No route access to \"${subItem.url}\" for role ${userRoleName}`\r\n            );\r\n            return false;\r\n          }\r\n\r\n          // Special handling for RBAC items - only super_admin can see them\r\n          if (subItem.title === \"RBAC\" || subItem.url.includes(\"rbac\")) {\r\n            return userRoleName === ROLES.SUPER_ADMIN;\r\n          }\r\n\r\n          console.log(\r\n            `[Navigation] Allowing subitem \"${subItem.title}\" for role ${userRoleName}`\r\n          );\r\n          return true;\r\n        });\r\n\r\n        // Update the item's subitems with the filtered list\r\n        item.items = filteredSubItems;\r\n      }\r\n\r\n      return item;\r\n    });\r\n\r\n    // Only return items that have at least one subitem (if they had subitems to begin with)\r\n    const result = itemsWithFilteredSubitems.filter(\r\n      (item) => !item.items || item.items.length > 0\r\n    );\r\n\r\n    return result;\r\n  };\r\n\r\n  // Filter navigation items based on permissions (for future use)\r\n  // This function is commented out for MVP, will be implemented in the future\r\n  // when the permission system is fully implemented\r\n\r\n  // Sample data for the sidebar\r\n  const data = {\r\n    user: {\r\n      name: user?.name || \"User\",\r\n      email: user?.email || \"<EMAIL>\",\r\n      avatar: \"/placeholder-avatar.jpg\",\r\n    },\r\n    teams: [\r\n      {\r\n        name: teamData.name,\r\n        logo: GalleryVerticalEnd,\r\n        plan: teamData.plan,\r\n      },\r\n    ],\r\n    // For MVP, use role-based filtering\r\n    navMain: getFilteredNavigationItemsByRole(),\r\n    // For future: navMain: getFilteredNavigationItems(),\r\n  };\r\n\r\n  const { state, isMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\" && !isMobile;\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"h-full flex flex-col sidebar-compact\",\r\n        isCollapsed && \"sidebar-collapsed\"\r\n      )}\r\n    >\r\n      <div className=\"flex-1 overflow-y-auto py-2\">\r\n        <div className={cn(\"px-3\", isCollapsed && \"px-2\")}>\r\n          <NavMain items={data.navMain} />\r\n        </div>\r\n      </div>\r\n      <div className={cn(\"p-3 border-t\", isCollapsed && \"p-2\")}>\r\n        <NavUser user={data.user} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,0EAA0E;AAC1E,gEAAgE;AAChE,wEAAwE;AACxE;AACA;AACA;AA3BA;;;;;;;;;;;;AAsEO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,yCAAyC;IACzC,2DAA2D;IAE3D,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,8BAA8B;IAC9B,MAAM,wBAAwB,IAAiB;YAC7C;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,mMAAA,CAAA,OAAQ;gBACd,UAAU,aAAa,gBAAgB,aAAa;YACtD;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,kNAAA,CAAA,YAAa;gBACnB,UACE,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAY;gBAClB,UACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL,4BAA4B;oBAC5B;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,wMAAA,CAAA,UAAO;gBACb,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,QAAQ,CAAC,6BAClB,SAAS,QAAQ,CAAC;gBACpB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,6CAA6C;oBAC7C,IAAI;oBACJ,8BAA8B;oBAC9B,iCAAiC;oBACjC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,IAAI;oBACJ,0BAA0B;oBAC1B,mCAAmC;oBACnC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,8MAAA,CAAA,WAAQ;gBACd,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,gNAAA,CAAA,YAAS;gBACf,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBAMD;YACH;YACA,IAAI;YACJ,0BAA0B;YAC1B,yBAAyB;YACzB,wBAAwB;YACxB,cAAc;YACd,6CAA6C;YAC7C,yCAAyC;YACzC,aAAa;YACb,QAAQ;YACR,wCAAwC;YACxC,6BAA6B;YAC7B,SAAS;YACT,QAAQ;YACR,uCAAuC;YACvC,sCAAsC;YACtC,SAAS;YACT,QAAQ;YACR,iCAAiC;YACjC,0CAA0C;YAC1C,SAAS;YACT,QAAQ;YACR,uCAAuC;YACvC,sCAAsC;YACtC,SAAS;YACT,QAAQ;YACR,4BAA4B;YAC5B,2BAA2B;YAC3B,SAAS;YACT,OAAO;YACP,KAAK;YACL;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,8MAAA,CAAA,aAAU;gBAChB,UACE,aAAa,oBAAoB,SAAS,UAAU,CAAC,qBACjD,OACA;gBACN,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;SAgCD;IAED,sDAAsD;IACtD,MAAM,mCAAmC;QACvC,MAAM,WAAW;QAEjB,QAAQ,GAAG,CACT,CAAC,aAAa,EAAE,aAAa,2CAA2C,CAAC;QAG3E,sDAAsD;QACtD,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC;YACrC,sEAAsE;YACtE,IAAI,CAAA,GAAA,4HAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,KAAK,EAAE,eAAe;gBACpD,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,cAAc;gBAExE,OAAO;YACT;YAEA,qEAAqE;YACrE,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG,EAAE,eAAe;gBAC3C,QAAQ,GAAG,CACT,CAAC,iCAAiC,EAAE,KAAK,GAAG,CAAC,WAAW,EAAE,cAAc;gBAE1E,OAAO;YACT;YAEA,QAAQ,GAAG,CACT,CAAC,+BAA+B,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,cAAc;YAE1E,OAAO;QACT;QAEA,8CAA8C;QAC9C,MAAM,4BAA4B,cAAc,GAAG,CAAC,CAAC;YACnD,sDAAsD;YACtD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACvC,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;gBAG9D,MAAM,mBAAmB,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,kDAAkD;oBAClD,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG,EAAE,eAAe;wBAC9C,QAAQ,GAAG,CACT,CAAC,iCAAiC,EAAE,QAAQ,GAAG,CAAC,WAAW,EAAE,cAAc;wBAE7E,OAAO;oBACT;oBAEA,kEAAkE;oBAClE,IAAI,QAAQ,KAAK,KAAK,UAAU,QAAQ,GAAG,CAAC,QAAQ,CAAC,SAAS;wBAC5D,OAAO,iBAAiB,4HAAA,CAAA,QAAK,CAAC,WAAW;oBAC3C;oBAEA,QAAQ,GAAG,CACT,CAAC,+BAA+B,EAAE,QAAQ,KAAK,CAAC,WAAW,EAAE,cAAc;oBAE7E,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,KAAK,KAAK,GAAG;YACf;YAEA,OAAO;QACT;QAEA,wFAAwF;QACxF,MAAM,SAAS,0BAA0B,MAAM,CAC7C,CAAC,OAAS,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG;QAG/C,OAAO;IACT;IAEA,gEAAgE;IAChE,4EAA4E;IAC5E,kDAAkD;IAElD,8BAA8B;IAC9B,MAAM,OAAO;QACX,MAAM;YACJ,MAAM,MAAM,QAAQ;YACpB,OAAO,MAAM,SAAS;YACtB,QAAQ;QACV;QACA,OAAO;YACL;gBACE,MAAM,SAAS,IAAI;gBACnB,MAAM,sOAAA,CAAA,qBAAkB;gBACxB,MAAM,SAAS,IAAI;YACrB;SACD;QACD,oCAAoC;QACpC,SAAS;IAEX;IAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACrC,MAAM,cAAc,UAAU,eAAe,CAAC;IAE9C,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wCACA,eAAe;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,eAAe;8BACxC,cAAA,8OAAC,iIAAA,CAAA,UAAO;wBAAC,OAAO,KAAK,OAAO;;;;;;;;;;;;;;;;0BAGhC,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,eAAe;0BAChD,cAAA,8OAAC,iIAAA,CAAA,UAAO;oBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;AAIhC", "debugId": null}}, {"offset": {"line": 3327, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/team-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// import { GalleryVerticalEnd } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n\r\nexport function TeamHeader() {\r\n  const { data: user } = useCurrentUser();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <div className=\"bg-white text-primary flex aspect-square size-6 items-center justify-center rounded-lg\">\r\n        {/* <GalleryVerticalEnd className=\"size-3\" /> */}\r\n        <Image\r\n          src={\"/images/simbatelecomlogo.png\"}\r\n          alt=\"Simba Telecom Logo\"\r\n          className=\"object-contain\"\r\n          width={16}\r\n          height={16}\r\n        />\r\n      </div>\r\n      <div className=\"grid flex-1 text-left leading-tight\">\r\n        <span className=\"truncate text-sm font-semibold text-primary-foreground\">\r\n          {teamData.name}\r\n        </span>\r\n        <span className=\"truncate text-xs text-primary-foreground/90 font-medium\">\r\n          {teamData.plan}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,qDAAqD;AACrD;AACA;AAJA;;;;AAMO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAEpC,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,WAAU;oBACV,OAAO;oBACP,QAAQ;;;;;;;;;;;0BAGZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;kCAEhB,8OAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;;;;;;;;;;;;;AAKxB", "debugId": null}}, {"offset": {"line": 3429, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/search-button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearch } from \"@/components/providers/search-provider\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\n\r\nexport function SearchButton() {\r\n  const { openSearch } = useSearch();\r\n\r\n  return (\r\n    <Button\r\n      variant=\"outline\"\r\n      size=\"sm\"\r\n      onClick={openSearch}\r\n      className=\"relative h-9 w-full justify-start bg-background text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-64\"\r\n    >\r\n      <span className=\"hidden lg:inline-flex\">Search anything...</span>\r\n      <span className=\"inline-flex lg:hidden\">Search...</span>\r\n      <kbd className=\"pointer-events-none absolute right-1.5 top-1.5 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex\">\r\n        <span className=\"text-xs\">⌘</span>K\r\n      </kbd>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,YAAS,AAAD;IAE/B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,8OAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,8OAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAU;;;;;;oBAAQ;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 3494, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/top-navigation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport {\r\n  Bell,\r\n  ChevronDown,\r\n  ChevronLeft,\r\n  LogOut,\r\n  Settings,\r\n  User,\r\n  PanelLeftIcon,\r\n  PanelRightIcon,\r\n  Home,\r\n} from \"lucide-react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser, useLogout } from \"@/features/auth/hooks/use-auth\";\r\nimport { TeamHeader } from \"@/components/team-header\";\r\nimport { SearchButton } from \"@/components/search-button\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport Link from \"next/link\";\r\n\r\nexport function TopNavigation() {\r\n  const { data: user } = useCurrentUser();\r\n  const logout = useLogout();\r\n  const { toggleSidebar, state, isMobile, setOpenMobile } = useSidebar();\r\n  const isExpanded = state === \"expanded\";\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Check if we're on the stock requests page\r\n  const isStockRequestsPage = pathname.includes('/inventory/stock-requests') &&\r\n    !pathname.includes('/create') &&\r\n    !pathname.match(/\\/inventory\\/stock-requests\\/\\d+/);\r\n\r\n  const handleLogout = () => {\r\n    logout.mutate();\r\n  };\r\n\r\n  const handleBackClick = () => {\r\n    router.back();\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-30 flex h-14 shrink-0 items-center gap-2 border-b border-primary-foreground/10 bg-primary text-primary-foreground transition-[width,height] ease-linear\">\r\n      <div className=\"flex flex-1 items-center justify-between px-4 w-full\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isMobile && isStockRequestsPage ? (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={handleBackClick}\r\n                aria-label=\"Go back\"\r\n                title=\"Go back\"\r\n              >\r\n                <ChevronLeft className=\"h-5 w-5\" />\r\n                <span className=\"sr-only\">Go Back</span>\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={() => (isMobile ? setOpenMobile(true) : toggleSidebar())}\r\n                data-mobile={isMobile ? \"true\" : \"false\"}\r\n                aria-label=\"Toggle sidebar\"\r\n                title={isExpanded ? \"Collapse sidebar\" : \"Expand sidebar\"}\r\n              >\r\n                {isExpanded ? (\r\n                  <PanelLeftIcon className=\"h-5 w-5\" />\r\n                ) : (\r\n                  <PanelRightIcon className=\"h-5 w-5\" />\r\n                )}\r\n                <span className=\"sr-only\">Toggle Sidebar</span>\r\n              </Button>\r\n            )}\r\n            <TeamHeader />\r\n\r\n            <div className=\"h-6 border-r border-primary-foreground/20 mx-2\"></div>\r\n\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n              asChild\r\n            >\r\n              <Link href=\"/dashboard\">\r\n                <Home className=\"h-4 w-4 mr-1\" />\r\n                Dashboard\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-center\">\r\n          <SearchButton />\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative h-8 w-8 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n            aria-label=\"Notifications\"\r\n          >\r\n            <Bell className=\"h-5 w-5\" />\r\n            <span className=\"absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-[10px] font-bold text-primary\">\r\n              3\r\n            </span>\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 gap-2 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                aria-label=\"Account\"\r\n              >\r\n                <Avatar className=\"h-7 w-7\">\r\n                  <AvatarImage\r\n                    src=\"/placeholder-avatar.jpg\"\r\n                    alt={user?.name || \"User\"}\r\n                  />\r\n                  <AvatarFallback className=\"text-xs font-semibold\">\r\n                    {user?.name ? user.name.charAt(0) : \"U\"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <span className=\"hidden text-sm font-semibold md:inline-block\">\r\n                  {user?.name || \"User\"}\r\n                </span>\r\n                <ChevronDown className=\"h-4 w-4 text-primary-foreground/80\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-56 rounded-lg\">\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-semibold leading-none\">\r\n                    {user?.name || \"User\"}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || \"<EMAIL>\"}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuGroup>\r\n                <DropdownMenuItem>\r\n                  <User className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Profile</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem>\r\n                  <Settings className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Settings</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuGroup>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span className=\"text-sm\">Log out</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAEA;AACA;AASA;AACA;AACA;AA/BA;;;;;;;;;;;;AAiCO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACnE,MAAM,aAAa,UAAU;IAC7B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,4CAA4C;IAC5C,MAAM,sBAAsB,SAAS,QAAQ,CAAC,gCAC5C,CAAC,SAAS,QAAQ,CAAC,cACnB,CAAC,SAAS,KAAK,CAAC;IAElB,MAAM,eAAe;QACnB,OAAO,MAAM;IACf;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI;IACb;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,YAAY,oCACX,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,cAAW;gCACX,OAAM;;kDAEN,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;qDAG5B,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAO,WAAW,cAAc,QAAQ;gCACjD,eAAa,WAAW,SAAS;gCACjC,cAAW;gCACX,OAAO,aAAa,qBAAqB;;oCAExC,2BACC,8OAAC,oNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;6DAEzB,8OAAC,sNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAE5B,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG9B,8OAAC,oIAAA,CAAA,aAAU;;;;;0CAEX,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,mMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,sIAAA,CAAA,eAAY;;;;;;;;;;8BAGf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,cAAW;;8CAEX,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAA6H;;;;;;;;;;;;sCAK/I,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,cAAW;;0DAEX,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDACV,KAAI;wDACJ,KAAK,MAAM,QAAQ;;;;;;kEAErB,8OAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGxC,8OAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;0DAEjB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG3B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,8OAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ;;;;;;kEAEjB,8OAAC;wDAAE,WAAU;kEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,oBAAiB;;8DAChB,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAG9B,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}, {"offset": {"line": 3929, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,8OAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 4061, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/breadcrumbs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from \"@/components/ui/breadcrumb\";\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\n\r\nexport function Breadcrumbs() {\r\n  const pathname = usePathname();\r\n\r\n  // Get the current page name from the pathname\r\n  const getPageName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length === 0) return \"Dashboard\";\r\n\r\n    // Get the last segment of the path and capitalize it\r\n    const lastSegment = path[path.length - 1];\r\n    return (\r\n      lastSegment.charAt(0).toUpperCase() +\r\n      lastSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  // Get the parent path for breadcrumb\r\n  const getParentPath = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Remove the last segment to get the parent path\r\n    return \"/\" + path.slice(0, path.length - 1).join(\"/\");\r\n  };\r\n\r\n  // Get the parent name for breadcrumb\r\n  const getParentName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Get the second-to-last segment and capitalize it\r\n    const parentSegment = path[path.length - 2];\r\n    return (\r\n      parentSegment.charAt(0).toUpperCase() +\r\n      parentSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  const parentPath = getParentPath();\r\n  const parentName = getParentName();\r\n  const pageName = getPageName();\r\n\r\n  return (\r\n    <div className=\"mb-4\">\r\n      <Breadcrumb>\r\n        <BreadcrumbList>\r\n          {parentPath && parentName && (\r\n            <>\r\n              <BreadcrumbItem className=\"hidden md:block\">\r\n                <BreadcrumbLink asChild>\r\n                  <NavigationLink\r\n                    href={parentPath}\r\n                    className=\"text-sm font-medium text-muted-foreground hover:text-foreground\"\r\n                  >\r\n                    {parentName}\r\n                  </NavigationLink>\r\n                </BreadcrumbLink>\r\n              </BreadcrumbItem>\r\n              <BreadcrumbSeparator className=\"hidden md:block h-3 w-3\" />\r\n            </>\r\n          )}\r\n          <BreadcrumbItem>\r\n            <BreadcrumbPage className=\"text-sm font-semibold\">\r\n              {pageName}\r\n            </BreadcrumbPage>\r\n          </BreadcrumbItem>\r\n        </BreadcrumbList>\r\n      </Breadcrumb>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AAXA;;;;;AAaO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,cAAc;QAClB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAE9B,qDAAqD;QACrD,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,OACE,YAAY,MAAM,CAAC,GAAG,WAAW,KACjC,YAAY,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEvC;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,iDAAiD;QACjD,OAAO,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC;IACnD;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,mDAAmD;QACnD,MAAM,gBAAgB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC3C,OACE,cAAc,MAAM,CAAC,GAAG,WAAW,KACnC,cAAc,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEzC;IAEA,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,WAAW;IAEjB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;sBACT,cAAA,8OAAC,sIAAA,CAAA,iBAAc;;oBACZ,cAAc,4BACb;;0CACE,8OAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;0CACxB,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wCACb,MAAM;wCACN,WAAU;kDAET;;;;;;;;;;;;;;;;0CAIP,8OAAC,sIAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;kCAGnC,8OAAC,sIAAA,CAAA,iBAAc;kCACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 4177, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/main-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { ReactNode, Suspense, useEffect } from \"react\";\r\nimport { RoleGuard } from \"./role-guard\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\nimport { SidebarProvider, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { AppSidebar } from \"@/components/app-sidebar\";\r\nimport { TopNavigation } from \"@/components/top-navigation\";\r\nimport { Breadcrumbs } from \"@/components/breadcrumbs\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\";\r\n\r\ninterface MainLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function MainLayout({ children }: MainLayoutProps) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Use useEffect for redirects to avoid hydration issues\r\n  useEffect(() => {\r\n    // Only show loading on the very first render if:\r\n    // 1. Auth is not initialized yet\r\n    // 2. We haven't shown the loading screen before in this session\r\n    if (!isInitialized && !hasShownLoading.main) {\r\n      setLoading(\"main\", true);\r\n    } else {\r\n      setLoading(\"main\", false);\r\n    }\r\n\r\n    if (!isInitialized) {\r\n      return; // Wait until auth state is initialized\r\n    }\r\n\r\n    // After auth state is initialized, we can hide the loading screen\r\n    setLoading(\"main\", false);\r\n\r\n    // If we have an access token, mark that we've shown loading\r\n    // This prevents showing loading on subsequent navigations\r\n    if (accessToken) {\r\n      markLoadingShown(\"main\");\r\n    }\r\n\r\n    // We're disabling client-side redirects to avoid redirect loops\r\n    // The middleware will handle redirects instead\r\n    if (!accessToken) {\r\n      // We're not redirecting here anymore\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [accessToken, isInitialized, hasShownLoading.main]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in loading state\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (isLoading.main) {\r\n        setLoading(\"main\", false);\r\n        markLoadingShown(\"main\");\r\n      }\r\n    }, 1500); // 1.5 second timeout for better UX\r\n\r\n    return () => clearTimeout(timeoutId);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.main]);\r\n\r\n  // Don't render anything if not authenticated\r\n  if (!accessToken) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider>\r\n      <RoleGuard>\r\n        <MainLayoutContent>{children}</MainLayoutContent>\r\n      </RoleGuard>\r\n    </SidebarProvider>\r\n  );\r\n}\r\n\r\nfunction MainLayoutContent({ children }: { children: ReactNode }) {\r\n  const { state, openMobile, setOpenMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-screen w-full bg-gray-50\">\r\n      <TopNavigation />\r\n      <div className=\"flex h-[calc(100vh-3.5rem)] overflow-hidden\">\r\n        {/* Desktop sidebar */}\r\n        <aside\r\n          className={cn(\r\n            \"border-r bg-white transition-all duration-300 shrink-0 hidden md:block\",\r\n            isCollapsed ? \"w-16\" : \"w-64\"\r\n          )}\r\n        >\r\n          <AppSidebar />\r\n        </aside>\r\n\r\n        {/* Mobile sidebar */}\r\n        <div className=\"md:hidden\">\r\n          <Sheet open={openMobile} onOpenChange={setOpenMobile}>\r\n            <SheetContent\r\n              side=\"left\"\r\n              className=\"p-0 w-[280px] border-r bg-white\"\r\n            >\r\n              <div className=\"h-full overflow-y-auto\">\r\n                <AppSidebar />\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        <main className=\"flex flex-col w-full overflow-y-auto overflow-x-hidden\">\r\n          <div className=\"p-6\">\r\n            <Suspense fallback={<div>Loading content...</div>}>\r\n              <Breadcrumbs />\r\n              {children}\r\n            </Suspense>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;AAoBO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD;IAEX,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iDAAiD;QACjD,iCAAiC;QACjC,gEAAgE;QAChE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;YAC3C,WAAW,QAAQ;QACrB,OAAO;YACL,WAAW,QAAQ;QACrB;QAEA,IAAI,CAAC,eAAe;YAClB,QAAQ,uCAAuC;QACjD;QAEA,kEAAkE;QAClE,WAAW,QAAQ;QAEnB,4DAA4D;QAC5D,0DAA0D;QAC1D,IAAI,aAAa;YACf,iBAAiB;QACnB;QAEA,gEAAgE;QAChE,+CAA+C;QAC/C,IAAI,CAAC,aAAa;QAChB,qCAAqC;QACvC;IACA,uDAAuD;IACzD,GAAG;QAAC;QAAa;QAAe,gBAAgB,IAAI;KAAC;IAErD,iEAAiE;IACjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,UAAU,IAAI,EAAE;gBAClB,WAAW,QAAQ;gBACnB,iBAAiB;YACnB;QACF,GAAG,OAAO,mCAAmC;QAE7C,OAAO,IAAM,aAAa;IAC1B,uDAAuD;IACzD,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6CAA6C;IAC7C,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,qBACE,8OAAC,mIAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,8IAAA,CAAA,YAAS;sBACR,cAAA,8OAAC;0BAAmB;;;;;;;;;;;;;;;;AAI5B;AAEA,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;IAC9D,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,cAAc,UAAU;IAE9B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA,cAAc,SAAS;kCAGzB,cAAA,8OAAC,oIAAA,CAAA,aAAU;;;;;;;;;;kCAIb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;sCACrC,cAAA,8OAAC,iIAAA,CAAA,eAAY;gCACX,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnB,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,wBAAU,8OAAC;8CAAI;;;;;;;kDACvB,8OAAC,iIAAA,CAAA,cAAW;;;;;oCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 4389, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4486, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4604, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/api/product-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse } from \"@/types/api\";\r\nimport {\r\n  Product,\r\n  CreateProductRequest,\r\n  UpdateProductRequest,\r\n  UpdateProductStatusRequest,\r\n} from \"@/types/product\";\r\nimport { toast } from \"sonner\";\r\n\r\nexport const productService = {\r\n  /**\r\n   * Get products without stock items at headquarters\r\n   * @param params Query parameters\r\n   * @returns Paginated list of products without stock items at headquarters\r\n   */\r\n  getProductsWithoutHQStock: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Product>> => {\r\n    try {\r\n      // Use the regular products endpoint with a filter for products without HQ stock\r\n      // This is a workaround until the backend endpoint is fixed\r\n      const allProducts = await productService.getProducts({\r\n        ...params,\r\n        limit: 1000, // Get a large number of products to filter client-side\r\n      });\r\n\r\n      // Get all stock items for headquarters\r\n      let stockItems: any[] = [];\r\n      try {\r\n        const stockItemsResponse = await apiClient.get<any>(\"/stock-items\", {\r\n          params: {\r\n            branch_id: 1, // Headquarters\r\n            limit: 1000,\r\n          }\r\n        });\r\n\r\n        if (stockItemsResponse && stockItemsResponse.data) {\r\n          stockItems = stockItemsResponse.data;\r\n        }\r\n      } catch (stockError) {\r\n        console.error(\"Error fetching stock items:\", stockError);\r\n        // Continue with empty stock items\r\n      }\r\n\r\n      // Create a set of product IDs that already have stock items at headquarters\r\n      const productsWithHQStock = new Set(\r\n        stockItems.map((item: any) => item.product_id)\r\n      );\r\n\r\n      // Filter out products that already have stock items at headquarters\r\n      const filteredProducts = allProducts.data.filter(\r\n        (product) => !productsWithHQStock.has(product.id)\r\n      );\r\n\r\n      // Return the filtered products with pagination\r\n      return {\r\n        data: filteredProducts,\r\n        pagination: {\r\n          total: filteredProducts.length,\r\n          page: parseInt(params?.page || \"1\", 10),\r\n          limit: parseInt(params?.limit || \"10\", 10),\r\n          totalPages: Math.ceil(filteredProducts.length / parseInt(params?.limit || \"10\", 10)),\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching products without HQ stock:\", error);\r\n      // Show a toast error message\r\n      try {\r\n        // @ts-ignore - toast is imported elsewhere\r\n        toast.error(\"Error loading products\", {\r\n          description: \"Could not load products without headquarters stock. Please try again later.\",\r\n        });\r\n      } catch (toastError) {\r\n        // Ignore toast errors\r\n      }\r\n\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getProducts: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Product>> => {\r\n    try {\r\n      // Extract branch_procurable parameter for client-side filtering\r\n      const branchProcurable = params?.branch_procurable;\r\n\r\n      // Create a new params object without branch_procurable\r\n      const apiParams = { ...params };\r\n      if (apiParams.branch_procurable !== undefined) {\r\n        delete apiParams.branch_procurable;\r\n      }\r\n\r\n      console.log(\"Fetching products with params:\", apiParams);\r\n      const response = await apiClient.get<any>(\"/products\", { params: apiParams });\r\n      console.log(\"API response for products:\", response);\r\n\r\n      // Map API response to our Product type\r\n      const mapApiProductToProduct = (apiProduct: any): Product => {\r\n        // Convert string prices to numbers for UI\r\n        const sellingPrice = apiProduct.suggested_selling_price\r\n          ? parseFloat(apiProduct.suggested_selling_price)\r\n          : 0;\r\n\r\n        // Calculate stock quantity from stock array if available\r\n        const stockQuantity =\r\n          apiProduct.stock?.reduce(\r\n            (total: number, item: any) => total + item.quantity,\r\n            0\r\n          ) || 0;\r\n\r\n        // Ensure category hierarchy is properly handled\r\n        let categoryHierarchy = apiProduct.categoryHierarchy || [];\r\n\r\n        // If categoryHierarchy is not available but ProductCategory is, create a simple hierarchy\r\n        if (\r\n          (!categoryHierarchy || categoryHierarchy.length === 0) &&\r\n          apiProduct.ProductCategory\r\n        ) {\r\n          const category = apiProduct.ProductCategory;\r\n\r\n          // If the category has a parent, include it in the hierarchy\r\n          if (category.Parent) {\r\n            categoryHierarchy = [\r\n              {\r\n                id: category.Parent.id,\r\n                name: category.Parent.name,\r\n                description: category.Parent.description,\r\n                level: category.Parent.level || 0,\r\n              },\r\n              {\r\n                id: category.id,\r\n                name: category.name,\r\n                description: category.description,\r\n                level: category.level || 1,\r\n              },\r\n            ];\r\n          } else {\r\n            // If no parent, just include the category itself\r\n            categoryHierarchy = [\r\n              {\r\n                id: category.id,\r\n                name: category.name,\r\n                description: category.description,\r\n                level: category.level || 0,\r\n              },\r\n            ];\r\n          }\r\n        }\r\n\r\n        return {\r\n          ...apiProduct,\r\n          // Map API fields to UI fields\r\n          price: sellingPrice, // Map suggested_selling_price to price for UI compatibility\r\n          stock_quantity: stockQuantity,\r\n          status: apiProduct.is_active ? \"active\" : \"inactive\", // Derive status from is_active\r\n          has_variants: apiProduct.has_serial, // Map has_serial to has_variants for UI compatibility\r\n          categoryHierarchy: categoryHierarchy, // Ensure category hierarchy is available\r\n        };\r\n      };\r\n\r\n      let mappedProducts: Product[] = [];\r\n\r\n      // If response is an array, convert to paginated format with mapped products\r\n      if (Array.isArray(response)) {\r\n        mappedProducts = response.map(mapApiProductToProduct);\r\n      }\r\n      // Check if response is already in paginated format\r\n      else if (response && response.data && Array.isArray(response.data)) {\r\n        mappedProducts = response.data.map(mapApiProductToProduct);\r\n      }\r\n\r\n      // Apply client-side filtering for branch_procurable if needed\r\n      if (branchProcurable === true) {\r\n        console.log(\"Filtering products for branch_procurable=true\");\r\n        mappedProducts = mappedProducts.filter(product => product.branch_procurable === true);\r\n      }\r\n\r\n      console.log(\"Filtered products:\", mappedProducts);\r\n\r\n      // Return the filtered products with pagination\r\n      return {\r\n        data: mappedProducts,\r\n        pagination: response.pagination || {\r\n          total: mappedProducts.length,\r\n          page: 1,\r\n          limit: mappedProducts.length,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching products:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getProductById: async (id: number): Promise<Product | null> => {\r\n    try {\r\n      const response = await apiClient.get(`/products/${id}`);\r\n      return response;\r\n    } catch (error: any) {\r\n      console.error(`Error fetching product with ID ${id}:`, error);\r\n\r\n      // If the error is a 404, return null instead of throwing\r\n      if (error.response && error.response.status === 404) {\r\n        console.warn(`Product with ID ${id} not found`);\r\n        return null;\r\n      }\r\n\r\n      // For other errors, rethrow\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  createProduct: async (product: CreateProductRequest): Promise<Product> => {\r\n    // Create a properly structured API request with only the required fields\r\n    const apiRequest: Record<string, any> = {\r\n      // Required fields\r\n      tenant_id: product.tenant_id,\r\n      name: product.name,\r\n      sku: product.sku,\r\n      has_serial: product.has_serial,\r\n      suggested_buying_price: product.suggested_buying_price,\r\n      suggested_selling_price: product.suggested_selling_price,\r\n    };\r\n\r\n    // Optional fields\r\n    if (product.category_id !== undefined)\r\n      apiRequest.category_id = product.category_id;\r\n    if (product.brand_id !== undefined) apiRequest.brand_id = product.brand_id;\r\n    if (product.brand_type_id !== undefined)\r\n      apiRequest.brand_type_id = product.brand_type_id;\r\n    if (product.description !== undefined)\r\n      apiRequest.description = product.description;\r\n    if (product.barcode !== undefined)\r\n      apiRequest.barcode = product.barcode;\r\n    if (product.default_wholesale_price !== undefined)\r\n      apiRequest.default_wholesale_price = product.default_wholesale_price;\r\n    if (product.warranty_period !== undefined)\r\n      apiRequest.warranty_period = product.warranty_period;\r\n    if (product.can_assign_to_dsa !== undefined)\r\n      apiRequest.can_assign_to_dsa = product.can_assign_to_dsa;\r\n\r\n    console.log(\"Creating product with API request:\", apiRequest);\r\n    return apiClient.post(\"/products\", apiRequest);\r\n  },\r\n\r\n  updateProduct: async (\r\n    id: number,\r\n    product: UpdateProductRequest\r\n  ): Promise<Product> => {\r\n    // Create a properly structured API request\r\n    const apiRequest: Record<string, any> = {};\r\n\r\n    // Basic information\r\n    if (product.name !== undefined) apiRequest.name = product.name;\r\n    if (product.sku !== undefined) apiRequest.sku = product.sku;\r\n    if (product.description !== undefined) apiRequest.description = product.description;\r\n    if (product.barcode !== undefined) apiRequest.barcode = product.barcode;\r\n    if (product.status !== undefined) apiRequest.is_active = product.status === 'active';\r\n    if (product.is_active !== undefined) apiRequest.is_active = product.is_active;\r\n\r\n    // Categorization\r\n    if (product.category_id !== undefined) apiRequest.category_id = product.category_id;\r\n    if (product.brand_id !== undefined) apiRequest.brand_id = product.brand_id;\r\n    if (product.brand_type_id !== undefined) apiRequest.brand_type_id = product.brand_type_id;\r\n\r\n    // Inventory tracking\r\n    if (product.has_serial !== undefined) {\r\n      apiRequest.has_serial = product.has_serial;\r\n    } else if (product.has_variants !== undefined) {\r\n      apiRequest.has_serial = product.has_variants;\r\n    }\r\n\r\n    if (product.has_variants !== undefined) apiRequest.has_variants = product.has_variants;\r\n    if (product.is_parent !== undefined) apiRequest.is_parent = product.is_parent;\r\n    if (product.parent_id !== undefined) apiRequest.parent_id = product.parent_id;\r\n\r\n    // Pricing fields\r\n    if (product.suggested_buying_price !== undefined) {\r\n      apiRequest.suggested_buying_price = product.suggested_buying_price;\r\n    } else if (product.buying_price !== undefined) {\r\n      apiRequest.suggested_buying_price = product.buying_price;\r\n    }\r\n\r\n    if (product.suggested_selling_price !== undefined) {\r\n      apiRequest.suggested_selling_price = product.suggested_selling_price;\r\n    } else if (product.selling_price !== undefined) {\r\n      apiRequest.suggested_selling_price = product.selling_price;\r\n    } else if (product.price !== undefined) {\r\n      apiRequest.suggested_selling_price = product.price;\r\n    }\r\n\r\n    if (product.default_wholesale_price !== undefined) {\r\n      apiRequest.default_wholesale_price = product.default_wholesale_price;\r\n    }\r\n\r\n    // VAT-related fields\r\n    if (product.vat_rate_id !== undefined) apiRequest.vat_rate_id = product.vat_rate_id;\r\n    if (product.is_vat_inclusive !== undefined) apiRequest.is_vat_inclusive = product.is_vat_inclusive;\r\n    if (product.is_vat_exempt !== undefined) apiRequest.is_vat_exempt = product.is_vat_exempt;\r\n    if (product.vat_exemption_reason !== undefined) apiRequest.vat_exemption_reason = product.vat_exemption_reason;\r\n\r\n    // Inventory management\r\n    if (product.reorder_level !== undefined) apiRequest.reorder_level = product.reorder_level;\r\n    if (product.reorder_quantity !== undefined) apiRequest.reorder_quantity = product.reorder_quantity;\r\n\r\n    // Other fields\r\n    if (product.warranty_period !== undefined) apiRequest.warranty_period = product.warranty_period;\r\n\r\n    // DSA-related fields\r\n    if (product.can_assign_to_dsa !== undefined) apiRequest.can_assign_to_dsa = product.can_assign_to_dsa;\r\n\r\n    console.log(\"Updating product with API request:\", apiRequest);\r\n    return apiClient.put(`/products/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteProduct: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/products/${id}`);\r\n  },\r\n\r\n  updateProductStatus: async (\r\n    id: number,\r\n    status: UpdateProductStatusRequest\r\n  ): Promise<Product> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      status: status.status, // 'active' or 'inactive'\r\n    };\r\n\r\n    return apiClient.put(`/products/${id}/status`, apiRequest);\r\n  },\r\n\r\n  uploadProductImage: async (\r\n    id: number,\r\n    file: File\r\n  ): Promise<{ image_url: string }> => {\r\n    const formData = new FormData();\r\n    formData.append(\"image\", file);\r\n\r\n    return apiClient.post(`/products/${id}/image`, formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n    });\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAQA;;;AAEO,MAAM,iBAAiB;IAC5B;;;;GAIC,GACD,2BAA2B,OACzB;QAEA,IAAI;YACF,gFAAgF;YAChF,2DAA2D;YAC3D,MAAM,cAAc,MAAM,eAAe,WAAW,CAAC;gBACnD,GAAG,MAAM;gBACT,OAAO;YACT;YAEA,uCAAuC;YACvC,IAAI,aAAoB,EAAE;YAC1B,IAAI;gBACF,MAAM,qBAAqB,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,gBAAgB;oBAClE,QAAQ;wBACN,WAAW;wBACX,OAAO;oBACT;gBACF;gBAEA,IAAI,sBAAsB,mBAAmB,IAAI,EAAE;oBACjD,aAAa,mBAAmB,IAAI;gBACtC;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,kCAAkC;YACpC;YAEA,4EAA4E;YAC5E,MAAM,sBAAsB,IAAI,IAC9B,WAAW,GAAG,CAAC,CAAC,OAAc,KAAK,UAAU;YAG/C,oEAAoE;YACpE,MAAM,mBAAmB,YAAY,IAAI,CAAC,MAAM,CAC9C,CAAC,UAAY,CAAC,oBAAoB,GAAG,CAAC,QAAQ,EAAE;YAGlD,+CAA+C;YAC/C,OAAO;gBACL,MAAM;gBACN,YAAY;oBACV,OAAO,iBAAiB,MAAM;oBAC9B,MAAM,SAAS,QAAQ,QAAQ,KAAK;oBACpC,OAAO,SAAS,QAAQ,SAAS,MAAM;oBACvC,YAAY,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG,SAAS,QAAQ,SAAS,MAAM;gBAClF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,6BAA6B;YAC7B,IAAI;gBACF,2CAA2C;gBAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0BAA0B;oBACpC,aAAa;gBACf;YACF,EAAE,OAAO,YAAY;YACnB,sBAAsB;YACxB;YAEA,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,aAAa,OACX;QAEA,IAAI;YACF,gEAAgE;YAChE,MAAM,mBAAmB,QAAQ;YAEjC,uDAAuD;YACvD,MAAM,YAAY;gBAAE,GAAG,MAAM;YAAC;YAC9B,IAAI,UAAU,iBAAiB,KAAK,WAAW;gBAC7C,OAAO,UAAU,iBAAiB;YACpC;YAEA,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,aAAa;gBAAE,QAAQ;YAAU;YAC3E,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,uCAAuC;YACvC,MAAM,yBAAyB,CAAC;gBAC9B,0CAA0C;gBAC1C,MAAM,eAAe,WAAW,uBAAuB,GACnD,WAAW,WAAW,uBAAuB,IAC7C;gBAEJ,yDAAyD;gBACzD,MAAM,gBACJ,WAAW,KAAK,EAAE,OAChB,CAAC,OAAe,OAAc,QAAQ,KAAK,QAAQ,EACnD,MACG;gBAEP,gDAAgD;gBAChD,IAAI,oBAAoB,WAAW,iBAAiB,IAAI,EAAE;gBAE1D,0FAA0F;gBAC1F,IACE,CAAC,CAAC,qBAAqB,kBAAkB,MAAM,KAAK,CAAC,KACrD,WAAW,eAAe,EAC1B;oBACA,MAAM,WAAW,WAAW,eAAe;oBAE3C,4DAA4D;oBAC5D,IAAI,SAAS,MAAM,EAAE;wBACnB,oBAAoB;4BAClB;gCACE,IAAI,SAAS,MAAM,CAAC,EAAE;gCACtB,MAAM,SAAS,MAAM,CAAC,IAAI;gCAC1B,aAAa,SAAS,MAAM,CAAC,WAAW;gCACxC,OAAO,SAAS,MAAM,CAAC,KAAK,IAAI;4BAClC;4BACA;gCACE,IAAI,SAAS,EAAE;gCACf,MAAM,SAAS,IAAI;gCACnB,aAAa,SAAS,WAAW;gCACjC,OAAO,SAAS,KAAK,IAAI;4BAC3B;yBACD;oBACH,OAAO;wBACL,iDAAiD;wBACjD,oBAAoB;4BAClB;gCACE,IAAI,SAAS,EAAE;gCACf,MAAM,SAAS,IAAI;gCACnB,aAAa,SAAS,WAAW;gCACjC,OAAO,SAAS,KAAK,IAAI;4BAC3B;yBACD;oBACH;gBACF;gBAEA,OAAO;oBACL,GAAG,UAAU;oBACb,8BAA8B;oBAC9B,OAAO;oBACP,gBAAgB;oBAChB,QAAQ,WAAW,SAAS,GAAG,WAAW;oBAC1C,cAAc,WAAW,UAAU;oBACnC,mBAAmB;gBACrB;YACF;YAEA,IAAI,iBAA4B,EAAE;YAElC,4EAA4E;YAC5E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,iBAAiB,SAAS,GAAG,CAAC;YAChC,OAEK,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAClE,iBAAiB,SAAS,IAAI,CAAC,GAAG,CAAC;YACrC;YAEA,8DAA8D;YAC9D,IAAI,qBAAqB,MAAM;gBAC7B,QAAQ,GAAG,CAAC;gBACZ,iBAAiB,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,iBAAiB,KAAK;YAClF;YAEA,QAAQ,GAAG,CAAC,sBAAsB;YAElC,+CAA+C;YAC/C,OAAO;gBACL,MAAM;gBACN,YAAY,SAAS,UAAU,IAAI;oBACjC,OAAO,eAAe,MAAM;oBAC5B,MAAM;oBACN,OAAO,eAAe,MAAM;oBAC5B,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;YACtD,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YAEvD,yDAAyD;YACzD,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACnD,QAAQ,IAAI,CAAC,CAAC,gBAAgB,EAAE,GAAG,UAAU,CAAC;gBAC9C,OAAO;YACT;YAEA,4BAA4B;YAC5B,MAAM;QACR;IACF;IAEA,eAAe,OAAO;QACpB,yEAAyE;QACzE,MAAM,aAAkC;YACtC,kBAAkB;YAClB,WAAW,QAAQ,SAAS;YAC5B,MAAM,QAAQ,IAAI;YAClB,KAAK,QAAQ,GAAG;YAChB,YAAY,QAAQ,UAAU;YAC9B,wBAAwB,QAAQ,sBAAsB;YACtD,yBAAyB,QAAQ,uBAAuB;QAC1D;QAEA,kBAAkB;QAClB,IAAI,QAAQ,WAAW,KAAK,WAC1B,WAAW,WAAW,GAAG,QAAQ,WAAW;QAC9C,IAAI,QAAQ,QAAQ,KAAK,WAAW,WAAW,QAAQ,GAAG,QAAQ,QAAQ;QAC1E,IAAI,QAAQ,aAAa,KAAK,WAC5B,WAAW,aAAa,GAAG,QAAQ,aAAa;QAClD,IAAI,QAAQ,WAAW,KAAK,WAC1B,WAAW,WAAW,GAAG,QAAQ,WAAW;QAC9C,IAAI,QAAQ,OAAO,KAAK,WACtB,WAAW,OAAO,GAAG,QAAQ,OAAO;QACtC,IAAI,QAAQ,uBAAuB,KAAK,WACtC,WAAW,uBAAuB,GAAG,QAAQ,uBAAuB;QACtE,IAAI,QAAQ,eAAe,KAAK,WAC9B,WAAW,eAAe,GAAG,QAAQ,eAAe;QACtD,IAAI,QAAQ,iBAAiB,KAAK,WAChC,WAAW,iBAAiB,GAAG,QAAQ,iBAAiB;QAE1D,QAAQ,GAAG,CAAC,sCAAsC;QAClD,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,aAAa;IACrC;IAEA,eAAe,OACb,IACA;QAEA,2CAA2C;QAC3C,MAAM,aAAkC,CAAC;QAEzC,oBAAoB;QACpB,IAAI,QAAQ,IAAI,KAAK,WAAW,WAAW,IAAI,GAAG,QAAQ,IAAI;QAC9D,IAAI,QAAQ,GAAG,KAAK,WAAW,WAAW,GAAG,GAAG,QAAQ,GAAG;QAC3D,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;QACnF,IAAI,QAAQ,OAAO,KAAK,WAAW,WAAW,OAAO,GAAG,QAAQ,OAAO;QACvE,IAAI,QAAQ,MAAM,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,MAAM,KAAK;QAC5E,IAAI,QAAQ,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,SAAS;QAE7E,iBAAiB;QACjB,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;QACnF,IAAI,QAAQ,QAAQ,KAAK,WAAW,WAAW,QAAQ,GAAG,QAAQ,QAAQ;QAC1E,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,aAAa,GAAG,QAAQ,aAAa;QAEzF,qBAAqB;QACrB,IAAI,QAAQ,UAAU,KAAK,WAAW;YACpC,WAAW,UAAU,GAAG,QAAQ,UAAU;QAC5C,OAAO,IAAI,QAAQ,YAAY,KAAK,WAAW;YAC7C,WAAW,UAAU,GAAG,QAAQ,YAAY;QAC9C;QAEA,IAAI,QAAQ,YAAY,KAAK,WAAW,WAAW,YAAY,GAAG,QAAQ,YAAY;QACtF,IAAI,QAAQ,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,SAAS;QAC7E,IAAI,QAAQ,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,SAAS;QAE7E,iBAAiB;QACjB,IAAI,QAAQ,sBAAsB,KAAK,WAAW;YAChD,WAAW,sBAAsB,GAAG,QAAQ,sBAAsB;QACpE,OAAO,IAAI,QAAQ,YAAY,KAAK,WAAW;YAC7C,WAAW,sBAAsB,GAAG,QAAQ,YAAY;QAC1D;QAEA,IAAI,QAAQ,uBAAuB,KAAK,WAAW;YACjD,WAAW,uBAAuB,GAAG,QAAQ,uBAAuB;QACtE,OAAO,IAAI,QAAQ,aAAa,KAAK,WAAW;YAC9C,WAAW,uBAAuB,GAAG,QAAQ,aAAa;QAC5D,OAAO,IAAI,QAAQ,KAAK,KAAK,WAAW;YACtC,WAAW,uBAAuB,GAAG,QAAQ,KAAK;QACpD;QAEA,IAAI,QAAQ,uBAAuB,KAAK,WAAW;YACjD,WAAW,uBAAuB,GAAG,QAAQ,uBAAuB;QACtE;QAEA,qBAAqB;QACrB,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;QACnF,IAAI,QAAQ,gBAAgB,KAAK,WAAW,WAAW,gBAAgB,GAAG,QAAQ,gBAAgB;QAClG,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,aAAa,GAAG,QAAQ,aAAa;QACzF,IAAI,QAAQ,oBAAoB,KAAK,WAAW,WAAW,oBAAoB,GAAG,QAAQ,oBAAoB;QAE9G,uBAAuB;QACvB,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,aAAa,GAAG,QAAQ,aAAa;QACzF,IAAI,QAAQ,gBAAgB,KAAK,WAAW,WAAW,gBAAgB,GAAG,QAAQ,gBAAgB;QAElG,eAAe;QACf,IAAI,QAAQ,eAAe,KAAK,WAAW,WAAW,eAAe,GAAG,QAAQ,eAAe;QAE/F,qBAAqB;QACrB,IAAI,QAAQ,iBAAiB,KAAK,WAAW,WAAW,iBAAiB,GAAG,QAAQ,iBAAiB;QAErG,QAAQ,GAAG,CAAC,sCAAsC;QAClD,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;IAC1C;IAEA,eAAe,OAAO;QACpB,OAAO,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IAC3C;IAEA,qBAAqB,OACnB,IACA;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,QAAQ,OAAO,MAAM;QACvB;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE;IACjD;IAEA,oBAAoB,OAClB,IACA;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,UAAU;YACvD,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 4897, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/hooks/use-products.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  CreateProductRequest,\r\n  UpdateProductRequest,\r\n  UpdateProductStatusRequest,\r\n} from \"@/types/product\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport { productService } from \"../api/product-service\";\r\n\r\nexport function useProducts(params?: Record<string, any>) {\r\n  return useQuery({\r\n    queryKey: [\"products\", params],\r\n    queryFn: () => productService.getProducts(params),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\n/**\r\n * Hook to fetch products without stock items at headquarters\r\n */\r\nexport function useProductsWithoutHQStock(params?: Record<string, any>) {\r\n  return useQuery({\r\n    queryKey: [\"products-without-hq-stock\", params],\r\n    queryFn: () => productService.getProductsWithoutHQStock(params),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useProduct(id: number, options?: any) {\r\n  return useQuery({\r\n    queryKey: [\"products\", id],\r\n    queryFn: async () => {\r\n      try {\r\n        return await productService.getProductById(id);\r\n      } catch (error: any) {\r\n        console.error(`Error fetching product with ID ${id}:`, error);\r\n        // Return null instead of throwing an error\r\n        return null;\r\n      }\r\n    },\r\n    enabled: !!id,\r\n    ...options,\r\n  });\r\n}\r\n\r\nexport function useCreateProduct() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (product: CreateProductRequest) =>\r\n      productService.createProduct(product),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"products\"] });\r\n      toast.success(\"Product created\", {\r\n        description: \"The product has been created successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error creating product\", {\r\n        description:\r\n          error.message || \"An error occurred while creating the product.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateProduct(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (product: UpdateProductRequest) =>\r\n      productService.updateProduct(id, product),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"products\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"products\"] });\r\n      toast.success(\"Product updated\", {\r\n        description: \"The product has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating product\", {\r\n        description:\r\n          error.message || \"An error occurred while updating the product.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteProduct() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => productService.deleteProduct(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"products\"] });\r\n      toast.success(\"Product deleted\", {\r\n        description: \"The product has been deleted successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error deleting product\", {\r\n        description:\r\n          error.message || \"An error occurred while deleting the product.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateProductStatus(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (status: UpdateProductStatusRequest) =>\r\n      productService.updateProductStatus(id, status),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"products\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"products\"] });\r\n      toast.success(\"Product status updated\", {\r\n        description: \"The product status has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating product status\", {\r\n        description:\r\n          error.message ||\r\n          \"An error occurred while updating the product status.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUploadProductImage(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (file: File) => productService.uploadProductImage(id, file),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"products\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"products\"] });\r\n      toast.success(\"Image uploaded\", {\r\n        description: \"The product image has been uploaded successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error uploading image\", {\r\n        description:\r\n          error.message || \"An error occurred while uploading the image.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAOA;AAAA;AAAA;AACA;AACA;AATA;;;;AAWO,SAAS,YAAY,MAA4B;IACtD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAO;QAC9B,SAAS,IAAM,wJAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;QAC1C,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,QAAQ,CAAC;YACP,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,MAAM,EAAE;oBACR,YAAY;wBACV,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YACA,OAAO;QACT;IACF;AACF;AAKO,SAAS,0BAA0B,MAA4B;IACpE,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAA6B;SAAO;QAC/C,SAAS,IAAM,wJAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC;QACxD,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,QAAQ,CAAC;YACP,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,MAAM,EAAE;oBACR,YAAY;wBACV,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YACA,OAAO;QACT;IACF;AACF;AAEO,SAAS,WAAW,EAAU,EAAE,OAAa;IAClD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAG;QAC1B,SAAS;YACP,IAAI;gBACF,OAAO,MAAM,wJAAA,CAAA,iBAAc,CAAC,cAAc,CAAC;YAC7C,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;gBACvD,2CAA2C;gBAC3C,OAAO;YACT;QACF;QACA,SAAS,CAAC,CAAC;QACX,GAAG,OAAO;IACZ;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,UACX,wJAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;QAC/B,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;gBAC/B,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0BAA0B;gBACpC,aACE,MAAM,OAAO,IAAI;YACrB;QACF;IACF;AACF;AAEO,SAAS,iBAAiB,EAAU;IACzC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,UACX,wJAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,IAAI;QACnC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY;iBAAG;YAAC;YAC3D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;gBAC/B,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0BAA0B;gBACpC,aACE,MAAM,OAAO,IAAI;YACrB;QACF;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,wJAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;QACzD,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;gBAC/B,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0BAA0B;gBACpC,aACE,MAAM,OAAO,IAAI;YACrB;QACF;IACF;AACF;AAEO,SAAS,uBAAuB,EAAU;IAC/C,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,SACX,wJAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI;QACzC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY;iBAAG;YAAC;YAC3D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,0BAA0B;gBACtC,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iCAAiC;gBAC3C,aACE,MAAM,OAAO,IACb;YACJ;QACF;IACF;AACF;AAEO,SAAS,sBAAsB,EAAU;IAC9C,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAAe,wJAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,IAAI;QAClE,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAY;iBAAG;YAAC;YAC3D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;gBAC9B,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;gBACnC,aACE,MAAM,OAAO,IAAI;YACrB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 5120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        success:\r\n          \"border-transparent bg-green-500 text-white [a&]:hover:bg-green-600 focus-visible:ring-green-500/20 dark:focus-visible:ring-green-500/40\",\r\n        warning:\r\n          \"border-transparent bg-yellow-500 text-white [a&]:hover:bg-yellow-600 focus-visible:ring-yellow-500/20 dark:focus-visible:ring-yellow-500/40\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5168, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/components/product-status-badge.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\n\r\ninterface ProductStatusBadgeProps {\r\n  status?: string;\r\n}\r\n\r\nexport function ProductStatusBadge({\r\n  status = \"unknown\",\r\n}: ProductStatusBadgeProps) {\r\n  const getVariant = () => {\r\n    switch (status.toLowerCase()) {\r\n      case \"active\":\r\n        return \"success\";\r\n      case \"inactive\":\r\n        return \"destructive\";\r\n      default:\r\n        return \"secondary\";\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Badge variant={getVariant() as any}>\r\n      {status.charAt(0).toUpperCase() + status.slice(1)}\r\n    </Badge>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQO,SAAS,mBAAmB,EACjC,SAAS,SAAS,EACM;IACxB,MAAM,aAAa;QACjB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC,iIAAA,CAAA,QAAK;QAAC,SAAS;kBACb,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;;;;;;AAGrD", "debugId": null}}, {"offset": {"line": 5202, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/store/product-store.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Product } from \"@/types/product\";\r\nimport { create } from \"zustand\";\r\n\r\ninterface ProductStore {\r\n  selectedProduct: Product | null;\r\n  setSelectedProduct: (product: Product | null) => void;\r\n}\r\n\r\nexport const useProductStore = create<ProductStore>()((set) => ({\r\n  selectedProduct: null,\r\n  setSelectedProduct: (product) => set({ selectedProduct: product }),\r\n}));\r\n"], "names": [], "mappings": ";;;AAGA;AAHA;;AAUO,MAAM,kBAAkB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAAkB,CAAC,MAAQ,CAAC;QAC9D,iBAAiB;QACjB,oBAAoB,CAAC,UAAY,IAAI;gBAAE,iBAAiB;YAAQ;IAClE,CAAC", "debugId": null}}, {"offset": {"line": 5220, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/components/products-table.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from \"@/components/ui/alert-dialog\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { Product } from \"@/types/product\";\r\nimport {\r\n  ArrowUpDown,\r\n  Ban,\r\n  CheckCircle,\r\n  Edit,\r\n  Eye,\r\n  MoreHorizontal,\r\n  Plus,\r\n  Search,\r\n  Trash2,\r\n} from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useState } from \"react\";\r\nimport {\r\n  useDeleteProduct,\r\n  useUpdateProductStatus,\r\n} from \"../hooks/use-products\";\r\nimport { ProductStatusBadge } from \"./product-status-badge\";\r\nimport { useProductStore } from \"../store/product-store\";\r\n\r\ninterface ProductsTableProps {\r\n  products: Product[];\r\n  isLoading: boolean;\r\n  onSearch: (query: string) => void;\r\n  onSort?: (field: string) => void;\r\n  sortField?: string | null;\r\n  sortDirection?: \"asc\" | \"desc\";\r\n}\r\n\r\nexport function ProductsTable({\r\n  products,\r\n  isLoading,\r\n  onSearch,\r\n  onSort,\r\n  sortField,\r\n  sortDirection = \"asc\",\r\n}: ProductsTableProps) {\r\n  const router = useRouter();\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\r\n  const [productToDelete, setProductToDelete] = useState<number | null>(null);\r\n\r\n  const deleteProduct = useDeleteProduct();\r\n  const updateProductStatus = useUpdateProductStatus(productToDelete || 0);\r\n  const { setSelectedProduct } = useProductStore();\r\n\r\n  const handleViewProduct = (id: number, product?: Product) => {\r\n    // If product is provided, store it in the product store\r\n    if (product) {\r\n      setSelectedProduct(product);\r\n    }\r\n    router.push(`/products/${id}`);\r\n  };\r\n\r\n  const handleEditProduct = (id: number, product?: Product) => {\r\n    // If product is provided, store it in the product store\r\n    if (product) {\r\n      setSelectedProduct(product);\r\n    }\r\n    router.push(`/products/${id}/edit`);\r\n  };\r\n\r\n  const handleDeleteClick = (id: number) => {\r\n    setProductToDelete(id);\r\n    setDeleteDialogOpen(true);\r\n  };\r\n\r\n  const handleDeleteConfirm = async () => {\r\n    if (productToDelete) {\r\n      await deleteProduct.mutateAsync(productToDelete);\r\n      setDeleteDialogOpen(false);\r\n      setProductToDelete(null);\r\n    }\r\n  };\r\n\r\n  const handleStatusChange = async (\r\n    productId: number,\r\n    status: \"active\" | \"inactive\"\r\n  ) => {\r\n    // Set the product ID for the status update\r\n    setProductToDelete(productId);\r\n    // Use the updated hook with the correct product ID\r\n    await updateProductStatus.mutateAsync({ status });\r\n  };\r\n\r\n  const handleSearch = () => {\r\n    onSearch(searchQuery);\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (e.key === \"Enter\") {\r\n      handleSearch();\r\n    }\r\n  };\r\n\r\n  const getProductInitials = (name?: string | null) => {\r\n    if (!name) return \"P\"; // Default fallback for undefined or null names\r\n\r\n    return (\r\n      name\r\n        .split(\" \")\r\n        .map((n) => n[0] || \"\")\r\n        .join(\"\")\r\n        .toUpperCase()\r\n        .substring(0, 2) || \"P\"\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <div className=\"flex items-center gap-2 w-full max-w-sm\">\r\n          <Input\r\n            placeholder=\"Search products...\"\r\n            value={searchQuery}\r\n            onChange={(e) => setSearchQuery(e.target.value)}\r\n            onKeyDown={handleKeyDown}\r\n            className=\"h-9\"\r\n          />\r\n          <Button variant=\"outline\" size=\"sm\" onClick={handleSearch}>\r\n            <Search className=\"h-4 w-4\" />\r\n          </Button>\r\n        </div>\r\n        <Button onClick={() => router.push(\"/products/create\")}>\r\n          <Plus className=\"h-4 w-4 mr-2\" />\r\n          Add Product\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"rounded-md border\">\r\n        <Table>\r\n          <TableHeader>\r\n            <TableRow>\r\n              <TableHead\r\n                className=\"cursor-pointer\"\r\n                onClick={() => onSort && onSort('name')}\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  Product\r\n                  {sortField === 'name' && (\r\n                    <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'asc' ? 'transform rotate-180' : ''}`} />\r\n                  )}\r\n                </div>\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"cursor-pointer\"\r\n                onClick={() => onSort && onSort('sku')}\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  SKU\r\n                  {sortField === 'sku' && (\r\n                    <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'asc' ? 'transform rotate-180' : ''}`} />\r\n                  )}\r\n                </div>\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"cursor-pointer\"\r\n                onClick={() => onSort && onSort('suggested_buying_price')}\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  Buying Price\r\n                  {sortField === 'suggested_buying_price' && (\r\n                    <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'asc' ? 'transform rotate-180' : ''}`} />\r\n                  )}\r\n                </div>\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"cursor-pointer\"\r\n                onClick={() => onSort && onSort('suggested_selling_price')}\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  Retail Price\r\n                  {sortField === 'suggested_selling_price' && (\r\n                    <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'asc' ? 'transform rotate-180' : ''}`} />\r\n                  )}\r\n                </div>\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"cursor-pointer\"\r\n                onClick={() => onSort && onSort('default_wholesale_price')}\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  Wholesale Price\r\n                  {sortField === 'default_wholesale_price' && (\r\n                    <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'asc' ? 'transform rotate-180' : ''}`} />\r\n                  )}\r\n                </div>\r\n              </TableHead>\r\n\r\n              <TableHead>Brand</TableHead>\r\n              <TableHead>Category</TableHead>\r\n              <TableHead\r\n                className=\"cursor-pointer\"\r\n                onClick={() => onSort && onSort('status')}\r\n              >\r\n                <div className=\"flex items-center\">\r\n                  Status\r\n                  {sortField === 'status' && (\r\n                    <ArrowUpDown className={`ml-2 h-4 w-4 ${sortDirection === 'asc' ? 'transform rotate-180' : ''}`} />\r\n                  )}\r\n                </div>\r\n              </TableHead>\r\n              <TableHead className=\"w-[80px]\">Actions</TableHead>\r\n            </TableRow>\r\n          </TableHeader>\r\n          <TableBody>\r\n            {isLoading ? (\r\n              <TableRow>\r\n                <TableCell colSpan={8} className=\"text-center py-8\">\r\n                  Loading products...\r\n                </TableCell>\r\n              </TableRow>\r\n            ) : products.length === 0 ? (\r\n              <TableRow>\r\n                <TableCell colSpan={8} className=\"text-center py-8\">\r\n                  No products found. Create your first product.\r\n                </TableCell>\r\n              </TableRow>\r\n            ) : (\r\n              products.map((product) => (\r\n                <TableRow\r\n                  key={product.id}\r\n                  className=\"cursor-pointer hover:bg-muted/50\"\r\n                  onClick={() => handleViewProduct(product.id, product)}\r\n                >\r\n                  <TableCell>\r\n                    <div className=\"flex items-center gap-3\">\r\n                      <Avatar className=\"h-9 w-9\">\r\n                        <AvatarImage\r\n                          src={product.image_url}\r\n                          alt={product.name}\r\n                        />\r\n                        <AvatarFallback>\r\n                          {getProductInitials(product.name)}\r\n                        </AvatarFallback>\r\n                      </Avatar>\r\n                      <div className=\"font-medium\">{product.name}</div>\r\n                    </div>\r\n                  </TableCell>\r\n                  <TableCell>{product.sku || \"-\"}</TableCell>\r\n                  <TableCell>{formatCurrency(parseFloat(product.suggested_buying_price as string) || 0)}</TableCell>\r\n                  <TableCell>\r\n                    {product.is_discounted || (product.discount_amount && product.discount_amount > 0) ? (\r\n                      <div className=\"flex flex-col\">\r\n                        <span className=\"font-medium text-primary\">\r\n                          {formatCurrency(product.final_price || parseFloat(product.suggested_selling_price as string) || 0)}\r\n                        </span>\r\n                        <span className=\"text-xs line-through text-muted-foreground\">\r\n                          {formatCurrency(product.original_price || parseFloat(product.suggested_selling_price as string) || 0)}\r\n                        </span>\r\n                        <span className=\"text-xs text-green-600\">\r\n                          {product.discount_source ? `${product.discount_source.toLowerCase()} discount` : 'Discounted'}\r\n                        </span>\r\n                      </div>\r\n                    ) : (\r\n                      formatCurrency(product.price || parseFloat(product.suggested_selling_price as string) || 0)\r\n                    )}\r\n                  </TableCell>\r\n                  <TableCell>{formatCurrency(parseFloat(product.default_wholesale_price as string) || 0)}</TableCell>\r\n\r\n                  <TableCell>\r\n                    {product.Brand ? (\r\n                      <span className=\"truncate max-w-[150px] inline-block\" title={product.Brand.name}>\r\n                        {product.Brand.name}\r\n                      </span>\r\n                    ) : product.brand_id ? (\r\n                      <span>ID: {product.brand_id}</span>\r\n                    ) : (\r\n                      \"-\"\r\n                    )}\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    {product.categoryHierarchy &&\r\n                    product.categoryHierarchy.length > 0 ? (\r\n                      <span\r\n                        className=\"truncate max-w-[150px] inline-block\"\r\n                        title={product.categoryHierarchy\r\n                          .map((cat) => cat.name)\r\n                          .join(\" > \")}\r\n                      >\r\n                        {product.categoryHierarchy\r\n                          .map((cat) => cat.name)\r\n                          .join(\" > \")}\r\n                      </span>\r\n                    ) : product.ProductCategory ? (\r\n                      <span className=\"truncate max-w-[150px] inline-block\" title={product.ProductCategory.name}>\r\n                        {product.ProductCategory.name}\r\n                      </span>\r\n                    ) : product.category?.name ? (\r\n                      <span className=\"truncate max-w-[150px] inline-block\" title={product.category.name}>\r\n                        {product.category.name}\r\n                      </span>\r\n                    ) : (\r\n                      \"-\"\r\n                    )}\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <ProductStatusBadge status={product.is_active ? \"active\" : \"inactive\"} />\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    <DropdownMenu>\r\n                      <DropdownMenuTrigger\r\n                        asChild\r\n                        onClick={(e) => e.stopPropagation()}\r\n                      >\r\n                        <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n                          <span className=\"sr-only\">Open menu</span>\r\n                          <MoreHorizontal className=\"h-4 w-4\" />\r\n                        </Button>\r\n                      </DropdownMenuTrigger>\r\n                      <DropdownMenuContent align=\"end\">\r\n                        <DropdownMenuLabel>Actions</DropdownMenuLabel>\r\n                        <DropdownMenuItem\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            handleViewProduct(product.id, product);\r\n                          }}\r\n                        >\r\n                          <Eye className=\"h-4 w-4 mr-2\" />\r\n                          View Details\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuItem\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            handleEditProduct(product.id, product);\r\n                          }}\r\n                        >\r\n                          <Edit className=\"h-4 w-4 mr-2\" />\r\n                          Edit\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuSeparator />\r\n                        {!product.is_active ? (\r\n                          <DropdownMenuItem\r\n                            onClick={(e) => {\r\n                              e.stopPropagation();\r\n                              handleStatusChange(product.id, \"active\");\r\n                            }}\r\n                          >\r\n                            <CheckCircle className=\"h-4 w-4 mr-2\" />\r\n                            Activate\r\n                          </DropdownMenuItem>\r\n                        ) : (\r\n                          <DropdownMenuItem\r\n                            onClick={(e) => {\r\n                              e.stopPropagation();\r\n                              handleStatusChange(product.id, \"inactive\");\r\n                            }}\r\n                          >\r\n                            <Ban className=\"h-4 w-4 mr-2\" />\r\n                            Deactivate\r\n                          </DropdownMenuItem>\r\n                        )}\r\n                        <DropdownMenuSeparator />\r\n                        <DropdownMenuItem\r\n                          className=\"text-destructive focus:text-destructive\"\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            handleDeleteClick(product.id);\r\n                          }}\r\n                        >\r\n                          <Trash2 className=\"h-4 w-4 mr-2\" />\r\n                          Delete\r\n                        </DropdownMenuItem>\r\n                      </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n\r\n      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              This action cannot be undone. This will permanently delete the\r\n              product and all associated data.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={handleDeleteConfirm}\r\n              className=\"bg-destructive text-destructive-foreground hover:bg-destructive/90\"\r\n            >\r\n              {deleteProduct.isPending ? \"Deleting...\" : \"Delete\"}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAUA;AACA;AACA;AAQA;AACA;AAQA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AAIA;AACA;AAnDA;;;;;;;;;;;;;;;AA8DO,SAAS,cAAc,EAC5B,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,MAAM,EACN,SAAS,EACT,gBAAgB,KAAK,EACF;IACnB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,MAAM,gBAAgB,CAAA,GAAA,uJAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,sBAAsB,CAAA,GAAA,uJAAA,CAAA,yBAAsB,AAAD,EAAE,mBAAmB;IACtE,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD;IAE7C,MAAM,oBAAoB,CAAC,IAAY;QACrC,wDAAwD;QACxD,IAAI,SAAS;YACX,mBAAmB;QACrB;QACA,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,IAAI;IAC/B;IAEA,MAAM,oBAAoB,CAAC,IAAY;QACrC,wDAAwD;QACxD,IAAI,SAAS;YACX,mBAAmB;QACrB;QACA,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,GAAG,KAAK,CAAC;IACpC;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,MAAM,cAAc,WAAW,CAAC;YAChC,oBAAoB;YACpB,mBAAmB;QACrB;IACF;IAEA,MAAM,qBAAqB,OACzB,WACA;QAEA,2CAA2C;QAC3C,mBAAmB;QACnB,mDAAmD;QACnD,MAAM,oBAAoB,WAAW,CAAC;YAAE;QAAO;IACjD;IAEA,MAAM,eAAe;QACnB,SAAS;IACX;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,MAAM,OAAO,KAAK,+CAA+C;QAEtE,OACE,KACG,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,IAAI,IACnB,IAAI,CAAC,IACL,WAAW,GACX,SAAS,CAAC,GAAG,MAAM;IAE1B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAW;gCACX,WAAU;;;;;;0CAEZ,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;0CAC3C,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGtB,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;0CACjC,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKrC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;sCACJ,8OAAC,iIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;kDACP,8OAAC,iIAAA,CAAA,YAAS;wCACR,WAAU;wCACV,SAAS,IAAM,UAAU,OAAO;kDAEhC,cAAA,8OAAC;4CAAI,WAAU;;gDAAoB;gDAEhC,cAAc,wBACb,8OAAC,wNAAA,CAAA,cAAW;oDAAC,WAAW,CAAC,aAAa,EAAE,kBAAkB,QAAQ,yBAAyB,IAAI;;;;;;;;;;;;;;;;;kDAIrG,8OAAC,iIAAA,CAAA,YAAS;wCACR,WAAU;wCACV,SAAS,IAAM,UAAU,OAAO;kDAEhC,cAAA,8OAAC;4CAAI,WAAU;;gDAAoB;gDAEhC,cAAc,uBACb,8OAAC,wNAAA,CAAA,cAAW;oDAAC,WAAW,CAAC,aAAa,EAAE,kBAAkB,QAAQ,yBAAyB,IAAI;;;;;;;;;;;;;;;;;kDAIrG,8OAAC,iIAAA,CAAA,YAAS;wCACR,WAAU;wCACV,SAAS,IAAM,UAAU,OAAO;kDAEhC,cAAA,8OAAC;4CAAI,WAAU;;gDAAoB;gDAEhC,cAAc,0CACb,8OAAC,wNAAA,CAAA,cAAW;oDAAC,WAAW,CAAC,aAAa,EAAE,kBAAkB,QAAQ,yBAAyB,IAAI;;;;;;;;;;;;;;;;;kDAIrG,8OAAC,iIAAA,CAAA,YAAS;wCACR,WAAU;wCACV,SAAS,IAAM,UAAU,OAAO;kDAEhC,cAAA,8OAAC;4CAAI,WAAU;;gDAAoB;gDAEhC,cAAc,2CACb,8OAAC,wNAAA,CAAA,cAAW;oDAAC,WAAW,CAAC,aAAa,EAAE,kBAAkB,QAAQ,yBAAyB,IAAI;;;;;;;;;;;;;;;;;kDAIrG,8OAAC,iIAAA,CAAA,YAAS;wCACR,WAAU;wCACV,SAAS,IAAM,UAAU,OAAO;kDAEhC,cAAA,8OAAC;4CAAI,WAAU;;gDAAoB;gDAEhC,cAAc,2CACb,8OAAC,wNAAA,CAAA,cAAW;oDAAC,WAAW,CAAC,aAAa,EAAE,kBAAkB,QAAQ,yBAAyB,IAAI;;;;;;;;;;;;;;;;;kDAKrG,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,iIAAA,CAAA,YAAS;wCACR,WAAU;wCACV,SAAS,IAAM,UAAU,OAAO;kDAEhC,cAAA,8OAAC;4CAAI,WAAU;;gDAAoB;gDAEhC,cAAc,0BACb,8OAAC,wNAAA,CAAA,cAAW;oDAAC,WAAW,CAAC,aAAa,EAAE,kBAAkB,QAAQ,yBAAyB,IAAI;;;;;;;;;;;;;;;;;kDAIrG,8OAAC,iIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;;;;;;;;;;;;sCAGpC,8OAAC,iIAAA,CAAA,YAAS;sCACP,0BACC,8OAAC,iIAAA,CAAA,WAAQ;0CACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;oCAAC,SAAS;oCAAG,WAAU;8CAAmB;;;;;;;;;;uCAIpD,SAAS,MAAM,KAAK,kBACtB,8OAAC,iIAAA,CAAA,WAAQ;0CACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;oCAAC,SAAS;oCAAG,WAAU;8CAAmB;;;;;;;;;;uCAKtD,SAAS,GAAG,CAAC,CAAC,wBACZ,8OAAC,iIAAA,CAAA,WAAQ;oCAEP,WAAU;oCACV,SAAS,IAAM,kBAAkB,QAAQ,EAAE,EAAE;;sDAE7C,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,8OAAC,kIAAA,CAAA,cAAW;gEACV,KAAK,QAAQ,SAAS;gEACtB,KAAK,QAAQ,IAAI;;;;;;0EAEnB,8OAAC,kIAAA,CAAA,iBAAc;0EACZ,mBAAmB,QAAQ,IAAI;;;;;;;;;;;;kEAGpC,8OAAC;wDAAI,WAAU;kEAAe,QAAQ,IAAI;;;;;;;;;;;;;;;;;sDAG9C,8OAAC,iIAAA,CAAA,YAAS;sDAAE,QAAQ,GAAG,IAAI;;;;;;sDAC3B,8OAAC,iIAAA,CAAA,YAAS;sDAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,QAAQ,sBAAsB,KAAe;;;;;;sDACnF,8OAAC,iIAAA,CAAA,YAAS;sDACP,QAAQ,aAAa,IAAK,QAAQ,eAAe,IAAI,QAAQ,eAAe,GAAG,kBAC9E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,WAAW,IAAI,WAAW,QAAQ,uBAAuB,KAAe;;;;;;kEAElG,8OAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,cAAc,IAAI,WAAW,QAAQ,uBAAuB,KAAe;;;;;;kEAErG,8OAAC;wDAAK,WAAU;kEACb,QAAQ,eAAe,GAAG,GAAG,QAAQ,eAAe,CAAC,WAAW,GAAG,SAAS,CAAC,GAAG;;;;;;;;;;;uDAIrF,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,KAAK,IAAI,WAAW,QAAQ,uBAAuB,KAAe;;;;;;sDAG7F,8OAAC,iIAAA,CAAA,YAAS;sDAAE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,QAAQ,uBAAuB,KAAe;;;;;;sDAEpF,8OAAC,iIAAA,CAAA,YAAS;sDACP,QAAQ,KAAK,iBACZ,8OAAC;gDAAK,WAAU;gDAAsC,OAAO,QAAQ,KAAK,CAAC,IAAI;0DAC5E,QAAQ,KAAK,CAAC,IAAI;;;;;uDAEnB,QAAQ,QAAQ,iBAClB,8OAAC;;oDAAK;oDAAK,QAAQ,QAAQ;;;;;;uDAE3B;;;;;;sDAGJ,8OAAC,iIAAA,CAAA,YAAS;sDACP,QAAQ,iBAAiB,IAC1B,QAAQ,iBAAiB,CAAC,MAAM,GAAG,kBACjC,8OAAC;gDACC,WAAU;gDACV,OAAO,QAAQ,iBAAiB,CAC7B,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI,EACrB,IAAI,CAAC;0DAEP,QAAQ,iBAAiB,CACvB,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI,EACrB,IAAI,CAAC;;;;;uDAER,QAAQ,eAAe,iBACzB,8OAAC;gDAAK,WAAU;gDAAsC,OAAO,QAAQ,eAAe,CAAC,IAAI;0DACtF,QAAQ,eAAe,CAAC,IAAI;;;;;uDAE7B,QAAQ,QAAQ,EAAE,qBACpB,8OAAC;gDAAK,WAAU;gDAAsC,OAAO,QAAQ,QAAQ,CAAC,IAAI;0DAC/E,QAAQ,QAAQ,CAAC,IAAI;;;;;uDAGxB;;;;;;sDAGJ,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,wKAAA,CAAA,qBAAkB;gDAAC,QAAQ,QAAQ,SAAS,GAAG,WAAW;;;;;;;;;;;sDAE7D,8OAAC,iIAAA,CAAA,YAAS;sDACR,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kEACX,8OAAC,4IAAA,CAAA,sBAAmB;wDAClB,OAAO;wDACP,SAAS,CAAC,IAAM,EAAE,eAAe;kEAEjC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,WAAU;;8EAChC,8OAAC;oEAAK,WAAU;8EAAU;;;;;;8EAC1B,8OAAC,gNAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,8OAAC,4IAAA,CAAA,oBAAiB;0EAAC;;;;;;0EACnB,8OAAC,4IAAA,CAAA,mBAAgB;gEACf,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,kBAAkB,QAAQ,EAAE,EAAE;gEAChC;;kFAEA,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGlC,8OAAC,4IAAA,CAAA,mBAAgB;gEACf,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,kBAAkB,QAAQ,EAAE,EAAE;gEAChC;;kFAEA,8OAAC,2MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;4DACrB,CAAC,QAAQ,SAAS,iBACjB,8OAAC,4IAAA,CAAA,mBAAgB;gEACf,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,mBAAmB,QAAQ,EAAE,EAAE;gEACjC;;kFAEA,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEAAiB;;;;;;qFAI1C,8OAAC,4IAAA,CAAA,mBAAgB;gEACf,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,mBAAmB,QAAQ,EAAE,EAAE;gEACjC;;kFAEA,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAIpC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0EACtB,8OAAC,4IAAA,CAAA,mBAAgB;gEACf,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,kBAAkB,QAAQ,EAAE;gEAC9B;;kFAEA,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;mCA3ItC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0BAwJ3B,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;8CAAC;;;;;;;;;;;;sCAK1B,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;8CAET,cAAc,SAAS,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzD", "debugId": null}}, {"offset": {"line": 6046, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 6271, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/components/product-filter-bar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { Search } from \"lucide-react\";\r\nimport React from \"react\";\r\n\r\ninterface ProductFilterBarProps {\r\n  searchQuery: string;\r\n  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;\r\n  onSearchKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;\r\n  onSearchClick: () => void;\r\n  onCategoryChange: (categoryId: string) => void;\r\n  onBrandChange: (brandId: string) => void;\r\n  categories: any[];\r\n  brands: any[];\r\n  placeholder?: string;\r\n}\r\n\r\nexport function ProductFilterBar({\r\n  searchQuery,\r\n  onSearchChange,\r\n  onSearchKeyDown,\r\n  onSearchClick,\r\n  onCategoryChange,\r\n  onBrandChange,\r\n  categories,\r\n  brands,\r\n  placeholder = \"Search products...\"\r\n}: ProductFilterBarProps) {\r\n  return (\r\n    <div className=\"flex flex-col md:flex-row gap-4 mb-4\">\r\n      <div className=\"flex items-center gap-2 w-full md:w-2/5\">\r\n        <Input\r\n          placeholder={placeholder}\r\n          value={searchQuery}\r\n          onChange={onSearchChange}\r\n          onKeyDown={onSearchKeyDown}\r\n          className=\"h-9\"\r\n        />\r\n        <Button variant=\"outline\" size=\"sm\" onClick={onSearchClick}>\r\n          <Search className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"w-full md:w-3/10\">\r\n        <Select onValueChange={onCategoryChange} defaultValue=\"all\">\r\n          <SelectTrigger>\r\n            <SelectValue placeholder=\"Filter by category\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Categories</SelectItem>\r\n            {categories\r\n              ? categories.map((category) => (\r\n                  <SelectItem\r\n                    key={category.id}\r\n                    value={category.id.toString()}\r\n                  >\r\n                    {category.name}\r\n                  </SelectItem>\r\n                ))\r\n              : null}\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n\r\n      <div className=\"w-full md:w-3/10\">\r\n        <Select onValueChange={onBrandChange} defaultValue=\"all\">\r\n          <SelectTrigger>\r\n            <SelectValue placeholder=\"Filter by brand\" />\r\n          </SelectTrigger>\r\n          <SelectContent>\r\n            <SelectItem value=\"all\">All Brands</SelectItem>\r\n            {brands\r\n              ? brands.map((brand) => (\r\n                  <SelectItem\r\n                    key={brand.id}\r\n                    value={brand.id.toString()}\r\n                  >\r\n                    {brand.name}\r\n                  </SelectItem>\r\n                ))\r\n              : null}\r\n          </SelectContent>\r\n        </Select>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAOA;AAXA;;;;;;AA0BO,SAAS,iBAAiB,EAC/B,WAAW,EACX,cAAc,EACd,eAAe,EACf,aAAa,EACb,gBAAgB,EAChB,aAAa,EACb,UAAU,EACV,MAAM,EACN,cAAc,oBAAoB,EACZ;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iIAAA,CAAA,QAAK;wBACJ,aAAa;wBACb,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,WAAU;;;;;;kCAEZ,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,SAAS;kCAC3C,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAItB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,eAAe;oBAAkB,cAAa;;sCACpD,8OAAC,kIAAA,CAAA,gBAAa;sCACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,aAAY;;;;;;;;;;;sCAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8CACZ,8OAAC,kIAAA,CAAA,aAAU;oCAAC,OAAM;8CAAM;;;;;;gCACvB,aACG,WAAW,GAAG,CAAC,CAAC,yBACd,8OAAC,kIAAA,CAAA,aAAU;wCAET,OAAO,SAAS,EAAE,CAAC,QAAQ;kDAE1B,SAAS,IAAI;uCAHT,SAAS,EAAE;;;;gDAMpB;;;;;;;;;;;;;;;;;;0BAKV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,eAAe;oBAAe,cAAa;;sCACjD,8OAAC,kIAAA,CAAA,gBAAa;sCACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,aAAY;;;;;;;;;;;sCAE3B,8OAAC,kIAAA,CAAA,gBAAa;;8CACZ,8OAAC,kIAAA,CAAA,aAAU;oCAAC,OAAM;8CAAM;;;;;;gCACvB,SACG,OAAO,GAAG,CAAC,CAAC,sBACV,8OAAC,kIAAA,CAAA,aAAU;wCAET,OAAO,MAAM,EAAE,CAAC,QAAQ;kDAEvB,MAAM,IAAI;uCAHN,MAAM,EAAE;;;;gDAMjB;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}, {"offset": {"line": 6446, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/api/category-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse, User } from \"@/types/api\";\r\nimport {\r\n  Category,\r\n  CreateCategoryRequest,\r\n  UpdateCategoryRequest,\r\n  UpdateCategoryStatusRequest,\r\n} from \"@/types/product\";\r\n\r\nexport const categoryService = {\r\n  getCategories: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Category>> => {\r\n    try {\r\n      // Get the current user to determine their role\r\n      const currentUser: User = await apiClient.get(\"/auth/me\");\r\n      console.log(\"Current user for categories:\", currentUser);\r\n\r\n      // Determine which endpoint to use based on user role\r\n      const isAdmin =\r\n        currentUser?.role_name &&\r\n        [\"super_admin\", \"company_admin\", \"tenant_admin\"].includes(\r\n          currentUser.role_name.toLowerCase()\r\n        );\r\n\r\n      // For non-admin users, add tenant_id filter if not already present\r\n      const queryParams = { ...params };\r\n      if (!isAdmin && currentUser?.tenant_id && !queryParams.tenant_id) {\r\n        queryParams.tenant_id = currentUser.tenant_id;\r\n      }\r\n\r\n      // console.log(\"Fetching categories with params:\", queryParams);\r\n      const response = await apiClient.get<any>(\"/product-categories\", {\r\n        params: queryParams,\r\n      });\r\n\r\n      // Map API response to our Category type\r\n      const mapApiCategoryToCategory = (apiCategory: any): Category => {\r\n        return {\r\n          ...apiCategory,\r\n          // Add missing fields that our UI expects but API doesn't provide\r\n          status: apiCategory.deleted_at ? \"inactive\" : \"active\", // Derive status from deleted_at\r\n          // Map Parent to parent for UI consistency\r\n          parent: apiCategory.Parent ? {\r\n            id: apiCategory.Parent.id,\r\n            name: apiCategory.Parent.name,\r\n            description: apiCategory.Parent.description,\r\n            tenant_id: apiCategory.Parent.tenant_id,\r\n            created_at: apiCategory.Parent.created_at,\r\n            updated_at: apiCategory.Parent.updated_at,\r\n            deleted_at: apiCategory.Parent.deleted_at,\r\n            status: apiCategory.Parent.deleted_at ? \"inactive\" : \"active\",\r\n          } : null,\r\n        };\r\n      };\r\n\r\n      // If response is an array, convert to paginated format with mapped categories\r\n      if (Array.isArray(response)) {\r\n        const mappedCategories = response.map(mapApiCategoryToCategory);\r\n        return {\r\n          data: mappedCategories,\r\n          pagination: {\r\n            total: mappedCategories.length,\r\n            page: 1,\r\n            limit: mappedCategories.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        const mappedCategories = response.data.map(mapApiCategoryToCategory);\r\n        return {\r\n          data: mappedCategories,\r\n          pagination: response.pagination || {\r\n            total: mappedCategories.length,\r\n            page: 1,\r\n            limit: mappedCategories.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Default fallback\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error: any) {\r\n      console.error(\"Error in getCategories:\", error?.message || error);\r\n\r\n      // Log more detailed error information\r\n      if (error.code === 'ECONNABORTED') {\r\n        console.error(\"Request timeout in getCategories\");\r\n      } else if (error.response) {\r\n        console.error(\"Response error in getCategories:\", error.response.status, error.response.data);\r\n      } else if (error.request) {\r\n        console.error(\"Request error in getCategories (no response)\");\r\n      }\r\n\r\n      // Rethrow network errors to allow proper handling by the UI\r\n      if (error.code === 'ECONNABORTED' || (error.request && !error.response)) {\r\n        throw new Error(\"Network error. Please check your connection.\");\r\n      }\r\n\r\n      // Return empty data on other errors\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getCategoryById: async (id: number): Promise<Category> => {\r\n    return apiClient.get(`/product-categories/${id}`);\r\n  },\r\n\r\n  createCategory: async (\r\n    category: CreateCategoryRequest\r\n  ): Promise<Category> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      description: category.description,\r\n      parent_id: category.parent_id,\r\n      // Include other fields that might be used by the UI\r\n      ...category,\r\n    };\r\n\r\n    return apiClient.post(\"/product-categories\", apiRequest);\r\n  },\r\n\r\n  updateCategory: async (\r\n    id: number,\r\n    category: UpdateCategoryRequest\r\n  ): Promise<Category> => {\r\n    // Ensure we're sending the correct fields to the API\r\n    const apiRequest = {\r\n      name: category.name,\r\n      description: category.description,\r\n      parent_id: category.parent_id,\r\n      // Include other fields that might be used by the UI\r\n      ...category,\r\n    };\r\n\r\n    return apiClient.put(`/product-categories/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteCategory: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/product-categories/${id}`);\r\n  },\r\n\r\n  updateCategoryStatus: async (\r\n    id: number,\r\n    status: UpdateCategoryStatusRequest\r\n  ): Promise<Category> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      status: status.status, // 'active' or 'inactive'\r\n    };\r\n\r\n    return apiClient.put(`/product-categories/${id}/status`, apiRequest);\r\n  },\r\n\r\n  uploadCategoryImage: async (\r\n    id: number,\r\n    file: File\r\n  ): Promise<{ image_url: string }> => {\r\n    const formData = new FormData();\r\n    formData.append(\"image\", file);\r\n\r\n    return apiClient.post(`/product-categories/${id}/image`, formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n    });\r\n  },\r\n\r\n  getCategoryTree: async (\r\n    params?: Record<string, any>\r\n  ): Promise<Category[]> => {\r\n    return apiClient.get(\"/product-categories/tree\", { params });\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AASO,MAAM,kBAAkB;IAC7B,eAAe,OACb;QAEA,IAAI;YACF,+CAA+C;YAC/C,MAAM,cAAoB,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;YAC9C,QAAQ,GAAG,CAAC,gCAAgC;YAE5C,qDAAqD;YACrD,MAAM,UACJ,aAAa,aACb;gBAAC;gBAAe;gBAAiB;aAAe,CAAC,QAAQ,CACvD,YAAY,SAAS,CAAC,WAAW;YAGrC,mEAAmE;YACnE,MAAM,cAAc;gBAAE,GAAG,MAAM;YAAC;YAChC,IAAI,CAAC,WAAW,aAAa,aAAa,CAAC,YAAY,SAAS,EAAE;gBAChE,YAAY,SAAS,GAAG,YAAY,SAAS;YAC/C;YAEA,gEAAgE;YAChE,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,uBAAuB;gBAC/D,QAAQ;YACV;YAEA,wCAAwC;YACxC,MAAM,2BAA2B,CAAC;gBAChC,OAAO;oBACL,GAAG,WAAW;oBACd,iEAAiE;oBACjE,QAAQ,YAAY,UAAU,GAAG,aAAa;oBAC9C,0CAA0C;oBAC1C,QAAQ,YAAY,MAAM,GAAG;wBAC3B,IAAI,YAAY,MAAM,CAAC,EAAE;wBACzB,MAAM,YAAY,MAAM,CAAC,IAAI;wBAC7B,aAAa,YAAY,MAAM,CAAC,WAAW;wBAC3C,WAAW,YAAY,MAAM,CAAC,SAAS;wBACvC,YAAY,YAAY,MAAM,CAAC,UAAU;wBACzC,YAAY,YAAY,MAAM,CAAC,UAAU;wBACzC,YAAY,YAAY,MAAM,CAAC,UAAU;wBACzC,QAAQ,YAAY,MAAM,CAAC,UAAU,GAAG,aAAa;oBACvD,IAAI;gBACN;YACF;YAEA,8EAA8E;YAC9E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,MAAM,mBAAmB,SAAS,GAAG,CAAC;gBACtC,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,iBAAiB,MAAM;wBAC9B,MAAM;wBACN,OAAO,iBAAiB,MAAM;wBAC9B,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,MAAM,mBAAmB,SAAS,IAAI,CAAC,GAAG,CAAC;gBAC3C,OAAO;oBACL,MAAM;oBACN,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,iBAAiB,MAAM;wBAC9B,MAAM;wBACN,OAAO,iBAAiB,MAAM;wBAC9B,YAAY;oBACd;gBACF;YACF;YAEA,mBAAmB;YACnB,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B,OAAO,WAAW;YAE3D,sCAAsC;YACtC,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,QAAQ,KAAK,CAAC;YAChB,OAAO,IAAI,MAAM,QAAQ,EAAE;gBACzB,QAAQ,KAAK,CAAC,oCAAoC,MAAM,QAAQ,CAAC,MAAM,EAAE,MAAM,QAAQ,CAAC,IAAI;YAC9F,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,QAAQ,KAAK,CAAC;YAChB;YAEA,4DAA4D;YAC5D,IAAI,MAAM,IAAI,KAAK,kBAAmB,MAAM,OAAO,IAAI,CAAC,MAAM,QAAQ,EAAG;gBACvE,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,iBAAiB,OAAO;QACtB,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;IAClD;IAEA,gBAAgB,OACd;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,aAAa,SAAS,WAAW;YACjC,WAAW,SAAS,SAAS;YAC7B,oDAAoD;YACpD,GAAG,QAAQ;QACb;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;IAC/C;IAEA,gBAAgB,OACd,IACA;QAEA,qDAAqD;QACrD,MAAM,aAAa;YACjB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW;YACjC,WAAW,SAAS,SAAS;YAC7B,oDAAoD;YACpD,GAAG,QAAQ;QACb;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;IACpD;IAEA,gBAAgB,OAAO;QACrB,OAAO,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;IACrD;IAEA,sBAAsB,OACpB,IACA;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,QAAQ,OAAO,MAAM;QACvB;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,GAAG,OAAO,CAAC,EAAE;IAC3D;IAEA,qBAAqB,OACnB,IACA;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,oBAAoB,EAAE,GAAG,MAAM,CAAC,EAAE,UAAU;YACjE,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF;IAEA,iBAAiB,OACf;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,4BAA4B;YAAE;QAAO;IAC5D;AACF", "debugId": null}}, {"offset": {"line": 6610, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/hooks/use-categories.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  CreateCategoryRequest,\r\n  UpdateCategoryRequest,\r\n  UpdateCategoryStatusRequest,\r\n} from \"@/types/product\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport { categoryService } from \"../api/category-service\";\r\n\r\nexport function useCategories(params?: Record<string, any>) {\r\n  return useQuery({\r\n    queryKey: [\"categories\", params],\r\n    queryFn: () => categoryService.getCategories(params),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useCategory(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"categories\", id],\r\n    queryFn: () => categoryService.getCategoryById(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCategoryTree(params?: Record<string, any>) {\r\n  return useQuery({\r\n    queryKey: [\"categoryTree\", params],\r\n    queryFn: () => categoryService.getCategoryTree(params),\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n  });\r\n}\r\n\r\nexport function useCreateCategory() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (category: CreateCategoryRequest) =>\r\n      categoryService.createCategory(category),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"categories\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"categoryTree\"] });\r\n      toast.success(\"Category created\", {\r\n        description: \"The category has been created successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error creating category\", {\r\n        description:\r\n          error.message || \"An error occurred while creating the category.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateCategory(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (category: UpdateCategoryRequest) =>\r\n      categoryService.updateCategory(id, category),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"categories\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"categories\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"categoryTree\"] });\r\n      toast.success(\"Category updated\", {\r\n        description: \"The category has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating category\", {\r\n        description:\r\n          error.message || \"An error occurred while updating the category.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteCategory() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => categoryService.deleteCategory(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"categories\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"categoryTree\"] });\r\n      toast.success(\"Category deleted\", {\r\n        description: \"The category has been deleted successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error deleting category\", {\r\n        description:\r\n          error.message || \"An error occurred while deleting the category.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateCategoryStatus(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (status: UpdateCategoryStatusRequest) =>\r\n      categoryService.updateCategoryStatus(id, status),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"categories\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"categories\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"categoryTree\"] });\r\n      toast.success(\"Category status updated\", {\r\n        description: \"The category status has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating category status\", {\r\n        description:\r\n          error.message ||\r\n          \"An error occurred while updating the category status.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUploadCategoryImage(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (file: File) => categoryService.uploadCategoryImage(id, file),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"categories\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"categories\"] });\r\n      toast.success(\"Image uploaded\", {\r\n        description: \"The category image has been uploaded successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error uploading image\", {\r\n        description:\r\n          error.message || \"An error occurred while uploading the image.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAOA;AAAA;AAAA;AACA;AACA;AATA;;;;AAWO,SAAS,cAAc,MAA4B;IACxD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAc;SAAO;QAChC,SAAS,IAAM,yJAAA,CAAA,kBAAe,CAAC,aAAa,CAAC;QAC7C,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,QAAQ,CAAC;YACP,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,MAAM,EAAE;oBACR,YAAY;wBACV,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YACA,OAAO;QACT;IACF;AACF;AAEO,SAAS,YAAY,EAAU;IACpC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAc;SAAG;QAC5B,SAAS,IAAM,yJAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;QAC/C,SAAS,CAAC,CAAC;IACb;AACF;AAEO,SAAS,gBAAgB,MAA4B;IAC1D,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAgB;SAAO;QAClC,SAAS,IAAM,yJAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;QAC/C,OAAO;QACP,sBAAsB;IACxB;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,WACX,yJAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;QACjC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAa;YAAC;YACzD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAe;YAAC;YAC3D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,oBAAoB;gBAChC,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;gBACrC,aACE,MAAM,OAAO,IAAI;YACrB;QACF;IACF;AACF;AAEO,SAAS,kBAAkB,EAAU;IAC1C,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,WACX,yJAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,IAAI;QACrC,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAc;iBAAG;YAAC;YAC7D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAa;YAAC;YACzD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAe;YAAC;YAC3D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,oBAAoB;gBAChC,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;gBACrC,aACE,MAAM,OAAO,IAAI;YACrB;QACF;IACF;AACF;AAEO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,yJAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;QAC3D,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAa;YAAC;YACzD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAe;YAAC;YAC3D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,oBAAoB;gBAChC,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2BAA2B;gBACrC,aACE,MAAM,OAAO,IAAI;YACrB;QACF;IACF;AACF;AAEO,SAAS,wBAAwB,EAAU;IAChD,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,SACX,yJAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,IAAI;QAC3C,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAc;iBAAG;YAAC;YAC7D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAa;YAAC;YACzD,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAe;YAAC;YAC3D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,2BAA2B;gBACvC,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,kCAAkC;gBAC5C,aACE,MAAM,OAAO,IACb;YACJ;QACF;IACF;AACF;AAEO,SAAS,uBAAuB,EAAU;IAC/C,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAAe,yJAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC,IAAI;QACpE,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAc;iBAAG;YAAC;YAC7D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAa;YAAC;YACzD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;gBAC9B,aAAa;YACf;QACF;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;gBACnC,aACE,MAAM,OAAO,IAAI;YACrB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 6827, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/api/brand-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse } from \"@/types/api\";\r\nimport { Brand, CreateBrandRequest, UpdateBrandRequest } from \"@/types/brand\";\r\n\r\nexport const brandService = {\r\n  /**\r\n   * Get all brands with optional filtering\r\n   * @param params Query parameters\r\n   * @returns Paginated list of brands\r\n   */\r\n  getBrands: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Brand>> => {\r\n    try {\r\n      console.log(\"Fetching brands with params:\", params);\r\n      const response = await apiClient.get<any>(\"/brands\", { params });\r\n\r\n      // Map API response to our Brand type\r\n      const mapApiBrandToBrand = (apiBrand: any): Brand => ({\r\n        ...apiBrand,\r\n        // Add missing fields that our UI expects but API doesn't provide\r\n        status: apiBrand.deleted_at ? \"inactive\" : \"active\", // Derive status from deleted_at\r\n      });\r\n\r\n      // If response is an array, convert to paginated format with mapped brands\r\n      if (Array.isArray(response)) {\r\n        const mappedBrands = response.map(mapApiBrandToBrand);\r\n        return {\r\n          data: mappedBrands,\r\n          pagination: {\r\n            total: mappedBrands.length,\r\n            page: 1,\r\n            limit: mappedBrands.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        const mappedBrands = response.data.map(mapApiBrandToBrand);\r\n        return {\r\n          data: mappedBrands,\r\n          pagination: response.pagination || {\r\n            total: mappedBrands.length,\r\n            page: 1,\r\n            limit: mappedBrands.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Default fallback\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error: any) {\r\n      console.error(\"Error in getBrands:\", error?.message || error);\r\n\r\n      // Log more detailed error information\r\n      if (error.code === 'ECONNABORTED') {\r\n        console.error(\"Request timeout in getBrands\");\r\n      } else if (error.response) {\r\n        console.error(\"Response error in getBrands:\", error.response.status, error.response.data);\r\n      } else if (error.request) {\r\n        console.error(\"Request error in getBrands (no response)\");\r\n      }\r\n\r\n      // Rethrow network errors to allow proper handling by the UI\r\n      if (error.code === 'ECONNABORTED' || (error.request && !error.response)) {\r\n        throw new Error(\"Network error. Please check your connection.\");\r\n      }\r\n\r\n      // Return empty data on other errors\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a brand by ID\r\n   * @param id Brand ID\r\n   * @returns Brand object\r\n   */\r\n  getBrandById: async (id: number): Promise<Brand> => {\r\n    return apiClient.get(`/brands/${id}`);\r\n  },\r\n\r\n  /**\r\n   * Create a new brand\r\n   * @param brand Brand data\r\n   * @returns Created brand\r\n   */\r\n  createBrand: async (brand: CreateBrandRequest): Promise<Brand> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      name: brand.name,\r\n      description: brand.description,\r\n    };\r\n\r\n    return apiClient.post(\"/brands\", apiRequest);\r\n  },\r\n\r\n  /**\r\n   * Update an existing brand\r\n   * @param id Brand ID\r\n   * @param brand Brand data to update\r\n   * @returns Updated brand\r\n   */\r\n  updateBrand: async (\r\n    id: number,\r\n    brand: UpdateBrandRequest\r\n  ): Promise<Brand> => {\r\n    // Ensure we're sending the correct fields to the API\r\n    const apiRequest = {\r\n      name: brand.name,\r\n      description: brand.description,\r\n    };\r\n\r\n    return apiClient.put(`/brands/${id}`, apiRequest);\r\n  },\r\n\r\n  /**\r\n   * Delete a brand\r\n   * @param id Brand ID\r\n   */\r\n  deleteBrand: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/brands/${id}`);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAIO,MAAM,eAAe;IAC1B;;;;GAIC,GACD,WAAW,OACT;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,WAAW;gBAAE;YAAO;YAE9D,qCAAqC;YACrC,MAAM,qBAAqB,CAAC,WAAyB,CAAC;oBACpD,GAAG,QAAQ;oBACX,iEAAiE;oBACjE,QAAQ,SAAS,UAAU,GAAG,aAAa;gBAC7C,CAAC;YAED,0EAA0E;YAC1E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,MAAM,eAAe,SAAS,GAAG,CAAC;gBAClC,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,aAAa,MAAM;wBAC1B,MAAM;wBACN,OAAO,aAAa,MAAM;wBAC1B,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,MAAM,eAAe,SAAS,IAAI,CAAC,GAAG,CAAC;gBACvC,OAAO;oBACL,MAAM;oBACN,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,aAAa,MAAM;wBAC1B,MAAM;wBACN,OAAO,aAAa,MAAM;wBAC1B,YAAY;oBACd;gBACF;YACF;YAEA,mBAAmB;YACnB,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uBAAuB,OAAO,WAAW;YAEvD,sCAAsC;YACtC,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,QAAQ,KAAK,CAAC;YAChB,OAAO,IAAI,MAAM,QAAQ,EAAE;gBACzB,QAAQ,KAAK,CAAC,gCAAgC,MAAM,QAAQ,CAAC,MAAM,EAAE,MAAM,QAAQ,CAAC,IAAI;YAC1F,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,QAAQ,KAAK,CAAC;YAChB;YAEA,4DAA4D;YAC5D,IAAI,MAAM,IAAI,KAAK,kBAAmB,MAAM,OAAO,IAAI,CAAC,MAAM,QAAQ,EAAG;gBACvE,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA;;;;GAIC,GACD,cAAc,OAAO;QACnB,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI;IACtC;IAEA;;;;GAIC,GACD,aAAa,OAAO;QAClB,sDAAsD;QACtD,MAAM,aAAa;YACjB,MAAM,MAAM,IAAI;YAChB,aAAa,MAAM,WAAW;QAChC;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,WAAW;IACnC;IAEA;;;;;GAKC,GACD,aAAa,OACX,IACA;QAEA,qDAAqD;QACrD,MAAM,aAAa;YACjB,MAAM,MAAM,IAAI;YAChB,aAAa,MAAM,WAAW;QAChC;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE;IACxC;IAEA;;;GAGC,GACD,aAAa,OAAO;QAClB,OAAO,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,IAAI;IACzC;AACF", "debugId": null}}, {"offset": {"line": 6956, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/hooks/use-brands.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { CreateBrandRequest, UpdateBrandRequest } from \"@/types/brand\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport { brandService } from \"../api/brand-service\";\r\n\r\nexport const BRANDS_QUERY_KEY = \"brands\";\r\n\r\n/**\r\n * Hook to fetch brands with optional filtering\r\n */\r\nexport function useBrands(params?: Record<string, any>) {\r\n  return useQuery({\r\n    queryKey: [BRANDS_QUERY_KEY, params],\r\n    queryFn: () => brandService.getBrands(params),\r\n  });\r\n}\r\n\r\n/**\r\n * Hook to fetch a brand by ID\r\n */\r\nexport function useBrand(id: number) {\r\n  return useQuery({\r\n    queryKey: [BRANDS_QUERY_KEY, id],\r\n    queryFn: () => brandService.getBrandById(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\n/**\r\n * Hook to create a new brand\r\n */\r\nexport function useCreateBrand() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateBrandRequest) => brandService.createBrand(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [BRANDS_QUERY_KEY] });\r\n      toast.success(\"Brand created successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        `Failed to create brand: ${error.message || \"Unknown error\"}`\r\n      );\r\n    },\r\n  });\r\n}\r\n\r\n/**\r\n * Hook to update an existing brand\r\n */\r\nexport function useUpdateBrand(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: UpdateBrandRequest) =>\r\n      brandService.updateBrand(id, data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [BRANDS_QUERY_KEY] });\r\n      queryClient.invalidateQueries({ queryKey: [BRANDS_QUERY_KEY, id] });\r\n      toast.success(\"Brand updated successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        `Failed to update brand: ${error.message || \"Unknown error\"}`\r\n      );\r\n    },\r\n  });\r\n}\r\n\r\n/**\r\n * Hook to delete a brand\r\n */\r\nexport function useDeleteBrand() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => brandService.deleteBrand(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [BRANDS_QUERY_KEY] });\r\n      toast.success(\"Brand deleted successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        `Failed to delete brand: ${error.message || \"Unknown error\"}`\r\n      );\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAGA;AAAA;AAAA;AACA;AACA;AALA;;;;AAOO,MAAM,mBAAmB;AAKzB,SAAS,UAAU,MAA4B;IACpD,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAkB;SAAO;QACpC,SAAS,IAAM,sJAAA,CAAA,eAAY,CAAC,SAAS,CAAC;IACxC;AACF;AAKO,SAAS,SAAS,EAAU;IACjC,OAAO,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAkB;SAAG;QAChC,SAAS,IAAM,sJAAA,CAAA,eAAY,CAAC,YAAY,CAAC;QACzC,SAAS,CAAC,CAAC;IACb;AACF;AAKO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OAA6B,sJAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QACnE,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAiB;YAAC;YAC7D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,wBAAwB,EAAE,MAAM,OAAO,IAAI,iBAAiB;QAEjE;IACF;AACF;AAKO,SAAS,eAAe,EAAU;IACvC,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,OACX,sJAAA,CAAA,eAAY,CAAC,WAAW,CAAC,IAAI;QAC/B,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAiB;YAAC;YAC7D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAkB;iBAAG;YAAC;YACjE,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,wBAAwB,EAAE,MAAM,OAAO,IAAI,iBAAiB;QAEjE;IACF;AACF;AAKO,SAAS;IACd,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY,CAAC,KAAe,sJAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QACrD,WAAW;YACT,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAiB;YAAC;YAC7D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,wBAAwB,EAAE,MAAM,OAAO,IAAI,iBAAiB;QAEjE;IACF;AACF", "debugId": null}}, {"offset": {"line": 7056, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 7120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport {\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n  MoreHorizontalIcon,\r\n} from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button, buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Pagination({ className, ...props }: React.ComponentProps<\"nav\">) {\r\n  return (\r\n    <nav\r\n      role=\"navigation\"\r\n      aria-label=\"pagination\"\r\n      data-slot=\"pagination\"\r\n      className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"pagination-content\"\r\n      className={cn(\"flex flex-row items-center gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationItem({ ...props }: React.ComponentProps<\"li\">) {\r\n  return <li data-slot=\"pagination-item\" {...props} />\r\n}\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<React.ComponentProps<typeof Button>, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nfunction PaginationLink({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) {\r\n  return (\r\n    <a\r\n      aria-current={isActive ? \"page\" : undefined}\r\n      data-slot=\"pagination-link\"\r\n      data-active={isActive}\r\n      className={cn(\r\n        buttonVariants({\r\n          variant: isActive ? \"outline\" : \"ghost\",\r\n          size,\r\n        }),\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationPrevious({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to previous page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pl-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <ChevronLeftIcon />\r\n      <span className=\"hidden sm:block\">Previous</span>\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationNext({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to next page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pr-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <span className=\"hidden sm:block\">Next</span>\r\n      <ChevronRightIcon />\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      aria-hidden\r\n      data-slot=\"pagination-ellipsis\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontalIcon className=\"size-4\" />\r\n      <span className=\"sr-only\">More pages</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationLink,\r\n  PaginationItem,\r\n  PaginationPrevious,\r\n  PaginationNext,\r\n  PaginationEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AAAA;AAAA;AAMA;AACA;;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,MAAK;QACL,cAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,GAAG,OAAmC;IAC9D,qBAAO,8OAAC;QAAG,aAAU;QAAmB,GAAG,KAAK;;;;;;AAClD;AAOA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACb,GAAG,OACiB;IACpB,qBACE,8OAAC;QACC,gBAAc,WAAW,SAAS;QAClC,aAAU;QACV,eAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,YAAY;YAChC;QACF,IACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,8OAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,8OAAC,wNAAA,CAAA,kBAAe;;;;;0BAChB,8OAAC;gBAAK,WAAU;0BAAkB;;;;;;;;;;;;AAGxC;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,8OAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BAAkB;;;;;;0BAClC,8OAAC,0NAAA,CAAA,mBAAgB;;;;;;;;;;;AAGvB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;0BAC9B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 7279, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/data-pagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo } from \"react\";\r\nimport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from \"@/components/ui/pagination\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ChevronFirst, ChevronLast } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nexport interface DataPaginationProps {\r\n  // Required props\r\n  currentPage: number;\r\n  totalPages: number;\r\n  onPageChange: (page: number) => void;\r\n\r\n  // Optional props\r\n  pageSize?: number;\r\n  pageSizes?: number[];\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  totalItems?: number;\r\n  isLoading?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showItemsInfo?: boolean;\r\n  showFirstLastButtons?: boolean;\r\n  maxPageButtons?: number;\r\n  className?: string;\r\n  compact?: boolean;\r\n  ariaLabel?: string;\r\n}\r\n\r\nexport function DataPagination({\r\n  currentPage,\r\n  totalPages,\r\n  onPageChange,\r\n  pageSize = 10,\r\n  pageSizes = [10, 25, 50, 100, 250, 500, 1000],\r\n  onPageSizeChange,\r\n  totalItems,\r\n  isLoading = false,\r\n  showPageSizeSelector = true,\r\n  showItemsInfo = true,\r\n  showFirstLastButtons = true,\r\n  maxPageButtons = 5,\r\n  className,\r\n  compact = false,\r\n  ariaLabel = \"Pagination\",\r\n}: DataPaginationProps) {\r\n  // Don't render pagination if there's only one page and no page size selector\r\n  if (totalPages <= 1 && (!showPageSizeSelector || !onPageSizeChange)) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate the current range of items being displayed\r\n  const itemRange = useMemo(() => {\r\n    if (!totalItems) return null;\r\n\r\n    const start = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;\r\n    const end = Math.min(currentPage * pageSize, totalItems);\r\n\r\n    return { start, end };\r\n  }, [currentPage, pageSize, totalItems]);\r\n\r\n  // Generate page numbers to display\r\n  const pageNumbers = useMemo(() => {\r\n    const numbers: (number | string)[] = [];\r\n\r\n    if (totalPages <= maxPageButtons) {\r\n      // If total pages is less than or equal to maxPageButtons, show all pages\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        numbers.push(i);\r\n      }\r\n    } else {\r\n      // Always include first page\r\n      numbers.push(1);\r\n\r\n      // Calculate start and end of page numbers to show\r\n      let start = Math.max(2, currentPage - Math.floor(maxPageButtons / 2) + 1);\r\n      let end = Math.min(totalPages - 1, start + maxPageButtons - 3);\r\n\r\n      // Adjust if we're at the beginning\r\n      if (currentPage <= Math.floor(maxPageButtons / 2)) {\r\n        end = maxPageButtons - 2;\r\n        start = 2;\r\n      }\r\n\r\n      // Adjust if we're at the end\r\n      if (currentPage > totalPages - Math.floor(maxPageButtons / 2)) {\r\n        start = Math.max(2, totalPages - maxPageButtons + 2);\r\n        end = totalPages - 1;\r\n      }\r\n\r\n      // Add ellipsis before middle pages if needed\r\n      if (start > 2) {\r\n        numbers.push(\"ellipsis-start\");\r\n      }\r\n\r\n      // Add middle pages\r\n      for (let i = start; i <= end; i++) {\r\n        numbers.push(i);\r\n      }\r\n\r\n      // Add ellipsis after middle pages if needed\r\n      if (end < totalPages - 1) {\r\n        numbers.push(\"ellipsis-end\");\r\n      }\r\n\r\n      // Always include last page if more than one page\r\n      if (totalPages > 1) {\r\n        numbers.push(totalPages);\r\n      }\r\n    }\r\n\r\n    return numbers;\r\n  }, [currentPage, totalPages, maxPageButtons]);\r\n\r\n  // Handle page click with validation\r\n  const handlePageClick = (page: number, e: React.MouseEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Only trigger page change if it's not the current page and not loading\r\n    if (page !== currentPage && !isLoading) {\r\n      // Force the page to be within valid range\r\n      const validPage = Math.max(1, Math.min(page, totalPages));\r\n      onPageChange(validPage);\r\n    }\r\n  };\r\n\r\n  // Handle page size change\r\n  const handlePageSizeChange = (value: string) => {\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(parseInt(value, 10));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex flex-col gap-4 w-full\",\r\n        compact\r\n          ? \"sm:flex-row sm:items-center sm:justify-between\"\r\n          : \"md:flex-row md:items-center md:justify-between\",\r\n        className\r\n      )}\r\n      aria-label={ariaLabel}\r\n    >\r\n      {/* Page size selector */}\r\n      {showPageSizeSelector && onPageSizeChange && (\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-sm text-muted-foreground\">Show</span>\r\n          <Select\r\n            value={pageSize.toString()}\r\n            onValueChange={handlePageSizeChange}\r\n            disabled={isLoading}\r\n          >\r\n            <SelectTrigger\r\n              className=\"h-8 w-[70px]\"\r\n              aria-label=\"Select page size\"\r\n            >\r\n              <SelectValue placeholder={pageSize.toString()} />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              {pageSizes.map((size) => (\r\n                <SelectItem key={size} value={size.toString()}>\r\n                  {size}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n          <span className=\"text-sm text-muted-foreground\">per page</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Pagination controls - Always show if we have pagination data */}\r\n      {totalPages >= 1 && (\r\n        <div className=\"bg-muted/50 rounded-md p-1\">\r\n          <Pagination>\r\n            <PaginationContent className=\"flex-wrap justify-center\">\r\n              {/* First page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(1, e)}\r\n                    disabled={currentPage === 1 || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to first page\"\r\n                  >\r\n                    <ChevronFirst className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n\r\n              {/* Previous page button */}\r\n              <PaginationItem>\r\n                <PaginationPrevious\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage - 1, e)}\r\n                  aria-disabled={currentPage === 1 || isLoading}\r\n                  className={cn(\r\n                    currentPage === 1 || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Page numbers */}\r\n              {!compact &&\r\n                pageNumbers.map((pageNumber, index) => {\r\n                  if (typeof pageNumber === \"string\") {\r\n                    return (\r\n                      <PaginationItem key={pageNumber}>\r\n                        <PaginationEllipsis />\r\n                      </PaginationItem>\r\n                    );\r\n                  }\r\n\r\n                  return (\r\n                    <PaginationItem key={`page-${pageNumber}`}>\r\n                      <PaginationLink\r\n                        href=\"#\"\r\n                        onClick={(e) => handlePageClick(pageNumber, e)}\r\n                        isActive={currentPage === pageNumber}\r\n                        aria-current={\r\n                          currentPage === pageNumber ? \"page\" : undefined\r\n                        }\r\n                      >\r\n                        {pageNumber}\r\n                      </PaginationLink>\r\n                    </PaginationItem>\r\n                  );\r\n                })}\r\n\r\n              {/* Compact view shows current/total instead of page numbers */}\r\n              {compact && (\r\n                <div className=\"flex items-center mx-2\">\r\n                  <span className=\"text-sm\">\r\n                    Page <span className=\"font-bold\">{currentPage}</span> of{\" \"}\r\n                    <span className=\"font-medium\">{totalPages}</span>\r\n                    {isLoading && (\r\n                      <span className=\"ml-1 text-muted-foreground\">\r\n                        (Loading...)\r\n                      </span>\r\n                    )}\r\n                  </span>\r\n                </div>\r\n              )}\r\n\r\n              {/* Next page button */}\r\n              <PaginationItem>\r\n                <PaginationNext\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage + 1, e)}\r\n                  aria-disabled={currentPage === totalPages || isLoading}\r\n                  className={cn(\r\n                    currentPage === totalPages || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Last page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(totalPages, e)}\r\n                    disabled={currentPage === totalPages || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to last page\"\r\n                  >\r\n                    <ChevronLast className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n            </PaginationContent>\r\n          </Pagination>\r\n        </div>\r\n      )}\r\n\r\n      {/* Items info */}\r\n      {showItemsInfo && totalItems !== undefined && (\r\n        <div className=\"text-sm text-muted-foreground whitespace-nowrap\">\r\n          {totalItems === 0 ? (\r\n            \"No items\"\r\n          ) : (\r\n            <>\r\n              Showing {itemRange?.start} to {itemRange?.end} of {totalItems}{\" \"}\r\n              items\r\n            </>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AAOA;AACA;AAAA;AACA;AArBA;;;;;;;;AA4CO,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,YAAY,EACZ,WAAW,EAAE,EACb,YAAY;IAAC;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;CAAK,EAC7C,gBAAgB,EAChB,UAAU,EACV,YAAY,KAAK,EACjB,uBAAuB,IAAI,EAC3B,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,iBAAiB,CAAC,EAClB,SAAS,EACT,UAAU,KAAK,EACf,YAAY,YAAY,EACJ;IACpB,6EAA6E;IAC7E,IAAI,cAAc,KAAK,CAAC,CAAC,wBAAwB,CAAC,gBAAgB,GAAG;QACnE,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,IAAI,CAAC,YAAY,OAAO;QAExB,MAAM,QAAQ,eAAe,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,WAAW;QACpE,MAAM,MAAM,KAAK,GAAG,CAAC,cAAc,UAAU;QAE7C,OAAO;YAAE;YAAO;QAAI;IACtB,GAAG;QAAC;QAAa;QAAU;KAAW;IAEtC,mCAAmC;IACnC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,MAAM,UAA+B,EAAE;QAEvC,IAAI,cAAc,gBAAgB;YAChC,yEAAyE;YACzE,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;gBACpC,QAAQ,IAAI,CAAC;YACf;QACF,OAAO;YACL,4BAA4B;YAC5B,QAAQ,IAAI,CAAC;YAEb,kDAAkD;YAClD,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB,KAAK;YACvE,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa,GAAG,QAAQ,iBAAiB;YAE5D,mCAAmC;YACnC,IAAI,eAAe,KAAK,KAAK,CAAC,iBAAiB,IAAI;gBACjD,MAAM,iBAAiB;gBACvB,QAAQ;YACV;YAEA,6BAA6B;YAC7B,IAAI,cAAc,aAAa,KAAK,KAAK,CAAC,iBAAiB,IAAI;gBAC7D,QAAQ,KAAK,GAAG,CAAC,GAAG,aAAa,iBAAiB;gBAClD,MAAM,aAAa;YACrB;YAEA,6CAA6C;YAC7C,IAAI,QAAQ,GAAG;gBACb,QAAQ,IAAI,CAAC;YACf;YAEA,mBAAmB;YACnB,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;gBACjC,QAAQ,IAAI,CAAC;YACf;YAEA,4CAA4C;YAC5C,IAAI,MAAM,aAAa,GAAG;gBACxB,QAAQ,IAAI,CAAC;YACf;YAEA,iDAAiD;YACjD,IAAI,aAAa,GAAG;gBAClB,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAa;QAAY;KAAe;IAE5C,oCAAoC;IACpC,MAAM,kBAAkB,CAAC,MAAc;QACrC,EAAE,cAAc;QAEhB,wEAAwE;QACxE,IAAI,SAAS,eAAe,CAAC,WAAW;YACtC,0CAA0C;YAC1C,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;YAC7C,aAAa;QACf;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,IAAI,kBAAkB;YACpB,iBAAiB,SAAS,OAAO;QACnC;IACF;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8BACA,UACI,mDACA,kDACJ;QAEF,cAAY;;YAGX,wBAAwB,kCACvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;kCAChD,8OAAC,kIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,QAAQ;wBACxB,eAAe;wBACf,UAAU;;0CAEV,8OAAC,kIAAA,CAAA,gBAAa;gCACZ,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC,kIAAA,CAAA,cAAW;oCAAC,aAAa,SAAS,QAAQ;;;;;;;;;;;0CAE7C,8OAAC,kIAAA,CAAA,gBAAa;0CACX,UAAU,GAAG,CAAC,CAAC,qBACd,8OAAC,kIAAA,CAAA,aAAU;wCAAY,OAAO,KAAK,QAAQ;kDACxC;uCADc;;;;;;;;;;;;;;;;kCAMvB,8OAAC;wBAAK,WAAU;kCAAgC;;;;;;;;;;;;YAKnD,cAAc,mBACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,sIAAA,CAAA,oBAAiB;wBAAC,WAAU;;4BAE1B,sCACC,8OAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,GAAG;oCACnC,UAAU,gBAAgB,KAAK;oCAC/B,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAM9B,8OAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;oCACjB,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,KAAK;oCACpC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,KAAK,YACjB,mCACA,IACJ;;;;;;;;;;;4BAML,CAAC,WACA,YAAY,GAAG,CAAC,CAAC,YAAY;gCAC3B,IAAI,OAAO,eAAe,UAAU;oCAClC,qBACE,8OAAC,sIAAA,CAAA,iBAAc;kDACb,cAAA,8OAAC,sIAAA,CAAA,qBAAkB;;;;;uCADA;;;;;gCAIzB;gCAEA,qBACE,8OAAC,sIAAA,CAAA,iBAAc;8CACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;wCACb,MAAK;wCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;wCAC5C,UAAU,gBAAgB;wCAC1B,gBACE,gBAAgB,aAAa,SAAS;kDAGvC;;;;;;mCATgB,CAAC,KAAK,EAAE,YAAY;;;;;4BAa7C;4BAGD,yBACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCAAU;sDACnB,8OAAC;4CAAK,WAAU;sDAAa;;;;;;wCAAmB;wCAAI;sDACzD,8OAAC;4CAAK,WAAU;sDAAe;;;;;;wCAC9B,2BACC,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;0CASrD,8OAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oCACb,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,cAAc;oCAC7C,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,cAAc,YAC1B,mCACA,IACJ;;;;;;;;;;;4BAML,sCACC,8OAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;oCAC5C,UAAU,gBAAgB,cAAc;oCACxC,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUpC,iBAAiB,eAAe,2BAC/B,8OAAC;gBAAI,WAAU;0BACZ,eAAe,IACd,2BAEA;;wBAAE;wBACS,WAAW;wBAAM;wBAAK,WAAW;wBAAI;wBAAK;wBAAY;wBAAI;;;;;;;;;;;;;;AAQjF", "debugId": null}}, {"offset": {"line": 7668, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/utils/export-products.ts"], "sourcesContent": ["import * as XLSX from \"xlsx\";\r\nimport { Product } from \"@/types/product\";\r\n\r\n/**\r\n * Export products to Excel with all columns\r\n * @param products List of products to export\r\n * @param filename Filename for the Excel file (without extension)\r\n */\r\nexport function exportProductsToExcel(\r\n  products: Product[],\r\n  filename: string = \"products-export\"\r\n) {\r\n  // Define all possible headers based on the Product type\r\n  const headers = [\r\n    \"ID\",\r\n    \"Name\",\r\n    \"SKU\",\r\n    \"Description\",\r\n    \"Category\",\r\n    \"Category Hierarchy\",\r\n    \"Brand\",\r\n    \"Brand Type\",\r\n    \"Suggested Buying Price\",\r\n    \"Suggested Selling Price\",\r\n    \"Wholesale Price\",\r\n    \"Has Serial/IMEI\",\r\n    \"Barcode\",\r\n    \"Reorder Level\",\r\n    \"Warranty Period (Days)\",\r\n    \"Status\",\r\n    \"Created At\",\r\n    \"Updated At\"\r\n  ];\r\n\r\n  // Map products to rows\r\n  const rows = products.map((product) => {\r\n    // Format category hierarchy\r\n    const categoryHierarchy = product.categoryHierarchy\r\n      ? product.categoryHierarchy.map((cat) => cat.name).join(\" > \")\r\n      : product.ProductCategory?.name || \"\";\r\n\r\n    return [\r\n      product.id,\r\n      product.name,\r\n      product.sku || \"\",\r\n      product.description || \"\",\r\n      product.ProductCategory?.name || \"\",\r\n      categoryHierarchy,\r\n      product.Brand?.name || \"\",\r\n      product.BrandType?.name || \"\",\r\n      product.suggested_buying_price || product.buying_price || \"\",\r\n      product.suggested_selling_price || product.selling_price || \"\",\r\n      product.default_wholesale_price || \"\",\r\n      product.has_serial ? \"Yes\" : \"No\",\r\n      product.barcode || \"\",\r\n      product.reorder_level || \"\",\r\n      product.warranty_period || \"\",\r\n      product.deleted_at ? \"Inactive\" : \"Active\",\r\n      new Date(product.created_at).toLocaleString(),\r\n      new Date(product.updated_at).toLocaleString()\r\n    ];\r\n  });\r\n\r\n  // Create worksheet\r\n  const data = [headers, ...rows];\r\n  const ws = XLSX.utils.aoa_to_sheet(data);\r\n\r\n  // Set column widths\r\n  const colWidths = [\r\n    { wch: 8 },    // ID\r\n    { wch: 30 },   // Name\r\n    { wch: 15 },   // SKU\r\n    { wch: 40 },   // Description\r\n    { wch: 20 },   // Category\r\n    { wch: 30 },   // Category Hierarchy\r\n    { wch: 20 },   // Brand\r\n    { wch: 20 },   // Brand Type\r\n    { wch: 20 },   // Suggested Buying Price\r\n    { wch: 20 },   // Suggested Selling Price\r\n    { wch: 20 },   // Wholesale Price\r\n    { wch: 15 },   // Has Serial/IMEI\r\n    { wch: 15 },   // Barcode\r\n    { wch: 15 },   // Reorder Level\r\n    { wch: 20 },   // Warranty Period\r\n    { wch: 10 },   // Status\r\n    { wch: 20 },   // Created At\r\n    { wch: 20 },   // Updated At\r\n  ];\r\n  ws[\"!cols\"] = colWidths;\r\n\r\n  // Apply styles to header row\r\n  const headerStyle = {\r\n    font: { bold: true },\r\n    fill: { fgColor: { rgb: \"EFEFEF\" } },\r\n    alignment: { horizontal: \"center\", vertical: \"center\" }\r\n  };\r\n\r\n  // Apply styles to the first row (headers)\r\n  for (let i = 0; i < headers.length; i++) {\r\n    const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });\r\n    if (!ws[cellRef]) ws[cellRef] = {};\r\n    ws[cellRef].s = headerStyle;\r\n  }\r\n\r\n  // Create workbook\r\n  const wb = XLSX.utils.book_new();\r\n  XLSX.utils.book_append_sheet(wb, ws, \"Products\");\r\n\r\n  // Generate Excel file and trigger download\r\n  XLSX.writeFile(wb, `${filename}.xlsx`);\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAQO,SAAS,sBACd,QAAmB,EACnB,WAAmB,iBAAiB;IAEpC,wDAAwD;IACxD,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,uBAAuB;IACvB,MAAM,OAAO,SAAS,GAAG,CAAC,CAAC;QACzB,4BAA4B;QAC5B,MAAM,oBAAoB,QAAQ,iBAAiB,GAC/C,QAAQ,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAQ,IAAI,IAAI,EAAE,IAAI,CAAC,SACtD,QAAQ,eAAe,EAAE,QAAQ;QAErC,OAAO;YACL,QAAQ,EAAE;YACV,QAAQ,IAAI;YACZ,QAAQ,GAAG,IAAI;YACf,QAAQ,WAAW,IAAI;YACvB,QAAQ,eAAe,EAAE,QAAQ;YACjC;YACA,QAAQ,KAAK,EAAE,QAAQ;YACvB,QAAQ,SAAS,EAAE,QAAQ;YAC3B,QAAQ,sBAAsB,IAAI,QAAQ,YAAY,IAAI;YAC1D,QAAQ,uBAAuB,IAAI,QAAQ,aAAa,IAAI;YAC5D,QAAQ,uBAAuB,IAAI;YACnC,QAAQ,UAAU,GAAG,QAAQ;YAC7B,QAAQ,OAAO,IAAI;YACnB,QAAQ,aAAa,IAAI;YACzB,QAAQ,eAAe,IAAI;YAC3B,QAAQ,UAAU,GAAG,aAAa;YAClC,IAAI,KAAK,QAAQ,UAAU,EAAE,cAAc;YAC3C,IAAI,KAAK,QAAQ,UAAU,EAAE,cAAc;SAC5C;IACH;IAEA,mBAAmB;IACnB,MAAM,OAAO;QAAC;WAAY;KAAK;IAC/B,MAAM,KAAK,6HAAA,CAAA,QAAU,CAAC,YAAY,CAAC;IAEnC,oBAAoB;IACpB,MAAM,YAAY;QAChB;YAAE,KAAK;QAAE;QACT;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;QACV;YAAE,KAAK;QAAG;KACX;IACD,EAAE,CAAC,QAAQ,GAAG;IAEd,6BAA6B;IAC7B,MAAM,cAAc;QAClB,MAAM;YAAE,MAAM;QAAK;QACnB,MAAM;YAAE,SAAS;gBAAE,KAAK;YAAS;QAAE;QACnC,WAAW;YAAE,YAAY;YAAU,UAAU;QAAS;IACxD;IAEA,0CAA0C;IAC1C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,MAAM,UAAU,6HAAA,CAAA,QAAU,CAAC,WAAW,CAAC;YAAE,GAAG;YAAG,GAAG;QAAE;QACpD,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,GAAG,CAAC;QACjC,EAAE,CAAC,QAAQ,CAAC,CAAC,GAAG;IAClB;IAEA,kBAAkB;IAClB,MAAM,KAAK,6HAAA,CAAA,QAAU,CAAC,QAAQ;IAC9B,6HAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,IAAI,IAAI;IAErC,2CAA2C;IAC3C,CAAA,GAAA,6HAAA,CAAA,YAAc,AAAD,EAAE,IAAI,GAAG,SAAS,KAAK,CAAC;AACvC", "debugId": null}}, {"offset": {"line": 7820, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/products/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { MainLayout } from \"@/components/layouts/main-layout\";\r\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { ProductsTable } from \"@/features/products/components/products-table\";\r\nimport { ProductFilterBar } from \"@/features/products/components/product-filter-bar\";\r\nimport { useCategories } from \"@/features/products/hooks/use-categories\";\r\nimport { useProducts } from \"@/features/products/hooks/use-products\";\r\nimport { useBrands } from \"@/features/products/hooks/use-brands\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport {\r\n  AlertTriangle,\r\n  CheckCircle,\r\n  DollarSign,\r\n  Download,\r\n  FileSpreadsheet,\r\n  Package,\r\n  Plus,\r\n  Upload,\r\n} from \"lucide-react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport { DataPagination } from \"@/components/ui/data-pagination\";\r\nimport { exportProductsToExcel } from \"@/features/products/utils/export-products\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nexport default function ProductsPage() {\r\n  const router = useRouter();\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [itemsPerPage, setItemsPerPage] = useState(10);\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [activeTab, setActiveTab] = useState(\"all\");\r\n  const [sortField, setSortField] = useState<string | null>(null);\r\n  const [sortDirection, setSortDirection] = useState<\"asc\" | \"desc\">(\"asc\");\r\n  const [categoryFilter, setCategoryFilter] = useState<number | undefined>(\r\n    undefined\r\n  );\r\n  const [brandFilter, setBrandFilter] = useState<number | undefined>(undefined);\r\n  const [statusFilter, setStatusFilter] = useState<boolean | undefined>(\r\n    undefined\r\n  );\r\n\r\n  // Build query parameters for API call\r\n  const queryParams = {\r\n    page: currentPage,\r\n    limit: itemsPerPage,\r\n    search: searchQuery || undefined,\r\n    category_id: categoryFilter,\r\n    brand_id: brandFilter,\r\n    is_active: statusFilter,\r\n    sort: sortField ? `${sortField}:${sortDirection}` : undefined,\r\n  };\r\n\r\n  // Fetch data with pagination from products table\r\n  const { data, isLoading } = useProducts(queryParams);\r\n\r\n  const { data: categoriesData } = useCategories();\r\n  const { data: brandsData } = useBrands();\r\n\r\n  // Calculate pagination values\r\n  const totalItems = data?.pagination?.total || 0;\r\n  const totalPages =\r\n    data?.pagination?.totalPages || Math.ceil(totalItems / itemsPerPage) || 1;\r\n\r\n  // Handle search input change\r\n  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setSearchQuery(e.target.value);\r\n  };\r\n\r\n  // Handle search button click\r\n  const handleSearchClick = () => {\r\n    setCurrentPage(1); // Reset to first page on new search\r\n    // searchQuery state is already updated, React Query will refetch automatically\r\n  };\r\n\r\n  // Handle search on Enter key\r\n  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (e.key === \"Enter\") {\r\n      handleSearchClick();\r\n    }\r\n  };\r\n\r\n  // Handle category filter change\r\n  const handleCategoryChange = (categoryId: string) => {\r\n    setCurrentPage(1); // Reset to first page on filter change\r\n    setCategoryFilter(\r\n      categoryId === \"all\" ? undefined : parseInt(categoryId, 10)\r\n    );\r\n  };\r\n\r\n  // Handle brand filter change\r\n  const handleBrandChange = (brandId: string) => {\r\n    setCurrentPage(1); // Reset to first page on filter change\r\n    setBrandFilter(brandId === \"all\" ? undefined : parseInt(brandId, 10));\r\n  };\r\n\r\n  // Handle status filter change\r\n  const handleStatusChange = (status: string) => {\r\n    setCurrentPage(1); // Reset to first page on filter change\r\n\r\n    if (status === \"active\") {\r\n      setStatusFilter(true);\r\n    } else if (status === \"inactive\") {\r\n      setStatusFilter(false);\r\n    } else {\r\n      // \"all\" or any other value\r\n      setStatusFilter(undefined);\r\n    }\r\n  };\r\n\r\n  // Stock level filter removed\r\n\r\n  // Handle tab change\r\n  const handleTabChange = (value: string) => {\r\n    setActiveTab(value);\r\n    setCurrentPage(1); // Reset to first page on tab change\r\n\r\n    switch (value) {\r\n      case \"active\":\r\n        setStatusFilter(true);\r\n        break;\r\n      case \"inactive\":\r\n        setStatusFilter(false);\r\n        break;\r\n      default:\r\n        setStatusFilter(undefined);\r\n    }\r\n  };\r\n\r\n  // Handle sort change\r\n  const handleSort = (field: string) => {\r\n    if (sortField === field) {\r\n      // Toggle direction if same field\r\n      setSortDirection(sortDirection === \"asc\" ? \"desc\" : \"asc\");\r\n    } else {\r\n      // Set new field and default to ascending\r\n      setSortField(field);\r\n      setSortDirection(\"asc\");\r\n    }\r\n  };\r\n\r\n  // Handle pagination\r\n  const handlePageChange = (page: number) => {\r\n    setCurrentPage(page);\r\n  };\r\n\r\n  // Handle items per page change\r\n  const handleItemsPerPageChange = (limit: number) => {\r\n    setItemsPerPage(limit);\r\n    setCurrentPage(1); // Reset to first page when changing items per page\r\n  };\r\n\r\n  // Handle export to Excel\r\n  const handleExportToExcel = () => {\r\n    if (data?.data && data.data.length > 0) {\r\n      exportProductsToExcel(\r\n        data.data,\r\n        `products-export-${new Date().toISOString().split(\"T\")[0]}`\r\n      );\r\n    }\r\n  };\r\n\r\n  // Ensure data.data is always an array\r\n  const productsData = Array.isArray(data?.data) ? data.data : [];\r\n\r\n  // Get counts from API response or calculate from current page as fallback\r\n  const activeProducts =\r\n    data?.counts?.active ||\r\n    productsData.filter((product) => product?.is_active).length;\r\n\r\n  const inactiveProducts =\r\n    data?.counts?.inactive ||\r\n    productsData.filter((product) => product?.is_active === false).length;\r\n\r\n  // Helper function to get stock quantity\r\n  const getStockQuantity = (product: any) => {\r\n    if (product.stock && Array.isArray(product.stock)) {\r\n      return product.stock.reduce(\r\n        (total, item) => total + (item.quantity || 0),\r\n        0\r\n      );\r\n    }\r\n    return product.stock_quantity || 0;\r\n  };\r\n\r\n  // Get low stock and out of stock counts from API response or calculate from current page as fallback\r\n  const lowStockProducts =\r\n    data?.counts?.lowStock ||\r\n    productsData.filter((product) => {\r\n      const stockQty = getStockQuantity(product);\r\n      return stockQty > 0 && stockQty <= 10;\r\n    }).length;\r\n\r\n  const outOfStockProducts =\r\n    data?.counts?.outOfStock ||\r\n    productsData.filter((product) => getStockQuantity(product) === 0).length;\r\n\r\n  // Calculate total value of inventory (based on selling price)\r\n  const totalInventoryValue = productsData.reduce((total, product) => {\r\n    if (!product) return total;\r\n    // Handle case where price or stock_quantity might be undefined\r\n    const price =\r\n      typeof product.price === \"number\"\r\n        ? product.price\r\n        : typeof product.selling_price === \"number\"\r\n        ? product.selling_price\r\n        : parseFloat(product.selling_price as string) || 0;\r\n    const stockQuantity = getStockQuantity(product);\r\n    return total + price * stockQuantity;\r\n  }, 0);\r\n\r\n  // Calculate total cost of inventory (based on buying price)\r\n  const totalInventoryCost = productsData.reduce((total, product) => {\r\n    if (!product) return total;\r\n    // Handle case where buying_price or stock_quantity might be undefined\r\n    const buyingPrice =\r\n      typeof product.buying_price === \"number\"\r\n        ? product.buying_price\r\n        : parseFloat(product.buying_price as string) || 0;\r\n    const stockQuantity = getStockQuantity(product);\r\n    return total + buyingPrice * stockQuantity;\r\n  }, 0);\r\n\r\n  // Calculate potential profit\r\n  const potentialProfit = totalInventoryValue - totalInventoryCost;\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex justify-between items-center\">\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold tracking-tight\">Products</h1>\r\n            <p className=\"text-muted-foreground\">\r\n              Manage your product inventory, categories, and pricing\r\n            </p>\r\n          </div>\r\n          <div className=\"flex gap-2\">\r\n            <Button variant=\"outline\" size=\"sm\" onClick={handleExportToExcel}>\r\n              <FileSpreadsheet className=\"h-4 w-4 mr-2\" />\r\n              Export to Excel\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={() => router.push(\"/products/import\")}\r\n            >\r\n              <Upload className=\"h-4 w-4 mr-2\" />\r\n              Import\r\n            </Button>\r\n            <Button onClick={() => router.push(\"/products/create\")}>\r\n              <Plus className=\"h-4 w-4 mr-2\" />\r\n              Add Product\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-5\">\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Total Products\r\n              </CardTitle>\r\n              <Package className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {isLoading\r\n                  ? \"...\"\r\n                  : data?.counts?.total ||\r\n                    data?.pagination?.total ||\r\n                    productsData.length ||\r\n                    0}\r\n              </div>\r\n              <p className=\"text-xs text-muted-foreground mt-1\">\r\n                {data?.counts?.active || activeProducts} active,{\" \"}\r\n                {data?.counts?.inactive || inactiveProducts} inactive\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Active Products\r\n              </CardTitle>\r\n              <CheckCircle className=\"h-4 w-4 text-green-500\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {isLoading ? \"...\" : activeProducts}\r\n              </div>\r\n              <p className=\"text-xs text-muted-foreground mt-1\">\r\n                Products available for sale\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Low Stock</CardTitle>\r\n              <AlertTriangle className=\"h-4 w-4 text-amber-500\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {isLoading ? \"...\" : lowStockProducts}\r\n              </div>\r\n              <p className=\"text-xs text-muted-foreground mt-1\">\r\n                Products with low inventory\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Inventory Value\r\n              </CardTitle>\r\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {isLoading ? \"...\" : formatCurrency(totalInventoryValue)}\r\n              </div>\r\n              <p className=\"text-xs text-muted-foreground mt-1\">\r\n                Based on selling price\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Potential Profit\r\n              </CardTitle>\r\n              <DollarSign className=\"h-4 w-4 text-green-500\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {isLoading ? \"...\" : formatCurrency(potentialProfit)}\r\n              </div>\r\n              <p className=\"text-xs text-muted-foreground mt-1\">\r\n                If all inventory is sold\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        <Tabs\r\n          defaultValue=\"all\"\r\n          value={activeTab}\r\n          onValueChange={handleTabChange}\r\n          className=\"w-full\"\r\n        >\r\n          <TabsList className=\"grid grid-cols-3 mb-4\">\r\n            <TabsTrigger value=\"all\">All Products</TabsTrigger>\r\n            <TabsTrigger value=\"active\">Active</TabsTrigger>\r\n            <TabsTrigger value=\"inactive\">Inactive</TabsTrigger>\r\n          </TabsList>\r\n\r\n          <TabsContent value=\"all\" className=\"space-y-4\">\r\n            <ProductFilterBar\r\n              searchQuery={searchQuery}\r\n              onSearchChange={handleSearchInputChange}\r\n              onSearchKeyDown={handleSearchKeyDown}\r\n              onSearchClick={handleSearchClick}\r\n              onCategoryChange={handleCategoryChange}\r\n              onBrandChange={handleBrandChange}\r\n              categories={categoriesData?.data || []}\r\n              brands={brandsData?.data || []}\r\n              placeholder=\"Search products...\"\r\n            />\r\n\r\n            <ProductsTable\r\n              products={data?.data || []}\r\n              isLoading={isLoading}\r\n              onSearch={handleSearchClick}\r\n              onSort={handleSort}\r\n              sortField={sortField}\r\n              sortDirection={sortDirection}\r\n            />\r\n\r\n            {/* Modern pagination component */}\r\n            <div className=\"mt-6 w-full\">\r\n              <DataPagination\r\n                currentPage={currentPage}\r\n                totalPages={totalPages}\r\n                onPageChange={handlePageChange}\r\n                pageSize={itemsPerPage}\r\n                onPageSizeChange={handleItemsPerPageChange}\r\n                totalItems={totalItems}\r\n                isLoading={isLoading}\r\n                showPageSizeSelector={true}\r\n                showItemsInfo={true}\r\n                showFirstLastButtons={true}\r\n              />\r\n            </div>\r\n          </TabsContent>\r\n\r\n          {/* The same content is used for all tabs, the filtering is handled by the tab change handler */}\r\n          <TabsContent value=\"active\" className=\"space-y-4\">\r\n            <ProductFilterBar\r\n              searchQuery={searchQuery}\r\n              onSearchChange={handleSearchInputChange}\r\n              onSearchKeyDown={handleSearchKeyDown}\r\n              onSearchClick={handleSearchClick}\r\n              onCategoryChange={handleCategoryChange}\r\n              onBrandChange={handleBrandChange}\r\n              categories={categoriesData?.data || []}\r\n              brands={brandsData?.data || []}\r\n              placeholder=\"Search active products...\"\r\n            />\r\n\r\n            <ProductsTable\r\n              products={data?.data || []}\r\n              isLoading={isLoading}\r\n              onSearch={handleSearchClick}\r\n              onSort={handleSort}\r\n              sortField={sortField}\r\n              sortDirection={sortDirection}\r\n            />\r\n\r\n            {/* Modern pagination component */}\r\n            <div className=\"mt-6 w-full\">\r\n              <DataPagination\r\n                currentPage={currentPage}\r\n                totalPages={totalPages}\r\n                onPageChange={handlePageChange}\r\n                pageSize={itemsPerPage}\r\n                onPageSizeChange={handleItemsPerPageChange}\r\n                totalItems={totalItems}\r\n                isLoading={isLoading}\r\n                showPageSizeSelector={true}\r\n                showItemsInfo={true}\r\n                showFirstLastButtons={true}\r\n              />\r\n            </div>\r\n          </TabsContent>\r\n\r\n          <TabsContent value=\"inactive\" className=\"space-y-4\">\r\n            <ProductFilterBar\r\n              searchQuery={searchQuery}\r\n              onSearchChange={handleSearchInputChange}\r\n              onSearchKeyDown={handleSearchKeyDown}\r\n              onSearchClick={handleSearchClick}\r\n              onCategoryChange={handleCategoryChange}\r\n              onBrandChange={handleBrandChange}\r\n              categories={categoriesData?.data || []}\r\n              brands={brandsData?.data || []}\r\n              placeholder=\"Search inactive products...\"\r\n            />\r\n\r\n            <ProductsTable\r\n              products={data?.data || []}\r\n              isLoading={isLoading}\r\n              onSearch={handleSearchClick}\r\n              onSort={handleSort}\r\n              sortField={sortField}\r\n              sortDirection={sortDirection}\r\n            />\r\n\r\n            {/* Modern pagination component */}\r\n            <div className=\"mt-6 w-full\">\r\n              <DataPagination\r\n                currentPage={currentPage}\r\n                totalPages={totalPages}\r\n                onPageChange={handlePageChange}\r\n                pageSize={itemsPerPage}\r\n                onPageSizeChange={handleItemsPerPageChange}\r\n                totalItems={totalItems}\r\n                isLoading={isLoading}\r\n                showPageSizeSelector={true}\r\n                showItemsInfo={true}\r\n                showFirstLastButtons={true}\r\n              />\r\n            </div>\r\n          </TabsContent>\r\n        </Tabs>\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AACA;AAzBA;;;;;;;;;;;;;;;;;AA2Be,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACnE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACjD;IAEF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACnE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7C;IAGF,sCAAsC;IACtC,MAAM,cAAc;QAClB,MAAM;QACN,OAAO;QACP,QAAQ,eAAe;QACvB,aAAa;QACb,UAAU;QACV,WAAW;QACX,MAAM,YAAY,GAAG,UAAU,CAAC,EAAE,eAAe,GAAG;IACtD;IAEA,iDAAiD;IACjD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uJAAA,CAAA,cAAW,AAAD,EAAE;IAExC,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD;IAC7C,MAAM,EAAE,MAAM,UAAU,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,YAAS,AAAD;IAErC,8BAA8B;IAC9B,MAAM,aAAa,MAAM,YAAY,SAAS;IAC9C,MAAM,aACJ,MAAM,YAAY,cAAc,KAAK,IAAI,CAAC,aAAa,iBAAiB;IAE1E,6BAA6B;IAC7B,MAAM,0BAA0B,CAAC;QAC/B,eAAe,EAAE,MAAM,CAAC,KAAK;IAC/B;IAEA,6BAA6B;IAC7B,MAAM,oBAAoB;QACxB,eAAe,IAAI,oCAAoC;IACvD,+EAA+E;IACjF;IAEA,6BAA6B;IAC7B,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,gCAAgC;IAChC,MAAM,uBAAuB,CAAC;QAC5B,eAAe,IAAI,uCAAuC;QAC1D,kBACE,eAAe,QAAQ,YAAY,SAAS,YAAY;IAE5D;IAEA,6BAA6B;IAC7B,MAAM,oBAAoB,CAAC;QACzB,eAAe,IAAI,uCAAuC;QAC1D,eAAe,YAAY,QAAQ,YAAY,SAAS,SAAS;IACnE;IAEA,8BAA8B;IAC9B,MAAM,qBAAqB,CAAC;QAC1B,eAAe,IAAI,uCAAuC;QAE1D,IAAI,WAAW,UAAU;YACvB,gBAAgB;QAClB,OAAO,IAAI,WAAW,YAAY;YAChC,gBAAgB;QAClB,OAAO;YACL,2BAA2B;YAC3B,gBAAgB;QAClB;IACF;IAEA,6BAA6B;IAE7B,oBAAoB;IACpB,MAAM,kBAAkB,CAAC;QACvB,aAAa;QACb,eAAe,IAAI,oCAAoC;QAEvD,OAAQ;YACN,KAAK;gBACH,gBAAgB;gBAChB;YACF,KAAK;gBACH,gBAAgB;gBAChB;YACF;gBACE,gBAAgB;QACpB;IACF;IAEA,qBAAqB;IACrB,MAAM,aAAa,CAAC;QAClB,IAAI,cAAc,OAAO;YACvB,iCAAiC;YACjC,iBAAiB,kBAAkB,QAAQ,SAAS;QACtD,OAAO;YACL,yCAAyC;YACzC,aAAa;YACb,iBAAiB;QACnB;IACF;IAEA,oBAAoB;IACpB,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,+BAA+B;IAC/B,MAAM,2BAA2B,CAAC;QAChC,gBAAgB;QAChB,eAAe,IAAI,mDAAmD;IACxE;IAEA,yBAAyB;IACzB,MAAM,sBAAsB;QAC1B,IAAI,MAAM,QAAQ,KAAK,IAAI,CAAC,MAAM,GAAG,GAAG;YACtC,CAAA,GAAA,0JAAA,CAAA,wBAAqB,AAAD,EAClB,KAAK,IAAI,EACT,CAAC,gBAAgB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;QAE/D;IACF;IAEA,sCAAsC;IACtC,MAAM,eAAe,MAAM,OAAO,CAAC,MAAM,QAAQ,KAAK,IAAI,GAAG,EAAE;IAE/D,0EAA0E;IAC1E,MAAM,iBACJ,MAAM,QAAQ,UACd,aAAa,MAAM,CAAC,CAAC,UAAY,SAAS,WAAW,MAAM;IAE7D,MAAM,mBACJ,MAAM,QAAQ,YACd,aAAa,MAAM,CAAC,CAAC,UAAY,SAAS,cAAc,OAAO,MAAM;IAEvE,wCAAwC;IACxC,MAAM,mBAAmB,CAAC;QACxB,IAAI,QAAQ,KAAK,IAAI,MAAM,OAAO,CAAC,QAAQ,KAAK,GAAG;YACjD,OAAO,QAAQ,KAAK,CAAC,MAAM,CACzB,CAAC,OAAO,OAAS,QAAQ,CAAC,KAAK,QAAQ,IAAI,CAAC,GAC5C;QAEJ;QACA,OAAO,QAAQ,cAAc,IAAI;IACnC;IAEA,qGAAqG;IACrG,MAAM,mBACJ,MAAM,QAAQ,YACd,aAAa,MAAM,CAAC,CAAC;QACnB,MAAM,WAAW,iBAAiB;QAClC,OAAO,WAAW,KAAK,YAAY;IACrC,GAAG,MAAM;IAEX,MAAM,qBACJ,MAAM,QAAQ,cACd,aAAa,MAAM,CAAC,CAAC,UAAY,iBAAiB,aAAa,GAAG,MAAM;IAE1E,8DAA8D;IAC9D,MAAM,sBAAsB,aAAa,MAAM,CAAC,CAAC,OAAO;QACtD,IAAI,CAAC,SAAS,OAAO;QACrB,+DAA+D;QAC/D,MAAM,QACJ,OAAO,QAAQ,KAAK,KAAK,WACrB,QAAQ,KAAK,GACb,OAAO,QAAQ,aAAa,KAAK,WACjC,QAAQ,aAAa,GACrB,WAAW,QAAQ,aAAa,KAAe;QACrD,MAAM,gBAAgB,iBAAiB;QACvC,OAAO,QAAQ,QAAQ;IACzB,GAAG;IAEH,4DAA4D;IAC5D,MAAM,qBAAqB,aAAa,MAAM,CAAC,CAAC,OAAO;QACrD,IAAI,CAAC,SAAS,OAAO;QACrB,sEAAsE;QACtE,MAAM,cACJ,OAAO,QAAQ,YAAY,KAAK,WAC5B,QAAQ,YAAY,GACpB,WAAW,QAAQ,YAAY,KAAe;QACpD,MAAM,gBAAgB,iBAAiB;QACvC,OAAO,QAAQ,cAAc;IAC/B,GAAG;IAEH,6BAA6B;IAC7B,MAAM,kBAAkB,sBAAsB;IAE9C,qBACE,8OAAC,+IAAA,CAAA,aAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS;;sDAC3C,8OAAC,4NAAA,CAAA,kBAAe;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAG9C,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC;;sDAE3B,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;sDACjC,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAMvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;8CAErB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,YACG,QACA,MAAM,QAAQ,SACd,MAAM,YAAY,SAClB,aAAa,MAAM,IACnB;;;;;;sDAEN,8OAAC;4CAAE,WAAU;;gDACV,MAAM,QAAQ,UAAU;gDAAe;gDAAS;gDAChD,MAAM,QAAQ,YAAY;gDAAiB;;;;;;;;;;;;;;;;;;;sCAIlD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,8OAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,YAAY,QAAQ;;;;;;sDAEvB,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;;;;;;;sCAKtD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;;8CAE3B,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,YAAY,QAAQ;;;;;;sDAEvB,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;;;;;;;sCAKtD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,YAAY,QAAQ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;sDAEtC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;;;;;;;sCAKtD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDACZ,YAAY,QAAQ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;sDAEtC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;;;;;;;;;;;;;8BAOxD,8OAAC,gIAAA,CAAA,OAAI;oBACH,cAAa;oBACb,OAAO;oBACP,eAAe;oBACf,WAAU;;sCAEV,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAM;;;;;;8CACzB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAS;;;;;;8CAC5B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;;;;;;;sCAGhC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAM,WAAU;;8CACjC,8OAAC,sKAAA,CAAA,mBAAgB;oCACf,aAAa;oCACb,gBAAgB;oCAChB,iBAAiB;oCACjB,eAAe;oCACf,kBAAkB;oCAClB,eAAe;oCACf,YAAY,gBAAgB,QAAQ,EAAE;oCACtC,QAAQ,YAAY,QAAQ,EAAE;oCAC9B,aAAY;;;;;;8CAGd,8OAAC,+JAAA,CAAA,gBAAa;oCACZ,UAAU,MAAM,QAAQ,EAAE;oCAC1B,WAAW;oCACX,UAAU;oCACV,QAAQ;oCACR,WAAW;oCACX,eAAe;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wCACb,aAAa;wCACb,YAAY;wCACZ,cAAc;wCACd,UAAU;wCACV,kBAAkB;wCAClB,YAAY;wCACZ,WAAW;wCACX,sBAAsB;wCACtB,eAAe;wCACf,sBAAsB;;;;;;;;;;;;;;;;;sCAM5B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAS,WAAU;;8CACpC,8OAAC,sKAAA,CAAA,mBAAgB;oCACf,aAAa;oCACb,gBAAgB;oCAChB,iBAAiB;oCACjB,eAAe;oCACf,kBAAkB;oCAClB,eAAe;oCACf,YAAY,gBAAgB,QAAQ,EAAE;oCACtC,QAAQ,YAAY,QAAQ,EAAE;oCAC9B,aAAY;;;;;;8CAGd,8OAAC,+JAAA,CAAA,gBAAa;oCACZ,UAAU,MAAM,QAAQ,EAAE;oCAC1B,WAAW;oCACX,UAAU;oCACV,QAAQ;oCACR,WAAW;oCACX,eAAe;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wCACb,aAAa;wCACb,YAAY;wCACZ,cAAc;wCACd,UAAU;wCACV,kBAAkB;wCAClB,YAAY;wCACZ,WAAW;wCACX,sBAAsB;wCACtB,eAAe;wCACf,sBAAsB;;;;;;;;;;;;;;;;;sCAK5B,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;;8CACtC,8OAAC,sKAAA,CAAA,mBAAgB;oCACf,aAAa;oCACb,gBAAgB;oCAChB,iBAAiB;oCACjB,eAAe;oCACf,kBAAkB;oCAClB,eAAe;oCACf,YAAY,gBAAgB,QAAQ,EAAE;oCACtC,QAAQ,YAAY,QAAQ,EAAE;oCAC9B,aAAY;;;;;;8CAGd,8OAAC,+JAAA,CAAA,gBAAa;oCACZ,UAAU,MAAM,QAAQ,EAAE;oCAC1B,WAAW;oCACX,UAAU;oCACV,QAAQ;oCACR,WAAW;oCACX,eAAe;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wCACb,aAAa;wCACb,YAAY;wCACZ,cAAc;wCACd,UAAU;wCACV,kBAAkB;wCAClB,YAAY;wCACZ,WAAW;wCACX,sBAAsB;wCACtB,eAAe;wCACf,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC", "debugId": null}}]}