const { DsaStockAssignment, DsaPayment, Product } = require('../models');
const logger = require('../utils/logger');
const AppError = require('../utils/error');

/**
 * DSA Item-Level Payment Service
 * Handles tracking payments at individual item level
 */
class DsaItemPaymentService {
  /**
   * Process item-level payment by specific quantities for each item
   * @param {string} assignmentIdentifier - Assignment identifier
   * @param {Array} selectedItemPayments - Array of { itemId, itemName, quantity, amountPaid, unitPrice }
   * @param {number} totalPaymentAmount - Total amount being paid
   * @param {Object} transaction - Database transaction
   * @returns {Object} Payment processing result
   */
  static async processItemLevelPaymentByAmount(assignmentIdentifier, selectedItemPayments, totalPaymentAmount, transaction = null) {
    try {
      logger.info(`Processing item-level payment by amount for assignment: ${assignmentIdentifier}`);

      // Get all items in the assignment
      const assignments = await DsaStockAssignment.findAll({
        where: { assignment_identifier: assignmentIdentifier },
        include: [
          {
            model: Product,
            attributes: ['id', 'name', 'default_wholesale_price']
          }
        ],
        transaction
      });

      if (assignments.length === 0) {
        throw new AppError(404, 'No assignments found for the given identifier');
      }

      // Update individual item payment status based on specific amounts
      const updateResults = [];
      for (const itemPayment of selectedItemPayments) {
        const assignment = assignments.find(a => a.id === itemPayment.itemId);
        if (!assignment) continue;

        // Calculate new payment amounts
        const currentAmountPaid = parseFloat(assignment.amount_paid || 0);
        const newAmountPaid = currentAmountPaid + itemPayment.amountPaid;
        const itemTotalValue = this.calculateItemTotalValue(assignment);
        const newBalance = Math.max(0, itemTotalValue - newAmountPaid);

        // Use quantity directly from the payment data
        const quantityPaidForThisPayment = parseInt(itemPayment.quantity || 0);
        const currentQuantitySold = parseInt(assignment.quantity_sold || 0);
        const newQuantitySold = currentQuantitySold + quantityPaidForThisPayment;

        // Determine payment status
        const paymentStatus = this.determineItemPaymentStatus(newAmountPaid, itemTotalValue, assignment);

        // Update the assignment record
        await DsaStockAssignment.update({
          amount_paid: newAmountPaid,
          balance: newBalance,
          payment_status: paymentStatus,
          quantity_sold: newQuantitySold, // Track quantity paid using quantity_sold
          last_payment_date: new Date()
        }, {
          where: { id: assignment.id },
          transaction
        });

        updateResults.push({
          itemId: assignment.id,
          productName: assignment.Product?.name || 'Unknown Product',
          quantity: quantityPaidForThisPayment,
          unitPrice: itemPayment.unitPrice || this.getItemUnitPrice(assignment),
          paymentAmount: itemPayment.amountPaid,
          newQuantitySold,
          newAmountPaid,
          newBalance,
          paymentStatus,
          itemTotalValue
        });

        logger.info(`Updated item ${assignment.id}: Paid ${itemPayment.amountPaid}, New balance: ${newBalance}, Status: ${paymentStatus}`);
      }

      return {
        success: true,
        assignmentIdentifier,
        totalPaymentAmount,
        itemsUpdated: updateResults.length,
        paymentDistribution: updateResults
      };

    } catch (error) {
      logger.error(`Error processing item-level payment by amount for ${assignmentIdentifier}:`, error);
      throw error;
    }
  }

  /**
   * Process item-level payment and update individual item payment status
   * @param {string} assignmentIdentifier - Assignment identifier
   * @param {Object} selectedQuantities - Object with itemId: quantity pairs
   * @param {number} totalPaymentAmount - Total amount being paid
   * @param {Object} transaction - Database transaction
   * @returns {Object} Payment processing result
   */
  static async processItemLevelPayment(assignmentIdentifier, selectedQuantities, totalPaymentAmount, transaction = null) {
    try {
      logger.info(`Processing item-level payment for assignment: ${assignmentIdentifier}`);

      // Get all items in the assignment
      const assignments = await DsaStockAssignment.findAll({
        where: { assignment_identifier: assignmentIdentifier },
        include: [
          {
            model: Product,
            attributes: ['id', 'name', 'default_wholesale_price']
          }
        ],
        transaction
      });

      if (assignments.length === 0) {
        throw new AppError(404, 'No assignments found for the given identifier');
      }

      // Calculate payment distribution
      const paymentDistribution = this.calculatePaymentDistribution(
        assignments,
        selectedQuantities,
        totalPaymentAmount
      );

      // Update individual item payment status
      const updateResults = [];
      for (const distribution of paymentDistribution) {
        const assignment = assignments.find(a => a.id === distribution.itemId);
        if (!assignment) continue;

        // Calculate new payment amounts
        const currentAmountPaid = parseFloat(assignment.amount_paid || 0);
        const newAmountPaid = currentAmountPaid + distribution.paymentAmount;
        const itemTotalValue = this.calculateItemTotalValue(assignment);
        const newBalance = Math.max(0, itemTotalValue - newAmountPaid);

        // Determine payment status
        const paymentStatus = this.determineItemPaymentStatus(newAmountPaid, itemTotalValue, assignment);

        // Calculate new quantity sold (quantity paid)
        const currentQuantitySold = parseInt(assignment.quantity_sold || 0);
        const newQuantitySold = currentQuantitySold + distribution.quantityPaid;

        // Update the assignment record
        await DsaStockAssignment.update({
          amount_paid: newAmountPaid,
          balance: newBalance,
          payment_status: paymentStatus,
          quantity_sold: newQuantitySold, // Track quantity paid using quantity_sold
          last_payment_date: new Date()
        }, {
          where: { id: assignment.id },
          transaction
        });

        updateResults.push({
          itemId: assignment.id,
          productName: assignment.Product?.name || 'Unknown Product',
          quantityPaid: distribution.quantityPaid,
          newQuantitySold,
          paymentAmount: distribution.paymentAmount,
          newAmountPaid,
          newBalance,
          paymentStatus,
          itemTotalValue
        });

        logger.info(`Updated item ${assignment.id}: Paid ${distribution.paymentAmount}, New balance: ${newBalance}, Status: ${paymentStatus}`);
      }

      return {
        success: true,
        assignmentIdentifier,
        totalPaymentAmount,
        itemsUpdated: updateResults.length,
        paymentDistribution: updateResults
      };

    } catch (error) {
      logger.error(`Error processing item-level payment for ${assignmentIdentifier}:`, error);
      throw error;
    }
  }

  /**
   * Calculate how payment should be distributed across selected items
   * @param {Array} assignments - All assignment items
   * @param {Object} selectedQuantities - Selected quantities per item
   * @param {number} totalPaymentAmount - Total payment amount
   * @returns {Array} Payment distribution array
   */
  static calculatePaymentDistribution(assignments, selectedQuantities, totalPaymentAmount) {
    const distribution = [];
    let remainingPayment = totalPaymentAmount;

    // Calculate total value of selected quantities
    let totalSelectedValue = 0;
    Object.entries(selectedQuantities).forEach(([itemId, quantity]) => {
      const assignment = assignments.find(a => a.id === parseInt(itemId));
      if (assignment) {
        const unitPrice = this.getItemUnitPrice(assignment);
        totalSelectedValue += quantity * unitPrice;
      }
    });

    // Distribute payment proportionally
    Object.entries(selectedQuantities).forEach(([itemId, quantity]) => {
      const assignment = assignments.find(a => a.id === parseInt(itemId));
      if (!assignment || remainingPayment <= 0) return;

      const unitPrice = this.getItemUnitPrice(assignment);
      const itemSelectedValue = quantity * unitPrice;

      // Calculate proportional payment for this item
      const proportionalPayment = totalSelectedValue > 0
        ? (itemSelectedValue / totalSelectedValue) * totalPaymentAmount
        : 0;

      const actualPayment = Math.min(proportionalPayment, remainingPayment);

      if (actualPayment > 0) {
        distribution.push({
          itemId: parseInt(itemId),
          quantityPaid: quantity,
          itemSelectedValue,
          paymentAmount: actualPayment,
          unitPrice
        });

        remainingPayment -= actualPayment;
      }
    });

    return distribution;
  }

  /**
   * Calculate total value for an item (remaining quantity × unit price)
   * @param {Object} assignment - Assignment item
   * @returns {number} Total item value
   */
  static calculateItemTotalValue(assignment) {
    const quantityAssigned = parseInt(assignment.quantity_assigned || 0);
    const quantityReturned = parseInt(assignment.quantity_returned || 0);
    const remainingQuantity = Math.max(0, quantityAssigned - quantityReturned);
    const unitPrice = this.getItemUnitPrice(assignment);

    return remainingQuantity * unitPrice;
  }

  /**
   * Get unit price for an item
   * @param {Object} assignment - Assignment item
   * @returns {number} Unit price
   */
  static getItemUnitPrice(assignment) {
    return parseFloat(
      assignment.default_wholesale_price ||
      assignment.Product?.default_wholesale_price ||
      0
    );
  }

  /**
   * Determine payment status for an individual item
   * @param {number} amountPaid - Amount paid for the item
   * @param {number} totalValue - Total value of the item
   * @param {Object} assignment - Assignment item
   * @returns {string} Payment status
   */
  static determineItemPaymentStatus(amountPaid, totalValue, assignment) {
    // Check if all quantity is returned
    const quantityAssigned = parseInt(assignment.quantity_assigned || 0);
    const quantityReturned = parseInt(assignment.quantity_returned || 0);
    const allReturned = quantityReturned >= quantityAssigned;

    if (allReturned) {
      return 'FULLY_PAID';
    }

    const balance = totalValue - amountPaid;

    if (balance <= 0.01) { // Allow for small rounding differences
      return 'FULLY_PAID';
    }

    if (amountPaid > 0) {
      return 'PARTIALLY_PAID';
    }

    return 'UNPAID';
  }

  /**
   * Get payment summary for an assignment with item-level details
   * @param {string} assignmentIdentifier - Assignment identifier
   * @returns {Object} Payment summary
   */
  static async getItemLevelPaymentSummary(assignmentIdentifier) {
    try {
      const assignments = await DsaStockAssignment.findAll({
        where: { assignment_identifier: assignmentIdentifier },
        include: [
          {
            model: Product,
            attributes: ['id', 'name', 'default_wholesale_price']
          }
        ]
      });

      const itemSummaries = assignments.map(assignment => {
        const totalValue = this.calculateItemTotalValue(assignment);
        const amountPaid = parseFloat(assignment.amount_paid || 0);
        const balance = Math.max(0, totalValue - amountPaid);

        return {
          itemId: assignment.id,
          productName: assignment.Product?.name || 'Unknown Product',
          quantityAssigned: assignment.quantity_assigned || 0,
          quantityReturned: assignment.quantity_returned || 0,
          quantitySold: assignment.quantity_sold || 0, // Quantity paid
          unitPrice: this.getItemUnitPrice(assignment),
          totalValue,
          amountPaid,
          balance,
          paymentStatus: assignment.payment_status || 'UNPAID',
          lastPaymentDate: assignment.last_payment_date
        };
      });

      const totalAssignedValue = itemSummaries.reduce((sum, item) => sum + item.totalValue, 0);
      const totalPaid = itemSummaries.reduce((sum, item) => sum + item.amountPaid, 0);
      const totalBalance = itemSummaries.reduce((sum, item) => sum + item.balance, 0);

      return {
        assignmentIdentifier,
        totalAssignedValue,
        totalPaid,
        totalBalance,
        itemCount: itemSummaries.length,
        items: itemSummaries
      };

    } catch (error) {
      logger.error(`Error getting item-level payment summary for ${assignmentIdentifier}:`, error);
      throw error;
    }
  }
}

module.exports = DsaItemPaymentService;
