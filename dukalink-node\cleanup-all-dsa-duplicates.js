const mysql = require('mysql2/promise');
require('dotenv').config();

async function cleanupAllDsaDuplicates() {
  let connection;

  try {
    console.log('🚨 EMERGENCY DSA DUPLICATE CLEANUP - ALL HISTORICAL DATA');
    console.log('📅 Analysis Date:', new Date().toISOString());
    console.log('⚠️  WARNING: This will permanently delete ALL duplicate DSA data!');
    console.log('');

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT || 3306,
    });

    console.log('✅ Connected to database successfully');

    // First, comprehensive analysis of what we're about to delete
    console.log('🔍 Analyzing ALL null receipt DSA sales for deletion...');

    const analysisQuery = `
      SELECT
        s.id as sale_id,
        s.customer_id,
        s.total_amount,
        s.payment_method_id,
        s.payment_reference,
        s.created_at,
        s.is_dsa,
        s.sale_type,
        c.name as customer_name,
        pm.name as payment_method_name,
        COUNT(si.id) as item_count,
        SUM(si.total_price) as items_total_value,
        DATE(s.created_at) as sale_date
      FROM sales s
      LEFT JOIN customers c ON s.customer_id = c.id
      LEFT JOIN payment_methods pm ON s.payment_method_id = pm.id
      LEFT JOIN sale_items si ON s.id = si.sale_id
      WHERE s.receipt_number IS NULL
        AND s.is_dsa = 1
      GROUP BY s.id, s.customer_id, s.total_amount, s.payment_method_id, s.payment_reference, s.created_at, s.is_dsa, s.sale_type, c.name, pm.name
      ORDER BY s.created_at DESC;
    `;

    const [salesToClean] = await connection.execute(analysisQuery);

    if (salesToClean.length === 0) {
      console.log('✅ No null receipt DSA sales found in database!');
      return;
    }

    console.log(`📊 Found ${salesToClean.length} null receipt DSA sales to delete:`);
    console.log('');

    // Group by date for analysis
    const salesByDate = {};
    let totalItemsToDelete = 0;
    let totalValueToDelete = 0;
    let salesWithItems = 0;

    salesToClean.forEach(sale => {
      const date = sale.sale_date;
      if (!salesByDate[date]) {
        salesByDate[date] = [];
      }
      salesByDate[date].push(sale);

      totalItemsToDelete += parseInt(sale.item_count || 0);
      totalValueToDelete += parseFloat(sale.items_total_value || 0);
      if (sale.item_count > 0) {
        salesWithItems++;
      }
    });

    // Display analysis by date
    Object.entries(salesByDate).forEach(([date, dateSales]) => {
      console.log(`📅 ${date}: ${dateSales.length} sales`);
      dateSales.forEach((sale, index) => {
        console.log(`   ${index + 1}. ${sale.customer_name} - ${sale.total_amount} KES (${sale.item_count} items)`);
      });
      console.log('');
    });

    console.log(`📈 CLEANUP SUMMARY:`);
    console.log(`   Total null receipt sales: ${salesToClean.length}`);
    console.log(`   Total sale items to delete: ${totalItemsToDelete}`);
    console.log(`   Total value to delete: ${totalValueToDelete} KES`);
    console.log(`   Sales with items: ${salesWithItems}`);
    console.log(`   Date range: ${salesToClean[salesToClean.length - 1]?.created_at} to ${salesToClean[0]?.created_at}`);
    console.log('');

    // Get current DSA statistics
    const [currentStats] = await connection.execute(`
      SELECT
        COUNT(*) as total_dsa_sales,
        SUM(total_amount) as total_dsa_value,
        COUNT(CASE WHEN receipt_number IS NOT NULL THEN 1 END) as sales_with_receipts,
        COUNT(CASE WHEN receipt_number IS NULL THEN 1 END) as sales_without_receipts
      FROM sales
      WHERE is_dsa = 1;
    `);

    const stats = currentStats[0];
    console.log(`📊 CURRENT DSA DATABASE STATE:`);
    console.log(`   Total DSA Sales: ${stats.total_dsa_sales}`);
    console.log(`   Total DSA Value: ${stats.total_dsa_value} KES`);
    console.log(`   Sales with Receipts: ${stats.sales_with_receipts}`);
    console.log(`   Sales without Receipts: ${stats.sales_without_receipts}`);
    console.log(`   Duplicate Rate: ${((stats.sales_without_receipts / stats.total_dsa_sales) * 100).toFixed(2)}%`);
    console.log('');

    console.log(`📊 AFTER CLEANUP PROJECTION:`);
    console.log(`   Remaining DSA Sales: ${stats.sales_with_receipts}`);
    console.log(`   Remaining DSA Value: ${stats.total_dsa_value - totalValueToDelete} KES`);
    console.log(`   Data Integrity: 100% (no duplicates)`);
    console.log('');

    // Safety checks and warnings
    console.log('🚨 CRITICAL SAFETY WARNINGS:');
    console.log('   1. This will delete ALL historical duplicate DSA data');
    console.log('   2. This affects multiple dates and customers');
    console.log('   3. Financial reports will change significantly');
    console.log('   4. This operation cannot be easily undone');
    console.log('   5. Ensure you have a complete database backup');
    console.log('');

    console.log('⚠️  IMPACT ASSESSMENT:');
    console.log(`   - ${salesToClean.length} sales will be deleted`);
    console.log(`   - ${totalItemsToDelete} sale items will be removed`);
    console.log(`   - ${totalValueToDelete} KES in duplicate data will be cleaned`);
    console.log(`   - Revenue will decrease by ~${((totalValueToDelete / stats.total_dsa_value) * 100).toFixed(1)}%`);
    console.log(`   - Duplicate rate will go from ${((stats.sales_without_receipts / stats.total_dsa_sales) * 100).toFixed(1)}% to 0%`);
    console.log('');

    // Require explicit confirmation
    console.log('🔄 CONFIRMATION REQUIRED:');
    console.log('   To proceed with this MASSIVE cleanup, set CONFIRM_MASSIVE_CLEANUP = true');

    const CONFIRM_MASSIVE_CLEANUP = true; // Set to true to proceed

    if (!CONFIRM_MASSIVE_CLEANUP) {
      console.log('❌ Massive cleanup not confirmed. Exiting safely.');
      console.log('   This is the right choice - ensure you have backups first!');
      console.log('   Set CONFIRM_MASSIVE_CLEANUP = true when ready to proceed.');
      return;
    }

    // If we reach here, massive cleanup is confirmed
    console.log('🚀 PROCEEDING WITH MASSIVE CLEANUP...');
    console.log('⏱️  This may take several minutes...');

    // Start transaction for safety
    await connection.beginTransaction();

    try {
      // Step 1: Get all null receipt DSA sale IDs
      const saleIdsQuery = `
        SELECT id
        FROM sales
        WHERE receipt_number IS NULL
          AND is_dsa = 1
        ORDER BY created_at;
      `;

      const [saleIds] = await connection.execute(saleIdsQuery);

      if (saleIds.length === 0) {
        console.log('❌ No sale IDs found for deletion');
        await connection.rollback();
        return;
      }

      const saleIdList = saleIds.map(row => row.id);
      console.log(`🎯 Deleting data for ${saleIdList.length} sales...`);
      console.log(`   Sale ID range: ${Math.min(...saleIdList)} to ${Math.max(...saleIdList)}`);

      // Step 2: Delete sale items first (foreign key constraint)
      console.log('🗑️  Step 1: Deleting sale items...');
      const deleteItemsQuery = `
        DELETE FROM sale_items
        WHERE sale_id IN (${saleIdList.map(() => '?').join(',')})
      `;

      const [deleteItemsResult] = await connection.execute(deleteItemsQuery, saleIdList);
      console.log(`   ✅ Deleted ${deleteItemsResult.affectedRows} sale items`);

      // Step 3: Delete the sales records
      console.log('🗑️  Step 2: Deleting sales records...');
      const deleteSalesQuery = `
        DELETE FROM sales
        WHERE id IN (${saleIdList.map(() => '?').join(',')})
      `;

      const [deleteSalesResult] = await connection.execute(deleteSalesQuery, saleIdList);
      console.log(`   ✅ Deleted ${deleteSalesResult.affectedRows} sales records`);

      // Step 4: Verification
      console.log('🔍 Step 3: Verifying cleanup...');

      // Check for remaining null receipt DSA sales
      const [verifyNullSales] = await connection.execute(`
        SELECT COUNT(*) as remaining_null_sales
        FROM sales
        WHERE receipt_number IS NULL AND is_dsa = 1
      `);

      // Check for orphaned sale items
      const [verifyOrphanItems] = await connection.execute(`
        SELECT COUNT(*) as orphaned_items
        FROM sale_items si
        LEFT JOIN sales s ON si.sale_id = s.id
        WHERE s.id IS NULL
      `);

      // Get final statistics
      const [finalStats] = await connection.execute(`
        SELECT
          COUNT(*) as total_dsa_sales,
          SUM(total_amount) as total_dsa_value,
          COUNT(CASE WHEN receipt_number IS NOT NULL THEN 1 END) as sales_with_receipts,
          COUNT(CASE WHEN receipt_number IS NULL THEN 1 END) as sales_without_receipts
        FROM sales
        WHERE is_dsa = 1;
      `);

      const finalStatsData = finalStats[0];
      const remainingNullSales = verifyNullSales[0].remaining_null_sales;
      const orphanedItems = verifyOrphanItems[0].orphaned_items;

      console.log('📊 VERIFICATION RESULTS:');
      console.log(`   Remaining null receipt DSA sales: ${remainingNullSales}`);
      console.log(`   Orphaned sale items: ${orphanedItems}`);
      console.log(`   Final DSA sales count: ${finalStatsData.total_dsa_sales}`);
      console.log(`   Final DSA value: ${finalStatsData.total_dsa_value} KES`);
      console.log(`   All sales now have receipts: ${finalStatsData.sales_without_receipts === 0 ? 'YES' : 'NO'}`);

      if (remainingNullSales === 0 && orphanedItems === 0) {
        console.log('✅ VERIFICATION PASSED: Cleanup successful!');
        await connection.commit();
        console.log('🎉 TRANSACTION COMMITTED SUCCESSFULLY!');
      } else {
        console.log('❌ VERIFICATION FAILED: Issues detected');
        console.log(`   - Null sales remaining: ${remainingNullSales}`);
        console.log(`   - Orphaned items: ${orphanedItems}`);
        await connection.rollback();
        console.log('🔄 TRANSACTION ROLLED BACK FOR SAFETY');
        return;
      }

    } catch (deleteError) {
      console.error('❌ Error during massive cleanup:', deleteError.message);
      await connection.rollback();
      console.log('🔄 Transaction rolled back due to error');
      throw deleteError;
    }

    // Final success summary
    console.log('');
    console.log('🎉 MASSIVE DSA DUPLICATE CLEANUP COMPLETED!');
    console.log('');
    console.log('📊 CLEANUP RESULTS:');
    console.log(`   ✅ Deleted ${salesToClean.length} duplicate sales`);
    console.log(`   ✅ Removed ${totalItemsToDelete} duplicate sale items`);
    console.log(`   ✅ Cleaned ${totalValueToDelete} KES worth of duplicate data`);
    console.log(`   ✅ Restored data integrity to 100%`);
    console.log(`   ✅ Eliminated ${((stats.sales_without_receipts / stats.total_dsa_sales) * 100).toFixed(1)}% duplicate rate`);
    console.log('');
    console.log('🎯 NEXT STEPS:');
    console.log('   1. ✅ Historical duplicates cleaned');
    console.log('   2. 🚀 Deploy DSA fix to prevent future duplicates');
    console.log('   3. 📊 Update all financial reports');
    console.log('   4. 📢 Notify stakeholders of corrected data');
    console.log('   5. 🔍 Monitor new DSA sales for any issues');
    console.log('');
    console.log('💡 The DSA system now has clean, accurate data!');

  } catch (error) {
    console.error('❌ Error in massive DSA cleanup:', error.message);
    console.error('Full error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the massive cleanup
cleanupAllDsaDuplicates();
