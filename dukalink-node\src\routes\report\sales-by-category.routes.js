const express = require('express');
const router = express.Router();
const { exportAllSalesByCategory, exportCustomSalesByCategory, exportLightweightSalesByCategory } = require('../../controllers/report/sales-by-category-export.controller');
const { authenticate, rbac } = require('../../middleware/auth.middleware');

/**
 * @swagger
 * /api/v1/reports/sales-by-category/export/all:
 *   get:
 *     summary: Export comprehensive sales by category to Excel
 *     description: Export all category data with multiple sheets including summary, details, and product breakdown
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by region ID
 *       - in: query
 *         name: category_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by category ID
 *     responses:
 *       200:
 *         description: Excel file with comprehensive sales by category data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No category data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/all',
  authenticate,
  rbac.checkPermission('sales_reports', 'read'),
  exportAllSalesByCategory
);

/**
 * @swagger
 * /api/v1/reports/sales-by-category/export/custom:
 *   get:
 *     summary: Export custom sales by category to Excel
 *     description: Export category data with customizable sheets and format options
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by region ID
 *       - in: query
 *         name: category_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by category ID
 *       - in: query
 *         name: include_summary
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include summary sheet
 *       - in: query
 *         name: include_details
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include category details sheet
 *       - in: query
 *         name: include_product_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include product breakdown sheet
 *       - in: query
 *         name: include_charts
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include charts and graphs
 *       - in: query
 *         name: format_type
 *         schema:
 *           type: string
 *           enum: [detailed, summary]
 *           default: detailed
 *         description: Format type for export
 *     responses:
 *       200:
 *         description: Excel file with custom sales by category data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No category data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/custom',
  authenticate,
  rbac.checkPermission('sales_reports', 'read'),
  exportCustomSalesByCategory
);

/**
 * @swagger
 * /api/v1/reports/sales-by-category/export/lightweight:
 *   get:
 *     summary: Export lightweight sales by category to Excel (fastest option)
 *     description: Export basic category data only for fastest processing
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by region ID
 *       - in: query
 *         name: category_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by category ID
 *     responses:
 *       200:
 *         description: Excel file with basic category data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No category data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/lightweight',
  authenticate,
  rbac.checkPermission('sales_reports', 'read'),
  exportLightweightSalesByCategory
);

module.exports = router;
