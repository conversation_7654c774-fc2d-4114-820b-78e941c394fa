import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import stockMovementItemsExportService, {
  StockMovementItemsExportParams,
  CustomStockMovementItemsExportParams,
} from "../api/stock-movement-items-export-service";

/**
 * Enhanced hook for comprehensive stock movement items export
 * Exports all data with multiple sheets
 */
export const useStockMovementItemsExportAll = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: StockMovementItemsExportParams) => {
      // Validate parameters
      const validation = stockMovementItemsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await stockMovementItemsExportService.exportAllItems(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = stockMovementItemsExportService.generateFilename(params, 'comprehensive');
        stockMovementItemsExportService.downloadBlob(blob, filename);
        
        toast.success("Stock movement items exported successfully!", {
          description: `Comprehensive report with all data sheets downloaded as ${filename}`,
          duration: 5000,
        });

        // Invalidate related queries to refresh any cached data
        queryClient.invalidateQueries({ queryKey: ["stock-movement-items"] });
        queryClient.invalidateQueries({ queryKey: ["stockTransfers"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Export error:', error);
      
      let errorMessage = "Failed to export stock movement items";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export stock movement reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try filtering the data or use summary export.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for custom stock movement items export
 * Allows format and column selection
 */
export const useStockMovementItemsExportCustom = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: CustomStockMovementItemsExportParams) => {
      // Validate parameters
      const validation = stockMovementItemsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await stockMovementItemsExportService.exportCustomItems(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const formatType = params.format_type || 'detailed';
        const filename = stockMovementItemsExportService.generateFilename(params, formatType);
        stockMovementItemsExportService.downloadBlob(blob, filename);
        
        toast.success("Custom stock movement items export completed!", {
          description: `${formatType.charAt(0).toUpperCase() + formatType.slice(1)} report downloaded as ${filename}`,
          duration: 5000,
        });

        queryClient.invalidateQueries({ queryKey: ["stock-movement-items"] });
        queryClient.invalidateQueries({ queryKey: ["stockTransfers"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Custom export error:', error);
      
      let errorMessage = "Failed to export custom stock movement items";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export stock movement reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try using summary format or filtering the data.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Custom Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for quick summary export
 * Lightweight export with essential data only
 */
export const useStockMovementItemsExportSummary = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: StockMovementItemsExportParams) => {
      const validation = stockMovementItemsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await stockMovementItemsExportService.exportSummaryItems(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = stockMovementItemsExportService.generateFilename(params, 'summary');
        stockMovementItemsExportService.downloadBlob(blob, filename);
        
        toast.success("Summary export completed!", {
          description: `Quick summary report downloaded as ${filename}`,
          duration: 4000,
        });

        queryClient.invalidateQueries({ queryKey: ["stock-movement-items"] });
        queryClient.invalidateQueries({ queryKey: ["stockTransfers"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Summary export error:', error);
      
      let errorMessage = "Failed to export summary stock movement items";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export stock movement reports";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Summary Export Failed", {
        description: errorMessage,
        duration: 6000,
      });
    },
  });
};

/**
 * Hook to get export recommendations based on data size
 */
export const useStockMovementItemsExportRecommendation = (estimatedRecords: number) => {
  return stockMovementItemsExportService.getExportRecommendation(estimatedRecords);
};

/**
 * Hook to get export format options for UI
 */
export const useStockMovementItemsExportFormatOptions = () => {
  return stockMovementItemsExportService.getExportFormatOptions();
};

/**
 * Hook to get column options for custom export
 */
export const useStockMovementItemsExportColumnOptions = () => {
  return stockMovementItemsExportService.getColumnOptions();
};

/**
 * Combined hook that provides all export functionality
 * Convenient single hook for components that need multiple export options
 */
export const useStockMovementItemsExportSuite = () => {
  const exportAll = useStockMovementItemsExportAll();
  const exportCustom = useStockMovementItemsExportCustom();
  const exportSummary = useStockMovementItemsExportSummary();

  const isAnyExporting = exportAll.isPending || exportCustom.isPending || exportSummary.isPending;

  return {
    // Individual export methods
    exportAll: exportAll.mutateAsync,
    exportCustom: exportCustom.mutateAsync,
    exportSummary: exportSummary.mutateAsync,
    
    // Loading states
    isExportingAll: exportAll.isPending,
    isExportingCustom: exportCustom.isPending,
    isExportingSummary: exportSummary.isPending,
    isAnyExporting,
    
    // Error states
    exportAllError: exportAll.error,
    exportCustomError: exportCustom.error,
    exportSummaryError: exportSummary.error,
    
    // Utility functions
    getRecommendation: stockMovementItemsExportService.getExportRecommendation,
    getFormatOptions: stockMovementItemsExportService.getExportFormatOptions,
    getColumnOptions: stockMovementItemsExportService.getColumnOptions,
    validateParams: stockMovementItemsExportService.validateExportParams,
    generateFilename: stockMovementItemsExportService.generateFilename,
    
    // Reset functions
    resetAll: () => {
      exportAll.reset();
      exportCustom.reset();
      exportSummary.reset();
    },
  };
};
