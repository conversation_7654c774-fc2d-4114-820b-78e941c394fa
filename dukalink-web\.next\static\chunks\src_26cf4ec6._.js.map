{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/hooks/use-permissions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCurrentUser } from \"./use-auth\";\r\nimport { usePermissionContext } from \"../context/permission-context\";\r\n\r\n// Define constants for resources and actions\r\nexport const RESOURCES = {\r\n  USERS: \"users\",\r\n  ROLES: \"roles\",\r\n  PERMISSIONS: \"permissions\",\r\n  PRODUCTS: \"products\",\r\n  CATEGORIES: \"categories\",\r\n  BRANDS: \"brands\",\r\n  INVENTORY: \"inventory\",\r\n  STOCK_ITEMS: \"stock_items\",\r\n  BRANCHES: \"branches\",\r\n  EMPLOYEES: \"employees\",\r\n  BANKING: \"banking\",\r\n  EXPENSES: \"expenses\",\r\n  EXPENSE_FIRST_APPROVAL: \"expense_first_approval\",\r\n  EXPENSE_SECOND_APPROVAL: \"expense_second_approval\",\r\n  SALES: \"sales\",\r\n  POS_SESSIONS: \"pos_sessions\",\r\n  CUSTOMERS: \"customers\",\r\n  PROFILE: \"profile\",\r\n  STOCK_LOCATIONS: \"stock_locations\",\r\n};\r\n\r\nexport const ACTIONS = {\r\n  CREATE: \"create\",\r\n  READ: \"read\",\r\n  UPDATE: \"update\",\r\n  DELETE: \"delete\",\r\n};\r\n\r\nexport const SCOPE = {\r\n  ANY: \"any\",\r\n  OWN: \"own\",\r\n};\r\n\r\nexport function usePermissions() {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const {\r\n    permissions,\r\n    isLoading: isPermissionsLoading,\r\n    hasPermission,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n  } = usePermissionContext();\r\n\r\n  // Check if the user is a company admin\r\n  const isCompanyAdmin = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the company_admin role\r\n    return (\r\n      user.role_name === \"company_admin\" ||\r\n      user.role_name === \"tenant_admin\" ||\r\n      user.role_name === \"super_admin\"\r\n    );\r\n  };\r\n\r\n  // Check if the user is a branch manager\r\n  const isBranchManager = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the branch_manager role\r\n    return user.role_name === \"branch_manager\";\r\n  };\r\n\r\n  // Check if the user can manage stock locations (create, update, delete)\r\n  const canManageStockLocations = (): boolean => {\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.CREATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.UPDATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.DELETE, SCOPE.ANY) ||\r\n      isCompanyAdmin()\r\n    );\r\n  };\r\n\r\n  // Check if the user can view stock locations\r\n  const canViewStockLocations = (): boolean => {\r\n    // Check for explicit permission or fallback to any authenticated user\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.READ, SCOPE.ANY) ||\r\n      !!user\r\n    );\r\n  };\r\n\r\n  // Check if the user can perform an action on a resource\r\n  const can = (\r\n    resource: string,\r\n    action: string,\r\n    scope: \"any\" | \"own\" = \"any\"\r\n  ): boolean => {\r\n    return hasPermission(resource, action, scope);\r\n  };\r\n\r\n  // Check if the user has any of the specified permissions\r\n  const hasAnyPermission = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.some(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  // Check if the user has all of the specified permissions\r\n  const hasAllPermissions = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.every(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  return {\r\n    isCompanyAdmin,\r\n    isBranchManager,\r\n    canManageStockLocations,\r\n    canViewStockLocations,\r\n    can,\r\n    hasPermission,\r\n    hasAnyPermission,\r\n    hasAllPermissions,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n    permissions, // Expose the raw permissions for debugging\r\n    isLoading: isUserLoading || isPermissionsLoading,\r\n    RESOURCES,\r\n    ACTIONS,\r\n    SCOPE,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;AAHA;;;AAMO,MAAM,YAAY;IACvB,OAAO;IACP,OAAO;IACP,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,aAAa;IACb,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;IACV,wBAAwB;IACxB,yBAAyB;IACzB,OAAO;IACP,cAAc;IACd,WAAW;IACX,SAAS;IACT,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,QAAQ;IACnB,KAAK;IACL,KAAK;AACP;AAEO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EACJ,WAAW,EACX,WAAW,oBAAoB,EAC/B,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,+JAAA,CAAA,uBAAoB,AAAD;IAEvB,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,+CAA+C;QAC/C,OACE,KAAK,SAAS,KAAK,mBACnB,KAAK,SAAS,KAAK,kBACnB,KAAK,SAAS,KAAK;IAEvB;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAElB,gDAAgD;QAChD,OAAO,KAAK,SAAS,KAAK;IAC5B;IAEA,wEAAwE;IACxE,MAAM,0BAA0B;QAC9B,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE;IAEJ;IAEA,6CAA6C;IAC7C,MAAM,wBAAwB;QAC5B,sEAAsE;QACtE,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,KAChE,CAAC,CAAC;IAEN;IAEA,wDAAwD;IACxD,MAAM,MAAM,CACV,UACA,QACA,QAAuB,KAAK;QAE5B,OAAO,cAAc,UAAU,QAAQ;IACzC;IAEA,yDAAyD;IACzD,MAAM,mBAAmB,CACvB;QAMA,OAAO,YAAY,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC1D,cAAc,UAAU,QAAQ;IAEpC;IAEA,yDAAyD;IACzD,MAAM,oBAAoB,CACxB;QAMA,OAAO,YAAY,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC3D,cAAc,UAAU,QAAQ;IAEpC;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,iBAAiB;QAC5B;QACA;QACA;IACF;AACF;GArGgB;;QACmC,kJAAA,CAAA,iBAAc;QAO3D,+JAAA,CAAA,uBAAoB", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/route-permissions.ts"], "sourcesContent": ["/**\r\n * Route Permission Mapping\r\n *\r\n * This file maps routes to the permissions required to access them.\r\n * Each key represents a route pattern, and the value is the permission required.\r\n */\r\n\r\nimport { NavigationPermission } from \"./navigation-permissions\";\r\n\r\nexport const routePermissions: Record<string, NavigationPermission> = {\r\n  // Dashboard routes\r\n  \"/dashboard\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/company\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/branch\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/finance\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/operations\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/stock\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/float\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/pos\": { resource: \"dashboard\", action: \"view\" },\r\n\r\n  // User management routes\r\n  \"/users\": { resource: \"users\", action: \"view\" },\r\n  \"/users/create\": { resource: \"users\", action: \"create\" },\r\n  \"/users/[id]\": { resource: \"users\", action: \"view\" },\r\n  \"/users/[id]/edit\": { resource: \"users\", action: \"update\" },\r\n\r\n  // Role management routes\r\n  \"/roles\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/create\": { resource: \"roles\", action: \"create\" },\r\n  \"/roles/[id]\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/[id]/edit\": { resource: \"roles\", action: \"update\" },\r\n\r\n  // RBAC routes\r\n  \"/rbac\": { resource: \"permissions\", action: \"manage\" },\r\n\r\n  // Branch management routes\r\n  \"/branches\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/create\": { resource: \"branches\", action: \"create\" },\r\n  \"/branches/[id]\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/[id]/edit\": { resource: \"branches\", action: \"update\" },\r\n\r\n  // Location management routes\r\n  \"/locations\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/locations/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n  \"/location-guides\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/location-guides/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n\r\n  // Employee management routes\r\n  \"/employees\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/create\": { resource: \"employees\", action: \"create\" },\r\n  \"/employees/[id]\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/[id]/edit\": { resource: \"employees\", action: \"update\" },\r\n\r\n  // Product management routes\r\n  \"/products\": { resource: \"products\", action: \"view\" },\r\n  \"/products/create\": { resource: \"products\", action: \"create\" },\r\n  \"/products/[id]\": { resource: \"products\", action: \"view\" },\r\n  \"/products/[id]/edit\": { resource: \"products\", action: \"update\" },\r\n\r\n  // Category management routes\r\n  \"/categories\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/create\": { resource: \"categories\", action: \"create\" },\r\n  \"/categories/[id]\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/[id]/edit\": { resource: \"categories\", action: \"update\" },\r\n\r\n  // Brand management routes\r\n  \"/brands\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/create\": { resource: \"brands\", action: \"create\" },\r\n  \"/brands/[id]\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/[id]/edit\": { resource: \"brands\", action: \"update\" },\r\n\r\n  // Inventory management routes\r\n  \"/inventory\": { resource: \"inventory\", action: \"view\" },\r\n  \"/inventory/transfers\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/bulk-transfer\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/reports\": { resource: \"inventory\", action: \"report\" },\r\n  \"/inventory/stock-cards\": { resource: \"inventory\", action: \"view\" },\r\n\r\n  // Banking routes\r\n  \"/banking\": { resource: \"banking\", action: \"view\" },\r\n  \"/banking/summary\": { resource: \"banking\", action: \"view\" },\r\n\r\n  // MPESA routes\r\n  \"/mpesa\": { resource: \"mpesa\", action: \"view\" },\r\n  \"/mpesa/transactions\": { resource: \"mpesa\", action: \"view\" },\r\n\r\n  // Float management routes\r\n  \"/float\": { resource: \"float\", action: \"view\" },\r\n  \"/float/movements\": { resource: \"float\", action: \"view\" },\r\n  \"/float/reconciliations\": { resource: \"float\", action: \"view\" },\r\n  \"/float/topups\": { resource: \"float\", action: \"view\" },\r\n\r\n  // DSA routes\r\n  \"/dsa\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/agents\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/assignments\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/reconciliations\": { resource: \"dsa\", action: \"view\" },\r\n\r\n  // Phone repairs routes\r\n  \"/phone-repairs\": { resource: \"phone_repairs\", action: \"view\" },\r\n  \"/phone-repairs/[id]\": { resource: \"phone_repairs\", action: \"view\" },\r\n\r\n  // Expense management routes\r\n  \"/expenses\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/create\": { resource: \"expenses\", action: \"create\" },\r\n  \"/expenses/[id]\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/[id]/edit\": { resource: \"expenses\", action: \"update\" },\r\n  \"/expense-analytics\": { resource: \"expenses\", action: \"view\" },\r\n\r\n  // Credit Note routes\r\n  \"/credit-notes\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/new\": { resource: \"invoices\", action: \"create\" },\r\n  \"/credit-notes/[id]\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/[id]/edit\": { resource: \"invoices\", action: \"update\" },\r\n\r\n  // Report routes\r\n  \"/reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-summary\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-item\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-category\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-employee\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-payment-type\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-banking\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/cash-status\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/running-balances\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-transactions\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/phone-repairs\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/expense-reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/dsa-sales\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/receipts\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/shifts\": { resource: \"reports\", action: \"view\" },\r\n\r\n  // Settings routes\r\n  \"/settings\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/system\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/company\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/payment-methods\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/health\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // Profile route\r\n  \"/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // POS routes\r\n  \"/pos\": { resource: \"pos\", action: \"view\" },\r\n  \"/pos/sessions\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]/edit\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/close\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/shift-closing\": {\r\n    resource: \"pos_sessions\",\r\n    action: \"view\",\r\n  },\r\n\r\n  // Sales routes\r\n  \"/sales\": { resource: \"sales\", action: \"view\" },\r\n  \"/sales/[id]\": { resource: \"sales\", action: \"view\" },\r\n\r\n  // Customer routes\r\n  \"/customers\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/create\": { resource: \"customers\", action: \"create\" },\r\n  \"/customers/[id]\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/[id]/edit\": { resource: \"customers\", action: \"update\" },\r\n\r\n  // Procurement routes\r\n  \"/procurement\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests/new\": { resource: \"procurement\", action: \"create\" },\r\n  \"/procurement/receipts\": { resource: \"procurement\", action: \"view\" },\r\n\r\n  // Supplier routes\r\n  \"/suppliers\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/create\": { resource: \"suppliers\", action: \"create\" },\r\n  \"/suppliers/[id]\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/[id]/edit\": { resource: \"suppliers\", action: \"update\" },\r\n\r\n  // Tenant routes (Super Admin only)\r\n  \"/tenants\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/create\": { resource: \"tenants\", action: \"create\" },\r\n  \"/tenants/[id]\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/[id]/edit\": { resource: \"tenants\", action: \"update\" },\r\n\r\n  // Super Admin routes\r\n  \"/super-admin\": { resource: \"tenants\", action: \"view\" },\r\n\r\n  // Debug routes\r\n  \"/permission-debug\": { resource: \"settings\", action: \"manage\" },\r\n};\r\n\r\n/**\r\n * Get the permission required for a route\r\n * @param route The route to check\r\n * @returns The permission required for the route, or null if no permission is required\r\n */\r\nexport function getRoutePermission(route: string): NavigationPermission | null {\r\n  // First try exact match\r\n  if (routePermissions[route]) {\r\n    return routePermissions[route];\r\n  }\r\n\r\n  // Then try dynamic routes\r\n  for (const [pattern, permission] of Object.entries(routePermissions)) {\r\n    if (pattern.includes(\"[\") && matchDynamicRoute(route, pattern)) {\r\n      return permission;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Check if a route matches a dynamic route pattern\r\n * @param route The route to check\r\n * @param pattern The pattern to match against\r\n * @returns Whether the route matches the pattern\r\n */\r\nfunction matchDynamicRoute(route: string, pattern: string): boolean {\r\n  const routeParts = route.split(\"/\");\r\n  const patternParts = pattern.split(\"/\");\r\n\r\n  if (routeParts.length !== patternParts.length) {\r\n    return false;\r\n  }\r\n\r\n  for (let i = 0; i < patternParts.length; i++) {\r\n    if (patternParts[i].startsWith(\"[\") && patternParts[i].endsWith(\"]\")) {\r\n      // This is a dynamic part, so it matches anything\r\n      continue;\r\n    }\r\n\r\n    if (patternParts[i] !== routeParts[i]) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAIM,MAAM,mBAAyD;IACpE,mBAAmB;IACnB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC7D,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,kBAAkB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,cAAc;IACd,SAAS;QAAE,UAAU;QAAe,QAAQ;IAAS;IAErD,2BAA2B;IAC3B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAClE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,2BAA2B;QAAE,UAAU;QAAa,QAAQ;IAAS;IACrE,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,8BAA8B;QAAE,UAAU;QAAa,QAAQ;IAAS;IAExE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,eAAe;QAAE,UAAU;QAAc,QAAQ;IAAO;IACxD,sBAAsB;QAAE,UAAU;QAAc,QAAQ;IAAS;IACjE,oBAAoB;QAAE,UAAU;QAAc,QAAQ;IAAO;IAC7D,yBAAyB;QAAE,UAAU;QAAc,QAAQ;IAAS;IAEpE,0BAA0B;IAC1B,WAAW;QAAE,UAAU;QAAU,QAAQ;IAAO;IAChD,kBAAkB;QAAE,UAAU;QAAU,QAAQ;IAAS;IACzD,gBAAgB;QAAE,UAAU;QAAU,QAAQ;IAAO;IACrD,qBAAqB;QAAE,UAAU;QAAU,QAAQ;IAAS;IAE5D,8BAA8B;IAC9B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAW;IACpE,4BAA4B;QAAE,UAAU;QAAa,QAAQ;IAAW;IACxE,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAChE,0BAA0B;QAAE,UAAU;QAAa,QAAQ;IAAO;IAElE,iBAAiB;IACjB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,oBAAoB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE1D,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,uBAAuB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAE3D,0BAA0B;IAC1B,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAO;IACxD,0BAA0B;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9D,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAErD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,eAAe;QAAE,UAAU;QAAO,QAAQ;IAAO;IACjD,oBAAoB;QAAE,UAAU;QAAO,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAO,QAAQ;IAAO;IAE1D,uBAAuB;IACvB,kBAAkB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAC9D,uBAAuB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAEnE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAChE,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAE7D,qBAAqB;IACrB,iBAAiB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACxD,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC7D,2BAA2B;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEpE,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,kCAAkC;QAAE,UAAU;QAAW,QAAQ;IAAO;IACxE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,wBAAwB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC9D,6BAA6B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACnE,+BAA+B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACrE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,4BAA4B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClE,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC5D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC3D,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEzD,kBAAkB;IAClB,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,6BAA6B;QAAE,UAAU;QAAY,QAAQ;IAAS;IACtE,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC3D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE3D,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAElD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,iBAAiB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IAC5D,sBAAsB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IACjE,2BAA2B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACxE,4BAA4B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACzE,oCAAoC;QAClC,UAAU;QACV,QAAQ;IACV;IAEA,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IAEnD,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAC1D,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IACnE,6BAA6B;QAAE,UAAU;QAAe,QAAQ;IAAS;IACzE,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAEnE,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,mCAAmC;IACnC,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAC3D,iBAAiB;QAAE,UAAU;QAAW,QAAQ;IAAO;IACvD,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAE9D,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEtD,eAAe;IACf,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;AAChE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,wBAAwB;IACxB,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,OAAO,gBAAgB,CAAC,MAAM;IAChC;IAEA,0BAA0B;IAC1B,KAAK,MAAM,CAAC,SAAS,WAAW,IAAI,OAAO,OAAO,CAAC,kBAAmB;QACpE,IAAI,QAAQ,QAAQ,CAAC,QAAQ,kBAAkB,OAAO,UAAU;YAC9D,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,KAAa,EAAE,OAAe;IACvD,MAAM,aAAa,MAAM,KAAK,CAAC;IAC/B,MAAM,eAAe,QAAQ,KAAK,CAAC;IAEnC,IAAI,WAAW,MAAM,KAAK,aAAa,MAAM,EAAE;QAC7C,OAAO;IACT;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,IAAI,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM;YAEpE;QACF;QAEA,IAAI,YAAY,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/role-guard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect, useState } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\nimport { getRoutePermission } from \"@/lib/route-permissions\";\r\nimport { hasRouteAccess, getDashboardByRole } from \"@/lib/rbac-config\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\nfunction mapActionToGrantAction(action: string): string {\r\n  switch (action) {\r\n    case \"view\":\r\n      return \"read:any\";\r\n    case \"create\":\r\n      return \"create:any\";\r\n    case \"update\":\r\n      return \"update:any\";\r\n    case \"delete\":\r\n      return \"delete:any\";\r\n    case \"manage\":\r\n      return \"update:any\"; // Manage maps to update:any\r\n    case \"transfer\":\r\n      return \"update:any\"; // Transfer maps to update:any\r\n    case \"report\":\r\n      return \"read:any\"; // Report maps to read:any\r\n    default:\r\n      return `${action}:any`;\r\n  }\r\n}\r\n\r\ninterface RoleGuardProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function RoleGuard({ children }: RoleGuardProps) {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const { hasPermission, isLoading: isPermissionsLoading } = usePermissions();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Track if this is the initial mount\r\n  const [isInitialMount, setIsInitialMount] = useState(true);\r\n\r\n  // Effect to mark initial mount as complete\r\n  useEffect(() => {\r\n    setIsInitialMount(false);\r\n  }, []);\r\n\r\n  // Effect to check access rights\r\n  useEffect(() => {\r\n    // Skip check if on auth pages\r\n    if (\r\n      pathname.startsWith(\"/login\") ||\r\n      pathname.startsWith(\"/forgot-password\") ||\r\n      pathname.startsWith(\"/reset-password\")\r\n    ) {\r\n      setLoading(\"role\", false);\r\n      return;\r\n    }\r\n\r\n    // Only show loading indicator on initial mount or when user data is loading\r\n    // and we haven't shown the loading screen before in this session\r\n    if (isInitialMount && isUserLoading && !hasShownLoading.role) {\r\n      setLoading(\"role\", true);\r\n      return;\r\n    } else {\r\n      setLoading(\"role\", false);\r\n    }\r\n\r\n    // If user data and permissions are loaded, check access rights\r\n    if (!isUserLoading && !isPermissionsLoading && user) {\r\n      // Safely access role_name with type checking\r\n      const roleName =\r\n        user && typeof user === \"object\" && \"role_name\" in user\r\n          ? user.role_name\r\n          : \"\";\r\n\r\n      // First try permission-based access control\r\n      const routePermission = getRoutePermission(pathname);\r\n\r\n      let hasAccess = true;\r\n      let accessMethod = \"default\";\r\n\r\n      // Use RBAC-based access control\r\n      hasAccess = hasRouteAccess(pathname, roleName);\r\n      accessMethod = \"rbac\";\r\n\r\n      // Add detailed debug logging\r\n      console.log(`[RoleGuard] Route: ${pathname}`);\r\n      console.log(`[RoleGuard] User role: ${roleName}`);\r\n      console.log(`[RoleGuard] Using RBAC-based access control`);\r\n      console.log(\r\n        `[RoleGuard] Access ${hasAccess ? \"GRANTED\" : \"DENIED\"} by RBAC check`\r\n      );\r\n\r\n      // If we have a permission mapping, check it too (for debugging purposes only)\r\n      if (routePermission) {\r\n        // Map the action to the format used in grants (e.g., \"view\" -> \"read:any\")\r\n        const grantAction = mapActionToGrantAction(routePermission.action);\r\n\r\n        // Check if user has the required permission (but don't use the result)\r\n        const permissionAccess = hasPermission(\r\n          routePermission.resource,\r\n          grantAction,\r\n          routePermission.scope\r\n        );\r\n\r\n        // Log the permission check result for debugging\r\n        console.log(\r\n          `[RoleGuard] Permission check (not used): ${\r\n            routePermission.resource\r\n          }:${routePermission.action}:${routePermission.scope || \"any\"}`\r\n        );\r\n        console.log(`[RoleGuard] Mapped to grant action: ${grantAction}`);\r\n        console.log(\r\n          `[RoleGuard] Permission would be ${\r\n            permissionAccess ? \"GRANTED\" : \"DENIED\"\r\n          } (for future reference)`\r\n        );\r\n      }\r\n\r\n      if (!hasAccess) {\r\n        console.log(\r\n          `[RoleGuard] Access denied for ${pathname}, redirecting to dashboard (method: ${accessMethod})`\r\n        );\r\n\r\n        // Redirect to the appropriate dashboard based on role using RBAC configuration\r\n        const dashboardRoute = getDashboardByRole(roleName);\r\n        console.log(\r\n          `[RoleGuard] Redirecting to ${dashboardRoute} for role ${roleName}`\r\n        );\r\n        router.replace(dashboardRoute);\r\n      }\r\n\r\n      // User data is loaded, we can stop checking and mark as shown\r\n      setLoading(\"role\", false);\r\n      markLoadingShown(\"role\");\r\n    } else if (!isUserLoading && !isPermissionsLoading) {\r\n      // No user data and not loading, stop checking\r\n      setLoading(\"role\", false);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    user,\r\n    isUserLoading,\r\n    isPermissionsLoading,\r\n    pathname,\r\n    router,\r\n    isInitialMount,\r\n    hasShownLoading.role,\r\n    hasPermission,\r\n  ]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in checking state\r\n  useEffect(() => {\r\n    let timeoutId: NodeJS.Timeout | null = null;\r\n\r\n    if (isLoading.role) {\r\n      timeoutId = setTimeout(() => {\r\n        setLoading(\"role\", false);\r\n        markLoadingShown(\"role\");\r\n      }, 1500); // 1.5 second timeout for better UX\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.role]);\r\n\r\n  // Show loading state only when actively checking role access\r\n  if (isLoading.role) {\r\n    return <LoadingScreen message=\"Checking access...\" />;\r\n  }\r\n\r\n  // Don't block rendering, the useEffect will handle redirects\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAaA;;;;CAIC,GACD,SAAS,uBAAuB,MAAc;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,cAAc,4BAA4B;QACnD,KAAK;YACH,OAAO,cAAc,8BAA8B;QACrD,KAAK;YACH,OAAO,YAAY,0BAA0B;QAC/C;YACE,OAAO,GAAG,OAAO,IAAI,CAAC;IAC1B;AACF;AAMO,SAAS,UAAU,EAAE,QAAQ,EAAkB;;IACpD,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EAAE,aAAa,EAAE,WAAW,oBAAoB,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;IAEX,qCAAqC;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,kBAAkB;QACpB;8BAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,8BAA8B;YAC9B,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,uBACpB,SAAS,UAAU,CAAC,oBACpB;gBACA,WAAW,QAAQ;gBACnB;YACF;YAEA,4EAA4E;YAC5E,iEAAiE;YACjE,IAAI,kBAAkB,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;gBAC5D,WAAW,QAAQ;gBACnB;YACF,OAAO;gBACL,WAAW,QAAQ;YACrB;YAEA,+DAA+D;YAC/D,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,MAAM;gBACnD,6CAA6C;gBAC7C,MAAM,WACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;gBAEN,4CAA4C;gBAC5C,MAAM,kBAAkB,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAE3C,IAAI,YAAY;gBAChB,IAAI,eAAe;gBAEnB,gCAAgC;gBAChC,YAAY,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBACrC,eAAe;gBAEf,6BAA6B;gBAC7B,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;gBAC5C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU;gBAChD,QAAQ,GAAG,CAAC,CAAC,2CAA2C,CAAC;gBACzD,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,YAAY,YAAY,SAAS,cAAc,CAAC;gBAGxE,8EAA8E;gBAC9E,IAAI,iBAAiB;oBACnB,2EAA2E;oBAC3E,MAAM,cAAc,uBAAuB,gBAAgB,MAAM;oBAEjE,uEAAuE;oBACvE,MAAM,mBAAmB,cACvB,gBAAgB,QAAQ,EACxB,aACA,gBAAgB,KAAK;oBAGvB,gDAAgD;oBAChD,QAAQ,GAAG,CACT,CAAC,yCAAyC,EACxC,gBAAgB,QAAQ,CACzB,CAAC,EAAE,gBAAgB,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,IAAI,OAAO;oBAEhE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,aAAa;oBAChE,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAC/B,mBAAmB,YAAY,SAChC,uBAAuB,CAAC;gBAE7B;gBAEA,IAAI,CAAC,WAAW;oBACd,QAAQ,GAAG,CACT,CAAC,8BAA8B,EAAE,SAAS,oCAAoC,EAAE,aAAa,CAAC,CAAC;oBAGjG,+EAA+E;oBAC/E,MAAM,iBAAiB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAAE;oBAC1C,QAAQ,GAAG,CACT,CAAC,2BAA2B,EAAE,eAAe,UAAU,EAAE,UAAU;oBAErE,OAAO,OAAO,CAAC;gBACjB;gBAEA,8DAA8D;gBAC9D,WAAW,QAAQ;gBACnB,iBAAiB;YACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;gBAClD,8CAA8C;gBAC9C,WAAW,QAAQ;YACrB;QACA,uDAAuD;QACzD;8BAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,IAAI;QACpB;KACD;IAED,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,YAAmC;YAEvC,IAAI,UAAU,IAAI,EAAE;gBAClB,YAAY;2CAAW;wBACrB,WAAW,QAAQ;wBACnB,iBAAiB;oBACnB;0CAAG,OAAO,mCAAmC;YAC/C;YAEA;uCAAO;oBACL,IAAI,WAAW,aAAa;gBAC9B;;QACA,uDAAuD;QACzD;8BAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6DAA6D;IAC7D,IAAI,UAAU,IAAI,EAAE;QAClB,qBAAO,6LAAC,yJAAA,CAAA,gBAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,6DAA6D;IAC7D,qBAAO;kBAAG;;AACZ;GAjJgB;;QACmC,kJAAA,CAAA,iBAAc;QACJ,yJAAA,CAAA,iBAAc;QAC1D,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QAE1B,yJAAA,CAAA,aAAU;;;KANE", "debugId": null}}, {"offset": {"line": 874, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;YACvE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 1206, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { VariantProps, cva } from \"class-variance-authority\";\r\nimport { PanelLeftIcon } from \"lucide-react\";\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = \"16rem\";\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\";\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\";\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\";\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\";\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\";\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  // Use useMemo to prevent recreating the button on every render\r\n  const button = React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, variant, className, props]);\r\n\r\n  // Use useMemo for tooltipContent to prevent recalculation on every render\r\n  const tooltipContent = React.useMemo(() => {\r\n    if (!tooltip) return null;\r\n    return typeof tooltip === \"string\" ? { children: tooltip } : tooltip;\r\n  }, [tooltip]);\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  // Use useMemo for the hidden prop to prevent recalculation on every render\r\n  const isHidden = React.useMemo(() => {\r\n    return state !== \"collapsed\" || isMobile;\r\n  }, [state, isMobile]);\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={isHidden}\r\n        {...tooltipContent}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, className, showOnHover, props]);\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [className, props]);\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean;\r\n  size?: \"sm\" | \"md\";\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, className, props]);\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;gDAC9B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;QACpG;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sDAAE;YACtC,OAAO,WAAW;8DAAc,CAAC,OAAS,CAAC;+DAAQ;8DAAQ,CAAC,OAAS,CAAC;;QACxE;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;qCAAE;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;iDAC/B,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,sIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IAhGS;;QAaU,gIAAA,CAAA,cAAW;;;KAbrB;AAkGT,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,oIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,oIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,oIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6LAAC,oIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IAxBS;;QAKmB;;;MALnB;AA0BT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,6LAAC,wIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,+rBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;;IAChD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,+DAA+D;IAC/D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;6CAAE,kBAC3B,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,aAAW;gBACX,eAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;oBAAE;oBAAS;gBAAK,IAAI;gBAC3D,GAAG,KAAK;;;;;;4CAEV;QAAC;QAAM;QAAM;QAAU;QAAS;QAAW;KAAM;IAEpD,0EAA0E;IAC1E,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qDAAE;YACnC,IAAI,CAAC,SAAS,OAAO;YACrB,OAAO,OAAO,YAAY,WAAW;gBAAE,UAAU;YAAQ,IAAI;QAC/D;oDAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,2EAA2E;IAC3E,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;+CAAE;YAC7B,OAAO,UAAU,eAAe;QAClC;8CAAG;QAAC;QAAO;KAAS;IAEpB,qBACE,6LAAC,sIAAA,CAAA,UAAO;;0BACN,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,sIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ;gBACP,GAAG,cAAc;;;;;;;;;;;;AAI1B;IAtDS;;QAcqB;;;OAdrB;AAwDT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;gBAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;gBAED,GAAG,KAAK;;;;;;oCAEV;QAAC;QAAM;QAAW;QAAa;KAAM;AAC1C;IA/BS;OAAA;AAiCT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;;IAC5B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;oCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;gBAED,GAAG,KAAK;;;;;;mCAEV;QAAC;QAAW;KAAM;AACvB;IArBS;OAAA;AAuBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;8CAAE;YAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;QAClD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IApCS;OAAA;AAsCT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;wCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,aAAW;gBACX,eAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;gBAED,GAAG,KAAK;;;;;;uCAEV;QAAC;QAAM;QAAM;QAAU;QAAW;KAAM;AAC7C;IA/BS;OAAA", "debugId": null}}, {"offset": {"line": 2109, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/navigation-link.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { ReactNode, MouseEvent, useMemo } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface NavigationLinkProps {\r\n  href: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;\r\n  replace?: boolean;\r\n  prefetch?: boolean;\r\n  scroll?: boolean;\r\n  exact?: boolean;\r\n}\r\n\r\n/**\r\n * NavigationLink component that ensures client-side navigation\r\n * This component wraps Next.js Link component and provides a consistent way to navigate\r\n */\r\nexport function NavigationLink({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(className)}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n      aria-current={isActive ? \"page\" : undefined}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n\r\n/**\r\n * NavigationButton component that looks like a button but navigates like a link\r\n */\r\nexport function NavigationButton({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(\r\n        \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAsBO,SAAS,eAAe,EAC7B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;QACzD;2CAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;QACb,gBAAc,WAAW,SAAS;kBAEjC;;;;;;AAGP;GA5CgB;;QAUC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAXd;AAiDT,SAAS,iBAAiB,EAC/B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;QACzD;6CAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0RACA;QAEF,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;kBAEZ;;;;;;AAGP;IA9CgB;;QAUC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;MAXd", "debugId": null}}, {"offset": {"line": 2230, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6LAAC,0KAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6LAAC,0KAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6LAAC,0KAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS", "debugId": null}}, {"offset": {"line": 2287, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-main.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\nimport { ChevronRight, CircleIcon, type LucideIcon } from \"lucide-react\";\r\n\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: {\r\n    title: string;\r\n    url: string;\r\n    icon?: LucideIcon;\r\n    isActive?: boolean;\r\n    items?: {\r\n      title: string;\r\n      url: string;\r\n    }[];\r\n  }[];\r\n}) {\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          // Special case for Dashboard - make it a direct link\r\n          if (item.title === \"Dashboard\") {\r\n            return (\r\n              <SidebarMenuItem key={item.title}>\r\n                <SidebarMenuButton\r\n                  asChild\r\n                  tooltip={item.title}\r\n                  isActive={item.isActive}\r\n                  className=\"nav-main-item\"\r\n                >\r\n                  <NavigationLink href={item.url}>\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                  </NavigationLink>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          }\r\n\r\n          // For all other items, use the collapsible dropdown\r\n          return (\r\n            <Collapsible\r\n              key={item.title}\r\n              asChild\r\n              defaultOpen={item.isActive}\r\n              className=\"group/collapsible mb-2\"\r\n            >\r\n              <SidebarMenuItem>\r\n                <CollapsibleTrigger asChild>\r\n                  <SidebarMenuButton\r\n                    tooltip={item.title}\r\n                    isActive={item.isActive}\r\n                    className=\"nav-main-item\"\r\n                  >\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                    <ChevronRight className=\"ml-auto h-2 w-2 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 nav-main-item-chevron\" />\r\n                  </SidebarMenuButton>\r\n                </CollapsibleTrigger>\r\n                <CollapsibleContent>\r\n                  <SidebarMenuSub className=\"nav-main-sub mt-2\">\r\n                    {item.items?.map((subItem) => (\r\n                      <SidebarMenuSubItem key={subItem.title}>\r\n                        <SidebarMenuSubButton asChild>\r\n                          <NavigationLink href={subItem.url} exact={true}>\r\n                            {/* No circle icon for child items */}\r\n                            <span className=\"text-xs\">{subItem.title}</span>\r\n                          </NavigationLink>\r\n                        </SidebarMenuSubButton>\r\n                      </SidebarMenuSubItem>\r\n                    ))}\r\n                  </SidebarMenuSub>\r\n                </CollapsibleContent>\r\n              </SidebarMenuItem>\r\n            </Collapsible>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAKA;AAVA;;;;;;AAoBO,SAAS,QAAQ,EACtB,KAAK,EAYN;IACC,qBACE,6LAAC,sIAAA,CAAA,eAAY;kBACX,cAAA,6LAAC,sIAAA,CAAA,cAAW;sBACT,MAAM,GAAG,CAAC,CAAC;gBACV,qDAAqD;gBACrD,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kCACd,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,SAAS,KAAK,KAAK;4BACnB,UAAU,KAAK,QAAQ;4BACvB,WAAU;sCAEV,cAAA,6LAAC,iJAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,GAAG;;kDAC5B,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CAClC,KAAK,QAAQ,kBACZ,6LAAC,6MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAG1B,6LAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAfG,KAAK,KAAK;;;;;gBAqBpC;gBAEA,oDAAoD;gBACpD,qBACE,6LAAC,0IAAA,CAAA,cAAW;oBAEV,OAAO;oBACP,aAAa,KAAK,QAAQ;oBAC1B,WAAU;8BAEV,cAAA,6LAAC,sIAAA,CAAA,kBAAe;;0CACd,6LAAC,0IAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;oCAChB,SAAS,KAAK,KAAK;oCACnB,UAAU,KAAK,QAAQ;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDAClC,KAAK,QAAQ,kBACZ,6LAAC,6MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAG1B,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;sDAEb,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG5B,6LAAC,0IAAA,CAAA,qBAAkB;0CACjB,cAAA,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,6LAAC,sIAAA,CAAA,qBAAkB;sDACjB,cAAA,6LAAC,sIAAA,CAAA,uBAAoB;gDAAC,OAAO;0DAC3B,cAAA,6LAAC,iJAAA,CAAA,iBAAc;oDAAC,MAAM,QAAQ,GAAG;oDAAE,OAAO;8DAExC,cAAA,6LAAC;wDAAK,WAAU;kEAAW,QAAQ,KAAK;;;;;;;;;;;;;;;;2CAJrB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;mBA3BzC,KAAK,KAAK;;;;;YAyCrB;;;;;;;;;;;AAIR;KA5FgB", "debugId": null}}, {"offset": {"line": 2505, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 2567, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 2863, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-user.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Chev<PERSON>UpDown, LogOut } from \"lucide-react\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavUser({\r\n  user,\r\n}: {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n  };\r\n}) {\r\n  const { isMobile } = useSidebar();\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground nav-user\"\r\n            >\r\n              <Avatar className=\"h-6 w-6 rounded-lg nav-user-avatar\">\r\n                <AvatarImage src={user.avatar} alt={user.name} />\r\n                <AvatarFallback className=\"rounded-lg text-xs\">\r\n                  {user.name.charAt(0)}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left leading-tight nav-user-details\">\r\n                <span className=\"truncate text-xs font-medium\">\r\n                  {user.name}\r\n                </span>\r\n                <span className=\"truncate text-[10px]\">{user.email}</span>\r\n              </div>\r\n              <ChevronsUpDown className=\"ml-auto h-3 w-3 nav-user-chevron\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar} alt={user.name} />\r\n                  <AvatarFallback className=\"rounded-lg\">CN</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name}</span>\r\n                  <span className=\"truncate text-xs\">{user.email}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem>\r\n                <BadgeCheck className=\"mr-2 h-3.5 w-3.5\" />\r\n                <span className=\"text-xs\">Account</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem>\r\n              <LogOut className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"text-xs\">Log out</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AASA;;;AAdA;;;;;AAqBO,SAAS,QAAQ,EACtB,IAAI,EAOL;;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IAE9B,qBACE,6LAAC,sIAAA,CAAA,cAAW;kBACV,cAAA,6LAAC,sIAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC,+IAAA,CAAA,eAAY;;kCACX,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,6LAAC,qIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,6LAAC,qIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;8CAGtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAAwB,KAAK,KAAK;;;;;;;;;;;;8CAEpD,6LAAC,iOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,6LAAC,+IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,6LAAC,+IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,6LAAC;oDAAK,WAAU;8DAAoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIpD,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC,+IAAA,CAAA,oBAAiB;0CAChB,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;;sDACf,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC,+IAAA,CAAA,mBAAgB;;kDACf,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GAtEgB;;QASO,sIAAA,CAAA,aAAU;;;KATjB", "debugId": null}}, {"offset": {"line": 3135, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport \"@/components/ui/sidebar-collapsed.css\";\r\nimport \"@/components/ui/sidebar-compact.css\";\r\nimport {\r\n  Banknote,\r\n  BarChart3 as BarChart3Icon,\r\n  Building as BuildingIcon,\r\n  FileText,\r\n  GalleryVerticalEnd,\r\n  Home as HomeIcon,\r\n  Landmark,\r\n  Package,\r\n  Smartphone,\r\n  UserCheck,\r\n} from \"lucide-react\";\r\n\r\nimport { NavMain } from \"@/components/nav-main\";\r\nimport { NavUser } from \"@/components/nav-user\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n// Import usePermissions for future use\r\n// import { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\n// import { getRoutePermission } from \"@/lib/route-permissions\";\r\n// import { navigationPermissions } from \"@/lib/navigation-permissions\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  hasRouteAccess,\r\n  isNavi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n} from \"@/lib/rbac-config\";\r\n\r\ntype NavItem = {\r\n  title: string;\r\n  url: string;\r\n  icon?: any;\r\n  isActive?: boolean;\r\n  items?: { title: string; url: string }[];\r\n};\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * This function is commented out for MVP, will be used in the future\r\n * when the permission system is fully implemented\r\n *\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\n// function mapActionToGrantAction(action: string): string {\r\n//   switch (action) {\r\n//     case \"view\":\r\n//       return \"read:any\";\r\n//     case \"create\":\r\n//       return \"create:any\";\r\n//     case \"update\":\r\n//       return \"update:any\";\r\n//     case \"delete\":\r\n//       return \"delete:any\";\r\n//     case \"manage\":\r\n//       return \"update:any\"; // Manage maps to update:any\r\n//     case \"transfer\":\r\n//       return \"update:any\"; // Transfer maps to update:any\r\n//     case \"report\":\r\n//       return \"read:any\"; // Report maps to read:any\r\n//     default:\r\n//       return `${action}:any`;\r\n//   }\r\n// }\r\n\r\nexport function AppSidebar() {\r\n  const { data: user } = useCurrentUser();\r\n  const pathname = usePathname();\r\n  // Permissions will be used in the future\r\n  // const { hasPermission, permissions } = usePermissions();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  // Define all navigation items\r\n  const getAllNavigationItems = (): NavItem[] => [\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/dashboard\",\r\n      icon: HomeIcon,\r\n      isActive: pathname === \"/dashboard\" || pathname === \"/\",\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      url: \"/reports/sales-summary\",\r\n      icon: BarChart3Icon,\r\n      isActive:\r\n        pathname.startsWith(\"/reports\") ||\r\n        pathname.startsWith(\"/expense-analytics\"),\r\n      items: [\r\n        {\r\n          title: \"Sales Summary\",\r\n          url: \"/reports/sales-summary\",\r\n        },\r\n        {\r\n          title: \"Sales by Item\",\r\n          url: \"/reports/sales-by-item\",\r\n        },\r\n        {\r\n          title: \"Sales by Category\",\r\n          url: \"/reports/sales-by-category\",\r\n        },\r\n        {\r\n          title: \"Current Stock Levels\",\r\n          url: \"/reports/current-stock-levels\",\r\n        },\r\n        {\r\n          title: \"Stock History\",\r\n          url: \"/reports/stock-history\",\r\n        },\r\n        {\r\n          title: \"Banking Transactions\",\r\n          url: \"/reports/banking-transactions\",\r\n        },\r\n        {\r\n          title: \"Tax Report\",\r\n          url: \"/reports/tax-report\",\r\n        },\r\n        {\r\n          title: \"Banking Summary\",\r\n          url: \"/banking?tab=summary\",\r\n        },\r\n        {\r\n          title: \"M-Pesa Transactions\",\r\n          url: \"/reports/mpesa-transactions\",\r\n        },\r\n        {\r\n          title: \"Running Balances\",\r\n          url: \"/reports/running-balances\",\r\n        },\r\n        {\r\n          title: \"Cash Status\",\r\n          url: \"/reports/cash-status\",\r\n        },\r\n        {\r\n          title: \"Cash Float\",\r\n          url: \"/reports/cash-float\",\r\n        },\r\n        {\r\n          title: \"DSA Sales\",\r\n          url: \"/reports/dsa-sales\",\r\n        },\r\n        {\r\n          title: \"Receipts\",\r\n          url: \"/reports/receipts\",\r\n        },\r\n        {\r\n          title: \"Shifts\",\r\n          url: \"/reports/shifts\",\r\n        },\r\n        {\r\n          title: \"Phone Repairs\",\r\n          url: \"/reports/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"Stock Valuation\",\r\n          url: \"/inventory/valuation\",\r\n        },\r\n        {\r\n          title: \"Stock Aging\",\r\n          url: \"/reports/stock-aging\",\r\n        },\r\n        {\r\n          title: \"Stock Movement\",\r\n          url: \"/reports/stock-movement\",\r\n        },\r\n        {\r\n          title: \"Expense Analytics\",\r\n          url: \"/expense-analytics\",\r\n        },\r\n        {\r\n          title: \"Customers\",\r\n          url: \"/customers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Administration\",\r\n      url: \"/users\",\r\n      icon: BuildingIcon,\r\n      isActive:\r\n        pathname.startsWith(\"/users\") ||\r\n        pathname.startsWith(\"/roles\") ||\r\n        pathname.startsWith(\"/rbac\") ||\r\n        pathname.startsWith(\"/branches\") ||\r\n        pathname.startsWith(\"/locations\") ||\r\n        pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        // Tenants menu item removed\r\n        {\r\n          title: \"Users\",\r\n          url: \"/users\",\r\n        },\r\n        {\r\n          title: \"Roles\",\r\n          url: \"/roles\",\r\n        },\r\n        {\r\n          title: \"RBAC\",\r\n          url: \"/rbac\",\r\n        },\r\n        {\r\n          title: \"Branches\",\r\n          url: \"/branches\",\r\n        },\r\n        {\r\n          title: \"Locations\",\r\n          url: \"/locations\",\r\n        },\r\n        {\r\n          title: \"Employees\",\r\n          url: \"/employees\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Stock & Inventory\",\r\n      url: \"/products\",\r\n      icon: Package,\r\n      isActive:\r\n        pathname.startsWith(\"/products\") ||\r\n        pathname.startsWith(\"/categories\") ||\r\n        pathname.startsWith(\"/brands\") ||\r\n        pathname.startsWith(\"/inventory\") ||\r\n        pathname.includes(\"/inventory/stock-cards\") ||\r\n        pathname.includes(\"/inventory/purchases\"),\r\n      items: [\r\n        {\r\n          title: \"Products\",\r\n          url: \"/products\",\r\n        },\r\n        {\r\n          title: \"Categories\",\r\n          url: \"/categories\",\r\n        },\r\n        {\r\n          title: \"Brands\",\r\n          url: \"/brands\",\r\n        },\r\n        {\r\n          title: \"Stock levels\",\r\n          url: \"/inventory\",\r\n        },\r\n        // Commented out Stock Locations as requested\r\n        // {\r\n        //   title: \"Stock Locations\",\r\n        //   url: \"/inventory/locations\",\r\n        // },\r\n        {\r\n          title: \"Purchases\",\r\n          url: \"/inventory/purchases\",\r\n        },\r\n        {\r\n          title: \"Create Purchase\",\r\n          url: \"/inventory/purchases/new\",\r\n        },\r\n        {\r\n          title: \"Stock Requests\",\r\n          url: \"/inventory/stock-requests\",\r\n        },\r\n        {\r\n          title: \"Stock Transfers\",\r\n          url: \"/inventory/transfers\",\r\n        },\r\n        {\r\n          title: \"Make Stock Transfer\",\r\n          url: \"/inventory/bulk-transfer\",\r\n        },\r\n        {\r\n          title: \"Stock Transfer History\",\r\n          url: \"/inventory/bulk-transfers\",\r\n        },\r\n        // {\r\n        //   title: \"Stock Cards\",\r\n        //   url: \"/inventory/stock-cards\",\r\n        // },\r\n        {\r\n          title: \"Inventory Reports\",\r\n          url: \"/inventory/reports\",\r\n        },\r\n        {\r\n          title: \"Excel Import/Export\",\r\n          url: \"/inventory/excel\",\r\n        },\r\n        {\r\n          title: \"Price List\",\r\n          url: \"/inventory/price-list\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Float Management\",\r\n      url: \"/float\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/float\"),\r\n      items: [\r\n        {\r\n          title: \"Float Dashboard\",\r\n          url: \"/float\",\r\n        },\r\n        {\r\n          title: \"Float Movements\",\r\n          url: \"/float/movements\",\r\n        },\r\n        {\r\n          title: \"Float Reconciliations\",\r\n          url: \"/float/reconciliations\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Banking Management\",\r\n      url: \"/banking\",\r\n      icon: Landmark,\r\n      isActive: pathname.startsWith(\"/banking\"),\r\n      items: [\r\n        {\r\n          title: \"Banking Records\",\r\n          url: \"/banking\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Expenses Management\",\r\n      url: \"/expenses\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/expenses\"),\r\n      items: [\r\n        {\r\n          title: \"Expenses\",\r\n          url: \"/expenses\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Invoice Management\",\r\n      url: \"/invoices\",\r\n      icon: FileText,\r\n      isActive:\r\n        pathname.startsWith(\"/invoices\") ||\r\n        pathname.startsWith(\"/credit-notes\"),\r\n      items: [\r\n        {\r\n          title: \"All Invoices\",\r\n          url: \"/invoices\",\r\n        },\r\n        {\r\n          title: \"Credit Notes\",\r\n          url: \"/credit-notes\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"DSA Management\",\r\n      url: \"/dsa\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/dsa\"),\r\n      items: [\r\n        {\r\n          title: \"DSA Dashboard\",\r\n          url: \"/dsa\",\r\n        },\r\n        {\r\n          title: \"DSA Agents\",\r\n          url: \"/dsa/customers\",\r\n        },\r\n        {\r\n          title: \"Stock Assignments\",\r\n          url: \"/dsa/assignments\",\r\n        },\r\n        // Commented out DSA Reconciliations as requested\r\n        // {\r\n        //   title: \"DSA Reconciliations\",\r\n        //   url: \"/dsa/reconciliations\",\r\n        // },\r\n      ],\r\n    },\r\n    // {\r\n    //   title: \"Procurement\",\r\n    //   url: \"/procurement\",\r\n    //   icon: ShoppingCart,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/procurement\") ||\r\n    //     pathname.startsWith(\"/suppliers\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"Procurement Dashboard\",\r\n    //       url: \"/procurement\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Procurement Requests\",\r\n    //       url: \"/procurement/requests\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Create Request\",\r\n    //       url: \"/procurement/requests/new\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Procurement Receipts\",\r\n    //       url: \"/procurement/receipts\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Suppliers\",\r\n    //       url: \"/suppliers\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    {\r\n      title: \"Phone Repairs\",\r\n      url: \"/phone-repairs\",\r\n      icon: Smartphone,\r\n      isActive:\r\n        pathname === \"/phone-repairs\" || pathname.startsWith(\"/phone-repairs/\")\r\n          ? true\r\n          : false,\r\n      items: [\r\n        {\r\n          title: \"All Repairs\",\r\n          url: \"/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"New Repair\",\r\n          url: \"/phone-repairs/new\",\r\n        },\r\n      ],\r\n    },\r\n    // Settings section completely hidden for production - not implemented\r\n    // {\r\n    //   title: \"Settings\",\r\n    //   url: \"/settings\",\r\n    //   icon: Settings,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/settings\") || pathname.startsWith(\"/profile\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"System Settings\",\r\n    //       url: \"/settings/system\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Company Settings\",\r\n    //       url: \"/settings/company\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Payment Methods\",\r\n    //       url: \"/settings/payment-methods\",\r\n    //     },\r\n    //     {\r\n    //       title: \"System Health\",\r\n    //       url: \"/settings/health\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Profile\",\r\n    //       url: \"/profile\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    // Permissions Demo section removed\r\n  ];\r\n\r\n  // Filter navigation items based on RBAC configuration\r\n  const getFilteredNavigationItemsByRole = (): NavItem[] => {\r\n    const allItems = getAllNavigationItems();\r\n\r\n    console.log(\r\n      `[Navigation] ${userRoleName} role detected - using RBAC-based filtering`\r\n    );\r\n\r\n    // Filter navigation items based on RBAC configuration\r\n    const filteredItems = allItems.filter((item) => {\r\n      // Check if the entire navigation group should be hidden for this role\r\n      if (isNavigationItemHidden(item.title, userRoleName)) {\r\n        console.log(\r\n          `[Navigation] Hiding section \"${item.title}\" for role ${userRoleName}`\r\n        );\r\n        return false;\r\n      }\r\n\r\n      // Check if user has access to the main route of this navigation item\r\n      if (!hasRouteAccess(item.url, userRoleName)) {\r\n        console.log(\r\n          `[Navigation] No route access to \"${item.url}\" for role ${userRoleName}`\r\n        );\r\n        return false;\r\n      }\r\n\r\n      console.log(\r\n        `[Navigation] Allowing section \"${item.title}\" for role ${userRoleName}`\r\n      );\r\n      return true;\r\n    });\r\n\r\n    // Filter subitems based on RBAC configuration\r\n    const itemsWithFilteredSubitems = filteredItems.map((item) => {\r\n      // If the item has subitems, filter them based on RBAC\r\n      if (item.items && item.items.length > 0) {\r\n        console.log(\r\n          `[Navigation] Checking subitems for \"${item.title}\" section`\r\n        );\r\n\r\n        const filteredSubItems = item.items.filter((subItem) => {\r\n          // Check if user has route access for this subitem\r\n          if (!hasRouteAccess(subItem.url, userRoleName)) {\r\n            console.log(\r\n              `[Navigation] No route access to \"${subItem.url}\" for role ${userRoleName}`\r\n            );\r\n            return false;\r\n          }\r\n\r\n          // Special handling for RBAC items - only super_admin can see them\r\n          if (subItem.title === \"RBAC\" || subItem.url.includes(\"rbac\")) {\r\n            return userRoleName === ROLES.SUPER_ADMIN;\r\n          }\r\n\r\n          console.log(\r\n            `[Navigation] Allowing subitem \"${subItem.title}\" for role ${userRoleName}`\r\n          );\r\n          return true;\r\n        });\r\n\r\n        // Update the item's subitems with the filtered list\r\n        item.items = filteredSubItems;\r\n      }\r\n\r\n      return item;\r\n    });\r\n\r\n    // Only return items that have at least one subitem (if they had subitems to begin with)\r\n    const result = itemsWithFilteredSubitems.filter(\r\n      (item) => !item.items || item.items.length > 0\r\n    );\r\n\r\n    return result;\r\n  };\r\n\r\n  // Filter navigation items based on permissions (for future use)\r\n  // This function is commented out for MVP, will be implemented in the future\r\n  // when the permission system is fully implemented\r\n\r\n  // Sample data for the sidebar\r\n  const data = {\r\n    user: {\r\n      name: user?.name || \"User\",\r\n      email: user?.email || \"<EMAIL>\",\r\n      avatar: \"/placeholder-avatar.jpg\",\r\n    },\r\n    teams: [\r\n      {\r\n        name: teamData.name,\r\n        logo: GalleryVerticalEnd,\r\n        plan: teamData.plan,\r\n      },\r\n    ],\r\n    // For MVP, use role-based filtering\r\n    navMain: getFilteredNavigationItemsByRole(),\r\n    // For future: navMain: getFilteredNavigationItems(),\r\n  };\r\n\r\n  const { state, isMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\" && !isMobile;\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"h-full flex flex-col sidebar-compact\",\r\n        isCollapsed && \"sidebar-collapsed\"\r\n      )}\r\n    >\r\n      <div className=\"flex-1 overflow-y-auto py-2\">\r\n        <div className={cn(\"px-3\", isCollapsed && \"px-2\")}>\r\n          <NavMain items={data.navMain} />\r\n        </div>\r\n      </div>\r\n      <div className={cn(\"p-3 border-t\", isCollapsed && \"p-2\")}>\r\n        <NavUser user={data.user} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,0EAA0E;AAC1E,gEAAgE;AAChE,wEAAwE;AACxE;AACA;AACA;;;AA3BA;;;;;;;;;;;AAsEO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,yCAAyC;IACzC,2DAA2D;IAE3D,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,8BAA8B;IAC9B,MAAM,wBAAwB,IAAiB;YAC7C;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,sMAAA,CAAA,OAAQ;gBACd,UAAU,aAAa,gBAAgB,aAAa;YACtD;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,qNAAA,CAAA,YAAa;gBACnB,UACE,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAY;gBAClB,UACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL,4BAA4B;oBAC5B;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,2MAAA,CAAA,UAAO;gBACb,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,QAAQ,CAAC,6BAClB,SAAS,QAAQ,CAAC;gBACpB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,6CAA6C;oBAC7C,IAAI;oBACJ,8BAA8B;oBAC9B,iCAAiC;oBACjC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,IAAI;oBACJ,0BAA0B;oBAC1B,mCAAmC;oBACnC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,iNAAA,CAAA,WAAQ;gBACd,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,mNAAA,CAAA,YAAS;gBACf,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBAMD;YACH;YACA,IAAI;YACJ,0BAA0B;YAC1B,yBAAyB;YACzB,wBAAwB;YACxB,cAAc;YACd,6CAA6C;YAC7C,yCAAyC;YACzC,aAAa;YACb,QAAQ;YACR,wCAAwC;YACxC,6BAA6B;YAC7B,SAAS;YACT,QAAQ;YACR,uCAAuC;YACvC,sCAAsC;YACtC,SAAS;YACT,QAAQ;YACR,iCAAiC;YACjC,0CAA0C;YAC1C,SAAS;YACT,QAAQ;YACR,uCAAuC;YACvC,sCAAsC;YACtC,SAAS;YACT,QAAQ;YACR,4BAA4B;YAC5B,2BAA2B;YAC3B,SAAS;YACT,OAAO;YACP,KAAK;YACL;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,iNAAA,CAAA,aAAU;gBAChB,UACE,aAAa,oBAAoB,SAAS,UAAU,CAAC,qBACjD,OACA;gBACN,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;SAgCD;IAED,sDAAsD;IACtD,MAAM,mCAAmC;QACvC,MAAM,WAAW;QAEjB,QAAQ,GAAG,CACT,CAAC,aAAa,EAAE,aAAa,2CAA2C,CAAC;QAG3E,sDAAsD;QACtD,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC;YACrC,sEAAsE;YACtE,IAAI,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,KAAK,EAAE,eAAe;gBACpD,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,cAAc;gBAExE,OAAO;YACT;YAEA,qEAAqE;YACrE,IAAI,CAAC,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG,EAAE,eAAe;gBAC3C,QAAQ,GAAG,CACT,CAAC,iCAAiC,EAAE,KAAK,GAAG,CAAC,WAAW,EAAE,cAAc;gBAE1E,OAAO;YACT;YAEA,QAAQ,GAAG,CACT,CAAC,+BAA+B,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,cAAc;YAE1E,OAAO;QACT;QAEA,8CAA8C;QAC9C,MAAM,4BAA4B,cAAc,GAAG,CAAC,CAAC;YACnD,sDAAsD;YACtD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACvC,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;gBAG9D,MAAM,mBAAmB,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,kDAAkD;oBAClD,IAAI,CAAC,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG,EAAE,eAAe;wBAC9C,QAAQ,GAAG,CACT,CAAC,iCAAiC,EAAE,QAAQ,GAAG,CAAC,WAAW,EAAE,cAAc;wBAE7E,OAAO;oBACT;oBAEA,kEAAkE;oBAClE,IAAI,QAAQ,KAAK,KAAK,UAAU,QAAQ,GAAG,CAAC,QAAQ,CAAC,SAAS;wBAC5D,OAAO,iBAAiB,+HAAA,CAAA,QAAK,CAAC,WAAW;oBAC3C;oBAEA,QAAQ,GAAG,CACT,CAAC,+BAA+B,EAAE,QAAQ,KAAK,CAAC,WAAW,EAAE,cAAc;oBAE7E,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,KAAK,KAAK,GAAG;YACf;YAEA,OAAO;QACT;QAEA,wFAAwF;QACxF,MAAM,SAAS,0BAA0B,MAAM,CAC7C,CAAC,OAAS,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG;QAG/C,OAAO;IACT;IAEA,gEAAgE;IAChE,4EAA4E;IAC5E,kDAAkD;IAElD,8BAA8B;IAC9B,MAAM,OAAO;QACX,MAAM;YACJ,MAAM,MAAM,QAAQ;YACpB,OAAO,MAAM,SAAS;YACtB,QAAQ;QACV;QACA,OAAO;YACL;gBACE,MAAM,SAAS,IAAI;gBACnB,MAAM,yOAAA,CAAA,qBAAkB;gBACxB,MAAM,SAAS,IAAI;YACrB;SACD;QACD,oCAAoC;QACpC,SAAS;IAEX;IAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACrC,MAAM,cAAc,UAAU,eAAe,CAAC;IAE9C,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wCACA,eAAe;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,eAAe;8BACxC,cAAA,6LAAC,oIAAA,CAAA,UAAO;wBAAC,OAAO,KAAK,OAAO;;;;;;;;;;;;;;;;0BAGhC,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,eAAe;0BAChD,cAAA,6LAAC,oIAAA,CAAA,UAAO;oBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;AAIhC;GApiBgB;;QACS,kJAAA,CAAA,iBAAc;QACpB,qIAAA,CAAA,cAAW;QA8gBA,sIAAA,CAAA,aAAU;;;KAhhBxB", "debugId": null}}, {"offset": {"line": 3670, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/team-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// import { GalleryVerticalEnd } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n\r\nexport function TeamHeader() {\r\n  const { data: user } = useCurrentUser();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <div className=\"bg-white text-primary flex aspect-square size-6 items-center justify-center rounded-lg\">\r\n        {/* <GalleryVerticalEnd className=\"size-3\" /> */}\r\n        <Image\r\n          src={\"/images/simbatelecomlogo.png\"}\r\n          alt=\"Simba Telecom Logo\"\r\n          className=\"object-contain\"\r\n          width={16}\r\n          height={16}\r\n        />\r\n      </div>\r\n      <div className=\"grid flex-1 text-left leading-tight\">\r\n        <span className=\"truncate text-sm font-semibold text-primary-foreground\">\r\n          {teamData.name}\r\n        </span>\r\n        <span className=\"truncate text-xs text-primary-foreground/90 font-medium\">\r\n          {teamData.plan}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,qDAAqD;AACrD;AACA;;;AAJA;;;AAMO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAEpC,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,WAAU;oBACV,OAAO;oBACP,QAAQ;;;;;;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;kCAEhB,6LAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;;;;;;;;;;;;;AAKxB;GAhEgB;;QACS,kJAAA,CAAA,iBAAc;;;KADvB", "debugId": null}}, {"offset": {"line": 3785, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/search-button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearch } from \"@/components/providers/search-provider\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\n\r\nexport function SearchButton() {\r\n  const { openSearch } = useSearch();\r\n\r\n  return (\r\n    <Button\r\n      variant=\"outline\"\r\n      size=\"sm\"\r\n      onClick={openSearch}\r\n      className=\"relative h-9 w-full justify-start bg-background text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-64\"\r\n    >\r\n      <span className=\"hidden lg:inline-flex\">Search anything...</span>\r\n      <span className=\"inline-flex lg:hidden\">Search...</span>\r\n      <kbd className=\"pointer-events-none absolute right-1.5 top-1.5 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex\">\r\n        <span className=\"text-xs\">⌘</span>K\r\n      </kbd>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD;IAE/B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,6LAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAU;;;;;;oBAAQ;;;;;;;;;;;;;AAI1C;GAjBgB;;QACS,wJAAA,CAAA,YAAS;;;KADlB", "debugId": null}}, {"offset": {"line": 3863, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/top-navigation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport {\r\n  Bell,\r\n  ChevronDown,\r\n  ChevronLeft,\r\n  LogOut,\r\n  Settings,\r\n  User,\r\n  PanelLeftIcon,\r\n  PanelRightIcon,\r\n  Home,\r\n} from \"lucide-react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser, useLogout } from \"@/features/auth/hooks/use-auth\";\r\nimport { TeamHeader } from \"@/components/team-header\";\r\nimport { SearchButton } from \"@/components/search-button\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport Link from \"next/link\";\r\n\r\nexport function TopNavigation() {\r\n  const { data: user } = useCurrentUser();\r\n  const logout = useLogout();\r\n  const { toggleSidebar, state, isMobile, setOpenMobile } = useSidebar();\r\n  const isExpanded = state === \"expanded\";\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Check if we're on the stock requests page\r\n  const isStockRequestsPage = pathname.includes('/inventory/stock-requests') &&\r\n    !pathname.includes('/create') &&\r\n    !pathname.match(/\\/inventory\\/stock-requests\\/\\d+/);\r\n\r\n  const handleLogout = () => {\r\n    logout.mutate();\r\n  };\r\n\r\n  const handleBackClick = () => {\r\n    router.back();\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-30 flex h-14 shrink-0 items-center gap-2 border-b border-primary-foreground/10 bg-primary text-primary-foreground transition-[width,height] ease-linear\">\r\n      <div className=\"flex flex-1 items-center justify-between px-4 w-full\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isMobile && isStockRequestsPage ? (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={handleBackClick}\r\n                aria-label=\"Go back\"\r\n                title=\"Go back\"\r\n              >\r\n                <ChevronLeft className=\"h-5 w-5\" />\r\n                <span className=\"sr-only\">Go Back</span>\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={() => (isMobile ? setOpenMobile(true) : toggleSidebar())}\r\n                data-mobile={isMobile ? \"true\" : \"false\"}\r\n                aria-label=\"Toggle sidebar\"\r\n                title={isExpanded ? \"Collapse sidebar\" : \"Expand sidebar\"}\r\n              >\r\n                {isExpanded ? (\r\n                  <PanelLeftIcon className=\"h-5 w-5\" />\r\n                ) : (\r\n                  <PanelRightIcon className=\"h-5 w-5\" />\r\n                )}\r\n                <span className=\"sr-only\">Toggle Sidebar</span>\r\n              </Button>\r\n            )}\r\n            <TeamHeader />\r\n\r\n            <div className=\"h-6 border-r border-primary-foreground/20 mx-2\"></div>\r\n\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n              asChild\r\n            >\r\n              <Link href=\"/dashboard\">\r\n                <Home className=\"h-4 w-4 mr-1\" />\r\n                Dashboard\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-center\">\r\n          <SearchButton />\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative h-8 w-8 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n            aria-label=\"Notifications\"\r\n          >\r\n            <Bell className=\"h-5 w-5\" />\r\n            <span className=\"absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-[10px] font-bold text-primary\">\r\n              3\r\n            </span>\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 gap-2 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                aria-label=\"Account\"\r\n              >\r\n                <Avatar className=\"h-7 w-7\">\r\n                  <AvatarImage\r\n                    src=\"/placeholder-avatar.jpg\"\r\n                    alt={user?.name || \"User\"}\r\n                  />\r\n                  <AvatarFallback className=\"text-xs font-semibold\">\r\n                    {user?.name ? user.name.charAt(0) : \"U\"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <span className=\"hidden text-sm font-semibold md:inline-block\">\r\n                  {user?.name || \"User\"}\r\n                </span>\r\n                <ChevronDown className=\"h-4 w-4 text-primary-foreground/80\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-56 rounded-lg\">\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-semibold leading-none\">\r\n                    {user?.name || \"User\"}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || \"<EMAIL>\"}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuGroup>\r\n                <DropdownMenuItem>\r\n                  <User className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Profile</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem>\r\n                  <Settings className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Settings</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuGroup>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span className=\"text-sm\">Log out</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAEA;AACA;AASA;AACA;AACA;;;AA/BA;;;;;;;;;;;AAiCO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACnE,MAAM,aAAa,UAAU;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,4CAA4C;IAC5C,MAAM,sBAAsB,SAAS,QAAQ,CAAC,gCAC5C,CAAC,SAAS,QAAQ,CAAC,cACnB,CAAC,SAAS,KAAK,CAAC;IAElB,MAAM,eAAe;QACnB,OAAO,MAAM;IACf;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI;IACb;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,YAAY,oCACX,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,cAAW;gCACX,OAAM;;kDAEN,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;qDAG5B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAO,WAAW,cAAc,QAAQ;gCACjD,eAAa,WAAW,SAAS;gCACjC,cAAW;gCACX,OAAO,aAAa,qBAAqB;;oCAExC,2BACC,6LAAC,uNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;6DAEzB,6LAAC,yNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAE5B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG9B,6LAAC,uIAAA,CAAA,aAAU;;;;;0CAEX,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,yIAAA,CAAA,eAAY;;;;;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,cAAW;;8CAEX,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAA6H;;;;;;;;;;;;sCAK/I,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,cAAW;;0DAEX,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDACV,KAAI;wDACJ,KAAK,MAAM,QAAQ;;;;;;kEAErB,6LAAC,qIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGxC,6LAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;0DAEjB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG3B,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,6LAAC,+IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ;;;;;;kEAEjB,6LAAC;wDAAE,WAAU;kEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,oBAAiB;;8DAChB,6LAAC,+IAAA,CAAA,mBAAgB;;sEACf,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC,+IAAA,CAAA,mBAAgB;;sEACf,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAG9B,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C;GAlJgB;;QACS,kJAAA,CAAA,iBAAc;QACtB,kJAAA,CAAA,YAAS;QACkC,sIAAA,CAAA,aAAU;QAErD,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KANd", "debugId": null}}, {"offset": {"line": 4315, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,6LAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;KAFS;AAIT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MAhBS;AAkBT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;MAhBS;AAkBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,mNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAhBS", "debugId": null}}, {"offset": {"line": 4465, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/breadcrumbs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from \"@/components/ui/breadcrumb\";\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\n\r\nexport function Breadcrumbs() {\r\n  const pathname = usePathname();\r\n\r\n  // Get the current page name from the pathname\r\n  const getPageName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length === 0) return \"Dashboard\";\r\n\r\n    // Get the last segment of the path and capitalize it\r\n    const lastSegment = path[path.length - 1];\r\n    return (\r\n      lastSegment.charAt(0).toUpperCase() +\r\n      lastSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  // Get the parent path for breadcrumb\r\n  const getParentPath = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Remove the last segment to get the parent path\r\n    return \"/\" + path.slice(0, path.length - 1).join(\"/\");\r\n  };\r\n\r\n  // Get the parent name for breadcrumb\r\n  const getParentName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Get the second-to-last segment and capitalize it\r\n    const parentSegment = path[path.length - 2];\r\n    return (\r\n      parentSegment.charAt(0).toUpperCase() +\r\n      parentSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  const parentPath = getParentPath();\r\n  const parentName = getParentName();\r\n  const pageName = getPageName();\r\n\r\n  return (\r\n    <div className=\"mb-4\">\r\n      <Breadcrumb>\r\n        <BreadcrumbList>\r\n          {parentPath && parentName && (\r\n            <>\r\n              <BreadcrumbItem className=\"hidden md:block\">\r\n                <BreadcrumbLink asChild>\r\n                  <NavigationLink\r\n                    href={parentPath}\r\n                    className=\"text-sm font-medium text-muted-foreground hover:text-foreground\"\r\n                  >\r\n                    {parentName}\r\n                  </NavigationLink>\r\n                </BreadcrumbLink>\r\n              </BreadcrumbItem>\r\n              <BreadcrumbSeparator className=\"hidden md:block h-3 w-3\" />\r\n            </>\r\n          )}\r\n          <BreadcrumbItem>\r\n            <BreadcrumbPage className=\"text-sm font-semibold\">\r\n              {pageName}\r\n            </BreadcrumbPage>\r\n          </BreadcrumbItem>\r\n        </BreadcrumbList>\r\n      </Breadcrumb>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;;;AAXA;;;;AAaO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,cAAc;QAClB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAE9B,qDAAqD;QACrD,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,OACE,YAAY,MAAM,CAAC,GAAG,WAAW,KACjC,YAAY,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEvC;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,iDAAiD;QACjD,OAAO,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC;IACnD;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,mDAAmD;QACnD,MAAM,gBAAgB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC3C,OACE,cAAc,MAAM,CAAC,GAAG,WAAW,KACnC,cAAc,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEzC;IAEA,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,WAAW;IAEjB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,yIAAA,CAAA,aAAU;sBACT,cAAA,6LAAC,yIAAA,CAAA,iBAAc;;oBACZ,cAAc,4BACb;;0CACE,6LAAC,yIAAA,CAAA,iBAAc;gCAAC,WAAU;0CACxB,cAAA,6LAAC,yIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC,iJAAA,CAAA,iBAAc;wCACb,MAAM;wCACN,WAAU;kDAET;;;;;;;;;;;;;;;;0CAIP,6LAAC,yIAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;kCAGnC,6LAAC,yIAAA,CAAA,iBAAc;kCACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAtEgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 4594, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/main-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { ReactNode, Suspense, useEffect } from \"react\";\r\nimport { RoleGuard } from \"./role-guard\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\nimport { SidebarProvider, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { AppSidebar } from \"@/components/app-sidebar\";\r\nimport { TopNavigation } from \"@/components/top-navigation\";\r\nimport { Breadcrumbs } from \"@/components/breadcrumbs\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\";\r\n\r\ninterface MainLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function MainLayout({ children }: MainLayoutProps) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Use useEffect for redirects to avoid hydration issues\r\n  useEffect(() => {\r\n    // Only show loading on the very first render if:\r\n    // 1. Auth is not initialized yet\r\n    // 2. We haven't shown the loading screen before in this session\r\n    if (!isInitialized && !hasShownLoading.main) {\r\n      setLoading(\"main\", true);\r\n    } else {\r\n      setLoading(\"main\", false);\r\n    }\r\n\r\n    if (!isInitialized) {\r\n      return; // Wait until auth state is initialized\r\n    }\r\n\r\n    // After auth state is initialized, we can hide the loading screen\r\n    setLoading(\"main\", false);\r\n\r\n    // If we have an access token, mark that we've shown loading\r\n    // This prevents showing loading on subsequent navigations\r\n    if (accessToken) {\r\n      markLoadingShown(\"main\");\r\n    }\r\n\r\n    // We're disabling client-side redirects to avoid redirect loops\r\n    // The middleware will handle redirects instead\r\n    if (!accessToken) {\r\n      // We're not redirecting here anymore\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [accessToken, isInitialized, hasShownLoading.main]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in loading state\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (isLoading.main) {\r\n        setLoading(\"main\", false);\r\n        markLoadingShown(\"main\");\r\n      }\r\n    }, 1500); // 1.5 second timeout for better UX\r\n\r\n    return () => clearTimeout(timeoutId);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.main]);\r\n\r\n  // Don't render anything if not authenticated\r\n  if (!accessToken) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider>\r\n      <RoleGuard>\r\n        <MainLayoutContent>{children}</MainLayoutContent>\r\n      </RoleGuard>\r\n    </SidebarProvider>\r\n  );\r\n}\r\n\r\nfunction MainLayoutContent({ children }: { children: ReactNode }) {\r\n  const { state, openMobile, setOpenMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-screen w-full bg-gray-50\">\r\n      <TopNavigation />\r\n      <div className=\"flex h-[calc(100vh-3.5rem)] overflow-hidden\">\r\n        {/* Desktop sidebar */}\r\n        <aside\r\n          className={cn(\r\n            \"border-r bg-white transition-all duration-300 shrink-0 hidden md:block\",\r\n            isCollapsed ? \"w-16\" : \"w-64\"\r\n          )}\r\n        >\r\n          <AppSidebar />\r\n        </aside>\r\n\r\n        {/* Mobile sidebar */}\r\n        <div className=\"md:hidden\">\r\n          <Sheet open={openMobile} onOpenChange={setOpenMobile}>\r\n            <SheetContent\r\n              side=\"left\"\r\n              className=\"p-0 w-[280px] border-r bg-white\"\r\n            >\r\n              <div className=\"h-full overflow-y-auto\">\r\n                <AppSidebar />\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        <main className=\"flex flex-col w-full overflow-y-auto overflow-x-hidden\">\r\n          <div className=\"p-6\">\r\n            <Suspense fallback={<div>Loading content...</div>}>\r\n              <Breadcrumbs />\r\n              {children}\r\n            </Suspense>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;AAoBO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;IAEX,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,iDAAiD;YACjD,iCAAiC;YACjC,gEAAgE;YAChE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;gBAC3C,WAAW,QAAQ;YACrB,OAAO;gBACL,WAAW,QAAQ;YACrB;YAEA,IAAI,CAAC,eAAe;gBAClB,QAAQ,uCAAuC;YACjD;YAEA,kEAAkE;YAClE,WAAW,QAAQ;YAEnB,4DAA4D;YAC5D,0DAA0D;YAC1D,IAAI,aAAa;gBACf,iBAAiB;YACnB;YAEA,gEAAgE;YAChE,+CAA+C;YAC/C,IAAI,CAAC,aAAa;YAChB,qCAAqC;YACvC;QACA,uDAAuD;QACzD;+BAAG;QAAC;QAAa;QAAe,gBAAgB,IAAI;KAAC;IAErD,iEAAiE;IACjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,YAAY;kDAAW;oBAC3B,IAAI,UAAU,IAAI,EAAE;wBAClB,WAAW,QAAQ;wBACnB,iBAAiB;oBACnB;gBACF;iDAAG,OAAO,mCAAmC;YAE7C;wCAAO,IAAM,aAAa;;QAC1B,uDAAuD;QACzD;+BAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6CAA6C;IAC7C,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,iJAAA,CAAA,YAAS;sBACR,cAAA,6LAAC;0BAAmB;;;;;;;;;;;;;;;;AAI5B;GA9DgB;;QACyB,wIAAA,CAAA,gBAAa;QAElD,yJAAA,CAAA,aAAU;;;KAHE;AAgEhB,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;;IAC9D,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,cAAc,UAAU;IAE9B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,cAAc,SAAS;kCAGzB,cAAA,6LAAC,uIAAA,CAAA,aAAU;;;;;;;;;;kCAIb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;sCACrC,cAAA,6LAAC,oIAAA,CAAA,eAAY;gCACX,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6JAAA,CAAA,WAAQ;gCAAC,wBAAU,6LAAC;8CAAI;;;;;;;kDACvB,6LAAC,oIAAA,CAAA,cAAW;;;;;oCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;IA3CS;;QACsC,sIAAA,CAAA,aAAU;;;MADhD", "debugId": null}}, {"offset": {"line": 4836, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 4951, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { cva, type VariantProps } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        success:\r\n          \"border-transparent bg-green-500 text-white [a&]:hover:bg-green-600 focus-visible:ring-green-500/20 dark:focus-visible:ring-green-500/40\",\r\n        warning:\r\n          \"border-transparent bg-yellow-500 text-white [a&]:hover:bg-yellow-600 focus-visible:ring-yellow-500/20 dark:focus-visible:ring-yellow-500/40\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"span\"> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"span\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;YACF,SACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 5005, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport {\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n  MoreHorizontalIcon,\r\n} from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button, buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Pagination({ className, ...props }: React.ComponentProps<\"nav\">) {\r\n  return (\r\n    <nav\r\n      role=\"navigation\"\r\n      aria-label=\"pagination\"\r\n      data-slot=\"pagination\"\r\n      className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"pagination-content\"\r\n      className={cn(\"flex flex-row items-center gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationItem({ ...props }: React.ComponentProps<\"li\">) {\r\n  return <li data-slot=\"pagination-item\" {...props} />\r\n}\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<React.ComponentProps<typeof Button>, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nfunction PaginationLink({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) {\r\n  return (\r\n    <a\r\n      aria-current={isActive ? \"page\" : undefined}\r\n      data-slot=\"pagination-link\"\r\n      data-active={isActive}\r\n      className={cn(\r\n        buttonVariants({\r\n          variant: isActive ? \"outline\" : \"ghost\",\r\n          size,\r\n        }),\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationPrevious({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to previous page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pl-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <ChevronLeftIcon />\r\n      <span className=\"hidden sm:block\">Previous</span>\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationNext({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to next page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pr-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <span className=\"hidden sm:block\">Next</span>\r\n      <ChevronRightIcon />\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      aria-hidden\r\n      data-slot=\"pagination-ellipsis\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontalIcon className=\"size-4\" />\r\n      <span className=\"sr-only\">More pages</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationLink,\r\n  PaginationItem,\r\n  PaginationPrevious,\r\n  PaginationNext,\r\n  PaginationEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AAAA;AAAA;AAMA;AACA;;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,MAAK;QACL,cAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EAAE,GAAG,OAAmC;IAC9D,qBAAO,6LAAC;QAAG,aAAU;QAAmB,GAAG,KAAK;;;;;;AAClD;MAFS;AAST,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACb,GAAG,OACiB;IACpB,qBACE,6LAAC;QACC,gBAAc,WAAW,SAAS;QAClC,aAAU;QACV,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,YAAY;YAChC;QACF,IACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,6LAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,6LAAC,2NAAA,CAAA,kBAAe;;;;;0BAChB,6LAAC;gBAAK,WAAU;0BAAkB;;;;;;;;;;;;AAGxC;MAfS;AAiBT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,6LAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BAAkB;;;;;;0BAClC,6LAAC,6NAAA,CAAA,mBAAgB;;;;;;;;;;;AAGvB;MAfS;AAiBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;0BAC9B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAfS", "debugId": null}}, {"offset": {"line": 5182, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 5431, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/data-pagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo } from \"react\";\r\nimport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from \"@/components/ui/pagination\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ChevronFirst, ChevronLast } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nexport interface DataPaginationProps {\r\n  // Required props\r\n  currentPage: number;\r\n  totalPages: number;\r\n  onPageChange: (page: number) => void;\r\n\r\n  // Optional props\r\n  pageSize?: number;\r\n  pageSizes?: number[];\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  totalItems?: number;\r\n  isLoading?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showItemsInfo?: boolean;\r\n  showFirstLastButtons?: boolean;\r\n  maxPageButtons?: number;\r\n  className?: string;\r\n  compact?: boolean;\r\n  ariaLabel?: string;\r\n}\r\n\r\nexport function DataPagination({\r\n  currentPage,\r\n  totalPages,\r\n  onPageChange,\r\n  pageSize = 10,\r\n  pageSizes = [10, 25, 50, 100, 250, 500, 1000],\r\n  onPageSizeChange,\r\n  totalItems,\r\n  isLoading = false,\r\n  showPageSizeSelector = true,\r\n  showItemsInfo = true,\r\n  showFirstLastButtons = true,\r\n  maxPageButtons = 5,\r\n  className,\r\n  compact = false,\r\n  ariaLabel = \"Pagination\",\r\n}: DataPaginationProps) {\r\n  // Don't render pagination if there's only one page and no page size selector\r\n  if (totalPages <= 1 && (!showPageSizeSelector || !onPageSizeChange)) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate the current range of items being displayed\r\n  const itemRange = useMemo(() => {\r\n    if (!totalItems) return null;\r\n\r\n    const start = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;\r\n    const end = Math.min(currentPage * pageSize, totalItems);\r\n\r\n    return { start, end };\r\n  }, [currentPage, pageSize, totalItems]);\r\n\r\n  // Generate page numbers to display\r\n  const pageNumbers = useMemo(() => {\r\n    const numbers: (number | string)[] = [];\r\n\r\n    if (totalPages <= maxPageButtons) {\r\n      // If total pages is less than or equal to maxPageButtons, show all pages\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        numbers.push(i);\r\n      }\r\n    } else {\r\n      // Always include first page\r\n      numbers.push(1);\r\n\r\n      // Calculate start and end of page numbers to show\r\n      let start = Math.max(2, currentPage - Math.floor(maxPageButtons / 2) + 1);\r\n      let end = Math.min(totalPages - 1, start + maxPageButtons - 3);\r\n\r\n      // Adjust if we're at the beginning\r\n      if (currentPage <= Math.floor(maxPageButtons / 2)) {\r\n        end = maxPageButtons - 2;\r\n        start = 2;\r\n      }\r\n\r\n      // Adjust if we're at the end\r\n      if (currentPage > totalPages - Math.floor(maxPageButtons / 2)) {\r\n        start = Math.max(2, totalPages - maxPageButtons + 2);\r\n        end = totalPages - 1;\r\n      }\r\n\r\n      // Add ellipsis before middle pages if needed\r\n      if (start > 2) {\r\n        numbers.push(\"ellipsis-start\");\r\n      }\r\n\r\n      // Add middle pages\r\n      for (let i = start; i <= end; i++) {\r\n        numbers.push(i);\r\n      }\r\n\r\n      // Add ellipsis after middle pages if needed\r\n      if (end < totalPages - 1) {\r\n        numbers.push(\"ellipsis-end\");\r\n      }\r\n\r\n      // Always include last page if more than one page\r\n      if (totalPages > 1) {\r\n        numbers.push(totalPages);\r\n      }\r\n    }\r\n\r\n    return numbers;\r\n  }, [currentPage, totalPages, maxPageButtons]);\r\n\r\n  // Handle page click with validation\r\n  const handlePageClick = (page: number, e: React.MouseEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Only trigger page change if it's not the current page and not loading\r\n    if (page !== currentPage && !isLoading) {\r\n      // Force the page to be within valid range\r\n      const validPage = Math.max(1, Math.min(page, totalPages));\r\n      onPageChange(validPage);\r\n    }\r\n  };\r\n\r\n  // Handle page size change\r\n  const handlePageSizeChange = (value: string) => {\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(parseInt(value, 10));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex flex-col gap-4 w-full\",\r\n        compact\r\n          ? \"sm:flex-row sm:items-center sm:justify-between\"\r\n          : \"md:flex-row md:items-center md:justify-between\",\r\n        className\r\n      )}\r\n      aria-label={ariaLabel}\r\n    >\r\n      {/* Page size selector */}\r\n      {showPageSizeSelector && onPageSizeChange && (\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-sm text-muted-foreground\">Show</span>\r\n          <Select\r\n            value={pageSize.toString()}\r\n            onValueChange={handlePageSizeChange}\r\n            disabled={isLoading}\r\n          >\r\n            <SelectTrigger\r\n              className=\"h-8 w-[70px]\"\r\n              aria-label=\"Select page size\"\r\n            >\r\n              <SelectValue placeholder={pageSize.toString()} />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              {pageSizes.map((size) => (\r\n                <SelectItem key={size} value={size.toString()}>\r\n                  {size}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n          <span className=\"text-sm text-muted-foreground\">per page</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Pagination controls - Always show if we have pagination data */}\r\n      {totalPages >= 1 && (\r\n        <div className=\"bg-muted/50 rounded-md p-1\">\r\n          <Pagination>\r\n            <PaginationContent className=\"flex-wrap justify-center\">\r\n              {/* First page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(1, e)}\r\n                    disabled={currentPage === 1 || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to first page\"\r\n                  >\r\n                    <ChevronFirst className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n\r\n              {/* Previous page button */}\r\n              <PaginationItem>\r\n                <PaginationPrevious\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage - 1, e)}\r\n                  aria-disabled={currentPage === 1 || isLoading}\r\n                  className={cn(\r\n                    currentPage === 1 || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Page numbers */}\r\n              {!compact &&\r\n                pageNumbers.map((pageNumber, index) => {\r\n                  if (typeof pageNumber === \"string\") {\r\n                    return (\r\n                      <PaginationItem key={pageNumber}>\r\n                        <PaginationEllipsis />\r\n                      </PaginationItem>\r\n                    );\r\n                  }\r\n\r\n                  return (\r\n                    <PaginationItem key={`page-${pageNumber}`}>\r\n                      <PaginationLink\r\n                        href=\"#\"\r\n                        onClick={(e) => handlePageClick(pageNumber, e)}\r\n                        isActive={currentPage === pageNumber}\r\n                        aria-current={\r\n                          currentPage === pageNumber ? \"page\" : undefined\r\n                        }\r\n                      >\r\n                        {pageNumber}\r\n                      </PaginationLink>\r\n                    </PaginationItem>\r\n                  );\r\n                })}\r\n\r\n              {/* Compact view shows current/total instead of page numbers */}\r\n              {compact && (\r\n                <div className=\"flex items-center mx-2\">\r\n                  <span className=\"text-sm\">\r\n                    Page <span className=\"font-bold\">{currentPage}</span> of{\" \"}\r\n                    <span className=\"font-medium\">{totalPages}</span>\r\n                    {isLoading && (\r\n                      <span className=\"ml-1 text-muted-foreground\">\r\n                        (Loading...)\r\n                      </span>\r\n                    )}\r\n                  </span>\r\n                </div>\r\n              )}\r\n\r\n              {/* Next page button */}\r\n              <PaginationItem>\r\n                <PaginationNext\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage + 1, e)}\r\n                  aria-disabled={currentPage === totalPages || isLoading}\r\n                  className={cn(\r\n                    currentPage === totalPages || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Last page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(totalPages, e)}\r\n                    disabled={currentPage === totalPages || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to last page\"\r\n                  >\r\n                    <ChevronLast className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n            </PaginationContent>\r\n          </Pagination>\r\n        </div>\r\n      )}\r\n\r\n      {/* Items info */}\r\n      {showItemsInfo && totalItems !== undefined && (\r\n        <div className=\"text-sm text-muted-foreground whitespace-nowrap\">\r\n          {totalItems === 0 ? (\r\n            \"No items\"\r\n          ) : (\r\n            <>\r\n              Showing {itemRange?.start} to {itemRange?.end} of {totalItems}{\" \"}\r\n              items\r\n            </>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AAOA;AACA;AAAA;AACA;;;AArBA;;;;;;;AA4CO,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,YAAY,EACZ,WAAW,EAAE,EACb,YAAY;IAAC;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;CAAK,EAC7C,gBAAgB,EAChB,UAAU,EACV,YAAY,KAAK,EACjB,uBAAuB,IAAI,EAC3B,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,iBAAiB,CAAC,EAClB,SAAS,EACT,UAAU,KAAK,EACf,YAAY,YAAY,EACJ;;IACpB,6EAA6E;IAC7E,IAAI,cAAc,KAAK,CAAC,CAAC,wBAAwB,CAAC,gBAAgB,GAAG;QACnE,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE;YACxB,IAAI,CAAC,YAAY,OAAO;YAExB,MAAM,QAAQ,eAAe,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,WAAW;YACpE,MAAM,MAAM,KAAK,GAAG,CAAC,cAAc,UAAU;YAE7C,OAAO;gBAAE;gBAAO;YAAI;QACtB;4CAAG;QAAC;QAAa;QAAU;KAAW;IAEtC,mCAAmC;IACnC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YAC1B,MAAM,UAA+B,EAAE;YAEvC,IAAI,cAAc,gBAAgB;gBAChC,yEAAyE;gBACzE,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;oBACpC,QAAQ,IAAI,CAAC;gBACf;YACF,OAAO;gBACL,4BAA4B;gBAC5B,QAAQ,IAAI,CAAC;gBAEb,kDAAkD;gBAClD,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB,KAAK;gBACvE,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa,GAAG,QAAQ,iBAAiB;gBAE5D,mCAAmC;gBACnC,IAAI,eAAe,KAAK,KAAK,CAAC,iBAAiB,IAAI;oBACjD,MAAM,iBAAiB;oBACvB,QAAQ;gBACV;gBAEA,6BAA6B;gBAC7B,IAAI,cAAc,aAAa,KAAK,KAAK,CAAC,iBAAiB,IAAI;oBAC7D,QAAQ,KAAK,GAAG,CAAC,GAAG,aAAa,iBAAiB;oBAClD,MAAM,aAAa;gBACrB;gBAEA,6CAA6C;gBAC7C,IAAI,QAAQ,GAAG;oBACb,QAAQ,IAAI,CAAC;gBACf;gBAEA,mBAAmB;gBACnB,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;oBACjC,QAAQ,IAAI,CAAC;gBACf;gBAEA,4CAA4C;gBAC5C,IAAI,MAAM,aAAa,GAAG;oBACxB,QAAQ,IAAI,CAAC;gBACf;gBAEA,iDAAiD;gBACjD,IAAI,aAAa,GAAG;oBAClB,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,OAAO;QACT;8CAAG;QAAC;QAAa;QAAY;KAAe;IAE5C,oCAAoC;IACpC,MAAM,kBAAkB,CAAC,MAAc;QACrC,EAAE,cAAc;QAEhB,wEAAwE;QACxE,IAAI,SAAS,eAAe,CAAC,WAAW;YACtC,0CAA0C;YAC1C,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;YAC7C,aAAa;QACf;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,IAAI,kBAAkB;YACpB,iBAAiB,SAAS,OAAO;QACnC;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,UACI,mDACA,kDACJ;QAEF,cAAY;;YAGX,wBAAwB,kCACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAgC;;;;;;kCAChD,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,QAAQ;wBACxB,eAAe;wBACf,UAAU;;0CAEV,6LAAC,qIAAA,CAAA,gBAAa;gCACZ,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAa,SAAS,QAAQ;;;;;;;;;;;0CAE7C,6LAAC,qIAAA,CAAA,gBAAa;0CACX,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,qIAAA,CAAA,aAAU;wCAAY,OAAO,KAAK,QAAQ;kDACxC;uCADc;;;;;;;;;;;;;;;;kCAMvB,6LAAC;wBAAK,WAAU;kCAAgC;;;;;;;;;;;;YAKnD,cAAc,mBACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,yIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,yIAAA,CAAA,oBAAiB;wBAAC,WAAU;;4BAE1B,sCACC,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,GAAG;oCACnC,UAAU,gBAAgB,KAAK;oCAC/B,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAM9B,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,yIAAA,CAAA,qBAAkB;oCACjB,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,KAAK;oCACpC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,KAAK,YACjB,mCACA,IACJ;;;;;;;;;;;4BAML,CAAC,WACA,YAAY,GAAG,CAAC,CAAC,YAAY;gCAC3B,IAAI,OAAO,eAAe,UAAU;oCAClC,qBACE,6LAAC,yIAAA,CAAA,iBAAc;kDACb,cAAA,6LAAC,yIAAA,CAAA,qBAAkB;;;;;uCADA;;;;;gCAIzB;gCAEA,qBACE,6LAAC,yIAAA,CAAA,iBAAc;8CACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;wCACb,MAAK;wCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;wCAC5C,UAAU,gBAAgB;wCAC1B,gBACE,gBAAgB,aAAa,SAAS;kDAGvC;;;;;;mCATgB,CAAC,KAAK,EAAE,YAAY;;;;;4BAa7C;4BAGD,yBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAU;sDACnB,6LAAC;4CAAK,WAAU;sDAAa;;;;;;wCAAmB;wCAAI;sDACzD,6LAAC;4CAAK,WAAU;sDAAe;;;;;;wCAC9B,2BACC,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;0CASrD,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;oCACb,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,cAAc;oCAC7C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,cAAc,YAC1B,mCACA,IACJ;;;;;;;;;;;4BAML,sCACC,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;oCAC5C,UAAU,gBAAgB,cAAc;oCACxC,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUpC,iBAAiB,eAAe,2BAC/B,6LAAC;gBAAI,WAAU;0BACZ,eAAe,IACd,2BAEA;;wBAAE;wBACS,WAAW;wBAAM;wBAAK,WAAW;wBAAI;wBAAK;wBAAY;wBAAI;;;;;;;;;;;;;;AAQjF;GA9QgB;KAAA", "debugId": null}}, {"offset": {"line": 5833, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 5971, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/branches/api/branch-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { Branch, PaginatedResponse, UpdateBranchStatusRequest } from \"@/types\";\r\nimport { CreateBranchRequest, UpdateBranchRequest } from \"@/types/branch\";\r\n\r\nexport const branchService = {\r\n  getBranches: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Branch>> => {\r\n    try {\r\n      console.log(\"Calling API: /branches with params:\", params);\r\n      let response;\r\n\r\n      try {\r\n        // Try the Next.js API route first\r\n        response = await apiClient.get<any>(\"/branches\", { params });\r\n      } catch (error) {\r\n        console.warn(\r\n          \"Error fetching from Next.js API route, trying direct backend:\",\r\n          error\r\n        );\r\n        // If that fails, try the backend directly\r\n        const API_URL = process.env.NEXT_PUBLIC_API_URL;\r\n        const token =\r\n          localStorage.getItem(\"token\") || localStorage.getItem(\"accessToken\");\r\n\r\n        if (!token) {\r\n          throw new Error(\"No authentication token available\");\r\n        }\r\n\r\n        const backendResponse = await fetch(\r\n          `${API_URL}/branches${\r\n            params ? `?${new URLSearchParams(params)}` : \"\"\r\n          }`,\r\n          {\r\n            headers: {\r\n              Authorization: `Bearer ${token}`,\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n          }\r\n        );\r\n\r\n        if (!backendResponse.ok) {\r\n          throw new Error(`Backend API error: ${backendResponse.status}`);\r\n        }\r\n\r\n        response = await backendResponse.json();\r\n      }\r\n\r\n      console.log(\"Raw branches API response:\", response);\r\n\r\n      // Map API response to our Branch type\r\n      const mapApiBranchToBranch = (apiBranch: any): Branch => ({\r\n        ...apiBranch,\r\n      });\r\n\r\n      // If response is an array, convert to paginated format with mapped branches\r\n      if (Array.isArray(response)) {\r\n        console.log(\r\n          \"Branches response is an array, converting to paginated format\"\r\n        );\r\n        const mappedBranches = response.map(mapApiBranchToBranch);\r\n        return {\r\n          data: mappedBranches,\r\n          pagination: {\r\n            total: mappedBranches.length,\r\n            page: 1,\r\n            limit: mappedBranches.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        console.log(\"Branches response is already in paginated format\");\r\n        const mappedBranches = response.data.map(mapApiBranchToBranch);\r\n        return {\r\n          data: mappedBranches,\r\n          pagination: response.pagination || {\r\n            total: mappedBranches.length,\r\n            page: 1,\r\n            limit: mappedBranches.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If response has data property but it's not an array, wrap it\r\n      if (response && response.data && !Array.isArray(response.data)) {\r\n        console.log(\r\n          \"Branches response has data property but it's not an array, wrapping it\"\r\n        );\r\n        return {\r\n          data: [mapApiBranchToBranch(response.data)],\r\n          pagination: response.pagination || {\r\n            total: 1,\r\n            page: 1,\r\n            limit: 1,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If response itself is not an array but contains branches directly\r\n      if (response && !Array.isArray(response) && !response.data) {\r\n        console.log(\"Response contains branches directly, converting to array\");\r\n        // Try to extract branches from the response\r\n        const branches = Object.values(response).filter(\r\n          (item) =>\r\n            typeof item === \"object\" &&\r\n            item !== null &&\r\n            \"id\" in item &&\r\n            \"name\" in item\r\n        );\r\n\r\n        if (branches.length > 0) {\r\n          const mappedBranches = branches.map(mapApiBranchToBranch);\r\n          return {\r\n            data: mappedBranches,\r\n            pagination: {\r\n              total: mappedBranches.length,\r\n              page: 1,\r\n              limit: mappedBranches.length,\r\n              totalPages: 1,\r\n            },\r\n          };\r\n        }\r\n      }\r\n\r\n      // Default fallback\r\n      console.log(\"Using default fallback for branches response\");\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error in getBranches:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getBranchById: async (id: number): Promise<Branch> => {\r\n    return apiClient.get(`/branches/${id}`);\r\n  },\r\n\r\n  createBranch: async (branch: CreateBranchRequest): Promise<Branch> => {\r\n    // Send exactly what the API expects according to the guide\r\n    const apiRequest = {\r\n      tenant_id: branch.tenant_id,\r\n      name: branch.name,\r\n      location: branch.location,\r\n      region_id: branch.region_id,\r\n      is_hq: false, // Always set to false as requested\r\n      status: \"active\", // Default status\r\n    };\r\n\r\n    // Add optional fields if they exist\r\n    if (branch.phone) {\r\n      apiRequest.phone_number = branch.phone;\r\n    }\r\n    if (branch.email) {\r\n      apiRequest.email = branch.email;\r\n    }\r\n\r\n    console.log(\"Creating branch with data:\", apiRequest);\r\n    return apiClient.post(\"/branches\", apiRequest);\r\n  },\r\n\r\n  updateBranch: async (\r\n    id: number,\r\n    branch: UpdateBranchRequest\r\n  ): Promise<Branch> => {\r\n    // Ensure we're sending the correct fields to the API\r\n    const apiRequest = {\r\n      name: branch.name,\r\n      location: branch.location,\r\n      region_id: branch.region_id,\r\n      level: branch.level,\r\n    };\r\n\r\n    console.log(\"Updating branch with data:\", apiRequest);\r\n    return apiClient.put(`/branches/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteBranch: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/branches/${id}`);\r\n  },\r\n\r\n  updateBranchStatus: async (\r\n    id: number,\r\n    status: UpdateBranchStatusRequest\r\n  ): Promise<Branch> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      status: status.status, // 'active' or 'inactive'\r\n    };\r\n\r\n    return apiClient.put(`/branches/${id}/status`, apiRequest);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAqBwB;AArBxB;;AAIO,MAAM,gBAAgB;IAC3B,aAAa,OACX;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,uCAAuC;YACnD,IAAI;YAEJ,IAAI;gBACF,kCAAkC;gBAClC,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,aAAa;oBAAE;gBAAO;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CACV,iEACA;gBAEF,0CAA0C;gBAC1C,MAAM;gBACN,MAAM,QACJ,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;gBAExD,IAAI,CAAC,OAAO;oBACV,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAkB,MAAM,MAC5B,GAAG,QAAQ,SAAS,EAClB,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,SAAS,GAAG,IAC7C,EACF;oBACE,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,OAAO;wBAChC,gBAAgB;oBAClB;gBACF;gBAGF,IAAI,CAAC,gBAAgB,EAAE,EAAE;oBACvB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,gBAAgB,MAAM,EAAE;gBAChE;gBAEA,WAAW,MAAM,gBAAgB,IAAI;YACvC;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,sCAAsC;YACtC,MAAM,uBAAuB,CAAC,YAA2B,CAAC;oBACxD,GAAG,SAAS;gBACd,CAAC;YAED,4EAA4E;YAC5E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,QAAQ,GAAG,CACT;gBAEF,MAAM,iBAAiB,SAAS,GAAG,CAAC;gBACpC,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,eAAe,MAAM;wBAC5B,MAAM;wBACN,OAAO,eAAe,MAAM;wBAC5B,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,QAAQ,GAAG,CAAC;gBACZ,MAAM,iBAAiB,SAAS,IAAI,CAAC,GAAG,CAAC;gBACzC,OAAO;oBACL,MAAM;oBACN,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,eAAe,MAAM;wBAC5B,MAAM;wBACN,OAAO,eAAe,MAAM;wBAC5B,YAAY;oBACd;gBACF;YACF;YAEA,+DAA+D;YAC/D,IAAI,YAAY,SAAS,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC9D,QAAQ,GAAG,CACT;gBAEF,OAAO;oBACL,MAAM;wBAAC,qBAAqB,SAAS,IAAI;qBAAE;oBAC3C,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YAEA,oEAAoE;YACpE,IAAI,YAAY,CAAC,MAAM,OAAO,CAAC,aAAa,CAAC,SAAS,IAAI,EAAE;gBAC1D,QAAQ,GAAG,CAAC;gBACZ,4CAA4C;gBAC5C,MAAM,WAAW,OAAO,MAAM,CAAC,UAAU,MAAM,CAC7C,CAAC,OACC,OAAO,SAAS,YAChB,SAAS,QACT,QAAQ,QACR,UAAU;gBAGd,IAAI,SAAS,MAAM,GAAG,GAAG;oBACvB,MAAM,iBAAiB,SAAS,GAAG,CAAC;oBACpC,OAAO;wBACL,MAAM;wBACN,YAAY;4BACV,OAAO,eAAe,MAAM;4BAC5B,MAAM;4BACN,OAAO,eAAe,MAAM;4BAC5B,YAAY;wBACd;oBACF;gBACF;YACF;YAEA,mBAAmB;YACnB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,eAAe,OAAO;QACpB,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;IACxC;IAEA,cAAc,OAAO;QACnB,2DAA2D;QAC3D,MAAM,aAAa;YACjB,WAAW,OAAO,SAAS;YAC3B,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,OAAO;YACP,QAAQ;QACV;QAEA,oCAAoC;QACpC,IAAI,OAAO,KAAK,EAAE;YAChB,WAAW,YAAY,GAAG,OAAO,KAAK;QACxC;QACA,IAAI,OAAO,KAAK,EAAE;YAChB,WAAW,KAAK,GAAG,OAAO,KAAK;QACjC;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,aAAa;IACrC;IAEA,cAAc,OACZ,IACA;QAEA,qDAAqD;QACrD,MAAM,aAAa;YACjB,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,OAAO,OAAO,KAAK;QACrB;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;IAC1C;IAEA,cAAc,OAAO;QACnB,OAAO,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IAC3C;IAEA,oBAAoB,OAClB,IACA;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,QAAQ,OAAO,MAAM;QACvB;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE;IACjD;AACF", "debugId": null}}, {"offset": {"line": 6151, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/branches/hooks/use-branches.ts"], "sourcesContent": ["import { UpdateBranchRequest, UpdateBranchStatusRequest } from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport { branchService } from \"../api/branch-service\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { CreateBranchRequest } from \"@/types/branch\";\r\n\r\nexport function useBranches(\r\n  params?: Record<string, any>,\r\n  options?: { enabled?: boolean }\r\n) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n\r\n  return useQuery({\r\n    queryKey: [\"branches\", params],\r\n    queryFn: () => branchService.getBranches(params),\r\n    // Only run query when authentication is ready and enabled option is true (if provided)\r\n    enabled: isInitialized && !!accessToken && (options?.enabled !== false),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useBranch(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"branches\", id],\r\n    queryFn: () => branchService.getBranchById(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateBranch() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (branch: CreateBranchRequest) =>\r\n      branchService.createBranch(branch),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch created\", {\r\n        description: \"The branch has been created successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error creating branch\", {\r\n        description:\r\n          error.message || \"An error occurred while creating the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateBranch(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (branch: UpdateBranchRequest) =>\r\n      branchService.updateBranch(id, branch),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch updated\", {\r\n        description: \"The branch has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating branch\", {\r\n        description:\r\n          error.message || \"An error occurred while updating the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteBranch() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => branchService.deleteBranch(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch deleted\", {\r\n        description: \"The branch has been deleted successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error deleting branch\", {\r\n        description:\r\n          error.message || \"An error occurred while deleting the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateBranchStatus(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (status: UpdateBranchStatusRequest) =>\r\n      branchService.updateBranchStatus(id, status),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch status updated\", {\r\n        description: \"The branch status has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating branch status\", {\r\n        description:\r\n          error.message ||\r\n          \"An error occurred while updating the branch status.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAGO,SAAS,YACd,MAA4B,EAC5B,OAA+B;;IAE/B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAEnD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAO;QAC9B,OAAO;oCAAE,IAAM,0JAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;;QACzC,uFAAuF;QACvF,SAAS,iBAAiB,CAAC,CAAC,eAAgB,SAAS,YAAY;QACjE,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,MAAM;oCAAE,CAAC;gBACP,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;oBACF;gBACF;gBACA,OAAO;YACT;;IACF;AACF;GA/BgB;;QAIyB,wIAAA,CAAA,gBAAa;QAE7C,8KAAA,CAAA,WAAQ;;;AA2BV,SAAS,UAAU,EAAU;;IAClC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAG;QAC1B,OAAO;kCAAE,IAAM,0JAAA,CAAA,gBAAa,CAAC,aAAa,CAAC;;QAC3C,SAAS,CAAC,CAAC;IACb;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAOV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;;QAC7B,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IAnBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS,gBAAgB,EAAU;;IACxC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,IAAI;;QACjC,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IApBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,KAAe,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;;QACvD,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IAlBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,SAAS,sBAAsB,EAAU;;IAC9C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,kBAAkB,CAAC,IAAI;;QACvC,SAAS;iDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,yBAAyB;oBACrC,aAAa;gBACf;YACF;;QACA,OAAO;iDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;oBAC1C,aACE,MAAM,OAAO,IACb;gBACJ;YACF;;IACF;AACF;IArBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 6387, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/users/api/user-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport {\r\n  User,\r\n  CreateUserRequest,\r\n  UpdateUserRequest,\r\n  PaginatedResponse,\r\n  UpdateUserStatusRequest,\r\n} from \"@/types\";\r\n\r\nimport { normalizeUser } from \"@/types/user\";\r\n\r\nexport const userService = {\r\n  getUsers: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<User>> => {\r\n    // Handle status parameter specially\r\n    const apiParams = { ...params };\r\n\r\n    // If status is provided, convert it to the appropriate deleted_at filter\r\n    if (apiParams.status) {\r\n      if (apiParams.status === \"active\") {\r\n        apiParams.deleted = false;\r\n      } else if (apiParams.status === \"inactive\") {\r\n        apiParams.deleted = true;\r\n      }\r\n      delete apiParams.status; // Remove the status parameter as the API uses deleted\r\n    }\r\n\r\n    // Add a timestamp to prevent caching issues\r\n    apiParams._t = new Date().getTime();\r\n\r\n    // Log the request for debugging\r\n    console.log(`Fetching users with API params:`, apiParams);\r\n\r\n    try {\r\n      const response = await apiClient.get<PaginatedResponse<User>>(\"/users\", { params: apiParams });\r\n      console.log(`Users API response success:`, {\r\n        page: response.pagination?.page,\r\n        totalPages: response.pagination?.totalPages,\r\n        total: response.pagination?.total,\r\n        count: response.data?.length\r\n      });\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Error fetching users:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  getUserById: async (id: number): Promise<User> => {\r\n    const user = await apiClient.get<User>(`/users/${id}`);\r\n    return user;\r\n  },\r\n\r\n  createUser: async (user: CreateUserRequest): Promise<User> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest: Record<string, any> = {\r\n      name: user.name,\r\n      email: user.email,\r\n      password: user.password,\r\n      tenant_id: user.tenant_id || 1, // Default to tenant_id 1 if not provided\r\n      branch_id: user.branch_id,\r\n      location_id: user.location_id,\r\n      phone: user.phone,\r\n      language_preference: user.language_preference || \"English\",\r\n      created_by: user.created_by, // Add created_by field\r\n    };\r\n\r\n    // Handle role_id\r\n    if (user.role_id) {\r\n      apiRequest.role_id = user.role_id;\r\n    } else if (user.role_name) {\r\n      // For backward compatibility, log a warning but don't send role parameter\r\n      console.warn(\"role_name is deprecated, please use role_id instead\");\r\n    }\r\n\r\n    console.log(\"Creating user with data:\", apiRequest);\r\n    return apiClient.post(\"/users\", apiRequest);\r\n  },\r\n\r\n  updateUser: async (id: number, user: UpdateUserRequest): Promise<User> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest: Record<string, any> = {};\r\n\r\n    // Only include fields that are provided\r\n    if (user.name !== undefined) apiRequest.name = user.name;\r\n    if (user.email !== undefined) apiRequest.email = user.email;\r\n    if (user.role_id !== undefined) apiRequest.role_id = user.role_id;\r\n    if (user.branch_id !== undefined) apiRequest.branch_id = user.branch_id;\r\n    if (user.location_id !== undefined)\r\n      apiRequest.location_id = user.location_id;\r\n    if (user.phone !== undefined) apiRequest.phone = user.phone;\r\n    if (user.language_preference !== undefined)\r\n      apiRequest.language_preference = user.language_preference;\r\n\r\n    return apiClient.put(`/users/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteUser: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/users/${id}`);\r\n  },\r\n\r\n  updateUserStatus: async (\r\n    id: number,\r\n    status: UpdateUserStatusRequest\r\n  ): Promise<User> => {\r\n    try {\r\n      console.log(`Updating user ${id} status to ${status.status}`);\r\n\r\n      // Use the dedicated status endpoint\r\n      const response = await apiClient.put(`/users/${id}/status`, {\r\n        status: status.status\r\n      });\r\n\r\n      console.log(`Successfully updated user ${id} status to ${status.status}`, response);\r\n\r\n      // Verify the response has the expected status\r\n      const hasExpectedStatus = status.status === 'inactive' ?\r\n        !!response.deleted_at :\r\n        !response.deleted_at;\r\n\r\n      if (!hasExpectedStatus) {\r\n        console.warn(`API returned inconsistent status for user ${id}. Forcing correct status.`);\r\n\r\n        // Force the correct status in the response\r\n        if (status.status === 'inactive') {\r\n          response.deleted_at = new Date().toISOString();\r\n        } else {\r\n          response.deleted_at = null;\r\n        }\r\n\r\n        // First try a direct PUT request to the user endpoint as a fallback\r\n        try {\r\n          console.log(`Attempting direct PUT request to fix status for user ${id}`);\r\n          const putResponse = await apiClient.put(`/users/${id}`, {\r\n            deleted_at: status.status === 'inactive' ? new Date().toISOString() : null\r\n          });\r\n\r\n          console.log(`Direct PUT request completed for user ${id}`, putResponse);\r\n\r\n          // Use the PUT response if it has the correct status\r\n          const putHasCorrectStatus = status.status === 'inactive' ?\r\n            !!putResponse.deleted_at :\r\n            !putResponse.deleted_at;\r\n\r\n          if (putHasCorrectStatus) {\r\n            return putResponse;\r\n          }\r\n\r\n          // If PUT doesn't work, try PATCH as a last resort\r\n          console.log(`PUT request didn't set the correct status. Trying PATCH as a last resort.`);\r\n          const patchResponse = await apiClient.patch(`/users/${id}`, {\r\n            deleted_at: status.status === 'inactive' ? new Date().toISOString() : null\r\n          });\r\n\r\n          console.log(`Direct PATCH request completed for user ${id}`, patchResponse);\r\n\r\n          // Use the PATCH response if it has the correct status\r\n          const patchHasCorrectStatus = status.status === 'inactive' ?\r\n            !!patchResponse.deleted_at :\r\n            !patchResponse.deleted_at;\r\n\r\n          if (patchHasCorrectStatus) {\r\n            return patchResponse;\r\n          }\r\n        } catch (error) {\r\n          console.error(`Direct update requests failed for user ${id}:`, error);\r\n          // Continue with the forced response\r\n        }\r\n      }\r\n\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Failed to update user ${id} status to ${status.status}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAWO,MAAM,cAAc;IACzB,UAAU,OACR;QAEA,oCAAoC;QACpC,MAAM,YAAY;YAAE,GAAG,MAAM;QAAC;QAE9B,yEAAyE;QACzE,IAAI,UAAU,MAAM,EAAE;YACpB,IAAI,UAAU,MAAM,KAAK,UAAU;gBACjC,UAAU,OAAO,GAAG;YACtB,OAAO,IAAI,UAAU,MAAM,KAAK,YAAY;gBAC1C,UAAU,OAAO,GAAG;YACtB;YACA,OAAO,UAAU,MAAM,EAAE,sDAAsD;QACjF;QAEA,4CAA4C;QAC5C,UAAU,EAAE,GAAG,IAAI,OAAO,OAAO;QAEjC,gCAAgC;QAChC,QAAQ,GAAG,CAAC,CAAC,+BAA+B,CAAC,EAAE;QAE/C,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAA0B,UAAU;gBAAE,QAAQ;YAAU;YAC5F,QAAQ,GAAG,CAAC,CAAC,2BAA2B,CAAC,EAAE;gBACzC,MAAM,SAAS,UAAU,EAAE;gBAC3B,YAAY,SAAS,UAAU,EAAE;gBACjC,OAAO,SAAS,UAAU,EAAE;gBAC5B,OAAO,SAAS,IAAI,EAAE;YACxB;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,CAAC,EAAE;YACvC,MAAM;QACR;IACF;IAEA,aAAa,OAAO;QAClB,MAAM,OAAO,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAO,CAAC,OAAO,EAAE,IAAI;QACrD,OAAO;IACT;IAEA,YAAY,OAAO;QACjB,sDAAsD;QACtD,MAAM,aAAkC;YACtC,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;YACvB,WAAW,KAAK,SAAS,IAAI;YAC7B,WAAW,KAAK,SAAS;YACzB,aAAa,KAAK,WAAW;YAC7B,OAAO,KAAK,KAAK;YACjB,qBAAqB,KAAK,mBAAmB,IAAI;YACjD,YAAY,KAAK,UAAU;QAC7B;QAEA,iBAAiB;QACjB,IAAI,KAAK,OAAO,EAAE;YAChB,WAAW,OAAO,GAAG,KAAK,OAAO;QACnC,OAAO,IAAI,KAAK,SAAS,EAAE;YACzB,0EAA0E;YAC1E,QAAQ,IAAI,CAAC;QACf;QAEA,QAAQ,GAAG,CAAC,4BAA4B;QACxC,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,UAAU;IAClC;IAEA,YAAY,OAAO,IAAY;QAC7B,sDAAsD;QACtD,MAAM,aAAkC,CAAC;QAEzC,wCAAwC;QACxC,IAAI,KAAK,IAAI,KAAK,WAAW,WAAW,IAAI,GAAG,KAAK,IAAI;QACxD,IAAI,KAAK,KAAK,KAAK,WAAW,WAAW,KAAK,GAAG,KAAK,KAAK;QAC3D,IAAI,KAAK,OAAO,KAAK,WAAW,WAAW,OAAO,GAAG,KAAK,OAAO;QACjE,IAAI,KAAK,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,KAAK,SAAS;QACvE,IAAI,KAAK,WAAW,KAAK,WACvB,WAAW,WAAW,GAAG,KAAK,WAAW;QAC3C,IAAI,KAAK,KAAK,KAAK,WAAW,WAAW,KAAK,GAAG,KAAK,KAAK;QAC3D,IAAI,KAAK,mBAAmB,KAAK,WAC/B,WAAW,mBAAmB,GAAG,KAAK,mBAAmB;QAE3D,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;IACvC;IAEA,YAAY,OAAO;QACjB,OAAO,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;IACxC;IAEA,kBAAkB,OAChB,IACA;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,WAAW,EAAE,OAAO,MAAM,EAAE;YAE5D,oCAAoC;YACpC,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,EAAE;gBAC1D,QAAQ,OAAO,MAAM;YACvB;YAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,GAAG,WAAW,EAAE,OAAO,MAAM,EAAE,EAAE;YAE1E,8CAA8C;YAC9C,MAAM,oBAAoB,OAAO,MAAM,KAAK,aAC1C,CAAC,CAAC,SAAS,UAAU,GACrB,CAAC,SAAS,UAAU;YAEtB,IAAI,CAAC,mBAAmB;gBACtB,QAAQ,IAAI,CAAC,CAAC,0CAA0C,EAAE,GAAG,yBAAyB,CAAC;gBAEvF,2CAA2C;gBAC3C,IAAI,OAAO,MAAM,KAAK,YAAY;oBAChC,SAAS,UAAU,GAAG,IAAI,OAAO,WAAW;gBAC9C,OAAO;oBACL,SAAS,UAAU,GAAG;gBACxB;gBAEA,oEAAoE;gBACpE,IAAI;oBACF,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,IAAI;oBACxE,MAAM,cAAc,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;wBACtD,YAAY,OAAO,MAAM,KAAK,aAAa,IAAI,OAAO,WAAW,KAAK;oBACxE;oBAEA,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,IAAI,EAAE;oBAE3D,oDAAoD;oBACpD,MAAM,sBAAsB,OAAO,MAAM,KAAK,aAC5C,CAAC,CAAC,YAAY,UAAU,GACxB,CAAC,YAAY,UAAU;oBAEzB,IAAI,qBAAqB;wBACvB,OAAO;oBACT;oBAEA,kDAAkD;oBAClD,QAAQ,GAAG,CAAC,CAAC,yEAAyE,CAAC;oBACvF,MAAM,gBAAgB,MAAM,8HAAA,CAAA,UAAS,CAAC,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;wBAC1D,YAAY,OAAO,MAAM,KAAK,aAAa,IAAI,OAAO,WAAW,KAAK;oBACxE;oBAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,IAAI,EAAE;oBAE7D,sDAAsD;oBACtD,MAAM,wBAAwB,OAAO,MAAM,KAAK,aAC9C,CAAC,CAAC,cAAc,UAAU,GAC1B,CAAC,cAAc,UAAU;oBAE3B,IAAI,uBAAuB;wBACzB,OAAO;oBACT;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,GAAG,CAAC,CAAC,EAAE;gBAC/D,oCAAoC;gBACtC;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,WAAW,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE;YACzE,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 6532, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/users/hooks/use-users.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { userService } from \"@/features/users/api/user-service\";\r\nimport {\r\n  CreateUserRequest,\r\n  UpdateUserRequest,\r\n  UpdateUserStatusRequest,\r\n} from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\n\r\nexport function useUsers(params?: Record<string, any>) {\r\n  return useQuery({\r\n    queryKey: [\"users\", params],\r\n    queryFn: async () => {\r\n      const response = await userService.getUsers(params);\r\n\r\n      // Normalize each user in the response\r\n      if (response.data) {\r\n        response.data = response.data.map((user) => {\r\n          // Import the normalizeUser function from @/types/user\r\n          const { normalizeUser } = require(\"@/types/user\");\r\n          return normalizeUser(user);\r\n        });\r\n      }\r\n      return response;\r\n    },\r\n    // Standard React Query settings for SPA\r\n    refetchOnWindowFocus: false,\r\n    // Keep previous data while loading new data to prevent UI jumps\r\n    placeholderData: \"keep\" as any,\r\n    // Standard stale time\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n  });\r\n}\r\n\r\nexport function useUser(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"users\", id],\r\n    queryFn: async () => {\r\n      const user = await userService.getUserById(id);\r\n      // Import the normalizeUser function from @/types/user\r\n      const { normalizeUser } = require(\"@/types/user\");\r\n      return normalizeUser(user);\r\n    },\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateUser() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreateUserRequest) => userService.createUser(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"users\"] });\r\n      // Toast notifications are now handled in the form component\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Error in user creation mutation:\", error);\r\n      // Toast notifications are now handled in the form component\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateUser() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ id, data }: { id: number; data: UpdateUserRequest }) =>\r\n      userService.updateUser(id, data),\r\n    onSuccess: (data, variables) => {\r\n      queryClient.setQueryData([\"users\", variables.id], data);\r\n      queryClient.invalidateQueries({ queryKey: [\"users\"] });\r\n      // Toast notifications are now handled in the form component\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Error in user update mutation:\", error);\r\n      // Toast notifications are now handled in the form component\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteUser() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => userService.deleteUser(id),\r\n    onSuccess: (_, id) => {\r\n      queryClient.removeQueries({ queryKey: [\"users\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"users\"] });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateUserStatus() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({\r\n      id,\r\n      status,\r\n    }: {\r\n      id: number;\r\n      status: UpdateUserStatusRequest;\r\n    }) => userService.updateUserStatus(id, status),\r\n    onSuccess: (data, variables) => {\r\n      // Ensure the data has the correct status\r\n      const expectedStatus = variables.status.status;\r\n      const hasCorrectStatus =\r\n        expectedStatus === \"inactive\" ? !!data.deleted_at : !data.deleted_at;\r\n\r\n      if (!hasCorrectStatus) {\r\n        console.warn(\r\n          `API returned user with incorrect status. Expected ${expectedStatus}, ` +\r\n            `but deleted_at is ${\r\n              data.deleted_at ? \"set\" : \"not set\"\r\n            }. Forcing correct status.`\r\n        );\r\n\r\n        // Force the correct status\r\n        data.deleted_at =\r\n          expectedStatus === \"inactive\" ? new Date().toISOString() : null;\r\n      }\r\n\r\n      // Update the cache with the corrected data\r\n      queryClient.setQueryData([\"users\", variables.id], data);\r\n\r\n      // Force a refetch of all user data\r\n      queryClient.invalidateQueries({ queryKey: [\"users\"] });\r\n\r\n      // Also invalidate any queries that might include this user\r\n      queryClient.invalidateQueries({ queryKey: [\"user\", variables.id] });\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Error updating user status:\", error);\r\n      // Error handling is done in the component\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAMA;AAAA;AAAA;;AARA;;;AAUO,SAAS,SAAS,MAA4B;;IACnD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAS;SAAO;QAC3B,OAAO;iCAAE;gBACP,MAAM,WAAW,MAAM,qJAAA,CAAA,cAAW,CAAC,QAAQ,CAAC;gBAE5C,sCAAsC;gBACtC,IAAI,SAAS,IAAI,EAAE;oBACjB,SAAS,IAAI,GAAG,SAAS,IAAI,CAAC,GAAG;6CAAC,CAAC;4BACjC,sDAAsD;4BACtD,MAAM,EAAE,aAAa,EAAE;4BACvB,OAAO,cAAc;wBACvB;;gBACF;gBACA,OAAO;YACT;;QACA,wCAAwC;QACxC,sBAAsB;QACtB,gEAAgE;QAChE,iBAAiB;QACjB,sBAAsB;QACtB,WAAW,IAAI,KAAK;IACtB;AACF;GAvBgB;;QACP,8KAAA,CAAA,WAAQ;;;AAwBV,SAAS,QAAQ,EAAU;;IAChC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAS;SAAG;QACvB,OAAO;gCAAE;gBACP,MAAM,OAAO,MAAM,qJAAA,CAAA,cAAW,CAAC,WAAW,CAAC;gBAC3C,sDAAsD;gBACtD,MAAM,EAAE,aAAa,EAAE;gBACvB,OAAO,cAAc;YACvB;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IAXgB;;QACP,8KAAA,CAAA,WAAQ;;;AAYV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,OAA4B,qJAAA,CAAA,cAAW,CAAC,UAAU,CAAC;;QAChE,SAAS;yCAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAQ;gBAAC;YACpD,4DAA4D;YAC9D;;QACA,OAAO;yCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,oCAAoC;YAClD,4DAA4D;YAC9D;;IACF;AACF;IAdgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAA2C,GAChE,qJAAA,CAAA,cAAW,CAAC,UAAU,CAAC,IAAI;;QAC7B,SAAS;yCAAE,CAAC,MAAM;gBAChB,YAAY,YAAY,CAAC;oBAAC;oBAAS,UAAU,EAAE;iBAAC,EAAE;gBAClD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAQ;gBAAC;YACpD,4DAA4D;YAC9D;;QACA,OAAO;yCAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,kCAAkC;YAChD,4DAA4D;YAC9D;;IACF;AACF;IAhBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAeb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,CAAC,KAAe,qJAAA,CAAA,cAAW,CAAC,UAAU,CAAC;;QACnD,SAAS;yCAAE,CAAC,GAAG;gBACb,YAAY,aAAa,CAAC;oBAAE,UAAU;wBAAC;wBAAS;qBAAG;gBAAC;gBACpD,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAQ;gBAAC;YACtD;;IACF;AACF;IAVgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AASb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;+CAAE,CAAC,EACX,EAAE,EACF,MAAM,EAIP,GAAK,qJAAA,CAAA,cAAW,CAAC,gBAAgB,CAAC,IAAI;;QACvC,SAAS;+CAAE,CAAC,MAAM;gBAChB,yCAAyC;gBACzC,MAAM,iBAAiB,UAAU,MAAM,CAAC,MAAM;gBAC9C,MAAM,mBACJ,mBAAmB,aAAa,CAAC,CAAC,KAAK,UAAU,GAAG,CAAC,KAAK,UAAU;gBAEtE,IAAI,CAAC,kBAAkB;oBACrB,QAAQ,IAAI,CACV,CAAC,kDAAkD,EAAE,eAAe,EAAE,CAAC,GACrE,CAAC,kBAAkB,EACjB,KAAK,UAAU,GAAG,QAAQ,UAC3B,yBAAyB,CAAC;oBAG/B,2BAA2B;oBAC3B,KAAK,UAAU,GACb,mBAAmB,aAAa,IAAI,OAAO,WAAW,KAAK;gBAC/D;gBAEA,2CAA2C;gBAC3C,YAAY,YAAY,CAAC;oBAAC;oBAAS,UAAU,EAAE;iBAAC,EAAE;gBAElD,mCAAmC;gBACnC,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAQ;gBAAC;gBAEpD,2DAA2D;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAQ,UAAU,EAAE;qBAAC;gBAAC;YACnE;;QACA,OAAO;+CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,0CAA0C;YAC5C;;IACF;AACF;IA5CgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 6763, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/users/components/status-badge.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { CheckCircle, Clock, XCircle } from \"lucide-react\";\r\n\r\ninterface StatusBadgeProps {\r\n  status: \"active\" | \"inactive\";\r\n  showIcon?: boolean;\r\n  size?: \"sm\" | \"md\" | \"lg\";\r\n  className?: string;\r\n}\r\n\r\nexport function StatusBadge({\r\n  status,\r\n  showIcon = true,\r\n  size = \"md\",\r\n  className,\r\n}: StatusBadgeProps) {\r\n  const isActive = status === \"active\";\r\n  \r\n  // Determine the variant based on status\r\n  const variant = isActive ? \"success\" : \"destructive\";\r\n  \r\n  // Determine the icon based on status\r\n  const Icon = isActive ? CheckCircle : XCircle;\r\n  \r\n  // Determine the size classes\r\n  const sizeClasses = {\r\n    sm: \"text-xs py-0 px-2 h-5\",\r\n    md: \"text-sm py-0.5 px-2.5\",\r\n    lg: \"text-base py-1 px-3\",\r\n  };\r\n  \r\n  // Determine the icon size\r\n  const iconSize = {\r\n    sm: 12,\r\n    md: 14,\r\n    lg: 16,\r\n  };\r\n  \r\n  return (\r\n    <Badge\r\n      variant={variant}\r\n      className={cn(\r\n        \"font-medium\",\r\n        sizeClasses[size],\r\n        showIcon && \"pl-1.5\",\r\n        className\r\n      )}\r\n    >\r\n      {showIcon && (\r\n        <Icon\r\n          className={cn(\"mr-1\", size === \"sm\" ? \"mt-0\" : \"mt-[1px]\")}\r\n          size={iconSize[size]}\r\n        />\r\n      )}\r\n      {isActive ? \"Active\" : \"Inactive\"}\r\n    </Badge>\r\n  );\r\n}\r\n\r\n// Component to show a pending status\r\nexport function PendingStatusBadge({\r\n  showIcon = true,\r\n  size = \"md\",\r\n  className,\r\n}: Omit<StatusBadgeProps, \"status\">) {\r\n  // Determine the size classes\r\n  const sizeClasses = {\r\n    sm: \"text-xs py-0 px-2 h-5\",\r\n    md: \"text-sm py-0.5 px-2.5\",\r\n    lg: \"text-base py-1 px-3\",\r\n  };\r\n  \r\n  // Determine the icon size\r\n  const iconSize = {\r\n    sm: 12,\r\n    md: 14,\r\n    lg: 16,\r\n  };\r\n  \r\n  return (\r\n    <Badge\r\n      variant=\"secondary\"\r\n      className={cn(\r\n        \"font-medium\",\r\n        sizeClasses[size],\r\n        showIcon && \"pl-1.5\",\r\n        className\r\n      )}\r\n    >\r\n      {showIcon && (\r\n        <Clock\r\n          className={cn(\"mr-1 animate-pulse\", size === \"sm\" ? \"mt-0\" : \"mt-[1px]\")}\r\n          size={iconSize[size]}\r\n        />\r\n      )}\r\n      Updating...\r\n    </Badge>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAaO,SAAS,YAAY,EAC1B,MAAM,EACN,WAAW,IAAI,EACf,OAAO,IAAI,EACX,SAAS,EACQ;IACjB,MAAM,WAAW,WAAW;IAE5B,wCAAwC;IACxC,MAAM,UAAU,WAAW,YAAY;IAEvC,qCAAqC;IACrC,MAAM,OAAO,WAAW,8NAAA,CAAA,cAAW,GAAG,+MAAA,CAAA,UAAO;IAE7C,6BAA6B;IAC7B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,0BAA0B;IAC1B,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,SAAS;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,eACA,WAAW,CAAC,KAAK,EACjB,YAAY,UACZ;;YAGD,0BACC,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,SAAS,OAAO,SAAS;gBAC/C,MAAM,QAAQ,CAAC,KAAK;;;;;;YAGvB,WAAW,WAAW;;;;;;;AAG7B;KA/CgB;AAkDT,SAAS,mBAAmB,EACjC,WAAW,IAAI,EACf,OAAO,IAAI,EACX,SAAS,EACwB;IACjC,6BAA6B;IAC7B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,0BAA0B;IAC1B,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,SAAQ;QACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,eACA,WAAW,CAAC,KAAK,EACjB,YAAY,UACZ;;YAGD,0BACC,6LAAC,uMAAA,CAAA,QAAK;gBACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB,SAAS,OAAO,SAAS;gBAC7D,MAAM,QAAQ,CAAC,KAAK;;;;;;YAEtB;;;;;;;AAIR;MAtCgB", "debugId": null}}, {"offset": {"line": 6863, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/excel-export.ts"], "sourcesContent": ["import * as XLSX from 'xlsx';\r\nimport { saveAs } from 'file-saver';\r\nimport { formatCurrency } from './utils';\r\n\r\n/**\r\n * Format a date for display in Excel\r\n */\r\nfunction formatExcelDate(date: Date | string): string {\r\n  if (!date) return '';\r\n  const d = typeof date === 'string' ? new Date(date) : date;\r\n  return d.toLocaleDateString('en-US', {\r\n    year: 'numeric',\r\n    month: 'short',\r\n    day: 'numeric'\r\n  });\r\n}\r\n\r\n/**\r\n * Exports data to an Excel file\r\n * @param data The data to export\r\n * @param fileName The name of the file to save (without extension)\r\n * @param sheetName The name of the sheet in the Excel file\r\n * @param excludeColumns Array of column keys to exclude from the export\r\n */\r\nexport function exportToExcel<T extends Record<string, any>>(\r\n  data: T[],\r\n  fileName: string,\r\n  sheetName: string = 'Sheet1',\r\n  excludeColumns: string[] = ['tenant_id']\r\n): void {\r\n  if (!data || data.length === 0) {\r\n    console.warn('No data to export');\r\n    return;\r\n  }\r\n\r\n  try {\r\n    // Filter out excluded columns\r\n    const filteredData = data.map(item => {\r\n      const filteredItem: Record<string, any> = {};\r\n      Object.keys(item).forEach(key => {\r\n        // Check if the key or its lowercase/uppercase variant is in the excluded columns\r\n        const keyLower = key.toLowerCase();\r\n        const isExcluded = excludeColumns.some(\r\n          col => col === key || col.toLowerCase() === keyLower\r\n        );\r\n\r\n        if (!isExcluded) {\r\n          // Handle nested objects\r\n          if (item[key] && typeof item[key] === 'object' && !Array.isArray(item[key]) && item[key] !== null) {\r\n            // For objects like Branch, Role, etc., just use their name property if available\r\n            if (item[key].name) {\r\n              filteredItem[key] = item[key].name;\r\n            } else {\r\n              // Otherwise stringify the object\r\n              filteredItem[key] = JSON.stringify(item[key]);\r\n            }\r\n          } else if (Array.isArray(item[key])) {\r\n            // For arrays, join the values or stringify\r\n            if (item[key].length > 0 && typeof item[key][0] === 'object' && item[key][0].name) {\r\n              // If array of objects with name property, join the names\r\n              filteredItem[key] = item[key].map((i: any) => i.name).join(', ');\r\n            } else {\r\n              // Otherwise stringify the array\r\n              filteredItem[key] = JSON.stringify(item[key]);\r\n            }\r\n          } else if (item[key] instanceof Date) {\r\n            // Format dates in a more readable format: YYYY-MM-DD HH:MM:SS\r\n            const date = new Date(item[key]);\r\n            filteredItem[key] = date.toLocaleString();\r\n          } else if (typeof item[key] === 'string' && /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}/.test(item[key])) {\r\n            // Handle ISO date strings\r\n            const date = new Date(item[key]);\r\n            if (!isNaN(date.getTime())) {\r\n              filteredItem[key] = date.toLocaleString();\r\n            } else {\r\n              filteredItem[key] = item[key];\r\n            }\r\n          } else if (typeof item[key] === 'boolean') {\r\n            // Format booleans as 'Yes' or 'No' for better readability\r\n            filteredItem[key] = item[key] ? 'Yes' : 'No';\r\n          } else {\r\n            // For other primitive values, use as is\r\n            filteredItem[key] = item[key];\r\n          }\r\n        }\r\n      });\r\n      return filteredItem;\r\n    });\r\n\r\n    // Format column headers to be more readable\r\n    const formattedData = filteredData.map(item => {\r\n      const formattedItem: Record<string, any> = {};\r\n\r\n      // Define the order of columns (prioritize important fields)\r\n      const priorityKeys = ['id', 'name', 'email', 'phone', 'status', 'role', 'branch', 'created_at'];\r\n\r\n      // First add priority keys in order\r\n      priorityKeys.forEach(priorityKey => {\r\n        Object.keys(item).forEach(key => {\r\n          if (key.toLowerCase() === priorityKey) {\r\n            // Convert snake_case to Title Case\r\n            const formattedKey = key\r\n              .split('_')\r\n              .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n              .join(' ');\r\n            formattedItem[formattedKey] = item[key];\r\n          }\r\n        });\r\n      });\r\n\r\n      // Then add remaining keys\r\n      Object.keys(item).forEach(key => {\r\n        if (!priorityKeys.includes(key.toLowerCase()) && !formattedItem[key]) {\r\n          // Convert snake_case to Title Case\r\n          const formattedKey = key\r\n            .split('_')\r\n            .map(word => word.charAt(0).toUpperCase() + word.slice(1))\r\n            .join(' ');\r\n          formattedItem[formattedKey] = item[key];\r\n        }\r\n      });\r\n\r\n      return formattedItem;\r\n    });\r\n\r\n    // Create a worksheet\r\n    const worksheet = XLSX.utils.json_to_sheet(formattedData);\r\n\r\n    // Create a workbook\r\n    const workbook = XLSX.utils.book_new();\r\n    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);\r\n\r\n    // Generate Excel file\r\n    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });\r\n    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });\r\n\r\n    // Save the file\r\n    saveAs(blob, `${fileName}.xlsx`);\r\n  } catch (error) {\r\n    console.error('Error exporting to Excel:', error);\r\n  }\r\n}\r\n\r\n/**\r\n * Export cash status report to Excel\r\n * @param data The cash status report data\r\n * @param dateRange The date range for the report\r\n * @returns The filename of the exported Excel file\r\n */\r\nexport function exportCashStatusToExcel(data: any, dateRange: { startDate: Date; endDate: Date }): string {\r\n  if (!data) {\r\n    console.warn('No data to export');\r\n    return '';\r\n  }\r\n\r\n  try {\r\n    // Create a new workbook\r\n    const workbook = XLSX.utils.book_new();\r\n\r\n    // Format the date range for the filename\r\n    const startDateStr = formatExcelDate(dateRange.startDate);\r\n    const endDateStr = formatExcelDate(dateRange.endDate);\r\n\r\n    // Create a summary sheet\r\n    if (data.summary) {\r\n      const summaryData = [{\r\n        'Report Period': `${startDateStr} to ${endDateStr}`,\r\n        'Opening Cash': formatCurrency(data.summary.totalOpeningCash || 0),\r\n        'Cash Inflows': formatCurrency(data.summary.totalInflows || 0),\r\n        'Cash Outflows': formatCurrency(data.summary.totalOutflows || 0),\r\n        'Net Change': formatCurrency(data.summary.netCashChange || 0),\r\n      }];\r\n\r\n      const summarySheet = XLSX.utils.json_to_sheet(summaryData);\r\n      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');\r\n    }\r\n\r\n    // Create Cash Inflows sheet\r\n    if (data.cashInflows) {\r\n      // Prepare data for Cash Inflows\r\n      const inflowsData = [];\r\n\r\n      // Add section header for Cash Sales\r\n      if (data.cashInflows.cashSales?.data?.length > 0) {\r\n        inflowsData.push({ 'Category': 'CASH SALES', 'Amount': formatCurrency(data.cashInflows.cashSales.total || 0) });\r\n        inflowsData.push({ 'Category': '', 'Amount': '' }); // Empty row for spacing\r\n\r\n        // Add column headers\r\n        inflowsData.push({\r\n          'Category': 'Branch',\r\n          'Region': 'Region',\r\n          'Amount': 'Amount',\r\n          'Transactions': 'Transactions'\r\n        });\r\n\r\n        // Add data rows\r\n        data.cashInflows.cashSales.data.forEach((item: any) => {\r\n          inflowsData.push({\r\n            'Category': item.branch_name || '',\r\n            'Region': item.region_name || '',\r\n            'Amount': formatCurrency(item.amount || 0),\r\n            'Transactions': item.transaction_count || 0\r\n          });\r\n        });\r\n\r\n        // Add spacing\r\n        inflowsData.push({ 'Category': '', 'Amount': '' });\r\n      }\r\n\r\n      // Add section header for MPESA Deposits\r\n      if (data.cashInflows.mpesaDeposits?.data?.length > 0) {\r\n        inflowsData.push({ 'Category': 'MPESA DEPOSITS', 'Amount': formatCurrency(data.cashInflows.mpesaDeposits.total || 0) });\r\n        inflowsData.push({ 'Category': '', 'Amount': '' }); // Empty row for spacing\r\n\r\n        // Add column headers\r\n        inflowsData.push({\r\n          'Category': 'Branch',\r\n          'Region': 'Region',\r\n          'Amount': 'Amount',\r\n          'Transactions': 'Transactions'\r\n        });\r\n\r\n        // Add data rows\r\n        data.cashInflows.mpesaDeposits.data.forEach((item: any) => {\r\n          inflowsData.push({\r\n            'Category': item.branch_name || '',\r\n            'Region': item.region_name || '',\r\n            'Amount': formatCurrency(item.amount || 0),\r\n            'Transactions': item.transaction_count || 0\r\n          });\r\n        });\r\n      }\r\n\r\n      // Create the sheet if we have data\r\n      if (inflowsData.length > 0) {\r\n        const inflowsSheet = XLSX.utils.json_to_sheet(inflowsData);\r\n        XLSX.utils.book_append_sheet(workbook, inflowsSheet, 'Cash Inflows');\r\n      }\r\n    }\r\n\r\n    // Create Cash Outflows sheet\r\n    if (data.cashOutflows) {\r\n      // Prepare data for Cash Outflows\r\n      const outflowsData = [];\r\n\r\n      // Add section header for Expenses\r\n      if (data.cashOutflows.expenses?.data?.length > 0) {\r\n        outflowsData.push({ 'Category': 'EXPENSES', 'Amount': formatCurrency(data.cashOutflows.expenses.total || 0) });\r\n        outflowsData.push({ 'Category': '', 'Amount': '' }); // Empty row for spacing\r\n\r\n        // Add column headers\r\n        outflowsData.push({\r\n          'Category': 'Branch',\r\n          'Region': 'Region',\r\n          'Amount': 'Amount',\r\n          'Transactions': 'Transactions'\r\n        });\r\n\r\n        // Add data rows\r\n        data.cashOutflows.expenses.data.forEach((item: any) => {\r\n          outflowsData.push({\r\n            'Category': item.branch_name || '',\r\n            'Region': item.region_name || '',\r\n            'Amount': formatCurrency(item.amount || 0),\r\n            'Transactions': item.transaction_count || 0\r\n          });\r\n        });\r\n\r\n        // Add spacing\r\n        outflowsData.push({ 'Category': '', 'Amount': '' });\r\n      }\r\n\r\n      // Add section header for Banking Deposits\r\n      if (data.cashOutflows.bankingDeposits?.data?.length > 0) {\r\n        outflowsData.push({ 'Category': 'BANKING DEPOSITS', 'Amount': formatCurrency(data.cashOutflows.bankingDeposits.total || 0) });\r\n        outflowsData.push({ 'Category': '', 'Amount': '' }); // Empty row for spacing\r\n\r\n        // Add column headers\r\n        outflowsData.push({\r\n          'Category': 'Branch',\r\n          'Region': 'Region',\r\n          'Amount': 'Amount',\r\n          'Transactions': 'Transactions'\r\n        });\r\n\r\n        // Add data rows\r\n        data.cashOutflows.bankingDeposits.data.forEach((item: any) => {\r\n          outflowsData.push({\r\n            'Category': item.branch_name || '',\r\n            'Region': item.region_name || '',\r\n            'Amount': formatCurrency(item.amount || 0),\r\n            'Transactions': item.transaction_count || 0\r\n          });\r\n        });\r\n      }\r\n\r\n      // Create the sheet if we have data\r\n      if (outflowsData.length > 0) {\r\n        const outflowsSheet = XLSX.utils.json_to_sheet(outflowsData);\r\n        XLSX.utils.book_append_sheet(workbook, outflowsSheet, 'Cash Outflows');\r\n      }\r\n    }\r\n\r\n    // Create By Region sheet\r\n    if (data.byRegion && data.byRegion.length > 0) {\r\n      const regionData = data.byRegion.map((region: any) => ({\r\n        'Region': region.name || '',\r\n        'Opening Cash': formatCurrency(region.summary.totalOpeningCash || 0),\r\n        'Cash Inflows': formatCurrency(region.summary.totalInflows || 0),\r\n        'Cash Outflows': formatCurrency(region.summary.totalOutflows || 0),\r\n        'Net Change': formatCurrency(region.summary.netCashChange || 0),\r\n      }));\r\n\r\n      const regionSheet = XLSX.utils.json_to_sheet(regionData);\r\n      XLSX.utils.book_append_sheet(workbook, regionSheet, 'By Region');\r\n    }\r\n\r\n    // Create By Branch sheet\r\n    if (data.byBranch && data.byBranch.length > 0) {\r\n      const branchData = data.byBranch.map((branch: any) => ({\r\n        'Branch': branch.name || '',\r\n        'Region': branch.region_name || '',\r\n        'Opening Cash': formatCurrency(branch.summary.totalOpeningCash || 0),\r\n        'Cash Inflows': formatCurrency(branch.summary.totalInflows || 0),\r\n        'Cash Outflows': formatCurrency(branch.summary.totalOutflows || 0),\r\n        'Net Change': formatCurrency(branch.summary.netCashChange || 0),\r\n      }));\r\n\r\n      const branchSheet = XLSX.utils.json_to_sheet(branchData);\r\n      XLSX.utils.book_append_sheet(workbook, branchSheet, 'By Branch');\r\n    }\r\n\r\n    // Generate filename\r\n    const filename = `Cash_Status_Report_${startDateStr.replace(/,/g, '')}_to_${endDateStr.replace(/,/g, '')}.xlsx`;\r\n\r\n    // Generate Excel file\r\n    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });\r\n    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });\r\n\r\n    // Save the file\r\n    saveAs(blob, filename);\r\n\r\n    return filename;\r\n  } catch (error) {\r\n    console.error('Error exporting Cash Status Report to Excel:', error);\r\n    return '';\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA;;CAEC,GACD,SAAS,gBAAgB,IAAmB;IAC1C,IAAI,CAAC,MAAM,OAAO;IAClB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB,CAAC,SAAS;QACnC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AASO,SAAS,cACd,IAAS,EACT,QAAgB,EAChB,YAAoB,QAAQ,EAC5B,iBAA2B;IAAC;CAAY;IAExC,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,QAAQ,IAAI,CAAC;QACb;IACF;IAEA,IAAI;QACF,8BAA8B;QAC9B,MAAM,eAAe,KAAK,GAAG,CAAC,CAAA;YAC5B,MAAM,eAAoC,CAAC;YAC3C,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;gBACxB,iFAAiF;gBACjF,MAAM,WAAW,IAAI,WAAW;gBAChC,MAAM,aAAa,eAAe,IAAI,CACpC,CAAA,MAAO,QAAQ,OAAO,IAAI,WAAW,OAAO;gBAG9C,IAAI,CAAC,YAAY;oBACf,wBAAwB;oBACxB,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM;wBACjG,iFAAiF;wBACjF,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;4BAClB,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI;wBACpC,OAAO;4BACL,iCAAiC;4BACjC,YAAY,CAAC,IAAI,GAAG,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI;wBAC9C;oBACF,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,GAAG;wBACnC,2CAA2C;wBAC3C,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,YAAY,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE;4BACjF,yDAAyD;4BACzD,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAW,EAAE,IAAI,EAAE,IAAI,CAAC;wBAC7D,OAAO;4BACL,gCAAgC;4BAChC,YAAY,CAAC,IAAI,GAAG,KAAK,SAAS,CAAC,IAAI,CAAC,IAAI;wBAC9C;oBACF,OAAO,IAAI,IAAI,CAAC,IAAI,YAAY,MAAM;wBACpC,8DAA8D;wBAC9D,MAAM,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI;wBAC/B,YAAY,CAAC,IAAI,GAAG,KAAK,cAAc;oBACzC,OAAO,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,YAAY,uCAAuC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;wBAClG,0BAA0B;wBAC1B,MAAM,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI;wBAC/B,IAAI,CAAC,MAAM,KAAK,OAAO,KAAK;4BAC1B,YAAY,CAAC,IAAI,GAAG,KAAK,cAAc;wBACzC,OAAO;4BACL,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;wBAC/B;oBACF,OAAO,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,WAAW;wBACzC,0DAA0D;wBAC1D,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,QAAQ;oBAC1C,OAAO;wBACL,wCAAwC;wBACxC,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;oBAC/B;gBACF;YACF;YACA,OAAO;QACT;QAEA,4CAA4C;QAC5C,MAAM,gBAAgB,aAAa,GAAG,CAAC,CAAA;YACrC,MAAM,gBAAqC,CAAC;YAE5C,4DAA4D;YAC5D,MAAM,eAAe;gBAAC;gBAAM;gBAAQ;gBAAS;gBAAS;gBAAU;gBAAQ;gBAAU;aAAa;YAE/F,mCAAmC;YACnC,aAAa,OAAO,CAAC,CAAA;gBACnB,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;oBACxB,IAAI,IAAI,WAAW,OAAO,aAAa;wBACrC,mCAAmC;wBACnC,MAAM,eAAe,IAClB,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;wBACR,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI;oBACzC;gBACF;YACF;YAEA,0BAA0B;YAC1B,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,CAAA;gBACxB,IAAI,CAAC,aAAa,QAAQ,CAAC,IAAI,WAAW,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE;oBACpE,mCAAmC;oBACnC,MAAM,eAAe,IAClB,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;oBACR,aAAa,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI;gBACzC;YACF;YAEA,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,YAAY,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC;QAE3C,oBAAoB;QACpB,MAAM,WAAW,gIAAA,CAAA,QAAU,CAAC,QAAQ;QACpC,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,WAAW;QAElD,sBAAsB;QACtB,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,QAAU,AAAD,EAAE,UAAU;YAAE,UAAU;YAAQ,MAAM;QAAQ;QAC3E,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAA2B;QAExE,gBAAgB;QAChB,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,MAAM,GAAG,SAAS,KAAK,CAAC;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;AACF;AAQO,SAAS,wBAAwB,IAAS,EAAE,SAA6C;IAC9F,IAAI,CAAC,MAAM;QACT,QAAQ,IAAI,CAAC;QACb,OAAO;IACT;IAEA,IAAI;QACF,wBAAwB;QACxB,MAAM,WAAW,gIAAA,CAAA,QAAU,CAAC,QAAQ;QAEpC,yCAAyC;QACzC,MAAM,eAAe,gBAAgB,UAAU,SAAS;QACxD,MAAM,aAAa,gBAAgB,UAAU,OAAO;QAEpD,yBAAyB;QACzB,IAAI,KAAK,OAAO,EAAE;YAChB,MAAM,cAAc;gBAAC;oBACnB,iBAAiB,GAAG,aAAa,IAAI,EAAE,YAAY;oBACnD,gBAAgB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,gBAAgB,IAAI;oBAChE,gBAAgB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,YAAY,IAAI;oBAC5D,iBAAiB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa,IAAI;oBAC9D,cAAc,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,OAAO,CAAC,aAAa,IAAI;gBAC7D;aAAE;YAEF,MAAM,eAAe,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC;YAC9C,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,cAAc;QACvD;QAEA,4BAA4B;QAC5B,IAAI,KAAK,WAAW,EAAE;YACpB,gCAAgC;YAChC,MAAM,cAAc,EAAE;YAEtB,oCAAoC;YACpC,IAAI,KAAK,WAAW,CAAC,SAAS,EAAE,MAAM,SAAS,GAAG;gBAChD,YAAY,IAAI,CAAC;oBAAE,YAAY;oBAAc,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW,CAAC,SAAS,CAAC,KAAK,IAAI;gBAAG;gBAC7G,YAAY,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG,IAAI,wBAAwB;gBAE5E,qBAAqB;gBACrB,YAAY,IAAI,CAAC;oBACf,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,gBAAgB;gBAClB;gBAEA,gBAAgB;gBAChB,KAAK,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACvC,YAAY,IAAI,CAAC;wBACf,YAAY,KAAK,WAAW,IAAI;wBAChC,UAAU,KAAK,WAAW,IAAI;wBAC9B,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,IAAI;wBACxC,gBAAgB,KAAK,iBAAiB,IAAI;oBAC5C;gBACF;gBAEA,cAAc;gBACd,YAAY,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG;YAClD;YAEA,wCAAwC;YACxC,IAAI,KAAK,WAAW,CAAC,aAAa,EAAE,MAAM,SAAS,GAAG;gBACpD,YAAY,IAAI,CAAC;oBAAE,YAAY;oBAAkB,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW,CAAC,aAAa,CAAC,KAAK,IAAI;gBAAG;gBACrH,YAAY,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG,IAAI,wBAAwB;gBAE5E,qBAAqB;gBACrB,YAAY,IAAI,CAAC;oBACf,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,gBAAgB;gBAClB;gBAEA,gBAAgB;gBAChB,KAAK,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC3C,YAAY,IAAI,CAAC;wBACf,YAAY,KAAK,WAAW,IAAI;wBAChC,UAAU,KAAK,WAAW,IAAI;wBAC9B,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,IAAI;wBACxC,gBAAgB,KAAK,iBAAiB,IAAI;oBAC5C;gBACF;YACF;YAEA,mCAAmC;YACnC,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,MAAM,eAAe,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC;gBAC9C,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,cAAc;YACvD;QACF;QAEA,6BAA6B;QAC7B,IAAI,KAAK,YAAY,EAAE;YACrB,iCAAiC;YACjC,MAAM,eAAe,EAAE;YAEvB,kCAAkC;YAClC,IAAI,KAAK,YAAY,CAAC,QAAQ,EAAE,MAAM,SAAS,GAAG;gBAChD,aAAa,IAAI,CAAC;oBAAE,YAAY;oBAAY,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY,CAAC,QAAQ,CAAC,KAAK,IAAI;gBAAG;gBAC5G,aAAa,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG,IAAI,wBAAwB;gBAE7E,qBAAqB;gBACrB,aAAa,IAAI,CAAC;oBAChB,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,gBAAgB;gBAClB;gBAEA,gBAAgB;gBAChB,KAAK,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBACvC,aAAa,IAAI,CAAC;wBAChB,YAAY,KAAK,WAAW,IAAI;wBAChC,UAAU,KAAK,WAAW,IAAI;wBAC9B,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,IAAI;wBACxC,gBAAgB,KAAK,iBAAiB,IAAI;oBAC5C;gBACF;gBAEA,cAAc;gBACd,aAAa,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG;YACnD;YAEA,0CAA0C;YAC1C,IAAI,KAAK,YAAY,CAAC,eAAe,EAAE,MAAM,SAAS,GAAG;gBACvD,aAAa,IAAI,CAAC;oBAAE,YAAY;oBAAoB,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY,CAAC,eAAe,CAAC,KAAK,IAAI;gBAAG;gBAC3H,aAAa,IAAI,CAAC;oBAAE,YAAY;oBAAI,UAAU;gBAAG,IAAI,wBAAwB;gBAE7E,qBAAqB;gBACrB,aAAa,IAAI,CAAC;oBAChB,YAAY;oBACZ,UAAU;oBACV,UAAU;oBACV,gBAAgB;gBAClB;gBAEA,gBAAgB;gBAChB,KAAK,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAC9C,aAAa,IAAI,CAAC;wBAChB,YAAY,KAAK,WAAW,IAAI;wBAChC,UAAU,KAAK,WAAW,IAAI;wBAC9B,UAAU,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,MAAM,IAAI;wBACxC,gBAAgB,KAAK,iBAAiB,IAAI;oBAC5C;gBACF;YACF;YAEA,mCAAmC;YACnC,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,MAAM,gBAAgB,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC;gBAC/C,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,eAAe;YACxD;QACF;QAEA,yBAAyB;QACzB,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC7C,MAAM,aAAa,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;oBACrD,UAAU,OAAO,IAAI,IAAI;oBACzB,gBAAgB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,gBAAgB,IAAI;oBAClE,gBAAgB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,YAAY,IAAI;oBAC9D,iBAAiB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa,IAAI;oBAChE,cAAc,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa,IAAI;gBAC/D,CAAC;YAED,MAAM,cAAc,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC;YAC7C,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,aAAa;QACtD;QAEA,yBAAyB;QACzB,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC7C,MAAM,aAAa,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAgB,CAAC;oBACrD,UAAU,OAAO,IAAI,IAAI;oBACzB,UAAU,OAAO,WAAW,IAAI;oBAChC,gBAAgB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,gBAAgB,IAAI;oBAClE,gBAAgB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,YAAY,IAAI;oBAC9D,iBAAiB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa,IAAI;oBAChE,cAAc,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO,CAAC,aAAa,IAAI;gBAC/D,CAAC;YAED,MAAM,cAAc,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC;YAC7C,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,UAAU,aAAa;QACtD;QAEA,oBAAoB;QACpB,MAAM,WAAW,CAAC,mBAAmB,EAAE,aAAa,OAAO,CAAC,MAAM,IAAI,IAAI,EAAE,WAAW,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC;QAE/G,sBAAsB;QACtB,MAAM,cAAc,CAAA,GAAA,gIAAA,CAAA,QAAU,AAAD,EAAE,UAAU;YAAE,UAAU;YAAQ,MAAM;QAAQ;QAC3E,MAAM,OAAO,IAAI,KAAK;YAAC;SAAY,EAAE;YAAE,MAAM;QAA2B;QAExE,gBAAgB;QAChB,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QAEb,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 7213, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-debounced-search.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\n\r\ninterface UseDebouncedSearchOptions {\r\n  initialValue?: string;\r\n  delay?: number;\r\n  minLength?: number;\r\n  onSearch?: (value: string) => void;\r\n  onTyping?: (value: string) => void;\r\n  onClear?: () => void;\r\n}\r\n\r\n/**\r\n * Hook for debounced search input\r\n *\r\n * @param options Configuration options\r\n * @returns Search state and handlers\r\n */\r\nexport function useDebouncedSearch({\r\n  initialValue = \"\",\r\n  delay = 300,\r\n  minLength = 0,\r\n  onSearch,\r\n  onTyping,\r\n  onClear,\r\n}: UseDebouncedSearchOptions = {}) {\r\n  const [inputValue, setInputValue] = useState(initialValue);\r\n  const [debouncedValue, setDebouncedValue] = useState(initialValue);\r\n  const [isSearching, setIsSearching] = useState(false);\r\n  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);\r\n  const previousValueRef = useRef(initialValue);\r\n\r\n  // Clear the debounce timer on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (debounceTimerRef.current) {\r\n        clearTimeout(debounceTimerRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Update debounced value after delay\r\n  useEffect(() => {\r\n    // Skip if the value hasn't changed\r\n    if (inputValue === previousValueRef.current) {\r\n      return;\r\n    }\r\n\r\n    // Call onTyping callback immediately\r\n    if (onTyping) {\r\n      onTyping(inputValue);\r\n    }\r\n\r\n    // Clear any existing timer\r\n    if (debounceTimerRef.current) {\r\n      clearTimeout(debounceTimerRef.current);\r\n    }\r\n\r\n    // Set searching state\r\n    setIsSearching(inputValue.length >= minLength);\r\n\r\n    // Set a new timer\r\n    debounceTimerRef.current = setTimeout(() => {\r\n      setDebouncedValue(inputValue);\r\n      previousValueRef.current = inputValue;\r\n\r\n      // Only trigger search if value meets minimum length\r\n      if (inputValue.length >= minLength) {\r\n        if (onSearch) {\r\n          onSearch(inputValue);\r\n        }\r\n      } else if (inputValue === \"\" && onClear) {\r\n        onClear();\r\n      }\r\n\r\n      setIsSearching(false);\r\n    }, delay);\r\n  }, [inputValue, delay, minLength, onSearch, onTyping, onClear]);\r\n\r\n  // Handle input change\r\n  const handleChange = useCallback((value: string) => {\r\n    setInputValue(value);\r\n  }, []);\r\n\r\n  // Clear search\r\n  const clearSearch = useCallback(() => {\r\n    setInputValue(\"\");\r\n    setDebouncedValue(\"\");\r\n    previousValueRef.current = \"\";\r\n\r\n    if (onClear) {\r\n      onClear();\r\n    }\r\n  }, [onClear]);\r\n\r\n  // Reset to initial value\r\n  const resetSearch = useCallback(() => {\r\n    setInputValue(initialValue);\r\n    setDebouncedValue(initialValue);\r\n    previousValueRef.current = initialValue;\r\n\r\n    if (initialValue === \"\" && onClear) {\r\n      onClear();\r\n    } else if (initialValue.length >= minLength && onSearch) {\r\n      onSearch(initialValue);\r\n    }\r\n  }, [initialValue, minLength, onSearch, onClear]);\r\n\r\n  // Force immediate search\r\n  const forceSearch = useCallback(() => {\r\n    if (debounceTimerRef.current) {\r\n      clearTimeout(debounceTimerRef.current);\r\n    }\r\n\r\n    setDebouncedValue(inputValue);\r\n    previousValueRef.current = inputValue;\r\n\r\n    if (inputValue.length >= minLength) {\r\n      if (onSearch) {\r\n        onSearch(inputValue);\r\n      }\r\n    } else if (inputValue === \"\" && onClear) {\r\n      onClear();\r\n    }\r\n\r\n    setIsSearching(false);\r\n  }, [inputValue, minLength, onSearch, onClear]);\r\n\r\n  return {\r\n    inputValue,\r\n    debouncedValue,\r\n    isSearching,\r\n    handleChange,\r\n    clearSearch,\r\n    resetSearch,\r\n    setInputValue,\r\n    forceSearch,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAmBO,SAAS,mBAAmB,EACjC,eAAe,EAAE,EACjB,QAAQ,GAAG,EACX,YAAY,CAAC,EACb,QAAQ,EACR,QAAQ,EACR,OAAO,EACmB,GAAG,CAAC,CAAC;;IAC/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACvD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;gDAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;uCAAG,EAAE;IAEL,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,mCAAmC;YACnC,IAAI,eAAe,iBAAiB,OAAO,EAAE;gBAC3C;YACF;YAEA,qCAAqC;YACrC,IAAI,UAAU;gBACZ,SAAS;YACX;YAEA,2BAA2B;YAC3B,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,sBAAsB;YACtB,eAAe,WAAW,MAAM,IAAI;YAEpC,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;gDAAW;oBACpC,kBAAkB;oBAClB,iBAAiB,OAAO,GAAG;oBAE3B,oDAAoD;oBACpD,IAAI,WAAW,MAAM,IAAI,WAAW;wBAClC,IAAI,UAAU;4BACZ,SAAS;wBACX;oBACF,OAAO,IAAI,eAAe,MAAM,SAAS;wBACvC;oBACF;oBAEA,eAAe;gBACjB;+CAAG;QACL;uCAAG;QAAC;QAAY;QAAO;QAAW;QAAU;QAAU;KAAQ;IAE9D,sBAAsB;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YAChC,cAAc;QAChB;uDAAG,EAAE;IAEL,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC9B,cAAc;YACd,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;YAE3B,IAAI,SAAS;gBACX;YACF;QACF;sDAAG;QAAC;KAAQ;IAEZ,yBAAyB;IACzB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC9B,cAAc;YACd,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;YAE3B,IAAI,iBAAiB,MAAM,SAAS;gBAClC;YACF,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,UAAU;gBACvD,SAAS;YACX;QACF;sDAAG;QAAC;QAAc;QAAW;QAAU;KAAQ;IAE/C,yBAAyB;IACzB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC9B,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;YAE3B,IAAI,WAAW,MAAM,IAAI,WAAW;gBAClC,IAAI,UAAU;oBACZ,SAAS;gBACX;YACF,OAAO,IAAI,eAAe,MAAM,SAAS;gBACvC;YACF;YAEA,eAAe;QACjB;sDAAG;QAAC;QAAY;QAAW;QAAU;KAAQ;IAE7C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAxHgB", "debugId": null}}, {"offset": {"line": 7362, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/search-input.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useCallback, forwardRef } from \"react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Search, X } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useDebouncedSearch } from \"@/hooks/use-debounced-search\";\r\n\r\nexport interface SearchInputProps {\r\n  /**\r\n   * Placeholder text for the search input\r\n   */\r\n  placeholder?: string;\r\n  /**\r\n   * Initial search value\r\n   */\r\n  value?: string;\r\n  /**\r\n   * Search callback function\r\n   */\r\n  onSearch?: (query: string) => void;\r\n  /**\r\n   * Clear callback function\r\n   */\r\n  onClear?: () => void;\r\n  /**\r\n   * Debounce delay in milliseconds\r\n   */\r\n  debounceMs?: number;\r\n  /**\r\n   * Minimum length before triggering search\r\n   */\r\n  minLength?: number;\r\n  /**\r\n   * Whether to show search button\r\n   */\r\n  showSearchButton?: boolean;\r\n  /**\r\n   * Whether to show clear button\r\n   */\r\n  showClearButton?: boolean;\r\n  /**\r\n   * Search mode: 'debounced' | 'manual' | 'realtime'\r\n   */\r\n  mode?: \"debounced\" | \"manual\" | \"realtime\";\r\n  /**\r\n   * Additional class names\r\n   */\r\n  className?: string;\r\n  /**\r\n   * Input class names\r\n   */\r\n  inputClassName?: string;\r\n  /**\r\n   * Button class names\r\n   */\r\n  buttonClassName?: string;\r\n  /**\r\n   * Whether the search is loading\r\n   */\r\n  isLoading?: boolean;\r\n  /**\r\n   * Disabled state\r\n   */\r\n  disabled?: boolean;\r\n  /**\r\n   * Size variant\r\n   */\r\n  size?: \"sm\" | \"default\" | \"lg\";\r\n}\r\n\r\n/**\r\n * Standardized search input component with consistent behavior\r\n */\r\nexport const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(\r\n  (\r\n    {\r\n      placeholder = \"Search...\",\r\n      value = \"\",\r\n      onSearch,\r\n      onClear,\r\n      debounceMs = 300,\r\n      minLength = 0,\r\n      showSearchButton = false,\r\n      showClearButton = true,\r\n      mode = \"debounced\",\r\n      className,\r\n      inputClassName,\r\n      buttonClassName,\r\n      isLoading = false,\r\n      disabled = false,\r\n      size = \"default\",\r\n    },\r\n    ref\r\n  ) => {\r\n    const [manualValue, setManualValue] = useState(value);\r\n\r\n    // Use debounced search for debounced mode\r\n    const {\r\n      inputValue,\r\n      handleChange,\r\n      clearSearch,\r\n      forceSearch,\r\n      isSearching,\r\n    } = useDebouncedSearch({\r\n      initialValue: value,\r\n      delay: debounceMs,\r\n      minLength,\r\n      onSearch: mode === \"debounced\" ? onSearch : undefined,\r\n      onClear,\r\n    });\r\n\r\n    // Handle different search modes\r\n    const currentValue = mode === \"manual\" ? manualValue : inputValue;\r\n    const currentIsLoading = mode === \"debounced\" ? isSearching : isLoading;\r\n\r\n    const handleInputChange = useCallback(\r\n      (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const newValue = e.target.value;\r\n\r\n        if (mode === \"manual\") {\r\n          setManualValue(newValue);\r\n        } else if (mode === \"realtime\") {\r\n          onSearch?.(newValue);\r\n        } else {\r\n          handleChange(newValue);\r\n        }\r\n      },\r\n      [mode, handleChange, onSearch]\r\n    );\r\n\r\n    const handleSearchClick = useCallback(() => {\r\n      if (mode === \"manual\") {\r\n        onSearch?.(manualValue);\r\n      } else {\r\n        forceSearch();\r\n      }\r\n    }, [mode, manualValue, onSearch, forceSearch]);\r\n\r\n    const handleClearClick = useCallback(() => {\r\n      if (mode === \"manual\") {\r\n        setManualValue(\"\");\r\n        onClear?.();\r\n      } else {\r\n        clearSearch();\r\n      }\r\n    }, [mode, clearSearch, onClear]);\r\n\r\n    const handleKeyDown = useCallback(\r\n      (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n        if (e.key === \"Enter\") {\r\n          e.preventDefault();\r\n          handleSearchClick();\r\n        }\r\n      },\r\n      [handleSearchClick]\r\n    );\r\n\r\n    const sizeClasses = {\r\n      sm: \"h-8 text-sm\",\r\n      default: \"h-9\",\r\n      lg: \"h-10\",\r\n    };\r\n\r\n    const buttonSizeClasses = {\r\n      sm: \"h-8 px-2\",\r\n      default: \"h-9 px-3\",\r\n      lg: \"h-10 px-4\",\r\n    };\r\n\r\n    return (\r\n      <div className={cn(\"flex items-center gap-2\", className)}>\r\n        <div className=\"relative flex-1\">\r\n          <Search className=\"absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            ref={ref}\r\n            type=\"search\"\r\n            placeholder={placeholder}\r\n            value={currentValue}\r\n            onChange={handleInputChange}\r\n            onKeyDown={handleKeyDown}\r\n            disabled={disabled || currentIsLoading}\r\n            className={cn(\r\n              \"pl-8\",\r\n              showClearButton && currentValue && \"pr-8\",\r\n              sizeClasses[size],\r\n              inputClassName\r\n            )}\r\n          />\r\n          {showClearButton && currentValue && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleClearClick}\r\n              disabled={disabled || currentIsLoading}\r\n              className=\"absolute right-2.5 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground disabled:opacity-50\"\r\n            >\r\n              <X className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Clear search</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {showSearchButton && (\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            onClick={handleSearchClick}\r\n            disabled={disabled || currentIsLoading}\r\n            className={cn(buttonSizeClasses[size], buttonClassName)}\r\n          >\r\n            {currentIsLoading ? (\r\n              <span className=\"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n            ) : (\r\n              <Search className=\"h-4 w-4\" />\r\n            )}\r\n            <span className=\"sr-only\">Search</span>\r\n          </Button>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nSearchInput.displayName = \"SearchInput\";\r\n\r\n/**\r\n * Simple search input for basic use cases\r\n */\r\nexport function SimpleSearchInput({\r\n  placeholder = \"Search...\",\r\n  onSearch,\r\n  className,\r\n  ...props\r\n}: {\r\n  placeholder?: string;\r\n  onSearch?: (query: string) => void;\r\n  className?: string;\r\n} & Omit<SearchInputProps, \"onSearch\">) {\r\n  return (\r\n    <SearchInput\r\n      placeholder={placeholder}\r\n      onSearch={onSearch}\r\n      mode=\"debounced\"\r\n      showClearButton={true}\r\n      className={cn(\"max-w-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\n/**\r\n * Manual search input with search button\r\n */\r\nexport function ManualSearchInput({\r\n  placeholder = \"Search...\",\r\n  onSearch,\r\n  className,\r\n  ...props\r\n}: {\r\n  placeholder?: string;\r\n  onSearch?: (query: string) => void;\r\n  className?: string;\r\n} & Omit<SearchInputProps, \"onSearch\" | \"mode\" | \"showSearchButton\">) {\r\n  return (\r\n    <SearchInput\r\n      placeholder={placeholder}\r\n      onSearch={onSearch}\r\n      mode=\"manual\"\r\n      showSearchButton={true}\r\n      showClearButton={true}\r\n      className={className}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\n/**\r\n * Real-time search input for instant filtering\r\n */\r\nexport function RealtimeSearchInput({\r\n  placeholder = \"Search...\",\r\n  onSearch,\r\n  className,\r\n  ...props\r\n}: {\r\n  placeholder?: string;\r\n  onSearch?: (query: string) => void;\r\n  className?: string;\r\n} & Omit<SearchInputProps, \"onSearch\" | \"mode\">) {\r\n  return (\r\n    <SearchInput\r\n      placeholder={placeholder}\r\n      onSearch={onSearch}\r\n      mode=\"realtime\"\r\n      showClearButton={true}\r\n      className={cn(\"max-w-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\n/**\r\n * Search input configurations for different use cases\r\n */\r\nexport const SearchConfigs = {\r\n  /**\r\n   * For large datasets that need server-side search\r\n   */\r\n  serverSide: {\r\n    mode: \"debounced\" as const,\r\n    debounceMs: 500,\r\n    minLength: 2,\r\n    showSearchButton: false,\r\n    showClearButton: true,\r\n  },\r\n\r\n  /**\r\n   * For client-side filtering of small datasets\r\n   */\r\n  clientSide: {\r\n    mode: \"realtime\" as const,\r\n    debounceMs: 100,\r\n    minLength: 0,\r\n    showSearchButton: false,\r\n    showClearButton: true,\r\n  },\r\n\r\n  /**\r\n   * For manual search with explicit search action\r\n   */\r\n  manual: {\r\n    mode: \"manual\" as const,\r\n    showSearchButton: true,\r\n    showClearButton: true,\r\n  },\r\n\r\n  /**\r\n   * For global search functionality\r\n   */\r\n  global: {\r\n    mode: \"debounced\" as const,\r\n    debounceMs: 300,\r\n    minLength: 1,\r\n    showSearchButton: false,\r\n    showClearButton: true,\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;;AA2EO,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,KAClC,CACE,EACE,cAAc,WAAW,EACzB,QAAQ,EAAE,EACV,QAAQ,EACR,OAAO,EACP,aAAa,GAAG,EAChB,YAAY,CAAC,EACb,mBAAmB,KAAK,EACxB,kBAAkB,IAAI,EACtB,OAAO,WAAW,EAClB,SAAS,EACT,cAAc,EACd,eAAe,EACf,YAAY,KAAK,EACjB,WAAW,KAAK,EAChB,OAAO,SAAS,EACjB,EACD;;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,0CAA0C;IAC1C,MAAM,EACJ,UAAU,EACV,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW,EACZ,GAAG,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD,EAAE;QACrB,cAAc;QACd,OAAO;QACP;QACA,UAAU,SAAS,cAAc,WAAW;QAC5C;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe,SAAS,WAAW,cAAc;IACvD,MAAM,mBAAmB,SAAS,cAAc,cAAc;IAE9D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAClC,CAAC;YACC,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;YAE/B,IAAI,SAAS,UAAU;gBACrB,eAAe;YACjB,OAAO,IAAI,SAAS,YAAY;gBAC9B,WAAW;YACb,OAAO;gBACL,aAAa;YACf;QACF;qDACA;QAAC;QAAM;QAAc;KAAS;IAGhC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACpC,IAAI,SAAS,UAAU;gBACrB,WAAW;YACb,OAAO;gBACL;YACF;QACF;qDAAG;QAAC;QAAM;QAAa;QAAU;KAAY;IAE7C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YACnC,IAAI,SAAS,UAAU;gBACrB,eAAe;gBACf;YACF,OAAO;gBACL;YACF;QACF;oDAAG;QAAC;QAAM;QAAa;KAAQ;IAE/B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAC9B,CAAC;YACC,IAAI,EAAE,GAAG,KAAK,SAAS;gBACrB,EAAE,cAAc;gBAChB;YACF;QACF;iDACA;QAAC;KAAkB;IAGrB,MAAM,cAAc;QAClB,IAAI;QACJ,SAAS;QACT,IAAI;IACN;IAEA,MAAM,oBAAoB;QACxB,IAAI;QACJ,SAAS;QACT,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAC5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC,oIAAA,CAAA,QAAK;wBACJ,KAAK;wBACL,MAAK;wBACL,aAAa;wBACb,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,UAAU,YAAY;wBACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,mBAAmB,gBAAgB,QACnC,WAAW,CAAC,KAAK,EACjB;;;;;;oBAGH,mBAAmB,8BAClB,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU,YAAY;wBACtB,WAAU;;0CAEV,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;YAK/B,kCACC,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,SAAS;gBACT,UAAU,YAAY;gBACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB,CAAC,KAAK,EAAE;;oBAEtC,iCACC,6LAAC;wBAAK,WAAU;;;;;6CAEhB,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAEpB,6LAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;;AAKpC;;QApHM,6IAAA,CAAA,qBAAkB;;;KA9Bb;AAqJb,YAAY,WAAW,GAAG;AAKnB,SAAS,kBAAkB,EAChC,cAAc,WAAW,EACzB,QAAQ,EACR,SAAS,EACT,GAAG,OAKiC;IACpC,qBACE,6LAAC;QACC,aAAa;QACb,UAAU;QACV,MAAK;QACL,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MApBgB;AAyBT,SAAS,kBAAkB,EAChC,cAAc,WAAW,EACzB,QAAQ,EACR,SAAS,EACT,GAAG,OAK+D;IAClE,qBACE,6LAAC;QACC,aAAa;QACb,UAAU;QACV,MAAK;QACL,kBAAkB;QAClB,iBAAiB;QACjB,WAAW;QACV,GAAG,KAAK;;;;;;AAGf;MArBgB;AA0BT,SAAS,oBAAoB,EAClC,cAAc,WAAW,EACzB,QAAQ,EACR,SAAS,EACT,GAAG,OAK0C;IAC7C,qBACE,6LAAC;QACC,aAAa;QACb,UAAU;QACV,MAAK;QACL,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MApBgB;AAyBT,MAAM,gBAAgB;IAC3B;;GAEC,GACD,YAAY;QACV,MAAM;QACN,YAAY;QACZ,WAAW;QACX,kBAAkB;QAClB,iBAAiB;IACnB;IAEA;;GAEC,GACD,YAAY;QACV,MAAM;QACN,YAAY;QACZ,WAAW;QACX,kBAAkB;QAClB,iBAAiB;IACnB;IAEA;;GAEC,GACD,QAAQ;QACN,MAAM;QACN,kBAAkB;QAClB,iBAAiB;IACnB;IAEA;;GAEC,GACD,QAAQ;QACN,MAAM;QACN,YAAY;QACZ,WAAW;QACX,kBAAkB;QAClB,iBAAiB;IACnB;AACF", "debugId": null}}, {"offset": {"line": 7667, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/users/components/users-table.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Bad<PERSON> } from \"@/components/ui/badge\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { DataPagination } from \"@/components/ui/data-pagination\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { useBranches } from \"@/features/branches/hooks/use-branches\";\r\nimport { useUpdateUserStatus } from \"@/features/users/hooks/use-users\";\r\nimport {\r\n  StatusBadge,\r\n  PendingStatusBadge,\r\n} from \"@/features/users/components/status-badge\";\r\nimport { exportToExcel } from \"@/lib/excel-export\";\r\nimport { SearchInput } from \"@/components/ui/search-input\";\r\nimport { formatDate } from \"@/lib/utils\";\r\nimport { Branch, User as UserType } from \"@/types\";\r\nimport {\r\n  Ban,\r\n  CheckCircle,\r\n  Download,\r\n  Edit,\r\n  MoreHorizontal,\r\n  Plus,\r\n  Search,\r\n  User,\r\n  Users as UsersIcon,\r\n  X,\r\n  XCircle,\r\n} from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useState, useEffect, useRef } from \"react\";\r\n\r\ninterface UsersTableProps {\r\n  users: UserType[];\r\n  isLoading: boolean;\r\n  onSearch: (query: string) => void;\r\n  selectedBranchId?: string;\r\n  onBranchChange?: (branchId: string) => void;\r\n  onStatusFilterChange?: (status: \"all\" | \"active\" | \"inactive\") => void;\r\n  pagination?: {\r\n    currentPage: number;\r\n    totalPages: number;\r\n    onPageChange: (page: number) => void;\r\n    itemsPerPage?: number;\r\n    onItemsPerPageChange?: (value: string) => void;\r\n    totalItems?: number;\r\n  };\r\n}\r\n\r\nexport function UsersTable({\r\n  users,\r\n  isLoading,\r\n  onSearch,\r\n  selectedBranchId = \"all\",\r\n  onBranchChange,\r\n  pagination,\r\n  onStatusFilterChange,\r\n}: UsersTableProps) {\r\n  const router = useRouter();\r\n  const [isExporting, setIsExporting] = useState(false);\r\n  const [statusFilter, setStatusFilter] = useState<\r\n    \"all\" | \"active\" | \"inactive\"\r\n  >(\"all\");\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n\r\n  // Fetch branches for the filter dropdown\r\n  const { data: branchesData, isLoading: branchesLoading } = useBranches();\r\n\r\n  const updateUserStatus = useUpdateUserStatus();\r\n\r\n  const handleViewUser = (id: number) => {\r\n    router.push(`/users/${id}`);\r\n  };\r\n\r\n  const handleEditUser = (id: number) => {\r\n    router.push(`/users/${id}/edit`);\r\n  };\r\n\r\n  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);\r\n  const [updatingUserId, setUpdatingUserId] = useState<number | null>(null);\r\n  const [statusUpdateError, setStatusUpdateError] = useState<string | null>(\r\n    null\r\n  );\r\n\r\n  const handleStatusChange = async (\r\n    id: number,\r\n    status: \"active\" | \"inactive\"\r\n  ) => {\r\n    try {\r\n      setIsUpdatingStatus(true);\r\n      setUpdatingUserId(id);\r\n      setStatusUpdateError(null);\r\n\r\n      console.log(`Attempting to change user ${id} status to ${status}`);\r\n\r\n      // Call the updated user service\r\n      const updatedUser = await updateUserStatus.mutateAsync({\r\n        id,\r\n        status: { status },\r\n      });\r\n\r\n      console.log(\r\n        `Successfully changed user ${id} status to ${status}`,\r\n        updatedUser\r\n      );\r\n\r\n      // Double-check that the status was actually changed\r\n      const hasCorrectStatus =\r\n        status === \"inactive\"\r\n          ? !!updatedUser.deleted_at\r\n          : !updatedUser.deleted_at;\r\n\r\n      if (!hasCorrectStatus) {\r\n        console.warn(\r\n          `Response still has incorrect status after all attempts. Forcing status in UI.`\r\n        );\r\n        // Force the correct status in the user object\r\n        updatedUser.deleted_at =\r\n          status === \"inactive\" ? new Date().toISOString() : null;\r\n      }\r\n\r\n      // Update the user in the local array to reflect the change immediately\r\n      const updatedUsers = users.map((user) => {\r\n        if (user.id === id) {\r\n          // Return the updated user with the correct status\r\n          return {\r\n            ...user,\r\n            deleted_at: status === \"inactive\" ? new Date().toISOString() : null,\r\n          };\r\n        }\r\n        return user;\r\n      });\r\n\r\n      // Show success message\r\n      alert(\r\n        `User ${\r\n          status === \"active\" ? \"activated\" : \"deactivated\"\r\n        } successfully.`\r\n      );\r\n\r\n      // Force a hard refresh to ensure we get fresh data from the server\r\n      // Use a small delay to ensure the alert is seen\r\n      setTimeout(() => {\r\n        window.location.href = window.location.href.split(\"?\")[0]; // Remove any query params\r\n      }, 500);\r\n    } catch (error: any) {\r\n      console.error(\r\n        `Error ${status === \"active\" ? \"activating\" : \"deactivating\"} user:`,\r\n        error\r\n      );\r\n\r\n      // Extract error message from the response if available\r\n      let errorMessage = `Failed to ${\r\n        status === \"active\" ? \"activate\" : \"deactivate\"\r\n      } user (ID: ${id}).`;\r\n\r\n      if (error.response?.data?.message) {\r\n        errorMessage += ` Server says: ${error.response.data.message}`;\r\n      } else if (error.message) {\r\n        errorMessage += ` Error: ${error.message}`;\r\n      }\r\n\r\n      // Log additional details for debugging\r\n      console.log(\"Error details:\", {\r\n        userId: id,\r\n        status,\r\n        errorStatus: error.response?.status,\r\n        errorData: error.response?.data,\r\n        errorMessage: error.message,\r\n      });\r\n\r\n      // As a last resort, try a direct database update\r\n      console.warn(\r\n        `API call failed. Attempting direct database update as a fallback.`\r\n      );\r\n\r\n      try {\r\n        // Make a direct fetch call to the API\r\n        const apiUrl =\r\n          process.env.NEXT_PUBLIC_API_URL ||\r\n          \"https://pos.apprenticecloud.com/api/v1\";\r\n        const token = localStorage.getItem(\"token\");\r\n\r\n        // First try the status endpoint\r\n        let response = await fetch(`${apiUrl}/users/${id}/status`, {\r\n          method: \"PUT\",\r\n          headers: {\r\n            \"Content-Type\": \"application/json\",\r\n            Authorization: `Bearer ${token}`,\r\n          },\r\n          body: JSON.stringify({\r\n            status: status,\r\n          }),\r\n        });\r\n\r\n        // If status endpoint fails, try direct update of deleted_at field\r\n        if (!response.ok) {\r\n          console.warn(\r\n            `Status endpoint failed with status ${response.status}. Trying direct update of deleted_at field.`\r\n          );\r\n\r\n          response = await fetch(`${apiUrl}/users/${id}`, {\r\n            method: \"PATCH\",\r\n            headers: {\r\n              \"Content-Type\": \"application/json\",\r\n              Authorization: `Bearer ${token}`,\r\n            },\r\n            body: JSON.stringify({\r\n              deleted_at:\r\n                status === \"inactive\" ? new Date().toISOString() : null,\r\n            }),\r\n          });\r\n        }\r\n\r\n        if (response.ok) {\r\n          console.log(\r\n            `Successfully updated user ${id} status using direct fetch`\r\n          );\r\n\r\n          // Update the users array locally\r\n          const updatedUsers = users.map((user) => {\r\n            if (user.id === id) {\r\n              return {\r\n                ...user,\r\n                deleted_at:\r\n                  status === \"inactive\" ? new Date().toISOString() : null,\r\n              };\r\n            }\r\n            return user;\r\n          });\r\n\r\n          // Show success message\r\n          alert(\r\n            `User ${\r\n              status === \"active\" ? \"activated\" : \"deactivated\"\r\n            } successfully using fallback method.`\r\n          );\r\n\r\n          // Force a hard refresh to ensure we get fresh data from the server\r\n          setTimeout(() => {\r\n            window.location.href = window.location.href.split(\"?\")[0]; // Remove any query params\r\n          }, 500);\r\n\r\n          return;\r\n        } else {\r\n          throw new Error(`Direct fetch failed with status ${response.status}`);\r\n        }\r\n      } catch (directError) {\r\n        console.error(`Direct fetch failed:`, directError);\r\n\r\n        // If direct update fails, try to update the UI locally\r\n        try {\r\n          // Update the users array locally\r\n          const updatedUsers = users.map((user) => {\r\n            if (user.id === id) {\r\n              return {\r\n                ...user,\r\n                deleted_at:\r\n                  status === \"inactive\" ? new Date().toISOString() : null,\r\n              };\r\n            }\r\n            return user;\r\n          });\r\n\r\n          // Show a warning message about the local-only update\r\n          const warningMessage = `API calls failed but UI has been updated locally. The change may not persist after refresh.`;\r\n          setStatusUpdateError(warningMessage);\r\n          alert(`${warningMessage}\\n\\nError: ${errorMessage}`);\r\n\r\n          // Don't refresh the page in this case, so the user can see the local change\r\n        } catch (localUpdateError) {\r\n          // If even the local update fails, show the original error\r\n          setStatusUpdateError(errorMessage);\r\n          alert(errorMessage);\r\n        }\r\n      }\r\n    } finally {\r\n      setIsUpdatingStatus(false);\r\n      setUpdatingUserId(null);\r\n    }\r\n  };\r\n\r\n  // We no longer need to filter users client-side since we're doing it server-side\r\n  const filteredUsers = users;\r\n\r\n  const handleExportToExcel = async () => {\r\n    try {\r\n      setIsExporting(true);\r\n      // Export all users data to Excel\r\n      // Generate a timestamp for the filename\r\n      const now = new Date();\r\n      const timestamp = now\r\n        .toISOString()\r\n        .replace(/[:.]/g, \"-\")\r\n        .replace(\"T\", \"_\")\r\n        .slice(0, 19);\r\n\r\n      // Create a copy of the users data with a formatted status field\r\n      // Use filtered users if status filter is applied\r\n      const usersToExport = statusFilter === \"all\" ? users : filteredUsers;\r\n      const usersWithStatus = usersToExport.map((user) => ({\r\n        ...user,\r\n        status: user.deleted_at ? \"Inactive\" : \"Active\",\r\n      }));\r\n\r\n      exportToExcel(usersWithStatus, `Users_Export_${timestamp}`, \"Users\", [\r\n        \"tenant_id\", // Exclude tenant ID\r\n        \"tenant\", // Exclude tenant object\r\n        \"Tenant\", // Exclude Tenant object (capitalized from Sequelize)\r\n        \"password_hash\", // Exclude password hash\r\n        \"deleted_at\", // Exclude deleted timestamp but we keep our formatted status field\r\n      ]);\r\n    } catch (error) {\r\n      console.error(\"Error exporting to Excel:\", error);\r\n    } finally {\r\n      // Add a small delay to show the loading state\r\n      setTimeout(() => {\r\n        setIsExporting(false);\r\n      }, 500);\r\n    }\r\n  };\r\n\r\n  const getRoleBadgeVariant = (roleName: string | null | undefined) => {\r\n    if (!roleName) return \"secondary\";\r\n\r\n    const role = roleName.toLowerCase();\r\n\r\n    // Admin roles\r\n    if (role.includes(\"admin\")) {\r\n      if (role.includes(\"super\")) {\r\n        return \"destructive\";\r\n      }\r\n      return \"default\";\r\n    }\r\n\r\n    // Manager roles\r\n    if (role.includes(\"manager\")) {\r\n      return \"secondary\";\r\n    }\r\n\r\n    // DSA roles\r\n    if (role.includes(\"dsa\")) {\r\n      return \"default\"; // Map \"warning\" to \"default\"\r\n    }\r\n\r\n    // Other specific roles\r\n    switch (role) {\r\n      case \"finance_manager\":\r\n      case \"shop_attendant\":\r\n        return \"outline\";\r\n      case \"float_manager\":\r\n        return \"secondary\"; // Map \"info\" to \"secondary\"\r\n      default:\r\n        return \"secondary\";\r\n    }\r\n  };\r\n\r\n  // Function to highlight search matches\r\n  const highlightMatch = (text: string, query: string) => {\r\n    if (!query || !text) return text;\r\n\r\n    try {\r\n      const parts = text.split(new RegExp(`(${query})`, \"gi\"));\r\n      return (\r\n        <>\r\n          {parts.map((part, i) =>\r\n            part.toLowerCase() === query.toLowerCase() ? (\r\n              <span\r\n                key={i}\r\n                className=\"bg-yellow-200 dark:bg-yellow-800 rounded px-1\"\r\n              >\r\n                {part}\r\n              </span>\r\n            ) : (\r\n              part\r\n            )\r\n          )}\r\n        </>\r\n      );\r\n    } catch (e) {\r\n      // If there's an error with the regex (e.g., special characters), just return the original text\r\n      return text;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <SearchInput\r\n          placeholder=\"Search by name, email, or phone...\"\r\n          onSearch={(query) => {\r\n            setSearchQuery(query);\r\n            onSearch(query);\r\n          }}\r\n          mode=\"debounced\"\r\n          debounceMs={300}\r\n          className=\"w-full max-w-sm\"\r\n          size=\"sm\"\r\n        />\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* Status filter */}\r\n          <div className=\"w-[180px]\">\r\n            <Select\r\n              value={statusFilter}\r\n              onValueChange={(value: \"all\" | \"active\" | \"inactive\") => {\r\n                setStatusFilter(value);\r\n                if (onStatusFilterChange) {\r\n                  onStatusFilterChange(value);\r\n                }\r\n              }}\r\n            >\r\n              <SelectTrigger\r\n                className={statusFilter !== \"all\" ? \"border-primary\" : \"\"}\r\n              >\r\n                <SelectValue placeholder=\"Filter by status\">\r\n                  {statusFilter === \"all\" ? (\r\n                    <span>All users</span>\r\n                  ) : (\r\n                    <div className=\"flex items-center\">\r\n                      <StatusBadge\r\n                        status={statusFilter as \"active\" | \"inactive\"}\r\n                        size=\"sm\"\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </SelectValue>\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                <SelectItem value=\"all\" className=\"flex items-center\">\r\n                  <div className=\"flex items-center\">\r\n                    <UsersIcon className=\"h-4 w-4 mr-2 text-muted-foreground\" />\r\n                    <span>All users</span>\r\n                  </div>\r\n                </SelectItem>\r\n                <SelectItem value=\"active\" className=\"flex items-center\">\r\n                  <div className=\"flex items-center\">\r\n                    <StatusBadge status=\"active\" size=\"sm\" />\r\n                  </div>\r\n                </SelectItem>\r\n                <SelectItem value=\"inactive\" className=\"flex items-center\">\r\n                  <div className=\"flex items-center\">\r\n                    <StatusBadge status=\"inactive\" size=\"sm\" />\r\n                  </div>\r\n                </SelectItem>\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n\r\n          {/* Branch filter */}\r\n          {onBranchChange && (\r\n            <div className=\"w-[200px]\">\r\n              <Select\r\n                value={selectedBranchId}\r\n                onValueChange={onBranchChange}\r\n                disabled={branchesLoading}\r\n              >\r\n                <SelectTrigger\r\n                  className={selectedBranchId !== \"all\" ? \"border-primary\" : \"\"}\r\n                >\r\n                  <SelectValue placeholder=\"Filter by branch\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All branches</SelectItem>\r\n                  {branchesLoading ? (\r\n                    <SelectItem value=\"loading\" disabled>\r\n                      Loading branches...\r\n                    </SelectItem>\r\n                  ) : branchesData?.data?.length === 0 ? (\r\n                    <SelectItem value=\"no-branches\" disabled>\r\n                      No branches found\r\n                    </SelectItem>\r\n                  ) : (\r\n                    branchesData?.data?.map((branch) => (\r\n                      <SelectItem key={branch.id} value={branch.id.toString()}>\r\n                        {branch.name}\r\n                      </SelectItem>\r\n                    ))\r\n                  )}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n          )}\r\n          <div className=\"flex items-center gap-2\">\r\n            <TooltipProvider>\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <Button\r\n                    variant=\"outline\"\r\n                    size=\"icon\"\r\n                    onClick={handleExportToExcel}\r\n                    className=\"h-9 w-9\"\r\n                    disabled={isExporting}\r\n                  >\r\n                    {isExporting ? (\r\n                      <span className=\"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n                    ) : (\r\n                      <Download className=\"h-4 w-4\" />\r\n                    )}\r\n                  </Button>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>{isExporting ? \"Exporting...\" : \"Export to Excel\"}</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n            </TooltipProvider>\r\n            <Button onClick={() => router.push(\"/users/create\")}>\r\n              <Plus className=\"h-4 w-4 mr-2\" />\r\n              Add User\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {(searchQuery ||\r\n        (selectedBranchId !== \"all\" && onBranchChange) ||\r\n        statusFilter !== \"all\") && (\r\n        <div className=\"text-sm text-muted-foreground mb-2 flex flex-wrap gap-2\">\r\n          {searchQuery && (\r\n            <span>\r\n              Found {filteredUsers.length}{\" \"}\r\n              {filteredUsers.length === 1 ? \"user\" : \"users\"} matching \"\r\n              {searchQuery}\"\r\n            </span>\r\n          )}\r\n          {selectedBranchId !== \"all\" &&\r\n            onBranchChange &&\r\n            branchesData?.data && (\r\n              <span className=\"flex items-center gap-1\">\r\n                <span>•</span>\r\n                <span>\r\n                  Filtered by branch:{\" \"}\r\n                  <span className=\"font-medium\">\r\n                    {branchesData.data.find(\r\n                      (b) => b.id.toString() === selectedBranchId\r\n                    )?.name || \"Unknown branch\"}\r\n                  </span>\r\n                </span>\r\n                <button\r\n                  onClick={() => onBranchChange(\"all\")}\r\n                  className=\"text-xs text-primary hover:underline ml-1\"\r\n                >\r\n                  Clear\r\n                </button>\r\n              </span>\r\n            )}\r\n          {statusFilter !== \"all\" && (\r\n            <span className=\"flex items-center gap-1\">\r\n              <span>•</span>\r\n              <span>\r\n                Showing{\" \"}\r\n                <span className=\"font-medium\">\r\n                  {statusFilter === \"active\" ? \"active\" : \"inactive\"}\r\n                </span>{\" \"}\r\n                users only\r\n              </span>\r\n              <button\r\n                onClick={() => {\r\n                  setStatusFilter(\"all\");\r\n                  if (onStatusFilterChange) {\r\n                    onStatusFilterChange(\"all\");\r\n                  }\r\n                }}\r\n                className=\"text-xs text-primary hover:underline ml-1\"\r\n              >\r\n                Clear\r\n              </button>\r\n            </span>\r\n          )}\r\n        </div>\r\n      )}\r\n      <div className=\"rounded-md border\">\r\n        <Table>\r\n          <TableHeader>\r\n            <TableRow>\r\n              <TableHead>Name</TableHead>\r\n              <TableHead>Email</TableHead>\r\n              <TableHead>Phone</TableHead>\r\n              <TableHead>Role</TableHead>\r\n              <TableHead>Branch</TableHead>\r\n              <TableHead>Status</TableHead>\r\n              <TableHead>Created</TableHead>\r\n              <TableHead className=\"w-[80px]\">Actions</TableHead>\r\n            </TableRow>\r\n          </TableHeader>\r\n          <TableBody>\r\n            {isLoading ? (\r\n              <TableRow>\r\n                <TableCell colSpan={8} className=\"text-center py-8\">\r\n                  Loading users...\r\n                </TableCell>\r\n              </TableRow>\r\n            ) : users.length === 0 ? (\r\n              <TableRow>\r\n                <TableCell colSpan={8} className=\"text-center py-8\">\r\n                  {searchQuery\r\n                    ? `No users found matching \"${searchQuery}\".`\r\n                    : \"No users found. Create your first user.\"}\r\n                </TableCell>\r\n              </TableRow>\r\n            ) : filteredUsers.length === 0 ? (\r\n              <TableRow>\r\n                <TableCell colSpan={8} className=\"text-center py-8\">\r\n                  No {statusFilter === \"all\" ? \"\" : statusFilter} users found\r\n                  {searchQuery ? ` matching \"${searchQuery}\"` : \"\"}.\r\n                  {statusFilter !== \"all\" && (\r\n                    <div className=\"mt-2\">\r\n                      <button\r\n                        onClick={() => {\r\n                          setStatusFilter(\"all\");\r\n                          if (onStatusFilterChange) {\r\n                            onStatusFilterChange(\"all\");\r\n                          }\r\n                        }}\r\n                        className=\"text-primary hover:underline text-sm\"\r\n                      >\r\n                        Show all users\r\n                      </button>\r\n                    </div>\r\n                  )}\r\n                </TableCell>\r\n              </TableRow>\r\n            ) : (\r\n              filteredUsers.map((user) => (\r\n                <TableRow\r\n                  key={user.id}\r\n                  className={`cursor-pointer hover:bg-muted/50 ${\r\n                    user.deleted_at ? \"bg-muted/30\" : \"\"\r\n                  }`}\r\n                  onClick={() => handleViewUser(user.id)}\r\n                >\r\n                  <TableCell className=\"font-medium\">\r\n                    {searchQuery\r\n                      ? highlightMatch(user.name, searchQuery)\r\n                      : user.name}\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    {user.email\r\n                      ? searchQuery\r\n                        ? highlightMatch(user.email, searchQuery)\r\n                        : user.email\r\n                      : \"-\"}\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    {user.phone\r\n                      ? searchQuery\r\n                        ? highlightMatch(user.phone, searchQuery)\r\n                        : user.phone\r\n                      : \"-\"}\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    {user.Role ? (\r\n                      <Badge variant={getRoleBadgeVariant(user.Role.name)}>\r\n                        {user.Role.name}\r\n                      </Badge>\r\n                    ) : user.role ? (\r\n                      <Badge variant={getRoleBadgeVariant(user.role.name)}>\r\n                        {user.role.name}\r\n                      </Badge>\r\n                    ) : user.role_name ? (\r\n                      <Badge variant={getRoleBadgeVariant(user.role_name)}>\r\n                        {user.role_name}\r\n                      </Badge>\r\n                    ) : (\r\n                      <Badge variant=\"outline\">Unknown</Badge>\r\n                    )}\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    {user.Branch\r\n                      ? user.Branch.name\r\n                      : user.branch\r\n                      ? user.branch.name\r\n                      : \"-\"}\r\n                  </TableCell>\r\n                  <TableCell>\r\n                    {isUpdatingStatus && user.id === updatingUserId ? (\r\n                      <PendingStatusBadge />\r\n                    ) : (\r\n                      <StatusBadge\r\n                        status={user.deleted_at ? \"inactive\" : \"active\"}\r\n                        showIcon={true}\r\n                      />\r\n                    )}\r\n                  </TableCell>\r\n                  <TableCell>{formatDate(user.created_at)}</TableCell>\r\n                  <TableCell>\r\n                    <DropdownMenu>\r\n                      <DropdownMenuTrigger\r\n                        asChild\r\n                        onClick={(e) => e.stopPropagation()}\r\n                        disabled={isUpdatingStatus}\r\n                      >\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          className=\"h-8 w-8 p-0\"\r\n                          disabled={isUpdatingStatus}\r\n                        >\r\n                          <span className=\"sr-only\">Open menu</span>\r\n                          {isUpdatingStatus ? (\r\n                            <span className=\"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n                          ) : (\r\n                            <MoreHorizontal className=\"h-4 w-4\" />\r\n                          )}\r\n                        </Button>\r\n                      </DropdownMenuTrigger>\r\n                      <DropdownMenuContent align=\"end\">\r\n                        <DropdownMenuLabel>Actions</DropdownMenuLabel>\r\n                        <DropdownMenuItem\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            handleViewUser(user.id);\r\n                          }}\r\n                          disabled={isUpdatingStatus}\r\n                        >\r\n                          <User className=\"h-4 w-4 mr-2\" />\r\n                          View Details\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuItem\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            handleEditUser(user.id);\r\n                          }}\r\n                          disabled={isUpdatingStatus}\r\n                        >\r\n                          <Edit className=\"h-4 w-4 mr-2\" />\r\n                          Edit\r\n                        </DropdownMenuItem>\r\n                        <DropdownMenuSeparator />\r\n                        {user.deleted_at ? (\r\n                          <DropdownMenuItem\r\n                            onClick={(e) => {\r\n                              e.stopPropagation();\r\n                              handleStatusChange(user.id, \"active\");\r\n                            }}\r\n                            disabled={isUpdatingStatus}\r\n                            className=\"text-success hover:text-success/90\"\r\n                          >\r\n                            {isUpdatingStatus && updatingUserId === user.id ? (\r\n                              <>\r\n                                <span className=\"h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n                                Activating...\r\n                              </>\r\n                            ) : (\r\n                              <>\r\n                                <CheckCircle className=\"h-4 w-4 mr-2\" />\r\n                                Activate User\r\n                              </>\r\n                            )}\r\n                          </DropdownMenuItem>\r\n                        ) : (\r\n                          <DropdownMenuItem\r\n                            onClick={(e) => {\r\n                              e.stopPropagation();\r\n                              handleStatusChange(user.id, \"inactive\");\r\n                            }}\r\n                            disabled={isUpdatingStatus}\r\n                            className=\"text-destructive hover:text-destructive/90\"\r\n                          >\r\n                            {isUpdatingStatus && updatingUserId === user.id ? (\r\n                              <>\r\n                                <span className=\"h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n                                Deactivating...\r\n                              </>\r\n                            ) : (\r\n                              <>\r\n                                <Ban className=\"h-4 w-4 mr-2\" />\r\n                                Deactivate User\r\n                              </>\r\n                            )}\r\n                          </DropdownMenuItem>\r\n                        )}\r\n                      </DropdownMenuContent>\r\n                    </DropdownMenu>\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n\r\n      {pagination && (\r\n        <div className=\"mt-6 w-full\">\r\n          <DataPagination\r\n            currentPage={pagination.currentPage}\r\n            totalPages={pagination.totalPages}\r\n            onPageChange={pagination.onPageChange}\r\n            pageSize={pagination.itemsPerPage || 10}\r\n            onPageSizeChange={(newPageSize) => {\r\n              if (pagination.onItemsPerPageChange) {\r\n                pagination.onItemsPerPageChange(newPageSize.toString());\r\n              }\r\n            }}\r\n            totalItems={pagination.totalItems || filteredUsers.length}\r\n            isLoading={isLoading}\r\n            showPageSizeSelector={true}\r\n            showItemsInfo={true}\r\n            showFirstLastButtons={true}\r\n          />\r\n        </div>\r\n      )}\r\n\r\n      {/* Show error message if status update fails */}\r\n      {statusUpdateError && (\r\n        <div className=\"mt-4 p-4 bg-destructive/10 border border-destructive/20 text-destructive rounded-md flex items-start gap-3\">\r\n          <XCircle className=\"h-5 w-5 mt-0.5 flex-shrink-0\" />\r\n          <div>\r\n            <h4 className=\"font-medium mb-1\">Status Update Error</h4>\r\n            <p>{statusUpdateError}</p>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AAgNU;;AA9MV;AACA;AACA;AACA;AASA;AAOA;AAMA;AAQA;AACA;AACA;AAIA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;;;AA3DA;;;;;;;;;;;;;;;;;AA8EO,SAAS,WAAW,EACzB,KAAK,EACL,SAAS,EACT,QAAQ,EACR,mBAAmB,KAAK,EACxB,cAAc,EACd,UAAU,EACV,oBAAoB,EACJ;;IAChB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE7C;IACF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,yCAAyC;IACzC,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,eAAe,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD;IAErE,MAAM,mBAAmB,CAAA,GAAA,oJAAA,CAAA,sBAAmB,AAAD;IAE3C,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,IAAI;IAC5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG,KAAK,CAAC;IACjC;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACvD;IAGF,MAAM,qBAAqB,OACzB,IACA;QAEA,IAAI;YACF,oBAAoB;YACpB,kBAAkB;YAClB,qBAAqB;YAErB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,GAAG,WAAW,EAAE,QAAQ;YAEjE,gCAAgC;YAChC,MAAM,cAAc,MAAM,iBAAiB,WAAW,CAAC;gBACrD;gBACA,QAAQ;oBAAE;gBAAO;YACnB;YAEA,QAAQ,GAAG,CACT,CAAC,0BAA0B,EAAE,GAAG,WAAW,EAAE,QAAQ,EACrD;YAGF,oDAAoD;YACpD,MAAM,mBACJ,WAAW,aACP,CAAC,CAAC,YAAY,UAAU,GACxB,CAAC,YAAY,UAAU;YAE7B,IAAI,CAAC,kBAAkB;gBACrB,QAAQ,IAAI,CACV,CAAC,6EAA6E,CAAC;gBAEjF,8CAA8C;gBAC9C,YAAY,UAAU,GACpB,WAAW,aAAa,IAAI,OAAO,WAAW,KAAK;YACvD;YAEA,uEAAuE;YACvE,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC;gBAC9B,IAAI,KAAK,EAAE,KAAK,IAAI;oBAClB,kDAAkD;oBAClD,OAAO;wBACL,GAAG,IAAI;wBACP,YAAY,WAAW,aAAa,IAAI,OAAO,WAAW,KAAK;oBACjE;gBACF;gBACA,OAAO;YACT;YAEA,uBAAuB;YACvB,MACE,CAAC,KAAK,EACJ,WAAW,WAAW,cAAc,cACrC,cAAc,CAAC;YAGlB,mEAAmE;YACnE,gDAAgD;YAChD,WAAW;gBACT,OAAO,QAAQ,CAAC,IAAI,GAAG,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,0BAA0B;YACvF,GAAG;QACL,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CACX,CAAC,MAAM,EAAE,WAAW,WAAW,eAAe,eAAe,MAAM,CAAC,EACpE;YAGF,uDAAuD;YACvD,IAAI,eAAe,CAAC,UAAU,EAC5B,WAAW,WAAW,aAAa,aACpC,WAAW,EAAE,GAAG,EAAE,CAAC;YAEpB,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACjC,gBAAgB,CAAC,cAAc,EAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;YAChE,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,gBAAgB,CAAC,QAAQ,EAAE,MAAM,OAAO,EAAE;YAC5C;YAEA,uCAAuC;YACvC,QAAQ,GAAG,CAAC,kBAAkB;gBAC5B,QAAQ;gBACR;gBACA,aAAa,MAAM,QAAQ,EAAE;gBAC7B,WAAW,MAAM,QAAQ,EAAE;gBAC3B,cAAc,MAAM,OAAO;YAC7B;YAEA,iDAAiD;YACjD,QAAQ,IAAI,CACV,CAAC,iEAAiE,CAAC;YAGrE,IAAI;gBACF,sCAAsC;gBACtC,MAAM,SACJ,oEACA;gBACF,MAAM,QAAQ,aAAa,OAAO,CAAC;gBAEnC,gCAAgC;gBAChC,IAAI,WAAW,MAAM,MAAM,GAAG,OAAO,OAAO,EAAE,GAAG,OAAO,CAAC,EAAE;oBACzD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,eAAe,CAAC,OAAO,EAAE,OAAO;oBAClC;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,QAAQ;oBACV;gBACF;gBAEA,kEAAkE;gBAClE,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,QAAQ,IAAI,CACV,CAAC,mCAAmC,EAAE,SAAS,MAAM,CAAC,2CAA2C,CAAC;oBAGpG,WAAW,MAAM,MAAM,GAAG,OAAO,OAAO,EAAE,IAAI,EAAE;wBAC9C,QAAQ;wBACR,SAAS;4BACP,gBAAgB;4BAChB,eAAe,CAAC,OAAO,EAAE,OAAO;wBAClC;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB,YACE,WAAW,aAAa,IAAI,OAAO,WAAW,KAAK;wBACvD;oBACF;gBACF;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,QAAQ,GAAG,CACT,CAAC,0BAA0B,EAAE,GAAG,0BAA0B,CAAC;oBAG7D,iCAAiC;oBACjC,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC;wBAC9B,IAAI,KAAK,EAAE,KAAK,IAAI;4BAClB,OAAO;gCACL,GAAG,IAAI;gCACP,YACE,WAAW,aAAa,IAAI,OAAO,WAAW,KAAK;4BACvD;wBACF;wBACA,OAAO;oBACT;oBAEA,uBAAuB;oBACvB,MACE,CAAC,KAAK,EACJ,WAAW,WAAW,cAAc,cACrC,oCAAoC,CAAC;oBAGxC,mEAAmE;oBACnE,WAAW;wBACT,OAAO,QAAQ,CAAC,IAAI,GAAG,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,0BAA0B;oBACvF,GAAG;oBAEH;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,SAAS,MAAM,EAAE;gBACtE;YACF,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,CAAC,oBAAoB,CAAC,EAAE;gBAEtC,uDAAuD;gBACvD,IAAI;oBACF,iCAAiC;oBACjC,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC;wBAC9B,IAAI,KAAK,EAAE,KAAK,IAAI;4BAClB,OAAO;gCACL,GAAG,IAAI;gCACP,YACE,WAAW,aAAa,IAAI,OAAO,WAAW,KAAK;4BACvD;wBACF;wBACA,OAAO;oBACT;oBAEA,qDAAqD;oBACrD,MAAM,iBAAiB,CAAC,2FAA2F,CAAC;oBACpH,qBAAqB;oBACrB,MAAM,GAAG,eAAe,WAAW,EAAE,cAAc;gBAEnD,4EAA4E;gBAC9E,EAAE,OAAO,kBAAkB;oBACzB,0DAA0D;oBAC1D,qBAAqB;oBACrB,MAAM;gBACR;YACF;QACF,SAAU;YACR,oBAAoB;YACpB,kBAAkB;QACpB;IACF;IAEA,iFAAiF;IACjF,MAAM,gBAAgB;IAEtB,MAAM,sBAAsB;QAC1B,IAAI;YACF,eAAe;YACf,iCAAiC;YACjC,wCAAwC;YACxC,MAAM,MAAM,IAAI;YAChB,MAAM,YAAY,IACf,WAAW,GACX,OAAO,CAAC,SAAS,KACjB,OAAO,CAAC,KAAK,KACb,KAAK,CAAC,GAAG;YAEZ,gEAAgE;YAChE,iDAAiD;YACjD,MAAM,gBAAgB,iBAAiB,QAAQ,QAAQ;YACvD,MAAM,kBAAkB,cAAc,GAAG,CAAC,CAAC,OAAS,CAAC;oBACnD,GAAG,IAAI;oBACP,QAAQ,KAAK,UAAU,GAAG,aAAa;gBACzC,CAAC;YAED,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB,CAAC,aAAa,EAAE,WAAW,EAAE,SAAS;gBACnE;gBACA;gBACA;gBACA;gBACA;aACD;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,8CAA8C;YAC9C,WAAW;gBACT,eAAe;YACjB,GAAG;QACL;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,OAAO,SAAS,WAAW;QAEjC,cAAc;QACd,IAAI,KAAK,QAAQ,CAAC,UAAU;YAC1B,IAAI,KAAK,QAAQ,CAAC,UAAU;gBAC1B,OAAO;YACT;YACA,OAAO;QACT;QAEA,gBAAgB;QAChB,IAAI,KAAK,QAAQ,CAAC,YAAY;YAC5B,OAAO;QACT;QAEA,YAAY;QACZ,IAAI,KAAK,QAAQ,CAAC,QAAQ;YACxB,OAAO,WAAW,6BAA6B;QACjD;QAEA,uBAAuB;QACvB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,aAAa,4BAA4B;YAClD;gBACE,OAAO;QACX;IACF;IAEA,uCAAuC;IACvC,MAAM,iBAAiB,CAAC,MAAc;QACpC,IAAI,CAAC,SAAS,CAAC,MAAM,OAAO;QAE5B,IAAI;YACF,MAAM,QAAQ,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE;YAClD,qBACE;0BACG,MAAM,GAAG,CAAC,CAAC,MAAM,IAChB,KAAK,WAAW,OAAO,MAAM,WAAW,mBACtC,6LAAC;wBAEC,WAAU;kCAET;uBAHI;;;;+BAMP;;QAKV,EAAE,OAAO,GAAG;YACV,+FAA+F;YAC/F,OAAO;QACT;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,8IAAA,CAAA,cAAW;wBACV,aAAY;wBACZ,UAAU,CAAC;4BACT,eAAe;4BACf,SAAS;wBACX;wBACA,MAAK;wBACL,YAAY;wBACZ,WAAU;wBACV,MAAK;;;;;;kCAEP,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO;oCACP,eAAe,CAAC;wCACd,gBAAgB;wCAChB,IAAI,sBAAsB;4CACxB,qBAAqB;wCACvB;oCACF;;sDAEA,6LAAC,qIAAA,CAAA,gBAAa;4CACZ,WAAW,iBAAiB,QAAQ,mBAAmB;sDAEvD,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;0DACtB,iBAAiB,sBAChB,6LAAC;8DAAK;;;;;yEAEN,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,6JAAA,CAAA,cAAW;wDACV,QAAQ;wDACR,MAAK;;;;;;;;;;;;;;;;;;;;;sDAMf,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAM,WAAU;8DAChC,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAS;gEAAC,WAAU;;;;;;0EACrB,6LAAC;0EAAK;;;;;;;;;;;;;;;;;8DAGV,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAS,WAAU;8DACnC,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,6JAAA,CAAA,cAAW;4DAAC,QAAO;4DAAS,MAAK;;;;;;;;;;;;;;;;8DAGtC,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAW,WAAU;8DACrC,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,6JAAA,CAAA,cAAW;4DAAC,QAAO;4DAAW,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQ7C,gCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO;oCACP,eAAe;oCACf,UAAU;;sDAEV,6LAAC,qIAAA,CAAA,gBAAa;4CACZ,WAAW,qBAAqB,QAAQ,mBAAmB;sDAE3D,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;gDACvB,gCACC,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAU,QAAQ;8DAAC;;;;;2DAGnC,cAAc,MAAM,WAAW,kBACjC,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;oDAAc,QAAQ;8DAAC;;;;;2DAIzC,cAAc,MAAM,IAAI,CAAC,uBACvB,6LAAC,qIAAA,CAAA,aAAU;wDAAiB,OAAO,OAAO,EAAE,CAAC,QAAQ;kEAClD,OAAO,IAAI;uDADG,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;0CAStC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,sIAAA,CAAA,kBAAe;kDACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;wDACV,UAAU;kEAET,4BACC,6LAAC;4DAAK,WAAU;;;;;iFAEhB,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAI1B,6LAAC,sIAAA,CAAA,iBAAc;8DACb,cAAA,6LAAC;kEAAG,cAAc,iBAAiB;;;;;;;;;;;;;;;;;;;;;;kDAIzC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;0DACjC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YAOxC,CAAC,eACC,qBAAqB,SAAS,kBAC/B,iBAAiB,KAAK,mBACtB,6LAAC;gBAAI,WAAU;;oBACZ,6BACC,6LAAC;;4BAAK;4BACG,cAAc,MAAM;4BAAE;4BAC5B,cAAc,MAAM,KAAK,IAAI,SAAS;4BAAQ;4BAC9C;4BAAY;;;;;;;oBAGhB,qBAAqB,SACpB,kBACA,cAAc,sBACZ,6LAAC;wBAAK,WAAU;;0CACd,6LAAC;0CAAK;;;;;;0CACN,6LAAC;;oCAAK;oCACgB;kDACpB,6LAAC;wCAAK,WAAU;kDACb,aAAa,IAAI,CAAC,IAAI,CACrB,CAAC,IAAM,EAAE,EAAE,CAAC,QAAQ,OAAO,mBAC1B,QAAQ;;;;;;;;;;;;0CAGf,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CACX;;;;;;;;;;;;oBAKN,iBAAiB,uBAChB,6LAAC;wBAAK,WAAU;;0CACd,6LAAC;0CAAK;;;;;;0CACN,6LAAC;;oCAAK;oCACI;kDACR,6LAAC;wCAAK,WAAU;kDACb,iBAAiB,WAAW,WAAW;;;;;;oCAClC;oCAAI;;;;;;;0CAGd,6LAAC;gCACC,SAAS;oCACP,gBAAgB;oCAChB,IAAI,sBAAsB;wCACxB,qBAAqB;oCACvB;gCACF;gCACA,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOT,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;sCACJ,6LAAC,oIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;kDACP,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,oIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;;;;;;;;;;;;sCAGpC,6LAAC,oIAAA,CAAA,YAAS;sCACP,0BACC,6LAAC,oIAAA,CAAA,WAAQ;0CACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oCAAC,SAAS;oCAAG,WAAU;8CAAmB;;;;;;;;;;uCAIpD,MAAM,MAAM,KAAK,kBACnB,6LAAC,oIAAA,CAAA,WAAQ;0CACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oCAAC,SAAS;oCAAG,WAAU;8CAC9B,cACG,CAAC,yBAAyB,EAAE,YAAY,EAAE,CAAC,GAC3C;;;;;;;;;;uCAGN,cAAc,MAAM,KAAK,kBAC3B,6LAAC,oIAAA,CAAA,WAAQ;0CACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;oCAAC,SAAS;oCAAG,WAAU;;wCAAmB;wCAC9C,iBAAiB,QAAQ,KAAK;wCAAa;wCAC9C,cAAc,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC,GAAG;wCAAG;wCAChD,iBAAiB,uBAChB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,SAAS;oDACP,gBAAgB;oDAChB,IAAI,sBAAsB;wDACxB,qBAAqB;oDACvB;gDACF;gDACA,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;uCAQT,cAAc,GAAG,CAAC,CAAC,qBACjB,6LAAC,oIAAA,CAAA,WAAQ;oCAEP,WAAW,CAAC,iCAAiC,EAC3C,KAAK,UAAU,GAAG,gBAAgB,IAClC;oCACF,SAAS,IAAM,eAAe,KAAK,EAAE;;sDAErC,6LAAC,oIAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,cACG,eAAe,KAAK,IAAI,EAAE,eAC1B,KAAK,IAAI;;;;;;sDAEf,6LAAC,oIAAA,CAAA,YAAS;sDACP,KAAK,KAAK,GACP,cACE,eAAe,KAAK,KAAK,EAAE,eAC3B,KAAK,KAAK,GACZ;;;;;;sDAEN,6LAAC,oIAAA,CAAA,YAAS;sDACP,KAAK,KAAK,GACP,cACE,eAAe,KAAK,KAAK,EAAE,eAC3B,KAAK,KAAK,GACZ;;;;;;sDAEN,6LAAC,oIAAA,CAAA,YAAS;sDACP,KAAK,IAAI,iBACR,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAS,oBAAoB,KAAK,IAAI,CAAC,IAAI;0DAC/C,KAAK,IAAI,CAAC,IAAI;;;;;uDAEf,KAAK,IAAI,iBACX,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAS,oBAAoB,KAAK,IAAI,CAAC,IAAI;0DAC/C,KAAK,IAAI,CAAC,IAAI;;;;;uDAEf,KAAK,SAAS,iBAChB,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAS,oBAAoB,KAAK,SAAS;0DAC/C,KAAK,SAAS;;;;;qEAGjB,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAU;;;;;;;;;;;sDAG7B,6LAAC,oIAAA,CAAA,YAAS;sDACP,KAAK,MAAM,GACR,KAAK,MAAM,CAAC,IAAI,GAChB,KAAK,MAAM,GACX,KAAK,MAAM,CAAC,IAAI,GAChB;;;;;;sDAEN,6LAAC,oIAAA,CAAA,YAAS;sDACP,oBAAoB,KAAK,EAAE,KAAK,+BAC/B,6LAAC,6JAAA,CAAA,qBAAkB;;;;qEAEnB,6LAAC,6JAAA,CAAA,cAAW;gDACV,QAAQ,KAAK,UAAU,GAAG,aAAa;gDACvC,UAAU;;;;;;;;;;;sDAIhB,6LAAC,oIAAA,CAAA,YAAS;sDAAE,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;sDACtC,6LAAC,oIAAA,CAAA,YAAS;sDACR,cAAA,6LAAC,+IAAA,CAAA,eAAY;;kEACX,6LAAC,+IAAA,CAAA,sBAAmB;wDAClB,OAAO;wDACP,SAAS,CAAC,IAAM,EAAE,eAAe;wDACjC,UAAU;kEAEV,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,WAAU;4DACV,UAAU;;8EAEV,6LAAC;oEAAK,WAAU;8EAAU;;;;;;gEACzB,iCACC,6LAAC;oEAAK,WAAU;;;;;yFAEhB,6LAAC,mNAAA,CAAA,iBAAc;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAIhC,6LAAC,+IAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,6LAAC,+IAAA,CAAA,oBAAiB;0EAAC;;;;;;0EACnB,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,eAAe,KAAK,EAAE;gEACxB;gEACA,UAAU;;kFAEV,6LAAC,qMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,eAAe,KAAK,EAAE;gEACxB;gEACA,UAAU;;kFAEV,6LAAC,8MAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,6LAAC,+IAAA,CAAA,wBAAqB;;;;;4DACrB,KAAK,UAAU,iBACd,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,mBAAmB,KAAK,EAAE,EAAE;gEAC9B;gEACA,UAAU;gEACV,WAAU;0EAET,oBAAoB,mBAAmB,KAAK,EAAE,iBAC7C;;sFACE,6LAAC;4EAAK,WAAU;;;;;;wEAAwF;;iGAI1G;;sFACE,6LAAC,8NAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;qFAM9C,6LAAC,+IAAA,CAAA,mBAAgB;gEACf,SAAS,CAAC;oEACR,EAAE,eAAe;oEACjB,mBAAmB,KAAK,EAAE,EAAE;gEAC9B;gEACA,UAAU;gEACV,WAAU;0EAET,oBAAoB,mBAAmB,KAAK,EAAE,iBAC7C;;sFACE,6LAAC;4EAAK,WAAU;;;;;;wEAAwF;;iGAI1G;;sFACE,6LAAC,mMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;mCA5IzC,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;YA4JvB,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iJAAA,CAAA,iBAAc;oBACb,aAAa,WAAW,WAAW;oBACnC,YAAY,WAAW,UAAU;oBACjC,cAAc,WAAW,YAAY;oBACrC,UAAU,WAAW,YAAY,IAAI;oBACrC,kBAAkB,CAAC;wBACjB,IAAI,WAAW,oBAAoB,EAAE;4BACnC,WAAW,oBAAoB,CAAC,YAAY,QAAQ;wBACtD;oBACF;oBACA,YAAY,WAAW,UAAU,IAAI,cAAc,MAAM;oBACzD,WAAW;oBACX,sBAAsB;oBACtB,eAAe;oBACf,sBAAsB;;;;;;;;;;;YAM3B,mCACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmB;;;;;;0CACjC,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;;;;;;;AAMhB;GA5vBgB;;QASC,qIAAA,CAAA,YAAS;QAQmC,0JAAA,CAAA,cAAW;QAE7C,oJAAA,CAAA,sBAAmB;;;KAnB9B", "debugId": null}}, {"offset": {"line": 8943, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/users/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { MainLayout } from \"@/components/layouts/main-layout\";\r\nimport { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\";\r\nimport { UsersTable } from \"@/features/users/components/users-table\";\r\nimport { useUsers } from \"@/features/users/hooks/use-users\";\r\nimport { User, UserCheck, Users as UsersIcon, UserX } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\n\r\nexport default function UsersPage() {\r\n  // Use simple React state instead of URL state for proper SPA behavior\r\n  const [searchQuery, setSearchQuery] = useState(\"\");\r\n  const [selectedBranchId, setSelectedBranchId] = useState<string>(\"all\");\r\n  const [statusFilter, setStatusFilter] = useState<\r\n    \"all\" | \"active\" | \"inactive\"\r\n  >(\"all\");\r\n  const [page, setPage] = useState(1);\r\n  const [pageSize, setPageSize] = useState(10);\r\n\r\n  // Prepare API params\r\n  const apiParams = {\r\n    page,\r\n    limit: pageSize,\r\n    search: searchQuery || undefined,\r\n    branch_id: selectedBranchId === \"all\" ? undefined : selectedBranchId,\r\n    status: statusFilter === \"all\" ? undefined : statusFilter,\r\n  };\r\n\r\n  const { data, isLoading } = useUsers(apiParams);\r\n\r\n  // Calculate total pages and items from the API response\r\n  const totalItems = data?.pagination?.total || 0;\r\n  // Calculate totalPages correctly if API doesn't provide it\r\n  const totalPages =\r\n    data?.pagination?.totalPages || Math.ceil(totalItems / pageSize) || 1;\r\n\r\n  // Handler functions using simple state management\r\n  const handleSearch = (query: string) => {\r\n    setSearchQuery(query);\r\n    setPage(1); // Reset to first page on new search\r\n  };\r\n\r\n  const handleStatusFilterChange = (status: \"all\" | \"active\" | \"inactive\") => {\r\n    setStatusFilter(status);\r\n    setPage(1); // Reset to first page on filter change\r\n  };\r\n\r\n  const handleBranchChange = (branchId: string) => {\r\n    setSelectedBranchId(branchId);\r\n    setPage(1); // Reset to first page on filter change\r\n  };\r\n\r\n  const handlePageChange = (newPage: number) => {\r\n    setPage(newPage);\r\n  };\r\n\r\n  const handlePageSizeChange = (newPageSize: number) => {\r\n    setPageSize(newPageSize);\r\n    setPage(1); // Reset to first page on page size change\r\n  };\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex justify-between items-center\">\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold tracking-tight\">Users</h1>\r\n            <p className=\"text-muted-foreground\">\r\n              Manage users and their access\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Total Users</CardTitle>\r\n              <UsersIcon className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {isLoading ? \"...\" : totalItems}\r\n              </div>\r\n              <p className=\"text-xs text-muted-foreground mt-1\">\r\n                All users (active and inactive)\r\n              </p>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Active Users\r\n              </CardTitle>\r\n              <UserCheck className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {isLoading\r\n                  ? \"...\"\r\n                  : data?.data\r\n                  ? data.data.filter((u) => !u.deleted_at).length\r\n                  : 0}\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">\r\n                Inactive Users\r\n              </CardTitle>\r\n              <UserX className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {isLoading\r\n                  ? \"...\"\r\n                  : data?.data\r\n                  ? data.data.filter((u) => u.deleted_at).length\r\n                  : 0}\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n          <Card>\r\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\r\n              <CardTitle className=\"text-sm font-medium\">Admins</CardTitle>\r\n              <User className=\"h-4 w-4 text-muted-foreground\" />\r\n            </CardHeader>\r\n            <CardContent>\r\n              <div className=\"text-2xl font-bold\">\r\n                {isLoading\r\n                  ? \"...\"\r\n                  : data?.data\r\n                  ? data.data.filter(\r\n                      (u) =>\r\n                        (u.Role?.name &&\r\n                          u.Role.name.toLowerCase().includes(\"admin\")) ||\r\n                        (u.role?.name &&\r\n                          u.role.name.toLowerCase().includes(\"admin\")) ||\r\n                        (u.role_name &&\r\n                          u.role_name.toLowerCase().includes(\"admin\"))\r\n                    ).length\r\n                  : 0}\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        <UsersTable\r\n          users={data?.data || []}\r\n          isLoading={isLoading}\r\n          onSearch={handleSearch}\r\n          selectedBranchId={selectedBranchId}\r\n          onBranchChange={handleBranchChange}\r\n          onStatusFilterChange={handleStatusFilterChange}\r\n          pagination={{\r\n            currentPage: page,\r\n            totalPages,\r\n            onPageChange: handlePageChange,\r\n            itemsPerPage: pageSize,\r\n            onItemsPerPageChange: (value) =>\r\n              handlePageSizeChange(parseInt(value, 10)),\r\n            totalItems,\r\n          }}\r\n        />\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,sEAAsE;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE7C;IACF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,qBAAqB;IACrB,MAAM,YAAY;QAChB;QACA,OAAO;QACP,QAAQ,eAAe;QACvB,WAAW,qBAAqB,QAAQ,YAAY;QACpD,QAAQ,iBAAiB,QAAQ,YAAY;IAC/C;IAEA,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,wDAAwD;IACxD,MAAM,aAAa,MAAM,YAAY,SAAS;IAC9C,2DAA2D;IAC3D,MAAM,aACJ,MAAM,YAAY,cAAc,KAAK,IAAI,CAAC,aAAa,aAAa;IAEtE,kDAAkD;IAClD,MAAM,eAAe,CAAC;QACpB,eAAe;QACf,QAAQ,IAAI,oCAAoC;IAClD;IAEA,MAAM,2BAA2B,CAAC;QAChC,gBAAgB;QAChB,QAAQ,IAAI,uCAAuC;IACrD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB;QACpB,QAAQ,IAAI,uCAAuC;IACrD;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ;IACV;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY;QACZ,QAAQ,IAAI,0CAA0C;IACxD;IAEA,qBACE,6LAAC,kJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;8BAMzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,uMAAA,CAAA,QAAS;4CAAC,WAAU;;;;;;;;;;;;8CAEvB,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDACZ,YAAY,QAAQ;;;;;;sDAEvB,6LAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;;;;;;;sCAKtD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;8CAEvB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,YACG,QACA,MAAM,OACN,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,IAAM,CAAC,EAAE,UAAU,EAAE,MAAM,GAC7C;;;;;;;;;;;;;;;;;sCAIV,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,6LAAC,2MAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,YACG,QACA,MAAM,OACN,KAAK,IAAI,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,UAAU,EAAE,MAAM,GAC5C;;;;;;;;;;;;;;;;;sCAIV,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;8CAElB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,YACG,QACA,MAAM,OACN,KAAK,IAAI,CAAC,MAAM,CACd,CAAC,IACC,AAAC,EAAE,IAAI,EAAE,QACP,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YACpC,EAAE,IAAI,EAAE,QACP,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YACpC,EAAE,SAAS,IACV,EAAE,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,UACvC,MAAM,GACR;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,6LAAC,4JAAA,CAAA,aAAU;oBACT,OAAO,MAAM,QAAQ,EAAE;oBACvB,WAAW;oBACX,UAAU;oBACV,kBAAkB;oBAClB,gBAAgB;oBAChB,sBAAsB;oBACtB,YAAY;wBACV,aAAa;wBACb;wBACA,cAAc;wBACd,cAAc;wBACd,sBAAsB,CAAC,QACrB,qBAAqB,SAAS,OAAO;wBACvC;oBACF;;;;;;;;;;;;;;;;;AAKV;GA9JwB;;QAmBM,oJAAA,CAAA,WAAQ;;;KAnBd", "debugId": null}}]}