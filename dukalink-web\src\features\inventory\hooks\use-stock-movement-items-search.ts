import { useQuery } from "@tanstack/react-query";
import apiClient from "@/lib/api-client";

export interface StockMovementItemsSearchParams {
  search?: string;
  status?: string;
  from_branch_id?: number;
  to_branch_id?: number;
  product_id?: number;
  reference_number?: string;
  date_from?: string;
  date_to?: string;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_direction?: string;
}

export interface StockMovementItemWithDetails {
  id: number;
  stock_movement_id: number;
  reference_number: string;
  from_branch_name: string;
  to_branch_name: string;
  product_id: number;
  product_name: string;
  product_sku: string;
  requested_quantity: number;
  dispatched_quantity: number;
  received_quantity: number;
  status: string;
  created_at: string;
  updated_at: string;
  notes: string;
  from_branch_location: string;
  to_branch_location: string;
  requested_by: string;
  requested_by_email: string;
  has_serial: boolean;
}

export interface StockMovementItemsResponse {
  data: StockMovementItemWithDetails[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/**
 * Hook to fetch stock movement items with server-side search and pagination
 * This uses the dedicated /stock-movement-items endpoint for better performance
 */
export const useStockMovementItemsSearch = (
  params: StockMovementItemsSearchParams = {},
  options?: any
) => {
  return useQuery<StockMovementItemsResponse>({
    queryKey: ["stock-movement-items-search", params],
    queryFn: async () => {
      console.log("Fetching stock movement items with server-side search:", params);
      
      const response = await apiClient.get<StockMovementItemsResponse>("/stock-movement-items", {
        params: {
          ...params,
          page: params.page || 1,
          limit: params.limit || 100,
          sort_by: params.sort_by || 'created_at',
          sort_direction: params.sort_direction || 'desc'
        }
      });

      console.log(`Server-side search returned ${response.data.length} items, total: ${response.pagination.total}`);
      
      return response;
    },
    ...options,
    // Enable the query by default, but allow override
    enabled: options?.enabled ?? true,
    // Refetch on window focus for fresh data
    refetchOnWindowFocus: false,
    // Cache for 5 minutes
    staleTime: 5 * 60 * 1000,
  });
};
