"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import {
  useCreateProcurementReceipt,
  useProcurementRequest,
} from "@/features/procurement/hooks/use-procurement";
import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import * as z from "zod";

const formSchema = z.object({
  procurement_request_id: z.number(),
  receipt_date: z.date(),
  delivery_note_number: z.string().optional(),
  notes: z.string().optional(),
  items: z.array(
    z.object({
      procurement_request_item_id: z.number(),
      quantity_received: z.coerce
        .number()
        .min(1, "Quantity must be at least 1")
        .positive("Quantity must be positive"),
      unit_price: z.coerce
        .number()
        .min(0, "Unit price must be at least 0")
        .nonnegative("Unit price must be non-negative"),
      serial_numbers: z.string().optional(),
      batch_number: z.string().optional(),
      expiry_date: z.union([
        z.string().length(0), // Allow empty string
        z.string().datetime(), // Valid ISO date string
        z.string().refine((date) => {
          if (!date || date.trim() === '') return true; // Allow empty
          const parsed = new Date(date);
          return !isNaN(parsed.getTime()); // Must be valid date if provided
        }, "Please enter a valid date or leave empty")
      ]).optional(),
      notes: z.string().optional(),
    })
  ),
});

type FormValues = z.infer<typeof formSchema>;

export default function NewProcurementReceiptPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const requestId = searchParams.get("requestId");

  const { data: requestData, isLoading: isLoadingRequest } =
    useProcurementRequest(requestId ? Number(requestId) : 0);
  const request = requestData?.data;

  const createMutation = useCreateProcurementReceipt();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      procurement_request_id: requestId ? Number(requestId) : 0,
      receipt_date: new Date(),
      delivery_note_number: "",
      notes: "",
      items: [],
    },
  });

  const { fields, replace } = useFieldArray({
    control: form.control,
    name: "items",
  });

  useEffect(() => {
    if (request && request.items) {
      // Initialize form items based on request items
      const formItems = request.items.map((item) => ({
        procurement_request_item_id: item.id,
        quantity_received: item.quantity,
        unit_price: item.unit_price,
        serial_numbers: "",
        batch_number: "",
        expiry_date: "",
        notes: "",
      }));
      replace(formItems);
    }
  }, [request, replace]);

  const onSubmit = (values: FormValues) => {
    const formattedValues = {
      ...values,
      receipt_date: values.receipt_date.toISOString(),
      items: values.items.map(item => ({
        ...item,
        // Convert empty strings to null for optional fields
        batch_number: item.batch_number?.trim() || null,
        expiry_date: item.expiry_date?.trim() || null,
        serial_numbers: item.serial_numbers?.trim() || null,
        notes: item.notes?.trim() || null,
      }))
    };

    createMutation.mutate(formattedValues, {
      onSuccess: (data) => {
        router.push(`/procurement/receipts/${data.data.id}`);
      },
    });
  };

  if (!requestId) {
    return (
      <div className="p-4">
        <Card>
          <CardHeader>
            <CardTitle>No Procurement Request Selected</CardTitle>
            <CardDescription>
              You need to select a procurement request to create a receipt
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              Please go to the procurement requests page and select a request to
              create a receipt for.
            </p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push("/procurement/requests")}>
              Go to Procurement Requests
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (isLoadingRequest) {
    return <div className="p-4">Loading...</div>;
  }

  if (!request) {
    return <div className="p-4">Procurement request not found</div>;
  }

  if (request.status !== "approved") {
    return (
      <div className="p-4">
        <Card>
          <CardHeader>
            <CardTitle>Invalid Request Status</CardTitle>
            <CardDescription>
              You can only create receipts for approved procurement requests
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              The selected procurement request has a status of{" "}
              <strong>{request.status}</strong>. Only requests with a status of{" "}
              <strong>approved</strong> can have receipts created.
            </p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push("/procurement/requests")}>
              Go to Procurement Requests
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 p-4 md:p-8">
      <div className="flex items-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
          className="mr-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Create Procurement Receipt</h1>
      </div>

      <Card className="mb-4">
        <CardHeader>
          <CardTitle>Procurement Request Details</CardTitle>
          <CardDescription>
            Creating receipt for request #{request.purchase_number}
          </CardDescription>
        </CardHeader>
        <CardContent className="grid gap-2 md:grid-cols-3">
          <div>
            <span className="text-sm font-medium">Supplier:</span>
            <span className="text-sm ml-2">{request.supplier?.name}</span>
          </div>
          <div>
            <span className="text-sm font-medium">Purchase Date:</span>
            <span className="text-sm ml-2">
              {new Date(request.purchase_date).toLocaleDateString()}
            </span>
          </div>
          <div>
            <span className="text-sm font-medium">Expected Delivery:</span>
            <span className="text-sm ml-2">
              {new Date(request.expected_delivery_date).toLocaleDateString()}
            </span>
          </div>
        </CardContent>
      </Card>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Receipt Details</CardTitle>
              <CardDescription>
                Enter the details for this procurement receipt
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="receipt_date"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Receipt Date</FormLabel>
                      <FormControl>
                        <DatePicker
                          date={field.value}
                          setDate={field.onChange}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="delivery_note_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Delivery Note Number</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter delivery note number"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        You can upload the delivery note after creating the
                        receipt
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any additional notes"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Items</CardTitle>
              <CardDescription>
                Enter the received quantities and details for each item
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>Requested Qty</TableHead>
                    <TableHead>Received Qty</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead>Order Number</TableHead>
                    <TableHead>Expiry Date</TableHead>
                    <TableHead>Serial Numbers</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {fields.map((field, index) => {
                    const requestItem = request.items?.find(
                      (item) => item.id === field.procurement_request_item_id
                    );
                    return (
                      <TableRow key={field.id}>
                        <TableCell>{requestItem?.product?.name}</TableCell>
                        <TableCell>{requestItem?.quantity}</TableCell>
                        <TableCell>
                          <FormField
                            control={form.control}
                            name={`items.${index}.quantity_received`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="1"
                                    {...field}
                                    onChange={(e) => {
                                      field.onChange(e);
                                      form.trigger(
                                        `items.${index}.quantity_received`
                                      );
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <FormField
                            control={form.control}
                            name={`items.${index}.unit_price`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    {...field}
                                    onChange={(e) => {
                                      field.onChange(e);
                                      form.trigger(
                                        `items.${index}.unit_price`
                                      );
                                    }}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <FormField
                            control={form.control}
                            name={`items.${index}.batch_number`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    placeholder="Batch number"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <FormField
                            control={form.control}
                            name={`items.${index}.expiry_date`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    type="date"
                                    placeholder="YYYY-MM-DD"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </TableCell>
                        <TableCell>
                          <FormField
                            control={form.control}
                            name={`items.${index}.serial_numbers`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    placeholder="Comma-separated serial numbers"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createMutation.isPending}
            >
              {createMutation.isPending ? "Creating..." : "Create Receipt"}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
