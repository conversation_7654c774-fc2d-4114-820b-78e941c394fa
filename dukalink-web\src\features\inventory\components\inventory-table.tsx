"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DataPagination } from "@/components/ui/data-pagination";
import { ManualSearchInput } from "@/components/ui/search-input";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatCurrency } from "@/lib/utils";
import { BranchInventory } from "@/types/inventory";
import {
  AlertTriangle,
  ArrowUpDown,
  Download,
  FileSpreadsheet,
  Package,
  Plus,
  Upload,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { exportInventoryToExcel } from "../utils/export-inventory";
import { InventoryExportDialog } from "./inventory-export-dialog";

interface InventoryTableProps {
  inventory: BranchInventory[];
  isLoading: boolean;
  onSearch?: (query: string) => void;
  pagination?: {
    page: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    limit?: number;
    onItemsPerPageChange?: (value: string) => void;
    total?: number;
  };
  // Props for enhanced export functionality
  currentFilters?: {
    branch_id?: number;
    search?: string;
    include_zero_stock?: boolean;
    include_inactive?: boolean;
  };
  // Search loading state
  isSearching?: boolean;
}

export function InventoryTable({
  inventory,
  isLoading,
  onSearch,
  pagination,
  currentFilters,
  isSearching = false,
}: InventoryTableProps) {
  const router = useRouter();
  // Search is now handled by ManualSearchInput component

  const handleExportToExcel = () => {
    if (inventory && inventory.length > 0) {
      exportInventoryToExcel(
        inventory,
        `inventory-export-${new Date().toISOString().split("T")[0]}`
      );
    }
  };

  return (
    <div className="space-y-4">
      {/* Search and action buttons */}
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <ManualSearchInput
          placeholder="Search inventory by product name..."
          onSearch={onSearch}
          className="w-full max-w-sm"
          isLoading={isSearching || isLoading}
        />
        <div className="flex flex-wrap gap-2">
          <Button onClick={() => router.push("/inventory/add")}>
            <Plus className="mr-2 h-4 w-4" /> Add Stock Item
          </Button>
          <Button onClick={() => router.push("/inventory/import")}>
            <Upload className="mr-2 h-4 w-4" /> Import Stock Items
          </Button>
          <Button onClick={() => router.push("/inventory/adjust")}>
            <Plus className="mr-2 h-4 w-4" /> Adjust Stock
          </Button>
          <Button onClick={() => router.push("/inventory/excel")}>
            <FileSpreadsheet className="mr-2 h-4 w-4" /> Excel Import/Export
          </Button>
          <InventoryExportDialog
            filters={currentFilters || {}}
            totalRecords={pagination?.total || inventory.length}
            trigger={
              <Button
                variant="outline"
                disabled={isLoading || inventory.length === 0}
              >
                <Download className="mr-2 h-4 w-4" /> Export Inventory
              </Button>
            }
          />
        </div>
      </div>

      {/* Inventory table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Product</TableHead>
              <TableHead>
                <div className="flex items-center">
                  Stock Quantity
                  <ArrowUpDown className="ml-2 h-4 w-4" />
                </div>
              </TableHead>
              <TableHead>Reorder Level</TableHead>
              <TableHead>Retail Price</TableHead>
              <TableHead>Wholesale Price</TableHead>
              <TableHead>Total Retail Value</TableHead>
              <TableHead>Branch</TableHead>
              <TableHead>Last Updated</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Show loading skeletons when loading
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-10 w-10 rounded-full" />
                      <div className="space-y-1">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-3 w-16" />
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-10" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-10" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-16" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-24" />
                  </TableCell>
                  <TableCell className="text-right">
                    <Skeleton className="h-9 w-16 ml-auto" />
                  </TableCell>
                </TableRow>
              ))
            ) : inventory.length === 0 ? (
              // Show message when no items are found
              <TableRow>
                <TableCell colSpan={9} className="h-32 text-center">
                  <div className="flex flex-col items-center justify-center space-y-2">
                    <Package className="h-8 w-8 text-muted-foreground" />
                    <div className="text-sm font-medium">
                      No inventory items found
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Try adjusting your search or add new products to inventory
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              // Show inventory items
              inventory.map((item) => {
                // Safely handle potentially undefined values
                const isLowStock =
                  item.reorder_level && item.quantity <= item.reorder_level;

                // Get prices from different possible locations
                const retailPrice =
                  (item.default_selling_price
                    ? parseFloat(item.default_selling_price)
                    : 0) ||
                  (item.selling_price ? parseFloat(item.selling_price) : 0) ||
                  0;

                const wholesalePrice =
                  (item.default_wholesale_price
                    ? parseFloat(item.default_wholesale_price)
                    : 0) || 0;

                // Calculate total retail value
                const totalRetailValue = retailPrice * (item.quantity || 0);

                return (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-10 w-10">
                          <AvatarImage
                            src={
                              item.Product?.image_url || item.product?.image_url
                            }
                            alt={item.Product?.name || item.product?.name}
                          />
                          <AvatarFallback>
                            {item.Product?.name || item.product?.name
                              ? (
                                  (item.Product?.name ||
                                    item.product?.name) as string
                                )
                                  .split(" ")
                                  .map((n) => n[0] || "")
                                  .join("")
                                  .toUpperCase()
                                  .substring(0, 2)
                              : "NA"}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {item.Product?.name ||
                              item.product?.name ||
                              "Unknown Product"}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            SKU:{" "}
                            {item.Product?.sku || item.product?.sku || "N/A"}
                          </div>
                          {(item.Product?.barcode || item.product?.barcode) && (
                            <div className="text-sm text-muted-foreground">
                              Barcode:{" "}
                              {item.Product?.barcode || item.product?.barcode}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {isLowStock ? (
                        <Badge
                          variant="destructive"
                          className="flex items-center gap-1"
                        >
                          <AlertTriangle className="h-3 w-3" />
                          {item.quantity}
                        </Badge>
                      ) : (
                        item.quantity
                      )}
                    </TableCell>
                    <TableCell>{item.reorder_level || "N/A"}</TableCell>
                    <TableCell>{formatCurrency(retailPrice)}</TableCell>
                    <TableCell>{formatCurrency(wholesalePrice)}</TableCell>
                    <TableCell>{formatCurrency(totalRetailValue)}</TableCell>
                    <TableCell>
                      {item.Branch?.name || item.branch?.name || "N/A"}
                    </TableCell>
                    <TableCell>
                      {item.updated_at || item.last_updated
                        ? new Date(
                            item.updated_at || item.last_updated || ""
                          ).toLocaleDateString()
                        : "N/A"}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          router.push(`/inventory/stock-items/${item.id}`)
                        }
                      >
                        View
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination controls */}
      {pagination && (
        <div className="px-4 py-2 border-t">
          <DataPagination
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            onPageChange={pagination.onPageChange}
            pageSize={pagination.limit || 100}
            onPageSizeChange={(newPageSize) => {
              if (pagination.onItemsPerPageChange) {
                pagination.onItemsPerPageChange(newPageSize.toString());
              }
            }}
            totalItems={pagination.total || 0}
            isLoading={isLoading}
            showPageSizeSelector={true}
            showItemsInfo={true}
            showFirstLastButtons={true}
          />
        </div>
      )}
    </div>
  );
}
