import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import salesByCategoryExportService, {
  SalesByCategoryExportParams,
  CustomSalesByCategoryExportParams,
} from "../api/sales-by-category-export-service";

/**
 * Hook for comprehensive sales by category export
 * Exports all data with multiple sheets
 */
export const useSalesByCategoryExportAll = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: SalesByCategoryExportParams) => {
      // Validate parameters
      const validation = salesByCategoryExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await salesByCategoryExportService.exportAllSalesByCategory(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = salesByCategoryExportService.generateFilename(params, 'comprehensive');
        salesByCategoryExportService.downloadBlob(blob, filename);
        
        toast.success("Sales by category exported successfully!", {
          description: `Comprehensive report with all data sheets downloaded as ${filename}`,
          duration: 5000,
        });

        // Invalidate related queries to refresh any cached data
        queryClient.invalidateQueries({ queryKey: ["sales-by-category"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Export error:', error);
      
      let errorMessage = "Failed to export sales by category";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export sales reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try filtering the data or use lightweight export.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for custom sales by category export
 * Allows format and sheet selection
 */
export const useSalesByCategoryExportCustom = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: CustomSalesByCategoryExportParams) => {
      // Validate parameters
      const validation = salesByCategoryExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await salesByCategoryExportService.exportCustomSalesByCategory(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const formatType = params.format_type || 'detailed';
        const filename = salesByCategoryExportService.generateFilename(params, formatType);
        salesByCategoryExportService.downloadBlob(blob, filename);
        
        toast.success("Custom sales by category export completed!", {
          description: `${formatType.charAt(0).toUpperCase() + formatType.slice(1)} report downloaded as ${filename}`,
          duration: 5000,
        });

        queryClient.invalidateQueries({ queryKey: ["sales-by-category"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Custom export error:', error);
      
      let errorMessage = "Failed to export custom sales by category";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export sales reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try using lightweight format or filtering the data.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Custom Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for lightweight sales by category export
 * Fastest export with essential data only
 */
export const useSalesByCategoryExportLightweight = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: SalesByCategoryExportParams) => {
      const validation = salesByCategoryExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await salesByCategoryExportService.exportLightweightSalesByCategory(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = salesByCategoryExportService.generateFilename(params, 'lightweight');
        salesByCategoryExportService.downloadBlob(blob, filename);
        
        toast.success("Quick export completed!", {
          description: `Category summary report downloaded as ${filename}`,
          duration: 4000,
        });

        queryClient.invalidateQueries({ queryKey: ["sales-by-category"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Lightweight export error:', error);
      
      let errorMessage = "Failed to export category sales data";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export sales reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. The system may be busy, please try again.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Quick Export Failed", {
        description: errorMessage,
        duration: 6000,
      });
    },
  });
};

/**
 * Hook to get export recommendations based on data size
 */
export const useSalesByCategoryExportRecommendation = (estimatedRecords: number) => {
  return salesByCategoryExportService.getExportRecommendation(estimatedRecords);
};

/**
 * Hook to get export format options for UI
 */
export const useSalesByCategoryExportFormatOptions = () => {
  return salesByCategoryExportService.getExportFormatOptions();
};

/**
 * Hook to get column options for custom export
 */
export const useSalesByCategoryExportColumnOptions = () => {
  return salesByCategoryExportService.getColumnOptions();
};

/**
 * Combined hook that provides all export functionality
 * Convenient single hook for components that need multiple export options
 */
export const useSalesByCategoryExportSuite = () => {
  const exportAll = useSalesByCategoryExportAll();
  const exportCustom = useSalesByCategoryExportCustom();
  const exportLightweight = useSalesByCategoryExportLightweight();

  const isAnyExporting = exportAll.isPending || exportCustom.isPending || exportLightweight.isPending;

  return {
    // Individual export methods
    exportAll: exportAll.mutateAsync,
    exportCustom: exportCustom.mutateAsync,
    exportLightweight: exportLightweight.mutateAsync,
    
    // Loading states
    isExportingAll: exportAll.isPending,
    isExportingCustom: exportCustom.isPending,
    isExportingLightweight: exportLightweight.isPending,
    isAnyExporting,
    
    // Error states
    exportAllError: exportAll.error,
    exportCustomError: exportCustom.error,
    exportLightweightError: exportLightweight.error,
    
    // Utility functions
    getRecommendation: salesByCategoryExportService.getExportRecommendation,
    getFormatOptions: salesByCategoryExportService.getExportFormatOptions,
    getColumnOptions: salesByCategoryExportService.getColumnOptions,
    validateParams: salesByCategoryExportService.validateExportParams,
    generateFilename: salesByCategoryExportService.generateFilename,
    
    // Reset functions
    resetAll: () => {
      exportAll.reset();
      exportCustom.reset();
      exportLightweight.reset();
    },
  };
};
