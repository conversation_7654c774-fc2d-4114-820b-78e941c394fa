"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Download,
  FileSpreadsheet,
  Settings,
  Zap,
  Clock,
  AlertCircle,
  Smartphone,
} from "lucide-react";
import {
  useMpesaTransactionsExportSuite,
  useMpesaTransactionsExportRecommendation,
} from "../hooks/use-mpesa-transactions-export";
import { MpesaTransactionsExportParams, CustomMpesaTransactionsExportParams } from "../api/mpesa-transactions-export-service";

interface MpesaTransactionsExportDialogProps {
  filters: any; // Current filters from the MPESA transactions page
  totalRecords?: number;
  trigger?: React.ReactNode;
}

export function MpesaTransactionsExportDialog({
  filters,
  totalRecords = 0,
  trigger,
}: MpesaTransactionsExportDialogProps) {
  const [open, setOpen] = useState(false);
  const [exportType, setExportType] = useState<"all" | "custom" | "lightweight">("lightweight");
  const [customOptions, setCustomOptions] = useState({
    include_summary: true,
    include_details: true,
    include_daily_breakdown: true,
    include_branch_breakdown: true,
    include_type_breakdown: true,
    include_status_breakdown: true,
    include_charts: true,
    format_type: "detailed" as "detailed" | "summary",
    columns: "all",
  });

  const exportSuite = useMpesaTransactionsExportSuite();
  const recommendation = useMpesaTransactionsExportRecommendation(totalRecords);

  const handleExport = async () => {
    try {
      const baseParams: MpesaTransactionsExportParams = {
        start_date: filters.start_date,
        end_date: filters.end_date,
        branch_id: filters.branch_id,
        region_id: filters.region_id,
        transaction_type: filters.transaction_type,
        status: filters.status,
        mpesa_code: filters.mpesa_code,
      };

      switch (exportType) {
        case "all":
          await exportSuite.exportAll({
            ...baseParams,
            include_summary: true,
            include_details: true,
            include_daily_breakdown: true,
            include_branch_breakdown: true,
            include_type_breakdown: true,
            include_status_breakdown: true,
            include_charts: true,
          });
          break;

        case "custom":
          const customParams: CustomMpesaTransactionsExportParams = {
            ...baseParams,
            ...customOptions,
          };
          await exportSuite.exportCustom(customParams);
          break;

        case "lightweight":
          await exportSuite.exportLightweight(baseParams);
          break;
      }

      setOpen(false);
    } catch (error) {
      // Error handling is done in the hooks
      console.error("Export failed:", error);
    }
  };

  const getExportIcon = (type: string) => {
    switch (type) {
      case "all":
        return <FileSpreadsheet className="h-4 w-4" />;
      case "custom":
        return <Settings className="h-4 w-4" />;
      case "lightweight":
        return <Zap className="h-4 w-4" />;
      default:
        return <Download className="h-4 w-4" />;
    }
  };

  const getEstimatedTime = () => {
    if (totalRecords <= 1000) return "< 30 seconds";
    if (totalRecords <= 5000) return "30-60 seconds";
    return "60-90 seconds";
  };

  const isExporting = exportSuite.isAnyExporting;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" disabled={isExporting}>
            {isExporting ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export MPESA
              </>
            )}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            Export MPESA Transactions
          </DialogTitle>
          <DialogDescription>
            Choose your export format and options. Exporting {totalRecords.toLocaleString()} MPESA transaction records.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Recommendation Banner */}
          <div className="rounded-lg border bg-blue-50 p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">Export Guidelines</h4>
                <p className="text-sm text-blue-700 mt-1">{recommendation.message}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-blue-700">
                    Estimated time: {recommendation.estimatedTime}
                  </span>
                </div>
                {totalRecords > 5000 && (
                  <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
                    <strong>Large Dataset Notice:</strong> For performance, consider using Quick Export for faster processing.
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Export Type Selection */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Export Format</Label>
            <div className="grid grid-cols-1 gap-3">
              {/* Quick Export */}
              <div
                className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                  exportType === "lightweight"
                    ? "border-green-500 bg-green-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setExportType("lightweight")}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    <input
                      type="radio"
                      checked={exportType === "lightweight"}
                      onChange={() => setExportType("lightweight")}
                      className="h-4 w-4"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      <span className="font-medium">Quick Export</span>
                      <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                        Recommended
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      MPESA transaction summary data only - fastest option (&lt; 30 seconds)
                    </p>
                  </div>
                </div>
              </div>

              {/* Custom Export */}
              <div
                className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                  exportType === "custom"
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setExportType("custom")}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    <input
                      type="radio"
                      checked={exportType === "custom"}
                      onChange={() => setExportType("custom")}
                      className="h-4 w-4"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      <span className="font-medium">Custom Export</span>
                      {recommendation.recommended === "custom" && (
                        <Badge variant="secondary" className="text-xs">
                          Recommended
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Select specific sheets and data to include (30-60 seconds)
                    </p>
                  </div>
                </div>
              </div>

              {/* Comprehensive Export */}
              <div
                className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                  exportType === "all"
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setExportType("all")}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    <input
                      type="radio"
                      checked={exportType === "all"}
                      onChange={() => setExportType("all")}
                      className="h-4 w-4"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <FileSpreadsheet className="h-4 w-4" />
                      <span className="font-medium">Comprehensive Export</span>
                      {recommendation.recommended === "all" && (
                        <Badge variant="secondary" className="text-xs">
                          Recommended
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Complete MPESA report with summary, details, and breakdowns (60-90 seconds)
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Custom Options */}
          {exportType === "custom" && (
            <>
              <Separator />
              <div className="space-y-4">
                <Label className="text-base font-medium">Custom Options</Label>
                
                {/* Format Type */}
                <div className="space-y-2">
                  <Label className="text-sm">Format Type</Label>
                  <Select
                    value={customOptions.format_type}
                    onValueChange={(value: "detailed" | "summary") =>
                      setCustomOptions({ ...customOptions, format_type: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="detailed">Detailed (All columns)</SelectItem>
                      <SelectItem value="summary">Summary (Essential columns)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Include Sheets */}
                <div className="space-y-3">
                  <Label className="text-sm">Include Sheets</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_summary"
                        checked={customOptions.include_summary}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_summary: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_summary" className="text-sm">
                        Transaction Summary
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_details"
                        checked={customOptions.include_details}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_details: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_details" className="text-sm">
                        Transaction Details
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_daily_breakdown"
                        checked={customOptions.include_daily_breakdown}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_daily_breakdown: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_daily_breakdown" className="text-sm">
                        Daily Breakdown
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_branch_breakdown"
                        checked={customOptions.include_branch_breakdown}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_branch_breakdown: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_branch_breakdown" className="text-sm">
                        Branch Breakdown
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_type_breakdown"
                        checked={customOptions.include_type_breakdown}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_type_breakdown: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_type_breakdown" className="text-sm">
                        Type Breakdown
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_status_breakdown"
                        checked={customOptions.include_status_breakdown}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_status_breakdown: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_status_breakdown" className="text-sm">
                        Status Breakdown
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Current Filters Display */}
          {(filters.start_date || filters.end_date || filters.branch_id || filters.region_id || filters.transaction_type || filters.status || filters.mpesa_code) && (
            <>
              <Separator />
              <div className="space-y-2">
                <Label className="text-sm font-medium">Applied Filters</Label>
                <div className="flex flex-wrap gap-2">
                  {filters.start_date && filters.end_date && (
                    <Badge variant="outline">Date: {filters.start_date} to {filters.end_date}</Badge>
                  )}
                  {filters.branch_id && (
                    <Badge variant="outline">Branch: {filters.branch_id}</Badge>
                  )}
                  {filters.region_id && (
                    <Badge variant="outline">Region: {filters.region_id}</Badge>
                  )}
                  {filters.transaction_type && (
                    <Badge variant="outline">Type: {filters.transaction_type}</Badge>
                  )}
                  {filters.status && (
                    <Badge variant="outline">Status: {filters.status}</Badge>
                  )}
                  {filters.mpesa_code && (
                    <Badge variant="outline">MPESA Code: {filters.mpesa_code}</Badge>
                  )}
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)} disabled={isExporting}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                Exporting...
              </>
            ) : (
              <>
                {getExportIcon(exportType)}
                <span className="ml-2">Export ({getEstimatedTime()})</span>
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
