{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/hooks/use-permissions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCurrentUser } from \"./use-auth\";\r\nimport { usePermissionContext } from \"../context/permission-context\";\r\n\r\n// Define constants for resources and actions\r\nexport const RESOURCES = {\r\n  USERS: \"users\",\r\n  ROLES: \"roles\",\r\n  PERMISSIONS: \"permissions\",\r\n  PRODUCTS: \"products\",\r\n  CATEGORIES: \"categories\",\r\n  BRANDS: \"brands\",\r\n  INVENTORY: \"inventory\",\r\n  STOCK_ITEMS: \"stock_items\",\r\n  BRANCHES: \"branches\",\r\n  EMPLOYEES: \"employees\",\r\n  BANKING: \"banking\",\r\n  EXPENSES: \"expenses\",\r\n  EXPENSE_FIRST_APPROVAL: \"expense_first_approval\",\r\n  EXPENSE_SECOND_APPROVAL: \"expense_second_approval\",\r\n  SALES: \"sales\",\r\n  POS_SESSIONS: \"pos_sessions\",\r\n  CUSTOMERS: \"customers\",\r\n  PROFILE: \"profile\",\r\n  STOCK_LOCATIONS: \"stock_locations\",\r\n};\r\n\r\nexport const ACTIONS = {\r\n  CREATE: \"create\",\r\n  READ: \"read\",\r\n  UPDATE: \"update\",\r\n  DELETE: \"delete\",\r\n};\r\n\r\nexport const SCOPE = {\r\n  ANY: \"any\",\r\n  OWN: \"own\",\r\n};\r\n\r\nexport function usePermissions() {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const {\r\n    permissions,\r\n    isLoading: isPermissionsLoading,\r\n    hasPermission,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n  } = usePermissionContext();\r\n\r\n  // Check if the user is a company admin\r\n  const isCompanyAdmin = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the company_admin role\r\n    return (\r\n      user.role_name === \"company_admin\" ||\r\n      user.role_name === \"tenant_admin\" ||\r\n      user.role_name === \"super_admin\"\r\n    );\r\n  };\r\n\r\n  // Check if the user is a branch manager\r\n  const isBranchManager = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the branch_manager role\r\n    return user.role_name === \"branch_manager\";\r\n  };\r\n\r\n  // Check if the user can manage stock locations (create, update, delete)\r\n  const canManageStockLocations = (): boolean => {\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.CREATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.UPDATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.DELETE, SCOPE.ANY) ||\r\n      isCompanyAdmin()\r\n    );\r\n  };\r\n\r\n  // Check if the user can view stock locations\r\n  const canViewStockLocations = (): boolean => {\r\n    // Check for explicit permission or fallback to any authenticated user\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.READ, SCOPE.ANY) ||\r\n      !!user\r\n    );\r\n  };\r\n\r\n  // Check if the user can perform an action on a resource\r\n  const can = (\r\n    resource: string,\r\n    action: string,\r\n    scope: \"any\" | \"own\" = \"any\"\r\n  ): boolean => {\r\n    return hasPermission(resource, action, scope);\r\n  };\r\n\r\n  // Check if the user has any of the specified permissions\r\n  const hasAnyPermission = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.some(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  // Check if the user has all of the specified permissions\r\n  const hasAllPermissions = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.every(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  return {\r\n    isCompanyAdmin,\r\n    isBranchManager,\r\n    canManageStockLocations,\r\n    canViewStockLocations,\r\n    can,\r\n    hasPermission,\r\n    hasAnyPermission,\r\n    hasAllPermissions,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n    permissions, // Expose the raw permissions for debugging\r\n    isLoading: isUserLoading || isPermissionsLoading,\r\n    RESOURCES,\r\n    ACTIONS,\r\n    SCOPE,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;AAHA;;;AAMO,MAAM,YAAY;IACvB,OAAO;IACP,OAAO;IACP,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,aAAa;IACb,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;IACV,wBAAwB;IACxB,yBAAyB;IACzB,OAAO;IACP,cAAc;IACd,WAAW;IACX,SAAS;IACT,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,QAAQ;IACnB,KAAK;IACL,KAAK;AACP;AAEO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EACJ,WAAW,EACX,WAAW,oBAAoB,EAC/B,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,+JAAA,CAAA,uBAAoB,AAAD;IAEvB,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,+CAA+C;QAC/C,OACE,KAAK,SAAS,KAAK,mBACnB,KAAK,SAAS,KAAK,kBACnB,KAAK,SAAS,KAAK;IAEvB;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAElB,gDAAgD;QAChD,OAAO,KAAK,SAAS,KAAK;IAC5B;IAEA,wEAAwE;IACxE,MAAM,0BAA0B;QAC9B,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE;IAEJ;IAEA,6CAA6C;IAC7C,MAAM,wBAAwB;QAC5B,sEAAsE;QACtE,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,KAChE,CAAC,CAAC;IAEN;IAEA,wDAAwD;IACxD,MAAM,MAAM,CACV,UACA,QACA,QAAuB,KAAK;QAE5B,OAAO,cAAc,UAAU,QAAQ;IACzC;IAEA,yDAAyD;IACzD,MAAM,mBAAmB,CACvB;QAMA,OAAO,YAAY,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC1D,cAAc,UAAU,QAAQ;IAEpC;IAEA,yDAAyD;IACzD,MAAM,oBAAoB,CACxB;QAMA,OAAO,YAAY,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC3D,cAAc,UAAU,QAAQ;IAEpC;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,iBAAiB;QAC5B;QACA;QACA;IACF;AACF;GArGgB;;QACmC,kJAAA,CAAA,iBAAc;QAO3D,+JAAA,CAAA,uBAAoB", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/route-permissions.ts"], "sourcesContent": ["/**\r\n * Route Permission Mapping\r\n *\r\n * This file maps routes to the permissions required to access them.\r\n * Each key represents a route pattern, and the value is the permission required.\r\n */\r\n\r\nimport { NavigationPermission } from \"./navigation-permissions\";\r\n\r\nexport const routePermissions: Record<string, NavigationPermission> = {\r\n  // Dashboard routes\r\n  \"/dashboard\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/company\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/branch\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/finance\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/operations\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/stock\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/float\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/pos\": { resource: \"dashboard\", action: \"view\" },\r\n\r\n  // User management routes\r\n  \"/users\": { resource: \"users\", action: \"view\" },\r\n  \"/users/create\": { resource: \"users\", action: \"create\" },\r\n  \"/users/[id]\": { resource: \"users\", action: \"view\" },\r\n  \"/users/[id]/edit\": { resource: \"users\", action: \"update\" },\r\n\r\n  // Role management routes\r\n  \"/roles\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/create\": { resource: \"roles\", action: \"create\" },\r\n  \"/roles/[id]\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/[id]/edit\": { resource: \"roles\", action: \"update\" },\r\n\r\n  // RBAC routes\r\n  \"/rbac\": { resource: \"permissions\", action: \"manage\" },\r\n\r\n  // Branch management routes\r\n  \"/branches\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/create\": { resource: \"branches\", action: \"create\" },\r\n  \"/branches/[id]\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/[id]/edit\": { resource: \"branches\", action: \"update\" },\r\n\r\n  // Location management routes\r\n  \"/locations\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/locations/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n  \"/location-guides\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/location-guides/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n\r\n  // Employee management routes\r\n  \"/employees\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/create\": { resource: \"employees\", action: \"create\" },\r\n  \"/employees/[id]\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/[id]/edit\": { resource: \"employees\", action: \"update\" },\r\n\r\n  // Product management routes\r\n  \"/products\": { resource: \"products\", action: \"view\" },\r\n  \"/products/create\": { resource: \"products\", action: \"create\" },\r\n  \"/products/[id]\": { resource: \"products\", action: \"view\" },\r\n  \"/products/[id]/edit\": { resource: \"products\", action: \"update\" },\r\n\r\n  // Category management routes\r\n  \"/categories\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/create\": { resource: \"categories\", action: \"create\" },\r\n  \"/categories/[id]\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/[id]/edit\": { resource: \"categories\", action: \"update\" },\r\n\r\n  // Brand management routes\r\n  \"/brands\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/create\": { resource: \"brands\", action: \"create\" },\r\n  \"/brands/[id]\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/[id]/edit\": { resource: \"brands\", action: \"update\" },\r\n\r\n  // Inventory management routes\r\n  \"/inventory\": { resource: \"inventory\", action: \"view\" },\r\n  \"/inventory/transfers\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/bulk-transfer\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/reports\": { resource: \"inventory\", action: \"report\" },\r\n  \"/inventory/stock-cards\": { resource: \"inventory\", action: \"view\" },\r\n\r\n  // Banking routes\r\n  \"/banking\": { resource: \"banking\", action: \"view\" },\r\n  \"/banking/summary\": { resource: \"banking\", action: \"view\" },\r\n\r\n  // MPESA routes\r\n  \"/mpesa\": { resource: \"mpesa\", action: \"view\" },\r\n  \"/mpesa/transactions\": { resource: \"mpesa\", action: \"view\" },\r\n\r\n  // Float management routes\r\n  \"/float\": { resource: \"float\", action: \"view\" },\r\n  \"/float/movements\": { resource: \"float\", action: \"view\" },\r\n  \"/float/reconciliations\": { resource: \"float\", action: \"view\" },\r\n  \"/float/topups\": { resource: \"float\", action: \"view\" },\r\n\r\n  // DSA routes\r\n  \"/dsa\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/agents\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/assignments\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/reconciliations\": { resource: \"dsa\", action: \"view\" },\r\n\r\n  // Phone repairs routes\r\n  \"/phone-repairs\": { resource: \"phone_repairs\", action: \"view\" },\r\n  \"/phone-repairs/[id]\": { resource: \"phone_repairs\", action: \"view\" },\r\n\r\n  // Expense management routes\r\n  \"/expenses\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/create\": { resource: \"expenses\", action: \"create\" },\r\n  \"/expenses/[id]\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/[id]/edit\": { resource: \"expenses\", action: \"update\" },\r\n  \"/expense-analytics\": { resource: \"expenses\", action: \"view\" },\r\n\r\n  // Credit Note routes\r\n  \"/credit-notes\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/new\": { resource: \"invoices\", action: \"create\" },\r\n  \"/credit-notes/[id]\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/[id]/edit\": { resource: \"invoices\", action: \"update\" },\r\n\r\n  // Report routes\r\n  \"/reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-summary\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-item\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-category\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-employee\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-payment-type\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-banking\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/cash-status\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/running-balances\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-transactions\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/phone-repairs\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/expense-reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/dsa-sales\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/receipts\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/shifts\": { resource: \"reports\", action: \"view\" },\r\n\r\n  // Settings routes\r\n  \"/settings\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/system\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/company\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/payment-methods\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/health\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // Profile route\r\n  \"/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // POS routes\r\n  \"/pos\": { resource: \"pos\", action: \"view\" },\r\n  \"/pos/sessions\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]/edit\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/close\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/shift-closing\": {\r\n    resource: \"pos_sessions\",\r\n    action: \"view\",\r\n  },\r\n\r\n  // Sales routes\r\n  \"/sales\": { resource: \"sales\", action: \"view\" },\r\n  \"/sales/[id]\": { resource: \"sales\", action: \"view\" },\r\n\r\n  // Customer routes\r\n  \"/customers\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/create\": { resource: \"customers\", action: \"create\" },\r\n  \"/customers/[id]\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/[id]/edit\": { resource: \"customers\", action: \"update\" },\r\n\r\n  // Procurement routes\r\n  \"/procurement\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests/new\": { resource: \"procurement\", action: \"create\" },\r\n  \"/procurement/receipts\": { resource: \"procurement\", action: \"view\" },\r\n\r\n  // Supplier routes\r\n  \"/suppliers\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/create\": { resource: \"suppliers\", action: \"create\" },\r\n  \"/suppliers/[id]\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/[id]/edit\": { resource: \"suppliers\", action: \"update\" },\r\n\r\n  // Tenant routes (Super Admin only)\r\n  \"/tenants\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/create\": { resource: \"tenants\", action: \"create\" },\r\n  \"/tenants/[id]\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/[id]/edit\": { resource: \"tenants\", action: \"update\" },\r\n\r\n  // Super Admin routes\r\n  \"/super-admin\": { resource: \"tenants\", action: \"view\" },\r\n\r\n  // Debug routes\r\n  \"/permission-debug\": { resource: \"settings\", action: \"manage\" },\r\n};\r\n\r\n/**\r\n * Get the permission required for a route\r\n * @param route The route to check\r\n * @returns The permission required for the route, or null if no permission is required\r\n */\r\nexport function getRoutePermission(route: string): NavigationPermission | null {\r\n  // First try exact match\r\n  if (routePermissions[route]) {\r\n    return routePermissions[route];\r\n  }\r\n\r\n  // Then try dynamic routes\r\n  for (const [pattern, permission] of Object.entries(routePermissions)) {\r\n    if (pattern.includes(\"[\") && matchDynamicRoute(route, pattern)) {\r\n      return permission;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Check if a route matches a dynamic route pattern\r\n * @param route The route to check\r\n * @param pattern The pattern to match against\r\n * @returns Whether the route matches the pattern\r\n */\r\nfunction matchDynamicRoute(route: string, pattern: string): boolean {\r\n  const routeParts = route.split(\"/\");\r\n  const patternParts = pattern.split(\"/\");\r\n\r\n  if (routeParts.length !== patternParts.length) {\r\n    return false;\r\n  }\r\n\r\n  for (let i = 0; i < patternParts.length; i++) {\r\n    if (patternParts[i].startsWith(\"[\") && patternParts[i].endsWith(\"]\")) {\r\n      // This is a dynamic part, so it matches anything\r\n      continue;\r\n    }\r\n\r\n    if (patternParts[i] !== routeParts[i]) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAIM,MAAM,mBAAyD;IACpE,mBAAmB;IACnB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC7D,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,kBAAkB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,cAAc;IACd,SAAS;QAAE,UAAU;QAAe,QAAQ;IAAS;IAErD,2BAA2B;IAC3B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAClE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,2BAA2B;QAAE,UAAU;QAAa,QAAQ;IAAS;IACrE,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,8BAA8B;QAAE,UAAU;QAAa,QAAQ;IAAS;IAExE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,eAAe;QAAE,UAAU;QAAc,QAAQ;IAAO;IACxD,sBAAsB;QAAE,UAAU;QAAc,QAAQ;IAAS;IACjE,oBAAoB;QAAE,UAAU;QAAc,QAAQ;IAAO;IAC7D,yBAAyB;QAAE,UAAU;QAAc,QAAQ;IAAS;IAEpE,0BAA0B;IAC1B,WAAW;QAAE,UAAU;QAAU,QAAQ;IAAO;IAChD,kBAAkB;QAAE,UAAU;QAAU,QAAQ;IAAS;IACzD,gBAAgB;QAAE,UAAU;QAAU,QAAQ;IAAO;IACrD,qBAAqB;QAAE,UAAU;QAAU,QAAQ;IAAS;IAE5D,8BAA8B;IAC9B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAW;IACpE,4BAA4B;QAAE,UAAU;QAAa,QAAQ;IAAW;IACxE,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAChE,0BAA0B;QAAE,UAAU;QAAa,QAAQ;IAAO;IAElE,iBAAiB;IACjB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,oBAAoB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE1D,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,uBAAuB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAE3D,0BAA0B;IAC1B,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAO;IACxD,0BAA0B;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9D,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAErD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,eAAe;QAAE,UAAU;QAAO,QAAQ;IAAO;IACjD,oBAAoB;QAAE,UAAU;QAAO,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAO,QAAQ;IAAO;IAE1D,uBAAuB;IACvB,kBAAkB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAC9D,uBAAuB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAEnE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAChE,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAE7D,qBAAqB;IACrB,iBAAiB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACxD,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC7D,2BAA2B;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEpE,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,kCAAkC;QAAE,UAAU;QAAW,QAAQ;IAAO;IACxE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,wBAAwB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC9D,6BAA6B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACnE,+BAA+B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACrE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,4BAA4B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClE,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC5D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC3D,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEzD,kBAAkB;IAClB,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,6BAA6B;QAAE,UAAU;QAAY,QAAQ;IAAS;IACtE,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC3D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE3D,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAElD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,iBAAiB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IAC5D,sBAAsB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IACjE,2BAA2B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACxE,4BAA4B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACzE,oCAAoC;QAClC,UAAU;QACV,QAAQ;IACV;IAEA,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IAEnD,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAC1D,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IACnE,6BAA6B;QAAE,UAAU;QAAe,QAAQ;IAAS;IACzE,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAEnE,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,mCAAmC;IACnC,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAC3D,iBAAiB;QAAE,UAAU;QAAW,QAAQ;IAAO;IACvD,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAE9D,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEtD,eAAe;IACf,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;AAChE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,wBAAwB;IACxB,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,OAAO,gBAAgB,CAAC,MAAM;IAChC;IAEA,0BAA0B;IAC1B,KAAK,MAAM,CAAC,SAAS,WAAW,IAAI,OAAO,OAAO,CAAC,kBAAmB;QACpE,IAAI,QAAQ,QAAQ,CAAC,QAAQ,kBAAkB,OAAO,UAAU;YAC9D,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,KAAa,EAAE,OAAe;IACvD,MAAM,aAAa,MAAM,KAAK,CAAC;IAC/B,MAAM,eAAe,QAAQ,KAAK,CAAC;IAEnC,IAAI,WAAW,MAAM,KAAK,aAAa,MAAM,EAAE;QAC7C,OAAO;IACT;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,IAAI,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM;YAEpE;QACF;QAEA,IAAI,YAAY,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/role-guard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect, useState } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\nimport { getRoutePermission } from \"@/lib/route-permissions\";\r\nimport { hasRouteAccess, getDashboardByRole } from \"@/lib/rbac-config\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\nfunction mapActionToGrantAction(action: string): string {\r\n  switch (action) {\r\n    case \"view\":\r\n      return \"read:any\";\r\n    case \"create\":\r\n      return \"create:any\";\r\n    case \"update\":\r\n      return \"update:any\";\r\n    case \"delete\":\r\n      return \"delete:any\";\r\n    case \"manage\":\r\n      return \"update:any\"; // Manage maps to update:any\r\n    case \"transfer\":\r\n      return \"update:any\"; // Transfer maps to update:any\r\n    case \"report\":\r\n      return \"read:any\"; // Report maps to read:any\r\n    default:\r\n      return `${action}:any`;\r\n  }\r\n}\r\n\r\ninterface RoleGuardProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function RoleGuard({ children }: RoleGuardProps) {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const { hasPermission, isLoading: isPermissionsLoading } = usePermissions();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Track if this is the initial mount\r\n  const [isInitialMount, setIsInitialMount] = useState(true);\r\n\r\n  // Effect to mark initial mount as complete\r\n  useEffect(() => {\r\n    setIsInitialMount(false);\r\n  }, []);\r\n\r\n  // Effect to check access rights\r\n  useEffect(() => {\r\n    // Skip check if on auth pages\r\n    if (\r\n      pathname.startsWith(\"/login\") ||\r\n      pathname.startsWith(\"/forgot-password\") ||\r\n      pathname.startsWith(\"/reset-password\")\r\n    ) {\r\n      setLoading(\"role\", false);\r\n      return;\r\n    }\r\n\r\n    // Only show loading indicator on initial mount or when user data is loading\r\n    // and we haven't shown the loading screen before in this session\r\n    if (isInitialMount && isUserLoading && !hasShownLoading.role) {\r\n      setLoading(\"role\", true);\r\n      return;\r\n    } else {\r\n      setLoading(\"role\", false);\r\n    }\r\n\r\n    // If user data and permissions are loaded, check access rights\r\n    if (!isUserLoading && !isPermissionsLoading && user) {\r\n      // Safely access role_name with type checking\r\n      const roleName =\r\n        user && typeof user === \"object\" && \"role_name\" in user\r\n          ? user.role_name\r\n          : \"\";\r\n\r\n      // First try permission-based access control\r\n      const routePermission = getRoutePermission(pathname);\r\n\r\n      let hasAccess = true;\r\n      let accessMethod = \"default\";\r\n\r\n      // Use RBAC-based access control\r\n      hasAccess = hasRouteAccess(pathname, roleName);\r\n      accessMethod = \"rbac\";\r\n\r\n      // Add detailed debug logging\r\n      console.log(`[RoleGuard] Route: ${pathname}`);\r\n      console.log(`[RoleGuard] User role: ${roleName}`);\r\n      console.log(`[RoleGuard] Using RBAC-based access control`);\r\n      console.log(\r\n        `[RoleGuard] Access ${hasAccess ? \"GRANTED\" : \"DENIED\"} by RBAC check`\r\n      );\r\n\r\n      // If we have a permission mapping, check it too (for debugging purposes only)\r\n      if (routePermission) {\r\n        // Map the action to the format used in grants (e.g., \"view\" -> \"read:any\")\r\n        const grantAction = mapActionToGrantAction(routePermission.action);\r\n\r\n        // Check if user has the required permission (but don't use the result)\r\n        const permissionAccess = hasPermission(\r\n          routePermission.resource,\r\n          grantAction,\r\n          routePermission.scope\r\n        );\r\n\r\n        // Log the permission check result for debugging\r\n        console.log(\r\n          `[RoleGuard] Permission check (not used): ${\r\n            routePermission.resource\r\n          }:${routePermission.action}:${routePermission.scope || \"any\"}`\r\n        );\r\n        console.log(`[RoleGuard] Mapped to grant action: ${grantAction}`);\r\n        console.log(\r\n          `[RoleGuard] Permission would be ${\r\n            permissionAccess ? \"GRANTED\" : \"DENIED\"\r\n          } (for future reference)`\r\n        );\r\n      }\r\n\r\n      if (!hasAccess) {\r\n        console.log(\r\n          `[RoleGuard] Access denied for ${pathname}, redirecting to dashboard (method: ${accessMethod})`\r\n        );\r\n\r\n        // Redirect to the appropriate dashboard based on role using RBAC configuration\r\n        const dashboardRoute = getDashboardByRole(roleName);\r\n        console.log(\r\n          `[RoleGuard] Redirecting to ${dashboardRoute} for role ${roleName}`\r\n        );\r\n        router.replace(dashboardRoute);\r\n      }\r\n\r\n      // User data is loaded, we can stop checking and mark as shown\r\n      setLoading(\"role\", false);\r\n      markLoadingShown(\"role\");\r\n    } else if (!isUserLoading && !isPermissionsLoading) {\r\n      // No user data and not loading, stop checking\r\n      setLoading(\"role\", false);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    user,\r\n    isUserLoading,\r\n    isPermissionsLoading,\r\n    pathname,\r\n    router,\r\n    isInitialMount,\r\n    hasShownLoading.role,\r\n    hasPermission,\r\n  ]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in checking state\r\n  useEffect(() => {\r\n    let timeoutId: NodeJS.Timeout | null = null;\r\n\r\n    if (isLoading.role) {\r\n      timeoutId = setTimeout(() => {\r\n        setLoading(\"role\", false);\r\n        markLoadingShown(\"role\");\r\n      }, 1500); // 1.5 second timeout for better UX\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.role]);\r\n\r\n  // Show loading state only when actively checking role access\r\n  if (isLoading.role) {\r\n    return <LoadingScreen message=\"Checking access...\" />;\r\n  }\r\n\r\n  // Don't block rendering, the useEffect will handle redirects\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAaA;;;;CAIC,GACD,SAAS,uBAAuB,MAAc;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,cAAc,4BAA4B;QACnD,KAAK;YACH,OAAO,cAAc,8BAA8B;QACrD,KAAK;YACH,OAAO,YAAY,0BAA0B;QAC/C;YACE,OAAO,GAAG,OAAO,IAAI,CAAC;IAC1B;AACF;AAMO,SAAS,UAAU,EAAE,QAAQ,EAAkB;;IACpD,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EAAE,aAAa,EAAE,WAAW,oBAAoB,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;IAEX,qCAAqC;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,kBAAkB;QACpB;8BAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,8BAA8B;YAC9B,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,uBACpB,SAAS,UAAU,CAAC,oBACpB;gBACA,WAAW,QAAQ;gBACnB;YACF;YAEA,4EAA4E;YAC5E,iEAAiE;YACjE,IAAI,kBAAkB,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;gBAC5D,WAAW,QAAQ;gBACnB;YACF,OAAO;gBACL,WAAW,QAAQ;YACrB;YAEA,+DAA+D;YAC/D,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,MAAM;gBACnD,6CAA6C;gBAC7C,MAAM,WACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;gBAEN,4CAA4C;gBAC5C,MAAM,kBAAkB,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD,EAAE;gBAE3C,IAAI,YAAY;gBAChB,IAAI,eAAe;gBAEnB,gCAAgC;gBAChC,YAAY,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBACrC,eAAe;gBAEf,6BAA6B;gBAC7B,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;gBAC5C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU;gBAChD,QAAQ,GAAG,CAAC,CAAC,2CAA2C,CAAC;gBACzD,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,YAAY,YAAY,SAAS,cAAc,CAAC;gBAGxE,8EAA8E;gBAC9E,IAAI,iBAAiB;oBACnB,2EAA2E;oBAC3E,MAAM,cAAc,uBAAuB,gBAAgB,MAAM;oBAEjE,uEAAuE;oBACvE,MAAM,mBAAmB,cACvB,gBAAgB,QAAQ,EACxB,aACA,gBAAgB,KAAK;oBAGvB,gDAAgD;oBAChD,QAAQ,GAAG,CACT,CAAC,yCAAyC,EACxC,gBAAgB,QAAQ,CACzB,CAAC,EAAE,gBAAgB,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,IAAI,OAAO;oBAEhE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,aAAa;oBAChE,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAC/B,mBAAmB,YAAY,SAChC,uBAAuB,CAAC;gBAE7B;gBAEA,IAAI,CAAC,WAAW;oBACd,QAAQ,GAAG,CACT,CAAC,8BAA8B,EAAE,SAAS,oCAAoC,EAAE,aAAa,CAAC,CAAC;oBAGjG,+EAA+E;oBAC/E,MAAM,iBAAiB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAAE;oBAC1C,QAAQ,GAAG,CACT,CAAC,2BAA2B,EAAE,eAAe,UAAU,EAAE,UAAU;oBAErE,OAAO,OAAO,CAAC;gBACjB;gBAEA,8DAA8D;gBAC9D,WAAW,QAAQ;gBACnB,iBAAiB;YACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;gBAClD,8CAA8C;gBAC9C,WAAW,QAAQ;YACrB;QACA,uDAAuD;QACzD;8BAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,IAAI;QACpB;KACD;IAED,kEAAkE;IAClE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,YAAmC;YAEvC,IAAI,UAAU,IAAI,EAAE;gBAClB,YAAY;2CAAW;wBACrB,WAAW,QAAQ;wBACnB,iBAAiB;oBACnB;0CAAG,OAAO,mCAAmC;YAC/C;YAEA;uCAAO;oBACL,IAAI,WAAW,aAAa;gBAC9B;;QACA,uDAAuD;QACzD;8BAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6DAA6D;IAC7D,IAAI,UAAU,IAAI,EAAE;QAClB,qBAAO,6LAAC,yJAAA,CAAA,gBAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,6DAA6D;IAC7D,qBAAO;kBAAG;;AACZ;GAjJgB;;QACmC,kJAAA,CAAA,iBAAc;QACJ,yJAAA,CAAA,iBAAc;QAC1D,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QAE1B,yJAAA,CAAA,aAAU;;;KANE", "debugId": null}}, {"offset": {"line": 874, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;iCAAE;YACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;YACvE,MAAM;kDAAW;oBACf,YAAY,OAAO,UAAU,GAAG;gBAClC;;YACA,IAAI,gBAAgB,CAAC,UAAU;YAC/B,YAAY,OAAO,UAAU,GAAG;YAChC;yCAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;;QACjD;gCAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX;GAdgB", "debugId": null}}, {"offset": {"line": 911, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,6LAAC,qKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;KAFS;AAIT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,6LAAC,qKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,qKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,6LAAC,mMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAnCS;AAqCT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,qKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KARS", "debugId": null}}, {"offset": {"line": 1206, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,6LAAC,sKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,6LAAC;kBACC,cAAA,6LAAC,sKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;MARS;AAUT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,6LAAC,sKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C;MAtBS", "debugId": null}}, {"offset": {"line": 1303, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { VariantProps, cva } from \"class-variance-authority\";\r\nimport { PanelLeftIcon } from \"lucide-react\";\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = \"16rem\";\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\";\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\";\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\";\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\";\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\";\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  // Use useMemo to prevent recreating the button on every render\r\n  const button = React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, variant, className, props]);\r\n\r\n  // Use useMemo for tooltipContent to prevent recalculation on every render\r\n  const tooltipContent = React.useMemo(() => {\r\n    if (!tooltip) return null;\r\n    return typeof tooltip === \"string\" ? { children: tooltip } : tooltip;\r\n  }, [tooltip]);\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  // Use useMemo for the hidden prop to prevent recalculation on every render\r\n  const isHidden = React.useMemo(() => {\r\n    return state !== \"collapsed\" || isMobile;\r\n  }, [state, isMobile]);\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={isHidden}\r\n        {...tooltipContent}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, className, showOnHover, props]);\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [className, props]);\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean;\r\n  size?: \"sm\" | \"md\";\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, className, props]);\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;;;AApBA;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GAPS;AAST,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;gDAC9B,CAAC;YACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;YAC9D,IAAI,aAAa;gBACf,YAAY;YACd,OAAO;gBACL,SAAS;YACX;YAEA,kDAAkD;YAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;QACpG;+CACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sDAAE;YACtC,OAAO,WAAW;8DAAc,CAAC,OAAS,CAAC;+DAAQ;8DAAQ,CAAC,OAAS,CAAC;;QACxE;qDAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;qCAAE;YACd,MAAM;2DAAgB,CAAC;oBACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;wBACA,MAAM,cAAc;wBACpB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;iDAC/B,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;gDACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,6LAAC,sIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,6LAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;IAhGS;;QAaU,gIAAA,CAAA,cAAW;;;KAbrB;AAkGT,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,6LAAC,oIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,6LAAC,oIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,6LAAC,oIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC,oIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,6LAAC,oIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,6LAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,6LAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,6LAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;IApGS;;QAYgD;;;MAZhD;AAsGT,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IAxBS;;QAKmB;;;MALnB;AA0BT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,6LAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;IAvBS;;QACmB;;;MADnB;AAyBT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,6LAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,6LAAC,wIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAZS;AAcT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;OAnBS;AAqBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OArBS;AAuBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;OATS;AAWT,MAAM,4BAA4B,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAClC,+rBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;;IAChD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,+DAA+D;IAC/D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;6CAAE,kBAC3B,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,aAAW;gBACX,eAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;oBAAE;oBAAS;gBAAK,IAAI;gBAC3D,GAAG,KAAK;;;;;;4CAEV;QAAC;QAAM;QAAM;QAAU;QAAS;QAAW;KAAM;IAEpD,0EAA0E;IAC1E,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qDAAE;YACnC,IAAI,CAAC,SAAS,OAAO;YACrB,OAAO,OAAO,YAAY,WAAW;gBAAE,UAAU;YAAQ,IAAI;QAC/D;oDAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,2EAA2E;IAC3E,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;+CAAE;YAC7B,OAAO,UAAU,eAAe;QAClC;8CAAG;QAAC;QAAO;KAAS;IAEpB,qBACE,6LAAC,sIAAA,CAAA,UAAO;;0BACN,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,6LAAC,sIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ;gBACP,GAAG,cAAc;;;;;;;;;;;;AAI1B;IAtDS;;QAcqB;;;OAdrB;AAwDT,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;gBAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;gBAED,GAAG,KAAK;;;;;;oCAEV;QAAC;QAAM;QAAW;QAAa;KAAM;AAC1C;IA/BS;OAAA;AAiCT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;;IAC5B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;oCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;gBAED,GAAG,KAAK;;;;;;mCAEV;QAAC;QAAW;KAAM;AACvB;IArBS;OAAA;AAuBT,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;8CAAE;YAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;QAClD;6CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,6LAAC,uIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;IApCS;OAAA;AAsCT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;OAbS;AAeT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;OAZS;AAcT,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;wCAAE,kBACnB,6LAAC;gBACC,aAAU;gBACV,gBAAa;gBACb,aAAW;gBACX,eAAa;gBACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;gBAED,GAAG,KAAK;;;;;;uCAEV;QAAC;QAAM;QAAM;QAAU;QAAW;KAAM;AAC7C;IA/BS;OAAA", "debugId": null}}, {"offset": {"line": 2109, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/navigation-link.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { ReactNode, MouseEvent, useMemo } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface NavigationLinkProps {\r\n  href: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;\r\n  replace?: boolean;\r\n  prefetch?: boolean;\r\n  scroll?: boolean;\r\n  exact?: boolean;\r\n}\r\n\r\n/**\r\n * NavigationLink component that ensures client-side navigation\r\n * This component wraps Next.js Link component and provides a consistent way to navigate\r\n */\r\nexport function NavigationLink({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(className)}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n      aria-current={isActive ? \"page\" : undefined}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n\r\n/**\r\n * NavigationButton component that looks like a button but navigates like a link\r\n */\r\nexport function NavigationButton({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(\r\n        \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAsBO,SAAS,eAAe,EAC7B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE;YACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;QACzD;2CAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;QACb,gBAAc,WAAW,SAAS;kBAEjC;;;;;;AAGP;GA5CgB;;QAUC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAXd;AAiDT,SAAS,iBAAiB,EAC/B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE;YACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;QACzD;6CAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0RACA;QAEF,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;kBAEZ;;;;;;AAGP;IA9CgB;;QAUC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;MAXd", "debugId": null}}, {"offset": {"line": 2230, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,6LAAC,0KAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6LAAC,0KAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,6LAAC,0KAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS", "debugId": null}}, {"offset": {"line": 2287, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-main.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\nimport { ChevronRight, CircleIcon, type LucideIcon } from \"lucide-react\";\r\n\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: {\r\n    title: string;\r\n    url: string;\r\n    icon?: LucideIcon;\r\n    isActive?: boolean;\r\n    items?: {\r\n      title: string;\r\n      url: string;\r\n    }[];\r\n  }[];\r\n}) {\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          // Special case for Dashboard - make it a direct link\r\n          if (item.title === \"Dashboard\") {\r\n            return (\r\n              <SidebarMenuItem key={item.title}>\r\n                <SidebarMenuButton\r\n                  asChild\r\n                  tooltip={item.title}\r\n                  isActive={item.isActive}\r\n                  className=\"nav-main-item\"\r\n                >\r\n                  <NavigationLink href={item.url}>\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                  </NavigationLink>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          }\r\n\r\n          // For all other items, use the collapsible dropdown\r\n          return (\r\n            <Collapsible\r\n              key={item.title}\r\n              asChild\r\n              defaultOpen={item.isActive}\r\n              className=\"group/collapsible mb-2\"\r\n            >\r\n              <SidebarMenuItem>\r\n                <CollapsibleTrigger asChild>\r\n                  <SidebarMenuButton\r\n                    tooltip={item.title}\r\n                    isActive={item.isActive}\r\n                    className=\"nav-main-item\"\r\n                  >\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                    <ChevronRight className=\"ml-auto h-2 w-2 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 nav-main-item-chevron\" />\r\n                  </SidebarMenuButton>\r\n                </CollapsibleTrigger>\r\n                <CollapsibleContent>\r\n                  <SidebarMenuSub className=\"nav-main-sub mt-2\">\r\n                    {item.items?.map((subItem) => (\r\n                      <SidebarMenuSubItem key={subItem.title}>\r\n                        <SidebarMenuSubButton asChild>\r\n                          <NavigationLink href={subItem.url} exact={true}>\r\n                            {/* No circle icon for child items */}\r\n                            <span className=\"text-xs\">{subItem.title}</span>\r\n                          </NavigationLink>\r\n                        </SidebarMenuSubButton>\r\n                      </SidebarMenuSubItem>\r\n                    ))}\r\n                  </SidebarMenuSub>\r\n                </CollapsibleContent>\r\n              </SidebarMenuItem>\r\n            </Collapsible>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAKA;AAVA;;;;;;AAoBO,SAAS,QAAQ,EACtB,KAAK,EAYN;IACC,qBACE,6LAAC,sIAAA,CAAA,eAAY;kBACX,cAAA,6LAAC,sIAAA,CAAA,cAAW;sBACT,MAAM,GAAG,CAAC,CAAC;gBACV,qDAAqD;gBACrD,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kCACd,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,SAAS,KAAK,KAAK;4BACnB,UAAU,KAAK,QAAQ;4BACvB,WAAU;sCAEV,cAAA,6LAAC,iJAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,GAAG;;kDAC5B,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CAClC,KAAK,QAAQ,kBACZ,6LAAC,6MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAG1B,6LAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAfG,KAAK,KAAK;;;;;gBAqBpC;gBAEA,oDAAoD;gBACpD,qBACE,6LAAC,0IAAA,CAAA,cAAW;oBAEV,OAAO;oBACP,aAAa,KAAK,QAAQ;oBAC1B,WAAU;8BAEV,cAAA,6LAAC,sIAAA,CAAA,kBAAe;;0CACd,6LAAC,0IAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;oCAChB,SAAS,KAAK,KAAK;oCACnB,UAAU,KAAK,QAAQ;oCACvB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,kBAAI,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDAClC,KAAK,QAAQ,kBACZ,6LAAC,6MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAG1B,6LAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;sDAEb,6LAAC,yNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG5B,6LAAC,0IAAA,CAAA,qBAAkB;0CACjB,cAAA,6LAAC,sIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,6LAAC,sIAAA,CAAA,qBAAkB;sDACjB,cAAA,6LAAC,sIAAA,CAAA,uBAAoB;gDAAC,OAAO;0DAC3B,cAAA,6LAAC,iJAAA,CAAA,iBAAc;oDAAC,MAAM,QAAQ,GAAG;oDAAE,OAAO;8DAExC,cAAA,6LAAC;wDAAK,WAAU;kEAAW,QAAQ,KAAK;;;;;;;;;;;;;;;;2CAJrB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;mBA3BzC,KAAK,KAAK;;;;;YAyCrB;;;;;;;;;;;AAIR;KA5FgB", "debugId": null}}, {"offset": {"line": 2505, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6LAAC,qKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 2567, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,6LAAC,+KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MAlBS;AAoBT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,6LAAC,+KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,6LAAC,+KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;MAxBS;AA0BT,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;MATS;AAWT,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,6MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;MAtBS;AAwBT,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAlBS;AAoBT,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;OAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS;AAgBT,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,6LAAC,+KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;OAJS;AAMT,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;OAtBS;AAwBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf;OAdS", "debugId": null}}, {"offset": {"line": 2863, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-user.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Chev<PERSON>UpDown, LogOut } from \"lucide-react\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavUser({\r\n  user,\r\n}: {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n  };\r\n}) {\r\n  const { isMobile } = useSidebar();\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground nav-user\"\r\n            >\r\n              <Avatar className=\"h-6 w-6 rounded-lg nav-user-avatar\">\r\n                <AvatarImage src={user.avatar} alt={user.name} />\r\n                <AvatarFallback className=\"rounded-lg text-xs\">\r\n                  {user.name.charAt(0)}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left leading-tight nav-user-details\">\r\n                <span className=\"truncate text-xs font-medium\">\r\n                  {user.name}\r\n                </span>\r\n                <span className=\"truncate text-[10px]\">{user.email}</span>\r\n              </div>\r\n              <ChevronsUpDown className=\"ml-auto h-3 w-3 nav-user-chevron\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar} alt={user.name} />\r\n                  <AvatarFallback className=\"rounded-lg\">CN</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name}</span>\r\n                  <span className=\"truncate text-xs\">{user.email}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem>\r\n                <BadgeCheck className=\"mr-2 h-3.5 w-3.5\" />\r\n                <span className=\"text-xs\">Account</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem>\r\n              <LogOut className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"text-xs\">Log out</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AASA;;;AAdA;;;;;AAqBO,SAAS,QAAQ,EACtB,IAAI,EAOL;;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IAE9B,qBACE,6LAAC,sIAAA,CAAA,cAAW;kBACV,cAAA,6LAAC,sIAAA,CAAA,kBAAe;sBACd,cAAA,6LAAC,+IAAA,CAAA,eAAY;;kCACX,6LAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,6LAAC,sIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,6LAAC,qIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,6LAAC,qIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;8CAGtB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;sDAEZ,6LAAC;4CAAK,WAAU;sDAAwB,KAAK,KAAK;;;;;;;;;;;;8CAEpD,6LAAC,iOAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,6LAAC,+IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,6LAAC,+IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6LAAC,qIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,6LAAC,qIAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,6LAAC;oDAAK,WAAU;8DAAoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIpD,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC,+IAAA,CAAA,oBAAiB;0CAChB,cAAA,6LAAC,+IAAA,CAAA,mBAAgB;;sDACf,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0CACtB,6LAAC,+IAAA,CAAA,mBAAgB;;kDACf,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GAtEgB;;QASO,sIAAA,CAAA,aAAU;;;KATjB", "debugId": null}}, {"offset": {"line": 3135, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport \"@/components/ui/sidebar-collapsed.css\";\r\nimport \"@/components/ui/sidebar-compact.css\";\r\nimport {\r\n  Banknote,\r\n  BarChart3 as BarChart3Icon,\r\n  Building as BuildingIcon,\r\n  FileText,\r\n  GalleryVerticalEnd,\r\n  Home as HomeIcon,\r\n  Landmark,\r\n  Package,\r\n  Smartphone,\r\n  UserCheck,\r\n} from \"lucide-react\";\r\n\r\nimport { NavMain } from \"@/components/nav-main\";\r\nimport { NavUser } from \"@/components/nav-user\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n// Import usePermissions for future use\r\n// import { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\n// import { getRoutePermission } from \"@/lib/route-permissions\";\r\n// import { navigationPermissions } from \"@/lib/navigation-permissions\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  hasRouteAccess,\r\n  isNavi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n} from \"@/lib/rbac-config\";\r\n\r\ntype NavItem = {\r\n  title: string;\r\n  url: string;\r\n  icon?: any;\r\n  isActive?: boolean;\r\n  items?: { title: string; url: string }[];\r\n};\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * This function is commented out for MVP, will be used in the future\r\n * when the permission system is fully implemented\r\n *\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\n// function mapActionToGrantAction(action: string): string {\r\n//   switch (action) {\r\n//     case \"view\":\r\n//       return \"read:any\";\r\n//     case \"create\":\r\n//       return \"create:any\";\r\n//     case \"update\":\r\n//       return \"update:any\";\r\n//     case \"delete\":\r\n//       return \"delete:any\";\r\n//     case \"manage\":\r\n//       return \"update:any\"; // Manage maps to update:any\r\n//     case \"transfer\":\r\n//       return \"update:any\"; // Transfer maps to update:any\r\n//     case \"report\":\r\n//       return \"read:any\"; // Report maps to read:any\r\n//     default:\r\n//       return `${action}:any`;\r\n//   }\r\n// }\r\n\r\nexport function AppSidebar() {\r\n  const { data: user } = useCurrentUser();\r\n  const pathname = usePathname();\r\n  // Permissions will be used in the future\r\n  // const { hasPermission, permissions } = usePermissions();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  // Define all navigation items\r\n  const getAllNavigationItems = (): NavItem[] => [\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/dashboard\",\r\n      icon: HomeIcon,\r\n      isActive: pathname === \"/dashboard\" || pathname === \"/\",\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      url: \"/reports/sales-summary\",\r\n      icon: BarChart3Icon,\r\n      isActive:\r\n        pathname.startsWith(\"/reports\") ||\r\n        pathname.startsWith(\"/expense-analytics\"),\r\n      items: [\r\n        {\r\n          title: \"Sales Summary\",\r\n          url: \"/reports/sales-summary\",\r\n        },\r\n        {\r\n          title: \"Sales by Item\",\r\n          url: \"/reports/sales-by-item\",\r\n        },\r\n        {\r\n          title: \"Sales by Category\",\r\n          url: \"/reports/sales-by-category\",\r\n        },\r\n        {\r\n          title: \"Current Stock Levels\",\r\n          url: \"/reports/current-stock-levels\",\r\n        },\r\n        {\r\n          title: \"Stock History\",\r\n          url: \"/reports/stock-history\",\r\n        },\r\n        {\r\n          title: \"Banking Transactions\",\r\n          url: \"/reports/banking-transactions\",\r\n        },\r\n        {\r\n          title: \"Tax Report\",\r\n          url: \"/reports/tax-report\",\r\n        },\r\n        {\r\n          title: \"Banking Summary\",\r\n          url: \"/banking?tab=summary\",\r\n        },\r\n        {\r\n          title: \"M-Pesa Transactions\",\r\n          url: \"/reports/mpesa-transactions\",\r\n        },\r\n        {\r\n          title: \"Running Balances\",\r\n          url: \"/reports/running-balances\",\r\n        },\r\n        {\r\n          title: \"Cash Status\",\r\n          url: \"/reports/cash-status\",\r\n        },\r\n        {\r\n          title: \"Cash Float\",\r\n          url: \"/reports/cash-float\",\r\n        },\r\n        {\r\n          title: \"DSA Sales\",\r\n          url: \"/reports/dsa-sales\",\r\n        },\r\n        {\r\n          title: \"Receipts\",\r\n          url: \"/reports/receipts\",\r\n        },\r\n        {\r\n          title: \"Shifts\",\r\n          url: \"/reports/shifts\",\r\n        },\r\n        {\r\n          title: \"Phone Repairs\",\r\n          url: \"/reports/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"Stock Valuation\",\r\n          url: \"/inventory/valuation\",\r\n        },\r\n        {\r\n          title: \"Stock Aging\",\r\n          url: \"/reports/stock-aging\",\r\n        },\r\n        {\r\n          title: \"Stock Movement\",\r\n          url: \"/reports/stock-movement\",\r\n        },\r\n        {\r\n          title: \"Expense Analytics\",\r\n          url: \"/expense-analytics\",\r\n        },\r\n        {\r\n          title: \"Customers\",\r\n          url: \"/customers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Administration\",\r\n      url: \"/users\",\r\n      icon: BuildingIcon,\r\n      isActive:\r\n        pathname.startsWith(\"/users\") ||\r\n        pathname.startsWith(\"/roles\") ||\r\n        pathname.startsWith(\"/rbac\") ||\r\n        pathname.startsWith(\"/branches\") ||\r\n        pathname.startsWith(\"/locations\") ||\r\n        pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        // Tenants menu item removed\r\n        {\r\n          title: \"Users\",\r\n          url: \"/users\",\r\n        },\r\n        {\r\n          title: \"Roles\",\r\n          url: \"/roles\",\r\n        },\r\n        {\r\n          title: \"RBAC\",\r\n          url: \"/rbac\",\r\n        },\r\n        {\r\n          title: \"Branches\",\r\n          url: \"/branches\",\r\n        },\r\n        {\r\n          title: \"Locations\",\r\n          url: \"/locations\",\r\n        },\r\n        {\r\n          title: \"Employees\",\r\n          url: \"/employees\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Stock & Inventory\",\r\n      url: \"/products\",\r\n      icon: Package,\r\n      isActive:\r\n        pathname.startsWith(\"/products\") ||\r\n        pathname.startsWith(\"/categories\") ||\r\n        pathname.startsWith(\"/brands\") ||\r\n        pathname.startsWith(\"/inventory\") ||\r\n        pathname.includes(\"/inventory/stock-cards\") ||\r\n        pathname.includes(\"/inventory/purchases\"),\r\n      items: [\r\n        {\r\n          title: \"Products\",\r\n          url: \"/products\",\r\n        },\r\n        {\r\n          title: \"Categories\",\r\n          url: \"/categories\",\r\n        },\r\n        {\r\n          title: \"Brands\",\r\n          url: \"/brands\",\r\n        },\r\n        {\r\n          title: \"Stock levels\",\r\n          url: \"/inventory\",\r\n        },\r\n        // Commented out Stock Locations as requested\r\n        // {\r\n        //   title: \"Stock Locations\",\r\n        //   url: \"/inventory/locations\",\r\n        // },\r\n        {\r\n          title: \"Purchases\",\r\n          url: \"/inventory/purchases\",\r\n        },\r\n        {\r\n          title: \"Create Purchase\",\r\n          url: \"/inventory/purchases/new\",\r\n        },\r\n        {\r\n          title: \"Stock Requests\",\r\n          url: \"/inventory/stock-requests\",\r\n        },\r\n        {\r\n          title: \"Stock Transfers\",\r\n          url: \"/inventory/transfers\",\r\n        },\r\n        {\r\n          title: \"Make Stock Transfer\",\r\n          url: \"/inventory/bulk-transfer\",\r\n        },\r\n        {\r\n          title: \"Stock Transfer History\",\r\n          url: \"/inventory/bulk-transfers\",\r\n        },\r\n        // {\r\n        //   title: \"Stock Cards\",\r\n        //   url: \"/inventory/stock-cards\",\r\n        // },\r\n        {\r\n          title: \"Inventory Reports\",\r\n          url: \"/inventory/reports\",\r\n        },\r\n        {\r\n          title: \"Excel Import/Export\",\r\n          url: \"/inventory/excel\",\r\n        },\r\n        {\r\n          title: \"Price List\",\r\n          url: \"/inventory/price-list\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Float Management\",\r\n      url: \"/float\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/float\"),\r\n      items: [\r\n        {\r\n          title: \"Float Dashboard\",\r\n          url: \"/float\",\r\n        },\r\n        {\r\n          title: \"Float Movements\",\r\n          url: \"/float/movements\",\r\n        },\r\n        {\r\n          title: \"Float Reconciliations\",\r\n          url: \"/float/reconciliations\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Banking Management\",\r\n      url: \"/banking\",\r\n      icon: Landmark,\r\n      isActive: pathname.startsWith(\"/banking\"),\r\n      items: [\r\n        {\r\n          title: \"Banking Records\",\r\n          url: \"/banking\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Expenses Management\",\r\n      url: \"/expenses\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/expenses\"),\r\n      items: [\r\n        {\r\n          title: \"Expenses\",\r\n          url: \"/expenses\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Invoice Management\",\r\n      url: \"/invoices\",\r\n      icon: FileText,\r\n      isActive:\r\n        pathname.startsWith(\"/invoices\") ||\r\n        pathname.startsWith(\"/credit-notes\"),\r\n      items: [\r\n        {\r\n          title: \"All Invoices\",\r\n          url: \"/invoices\",\r\n        },\r\n        {\r\n          title: \"Credit Notes\",\r\n          url: \"/credit-notes\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"DSA Management\",\r\n      url: \"/dsa\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/dsa\"),\r\n      items: [\r\n        {\r\n          title: \"DSA Dashboard\",\r\n          url: \"/dsa\",\r\n        },\r\n        {\r\n          title: \"DSA Agents\",\r\n          url: \"/dsa/customers\",\r\n        },\r\n        {\r\n          title: \"Stock Assignments\",\r\n          url: \"/dsa/assignments\",\r\n        },\r\n        // Commented out DSA Reconciliations as requested\r\n        // {\r\n        //   title: \"DSA Reconciliations\",\r\n        //   url: \"/dsa/reconciliations\",\r\n        // },\r\n      ],\r\n    },\r\n    // {\r\n    //   title: \"Procurement\",\r\n    //   url: \"/procurement\",\r\n    //   icon: ShoppingCart,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/procurement\") ||\r\n    //     pathname.startsWith(\"/suppliers\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"Procurement Dashboard\",\r\n    //       url: \"/procurement\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Procurement Requests\",\r\n    //       url: \"/procurement/requests\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Create Request\",\r\n    //       url: \"/procurement/requests/new\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Procurement Receipts\",\r\n    //       url: \"/procurement/receipts\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Suppliers\",\r\n    //       url: \"/suppliers\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    {\r\n      title: \"Phone Repairs\",\r\n      url: \"/phone-repairs\",\r\n      icon: Smartphone,\r\n      isActive:\r\n        pathname === \"/phone-repairs\" || pathname.startsWith(\"/phone-repairs/\")\r\n          ? true\r\n          : false,\r\n      items: [\r\n        {\r\n          title: \"All Repairs\",\r\n          url: \"/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"New Repair\",\r\n          url: \"/phone-repairs/new\",\r\n        },\r\n      ],\r\n    },\r\n    // Settings section completely hidden for production - not implemented\r\n    // {\r\n    //   title: \"Settings\",\r\n    //   url: \"/settings\",\r\n    //   icon: Settings,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/settings\") || pathname.startsWith(\"/profile\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"System Settings\",\r\n    //       url: \"/settings/system\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Company Settings\",\r\n    //       url: \"/settings/company\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Payment Methods\",\r\n    //       url: \"/settings/payment-methods\",\r\n    //     },\r\n    //     {\r\n    //       title: \"System Health\",\r\n    //       url: \"/settings/health\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Profile\",\r\n    //       url: \"/profile\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    // Permissions Demo section removed\r\n  ];\r\n\r\n  // Filter navigation items based on RBAC configuration\r\n  const getFilteredNavigationItemsByRole = (): NavItem[] => {\r\n    const allItems = getAllNavigationItems();\r\n\r\n    console.log(\r\n      `[Navigation] ${userRoleName} role detected - using RBAC-based filtering`\r\n    );\r\n\r\n    // Filter navigation items based on RBAC configuration\r\n    const filteredItems = allItems.filter((item) => {\r\n      // Check if the entire navigation group should be hidden for this role\r\n      if (isNavigationItemHidden(item.title, userRoleName)) {\r\n        console.log(\r\n          `[Navigation] Hiding section \"${item.title}\" for role ${userRoleName}`\r\n        );\r\n        return false;\r\n      }\r\n\r\n      // Check if user has access to the main route of this navigation item\r\n      if (!hasRouteAccess(item.url, userRoleName)) {\r\n        console.log(\r\n          `[Navigation] No route access to \"${item.url}\" for role ${userRoleName}`\r\n        );\r\n        return false;\r\n      }\r\n\r\n      console.log(\r\n        `[Navigation] Allowing section \"${item.title}\" for role ${userRoleName}`\r\n      );\r\n      return true;\r\n    });\r\n\r\n    // Filter subitems based on RBAC configuration\r\n    const itemsWithFilteredSubitems = filteredItems.map((item) => {\r\n      // If the item has subitems, filter them based on RBAC\r\n      if (item.items && item.items.length > 0) {\r\n        console.log(\r\n          `[Navigation] Checking subitems for \"${item.title}\" section`\r\n        );\r\n\r\n        const filteredSubItems = item.items.filter((subItem) => {\r\n          // Check if user has route access for this subitem\r\n          if (!hasRouteAccess(subItem.url, userRoleName)) {\r\n            console.log(\r\n              `[Navigation] No route access to \"${subItem.url}\" for role ${userRoleName}`\r\n            );\r\n            return false;\r\n          }\r\n\r\n          // Special handling for RBAC items - only super_admin can see them\r\n          if (subItem.title === \"RBAC\" || subItem.url.includes(\"rbac\")) {\r\n            return userRoleName === ROLES.SUPER_ADMIN;\r\n          }\r\n\r\n          console.log(\r\n            `[Navigation] Allowing subitem \"${subItem.title}\" for role ${userRoleName}`\r\n          );\r\n          return true;\r\n        });\r\n\r\n        // Update the item's subitems with the filtered list\r\n        item.items = filteredSubItems;\r\n      }\r\n\r\n      return item;\r\n    });\r\n\r\n    // Only return items that have at least one subitem (if they had subitems to begin with)\r\n    const result = itemsWithFilteredSubitems.filter(\r\n      (item) => !item.items || item.items.length > 0\r\n    );\r\n\r\n    return result;\r\n  };\r\n\r\n  // Filter navigation items based on permissions (for future use)\r\n  // This function is commented out for MVP, will be implemented in the future\r\n  // when the permission system is fully implemented\r\n\r\n  // Sample data for the sidebar\r\n  const data = {\r\n    user: {\r\n      name: user?.name || \"User\",\r\n      email: user?.email || \"<EMAIL>\",\r\n      avatar: \"/placeholder-avatar.jpg\",\r\n    },\r\n    teams: [\r\n      {\r\n        name: teamData.name,\r\n        logo: GalleryVerticalEnd,\r\n        plan: teamData.plan,\r\n      },\r\n    ],\r\n    // For MVP, use role-based filtering\r\n    navMain: getFilteredNavigationItemsByRole(),\r\n    // For future: navMain: getFilteredNavigationItems(),\r\n  };\r\n\r\n  const { state, isMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\" && !isMobile;\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"h-full flex flex-col sidebar-compact\",\r\n        isCollapsed && \"sidebar-collapsed\"\r\n      )}\r\n    >\r\n      <div className=\"flex-1 overflow-y-auto py-2\">\r\n        <div className={cn(\"px-3\", isCollapsed && \"px-2\")}>\r\n          <NavMain items={data.navMain} />\r\n        </div>\r\n      </div>\r\n      <div className={cn(\"p-3 border-t\", isCollapsed && \"p-2\")}>\r\n        <NavUser user={data.user} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,0EAA0E;AAC1E,gEAAgE;AAChE,wEAAwE;AACxE;AACA;AACA;;;AA3BA;;;;;;;;;;;AAsEO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,yCAAyC;IACzC,2DAA2D;IAE3D,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,8BAA8B;IAC9B,MAAM,wBAAwB,IAAiB;YAC7C;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,sMAAA,CAAA,OAAQ;gBACd,UAAU,aAAa,gBAAgB,aAAa;YACtD;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,qNAAA,CAAA,YAAa;gBACnB,UACE,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAY;gBAClB,UACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL,4BAA4B;oBAC5B;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,2MAAA,CAAA,UAAO;gBACb,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,QAAQ,CAAC,6BAClB,SAAS,QAAQ,CAAC;gBACpB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,6CAA6C;oBAC7C,IAAI;oBACJ,8BAA8B;oBAC9B,iCAAiC;oBACjC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,IAAI;oBACJ,0BAA0B;oBAC1B,mCAAmC;oBACnC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,6MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,iNAAA,CAAA,WAAQ;gBACd,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,mNAAA,CAAA,YAAS;gBACf,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBAMD;YACH;YACA,IAAI;YACJ,0BAA0B;YAC1B,yBAAyB;YACzB,wBAAwB;YACxB,cAAc;YACd,6CAA6C;YAC7C,yCAAyC;YACzC,aAAa;YACb,QAAQ;YACR,wCAAwC;YACxC,6BAA6B;YAC7B,SAAS;YACT,QAAQ;YACR,uCAAuC;YACvC,sCAAsC;YACtC,SAAS;YACT,QAAQ;YACR,iCAAiC;YACjC,0CAA0C;YAC1C,SAAS;YACT,QAAQ;YACR,uCAAuC;YACvC,sCAAsC;YACtC,SAAS;YACT,QAAQ;YACR,4BAA4B;YAC5B,2BAA2B;YAC3B,SAAS;YACT,OAAO;YACP,KAAK;YACL;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,iNAAA,CAAA,aAAU;gBAChB,UACE,aAAa,oBAAoB,SAAS,UAAU,CAAC,qBACjD,OACA;gBACN,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;SAgCD;IAED,sDAAsD;IACtD,MAAM,mCAAmC;QACvC,MAAM,WAAW;QAEjB,QAAQ,GAAG,CACT,CAAC,aAAa,EAAE,aAAa,2CAA2C,CAAC;QAG3E,sDAAsD;QACtD,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC;YACrC,sEAAsE;YACtE,IAAI,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,KAAK,EAAE,eAAe;gBACpD,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,cAAc;gBAExE,OAAO;YACT;YAEA,qEAAqE;YACrE,IAAI,CAAC,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG,EAAE,eAAe;gBAC3C,QAAQ,GAAG,CACT,CAAC,iCAAiC,EAAE,KAAK,GAAG,CAAC,WAAW,EAAE,cAAc;gBAE1E,OAAO;YACT;YAEA,QAAQ,GAAG,CACT,CAAC,+BAA+B,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,cAAc;YAE1E,OAAO;QACT;QAEA,8CAA8C;QAC9C,MAAM,4BAA4B,cAAc,GAAG,CAAC,CAAC;YACnD,sDAAsD;YACtD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACvC,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;gBAG9D,MAAM,mBAAmB,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,kDAAkD;oBAClD,IAAI,CAAC,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG,EAAE,eAAe;wBAC9C,QAAQ,GAAG,CACT,CAAC,iCAAiC,EAAE,QAAQ,GAAG,CAAC,WAAW,EAAE,cAAc;wBAE7E,OAAO;oBACT;oBAEA,kEAAkE;oBAClE,IAAI,QAAQ,KAAK,KAAK,UAAU,QAAQ,GAAG,CAAC,QAAQ,CAAC,SAAS;wBAC5D,OAAO,iBAAiB,+HAAA,CAAA,QAAK,CAAC,WAAW;oBAC3C;oBAEA,QAAQ,GAAG,CACT,CAAC,+BAA+B,EAAE,QAAQ,KAAK,CAAC,WAAW,EAAE,cAAc;oBAE7E,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,KAAK,KAAK,GAAG;YACf;YAEA,OAAO;QACT;QAEA,wFAAwF;QACxF,MAAM,SAAS,0BAA0B,MAAM,CAC7C,CAAC,OAAS,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG;QAG/C,OAAO;IACT;IAEA,gEAAgE;IAChE,4EAA4E;IAC5E,kDAAkD;IAElD,8BAA8B;IAC9B,MAAM,OAAO;QACX,MAAM;YACJ,MAAM,MAAM,QAAQ;YACpB,OAAO,MAAM,SAAS;YACtB,QAAQ;QACV;QACA,OAAO;YACL;gBACE,MAAM,SAAS,IAAI;gBACnB,MAAM,yOAAA,CAAA,qBAAkB;gBACxB,MAAM,SAAS,IAAI;YACrB;SACD;QACD,oCAAoC;QACpC,SAAS;IAEX;IAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACrC,MAAM,cAAc,UAAU,eAAe,CAAC;IAE9C,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wCACA,eAAe;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,eAAe;8BACxC,cAAA,6LAAC,oIAAA,CAAA,UAAO;wBAAC,OAAO,KAAK,OAAO;;;;;;;;;;;;;;;;0BAGhC,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,eAAe;0BAChD,cAAA,6LAAC,oIAAA,CAAA,UAAO;oBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;AAIhC;GApiBgB;;QACS,kJAAA,CAAA,iBAAc;QACpB,qIAAA,CAAA,cAAW;QA8gBA,sIAAA,CAAA,aAAU;;;KAhhBxB", "debugId": null}}, {"offset": {"line": 3670, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/team-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// import { GalleryVerticalEnd } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n\r\nexport function TeamHeader() {\r\n  const { data: user } = useCurrentUser();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <div className=\"bg-white text-primary flex aspect-square size-6 items-center justify-center rounded-lg\">\r\n        {/* <GalleryVerticalEnd className=\"size-3\" /> */}\r\n        <Image\r\n          src={\"/images/simbatelecomlogo.png\"}\r\n          alt=\"Simba Telecom Logo\"\r\n          className=\"object-contain\"\r\n          width={16}\r\n          height={16}\r\n        />\r\n      </div>\r\n      <div className=\"grid flex-1 text-left leading-tight\">\r\n        <span className=\"truncate text-sm font-semibold text-primary-foreground\">\r\n          {teamData.name}\r\n        </span>\r\n        <span className=\"truncate text-xs text-primary-foreground/90 font-medium\">\r\n          {teamData.plan}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,qDAAqD;AACrD;AACA;;;AAJA;;;AAMO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IAEpC,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,WAAU;oBACV,OAAO;oBACP,QAAQ;;;;;;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;kCAEhB,6LAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;;;;;;;;;;;;;AAKxB;GAhEgB;;QACS,kJAAA,CAAA,iBAAc;;;KADvB", "debugId": null}}, {"offset": {"line": 3785, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/search-button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearch } from \"@/components/providers/search-provider\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\n\r\nexport function SearchButton() {\r\n  const { openSearch } = useSearch();\r\n\r\n  return (\r\n    <Button\r\n      variant=\"outline\"\r\n      size=\"sm\"\r\n      onClick={openSearch}\r\n      className=\"relative h-9 w-full justify-start bg-background text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-64\"\r\n    >\r\n      <span className=\"hidden lg:inline-flex\">Search anything...</span>\r\n      <span className=\"inline-flex lg:hidden\">Search...</span>\r\n      <kbd className=\"pointer-events-none absolute right-1.5 top-1.5 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex\">\r\n        <span className=\"text-xs\">⌘</span>K\r\n      </kbd>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD;IAE/B,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,6LAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,6LAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAU;;;;;;oBAAQ;;;;;;;;;;;;;AAI1C;GAjBgB;;QACS,wJAAA,CAAA,YAAS;;;KADlB", "debugId": null}}, {"offset": {"line": 3863, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/top-navigation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport {\r\n  Bell,\r\n  ChevronDown,\r\n  ChevronLeft,\r\n  LogOut,\r\n  Settings,\r\n  User,\r\n  PanelLeftIcon,\r\n  PanelRightIcon,\r\n  Home,\r\n} from \"lucide-react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser, useLogout } from \"@/features/auth/hooks/use-auth\";\r\nimport { TeamHeader } from \"@/components/team-header\";\r\nimport { SearchButton } from \"@/components/search-button\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport Link from \"next/link\";\r\n\r\nexport function TopNavigation() {\r\n  const { data: user } = useCurrentUser();\r\n  const logout = useLogout();\r\n  const { toggleSidebar, state, isMobile, setOpenMobile } = useSidebar();\r\n  const isExpanded = state === \"expanded\";\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Check if we're on the stock requests page\r\n  const isStockRequestsPage = pathname.includes('/inventory/stock-requests') &&\r\n    !pathname.includes('/create') &&\r\n    !pathname.match(/\\/inventory\\/stock-requests\\/\\d+/);\r\n\r\n  const handleLogout = () => {\r\n    logout.mutate();\r\n  };\r\n\r\n  const handleBackClick = () => {\r\n    router.back();\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-30 flex h-14 shrink-0 items-center gap-2 border-b border-primary-foreground/10 bg-primary text-primary-foreground transition-[width,height] ease-linear\">\r\n      <div className=\"flex flex-1 items-center justify-between px-4 w-full\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isMobile && isStockRequestsPage ? (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={handleBackClick}\r\n                aria-label=\"Go back\"\r\n                title=\"Go back\"\r\n              >\r\n                <ChevronLeft className=\"h-5 w-5\" />\r\n                <span className=\"sr-only\">Go Back</span>\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={() => (isMobile ? setOpenMobile(true) : toggleSidebar())}\r\n                data-mobile={isMobile ? \"true\" : \"false\"}\r\n                aria-label=\"Toggle sidebar\"\r\n                title={isExpanded ? \"Collapse sidebar\" : \"Expand sidebar\"}\r\n              >\r\n                {isExpanded ? (\r\n                  <PanelLeftIcon className=\"h-5 w-5\" />\r\n                ) : (\r\n                  <PanelRightIcon className=\"h-5 w-5\" />\r\n                )}\r\n                <span className=\"sr-only\">Toggle Sidebar</span>\r\n              </Button>\r\n            )}\r\n            <TeamHeader />\r\n\r\n            <div className=\"h-6 border-r border-primary-foreground/20 mx-2\"></div>\r\n\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n              asChild\r\n            >\r\n              <Link href=\"/dashboard\">\r\n                <Home className=\"h-4 w-4 mr-1\" />\r\n                Dashboard\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-center\">\r\n          <SearchButton />\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative h-8 w-8 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n            aria-label=\"Notifications\"\r\n          >\r\n            <Bell className=\"h-5 w-5\" />\r\n            <span className=\"absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-[10px] font-bold text-primary\">\r\n              3\r\n            </span>\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 gap-2 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                aria-label=\"Account\"\r\n              >\r\n                <Avatar className=\"h-7 w-7\">\r\n                  <AvatarImage\r\n                    src=\"/placeholder-avatar.jpg\"\r\n                    alt={user?.name || \"User\"}\r\n                  />\r\n                  <AvatarFallback className=\"text-xs font-semibold\">\r\n                    {user?.name ? user.name.charAt(0) : \"U\"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <span className=\"hidden text-sm font-semibold md:inline-block\">\r\n                  {user?.name || \"User\"}\r\n                </span>\r\n                <ChevronDown className=\"h-4 w-4 text-primary-foreground/80\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-56 rounded-lg\">\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-semibold leading-none\">\r\n                    {user?.name || \"User\"}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || \"<EMAIL>\"}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuGroup>\r\n                <DropdownMenuItem>\r\n                  <User className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Profile</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem>\r\n                  <Settings className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Settings</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuGroup>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span className=\"text-sm\">Log out</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAEA;AACA;AASA;AACA;AACA;;;AA/BA;;;;;;;;;;;AAiCO,SAAS;;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACnE,MAAM,aAAa,UAAU;IAC7B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,4CAA4C;IAC5C,MAAM,sBAAsB,SAAS,QAAQ,CAAC,gCAC5C,CAAC,SAAS,QAAQ,CAAC,cACnB,CAAC,SAAS,KAAK,CAAC;IAElB,MAAM,eAAe;QACnB,OAAO,MAAM;IACf;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI;IACb;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,YAAY,oCACX,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,cAAW;gCACX,OAAM;;kDAEN,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;qDAG5B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAO,WAAW,cAAc,QAAQ;gCACjD,eAAa,WAAW,SAAS;gCACjC,cAAW;gCACX,OAAO,aAAa,qBAAqB;;oCAExC,2BACC,6LAAC,uNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;6DAEzB,6LAAC,yNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAE5B,6LAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG9B,6LAAC,uIAAA,CAAA,aAAU;;;;;0CAEX,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,6LAAC,sMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,yIAAA,CAAA,eAAY;;;;;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,cAAW;;8CAEX,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAA6H;;;;;;;;;;;;sCAK/I,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,cAAW;;0DAEX,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDACV,KAAI;wDACJ,KAAK,MAAM,QAAQ;;;;;;kEAErB,6LAAC,qIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGxC,6LAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;0DAEjB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG3B,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,6LAAC,+IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ;;;;;;kEAEjB,6LAAC;wDAAE,WAAU;kEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,oBAAiB;;8DAChB,6LAAC,+IAAA,CAAA,mBAAgB;;sEACf,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,6LAAC,+IAAA,CAAA,mBAAgB;;sEACf,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAG9B,6LAAC,+IAAA,CAAA,wBAAqB;;;;;sDACtB,6LAAC,+IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C;GAlJgB;;QACS,kJAAA,CAAA,iBAAc;QACtB,kJAAA,CAAA,YAAS;QACkC,sIAAA,CAAA,aAAU;QAErD,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KANd", "debugId": null}}, {"offset": {"line": 4315, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,6LAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;KAFS;AAIT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MAhBS;AAkBT,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,6LAAC,yNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;MAhBS;AAkBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,mNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAhBS", "debugId": null}}, {"offset": {"line": 4465, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/breadcrumbs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from \"@/components/ui/breadcrumb\";\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\n\r\nexport function Breadcrumbs() {\r\n  const pathname = usePathname();\r\n\r\n  // Get the current page name from the pathname\r\n  const getPageName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length === 0) return \"Dashboard\";\r\n\r\n    // Get the last segment of the path and capitalize it\r\n    const lastSegment = path[path.length - 1];\r\n    return (\r\n      lastSegment.charAt(0).toUpperCase() +\r\n      lastSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  // Get the parent path for breadcrumb\r\n  const getParentPath = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Remove the last segment to get the parent path\r\n    return \"/\" + path.slice(0, path.length - 1).join(\"/\");\r\n  };\r\n\r\n  // Get the parent name for breadcrumb\r\n  const getParentName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Get the second-to-last segment and capitalize it\r\n    const parentSegment = path[path.length - 2];\r\n    return (\r\n      parentSegment.charAt(0).toUpperCase() +\r\n      parentSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  const parentPath = getParentPath();\r\n  const parentName = getParentName();\r\n  const pageName = getPageName();\r\n\r\n  return (\r\n    <div className=\"mb-4\">\r\n      <Breadcrumb>\r\n        <BreadcrumbList>\r\n          {parentPath && parentName && (\r\n            <>\r\n              <BreadcrumbItem className=\"hidden md:block\">\r\n                <BreadcrumbLink asChild>\r\n                  <NavigationLink\r\n                    href={parentPath}\r\n                    className=\"text-sm font-medium text-muted-foreground hover:text-foreground\"\r\n                  >\r\n                    {parentName}\r\n                  </NavigationLink>\r\n                </BreadcrumbLink>\r\n              </BreadcrumbItem>\r\n              <BreadcrumbSeparator className=\"hidden md:block h-3 w-3\" />\r\n            </>\r\n          )}\r\n          <BreadcrumbItem>\r\n            <BreadcrumbPage className=\"text-sm font-semibold\">\r\n              {pageName}\r\n            </BreadcrumbPage>\r\n          </BreadcrumbItem>\r\n        </BreadcrumbList>\r\n      </Breadcrumb>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;;;AAXA;;;;AAaO,SAAS;;IACd,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,cAAc;QAClB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAE9B,qDAAqD;QACrD,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,OACE,YAAY,MAAM,CAAC,GAAG,WAAW,KACjC,YAAY,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEvC;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,iDAAiD;QACjD,OAAO,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC;IACnD;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,mDAAmD;QACnD,MAAM,gBAAgB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC3C,OACE,cAAc,MAAM,CAAC,GAAG,WAAW,KACnC,cAAc,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEzC;IAEA,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,WAAW;IAEjB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,yIAAA,CAAA,aAAU;sBACT,cAAA,6LAAC,yIAAA,CAAA,iBAAc;;oBACZ,cAAc,4BACb;;0CACE,6LAAC,yIAAA,CAAA,iBAAc;gCAAC,WAAU;0CACxB,cAAA,6LAAC,yIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,6LAAC,iJAAA,CAAA,iBAAc;wCACb,MAAM;wCACN,WAAU;kDAET;;;;;;;;;;;;;;;;0CAIP,6LAAC,yIAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;kCAGnC,6LAAC,yIAAA,CAAA,iBAAc;kCACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAtEgB;;QACG,qIAAA,CAAA,cAAW;;;KADd", "debugId": null}}, {"offset": {"line": 4594, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/main-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { ReactNode, Suspense, useEffect } from \"react\";\r\nimport { RoleGuard } from \"./role-guard\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\nimport { SidebarProvider, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { AppSidebar } from \"@/components/app-sidebar\";\r\nimport { TopNavigation } from \"@/components/top-navigation\";\r\nimport { Breadcrumbs } from \"@/components/breadcrumbs\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\";\r\n\r\ninterface MainLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function MainLayout({ children }: MainLayoutProps) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Use useEffect for redirects to avoid hydration issues\r\n  useEffect(() => {\r\n    // Only show loading on the very first render if:\r\n    // 1. Auth is not initialized yet\r\n    // 2. We haven't shown the loading screen before in this session\r\n    if (!isInitialized && !hasShownLoading.main) {\r\n      setLoading(\"main\", true);\r\n    } else {\r\n      setLoading(\"main\", false);\r\n    }\r\n\r\n    if (!isInitialized) {\r\n      return; // Wait until auth state is initialized\r\n    }\r\n\r\n    // After auth state is initialized, we can hide the loading screen\r\n    setLoading(\"main\", false);\r\n\r\n    // If we have an access token, mark that we've shown loading\r\n    // This prevents showing loading on subsequent navigations\r\n    if (accessToken) {\r\n      markLoadingShown(\"main\");\r\n    }\r\n\r\n    // We're disabling client-side redirects to avoid redirect loops\r\n    // The middleware will handle redirects instead\r\n    if (!accessToken) {\r\n      // We're not redirecting here anymore\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [accessToken, isInitialized, hasShownLoading.main]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in loading state\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (isLoading.main) {\r\n        setLoading(\"main\", false);\r\n        markLoadingShown(\"main\");\r\n      }\r\n    }, 1500); // 1.5 second timeout for better UX\r\n\r\n    return () => clearTimeout(timeoutId);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.main]);\r\n\r\n  // Don't render anything if not authenticated\r\n  if (!accessToken) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider>\r\n      <RoleGuard>\r\n        <MainLayoutContent>{children}</MainLayoutContent>\r\n      </RoleGuard>\r\n    </SidebarProvider>\r\n  );\r\n}\r\n\r\nfunction MainLayoutContent({ children }: { children: ReactNode }) {\r\n  const { state, openMobile, setOpenMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-screen w-full bg-gray-50\">\r\n      <TopNavigation />\r\n      <div className=\"flex h-[calc(100vh-3.5rem)] overflow-hidden\">\r\n        {/* Desktop sidebar */}\r\n        <aside\r\n          className={cn(\r\n            \"border-r bg-white transition-all duration-300 shrink-0 hidden md:block\",\r\n            isCollapsed ? \"w-16\" : \"w-64\"\r\n          )}\r\n        >\r\n          <AppSidebar />\r\n        </aside>\r\n\r\n        {/* Mobile sidebar */}\r\n        <div className=\"md:hidden\">\r\n          <Sheet open={openMobile} onOpenChange={setOpenMobile}>\r\n            <SheetContent\r\n              side=\"left\"\r\n              className=\"p-0 w-[280px] border-r bg-white\"\r\n            >\r\n              <div className=\"h-full overflow-y-auto\">\r\n                <AppSidebar />\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        <main className=\"flex flex-col w-full overflow-y-auto overflow-x-hidden\">\r\n          <div className=\"p-6\">\r\n            <Suspense fallback={<div>Loading content...</div>}>\r\n              <Breadcrumbs />\r\n              {children}\r\n            </Suspense>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;AAoBO,SAAS,WAAW,EAAE,QAAQ,EAAmB;;IACtD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD;IAEX,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,iDAAiD;YACjD,iCAAiC;YACjC,gEAAgE;YAChE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;gBAC3C,WAAW,QAAQ;YACrB,OAAO;gBACL,WAAW,QAAQ;YACrB;YAEA,IAAI,CAAC,eAAe;gBAClB,QAAQ,uCAAuC;YACjD;YAEA,kEAAkE;YAClE,WAAW,QAAQ;YAEnB,4DAA4D;YAC5D,0DAA0D;YAC1D,IAAI,aAAa;gBACf,iBAAiB;YACnB;YAEA,gEAAgE;YAChE,+CAA+C;YAC/C,IAAI,CAAC,aAAa;YAChB,qCAAqC;YACvC;QACA,uDAAuD;QACzD;+BAAG;QAAC;QAAa;QAAe,gBAAgB,IAAI;KAAC;IAErD,iEAAiE;IACjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,YAAY;kDAAW;oBAC3B,IAAI,UAAU,IAAI,EAAE;wBAClB,WAAW,QAAQ;wBACnB,iBAAiB;oBACnB;gBACF;iDAAG,OAAO,mCAAmC;YAE7C;wCAAO,IAAM,aAAa;;QAC1B,uDAAuD;QACzD;+BAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6CAA6C;IAC7C,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,qBACE,6LAAC,sIAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,iJAAA,CAAA,YAAS;sBACR,cAAA,6LAAC;0BAAmB;;;;;;;;;;;;;;;;AAI5B;GA9DgB;;QACyB,wIAAA,CAAA,gBAAa;QAElD,yJAAA,CAAA,aAAU;;;KAHE;AAgEhB,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;;IAC9D,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,cAAc,UAAU;IAE9B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,0IAAA,CAAA,gBAAa;;;;;0BACd,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,cAAc,SAAS;kCAGzB,cAAA,6LAAC,uIAAA,CAAA,aAAU;;;;;;;;;;kCAIb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;sCACrC,cAAA,6LAAC,oIAAA,CAAA,eAAY;gCACX,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6JAAA,CAAA,WAAQ;gCAAC,wBAAU,6LAAC;8CAAI;;;;;;;kDACvB,6LAAC,oIAAA,CAAA,cAAW;;;;;oCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;IA3CS;;QACsC,sIAAA,CAAA,aAAU;;;MADhD", "debugId": null}}, {"offset": {"line": 4836, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 4951, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\r\nimport { DayPicker } from \"react-day-picker\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DayPicker>) {\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\"p-3\", className)}\r\n      classNames={{\r\n        months: \"flex flex-col sm:flex-row gap-2\",\r\n        month: \"flex flex-col gap-4\",\r\n        caption: \"flex justify-center pt-1 relative items-center w-full\",\r\n        caption_label: \"text-sm font-medium\",\r\n        nav: \"flex items-center gap-1\",\r\n        nav_button: cn(\r\n          buttonVariants({ variant: \"outline\" }),\r\n          \"size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n        ),\r\n        nav_button_previous: \"absolute left-1\",\r\n        nav_button_next: \"absolute right-1\",\r\n        table: \"w-full border-collapse space-x-1\",\r\n        head_row: \"flex\",\r\n        head_cell:\r\n          \"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]\",\r\n        row: \"flex w-full mt-2\",\r\n        cell: cn(\r\n          \"relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md\",\r\n          props.mode === \"range\"\r\n            ? \"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md\"\r\n            : \"[&:has([aria-selected])]:rounded-md\"\r\n        ),\r\n        day: cn(\r\n          buttonVariants({ variant: \"ghost\" }),\r\n          \"size-8 p-0 font-normal aria-selected:opacity-100\"\r\n        ),\r\n        day_range_start:\r\n          \"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_range_end:\r\n          \"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_selected:\r\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\r\n        day_today: \"bg-accent text-accent-foreground\",\r\n        day_outside:\r\n          \"day-outside text-muted-foreground aria-selected:text-muted-foreground\",\r\n        day_disabled: \"text-muted-foreground opacity-50\",\r\n        day_range_middle:\r\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\r\n        day_hidden: \"invisible\",\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ className, ...props }) => (\r\n          <ChevronLeft className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n        IconRight: ({ className, ...props }) => (\r\n          <ChevronRight className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Calendar }\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AASA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACoC;IACvC,qBACE,6LAAC,iKAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YAC<PERSON>,MAAM,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACL,mKACA,MAAM,IAAI,KAAK,UACX,yKACA;YAEN,KAAK,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,iBACE;YACF,eACE;YACF,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAE5D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,6LAAC,yNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;QAE/D;QACC,GAAG,KAAK;;;;;;AAGf;KA/DS", "debugId": null}}, {"offset": {"line": 5037, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 5118, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/date-range-picker.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { format } from \"date-fns\";\r\nimport { Calendar as CalendarIcon } from \"lucide-react\";\r\nimport { DateRange } from \"react-day-picker\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Calendar } from \"@/components/ui/calendar\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\n\r\ninterface DateRangePickerProps {\r\n  value: DateRange;\r\n  onChange: (value: DateRange) => void;\r\n  className?: string;\r\n}\r\n\r\nexport function DateRangePicker({\r\n  value,\r\n  onChange,\r\n  className,\r\n}: DateRangePickerProps) {\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const checkIsMobile = () => {\r\n      setIsMobile(window.innerWidth < 768);\r\n    };\r\n\r\n    // Initial check\r\n    checkIsMobile();\r\n\r\n    // Add event listener for window resize\r\n    window.addEventListener(\"resize\", checkIsMobile);\r\n\r\n    // Cleanup\r\n    return () => window.removeEventListener(\"resize\", checkIsMobile);\r\n  }, []);\r\n  return (\r\n    <div className={cn(\"grid gap-2\", className)}>\r\n      <Popover>\r\n        <PopoverTrigger asChild>\r\n          <Button\r\n            id=\"date\"\r\n            variant={\"outline\"}\r\n            className={cn(\r\n              \"w-full md:w-[250px] justify-start text-left font-normal\",\r\n              !value && \"text-muted-foreground\"\r\n            )}\r\n          >\r\n            <CalendarIcon className=\"mr-2 h-4 w-4\" />\r\n            {value?.from ? (\r\n              value.to ? (\r\n                <>\r\n                  {format(value.from, \"LLL dd, y\")} -{\" \"}\r\n                  {format(value.to, \"LLL dd, y\")}\r\n                </>\r\n              ) : (\r\n                format(value.from, \"LLL dd, y\")\r\n              )\r\n            ) : (\r\n              <span>Pick a date range</span>\r\n            )}\r\n          </Button>\r\n        </PopoverTrigger>\r\n        <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n          <div className=\"overflow-x-auto\">\r\n            <Calendar\r\n              initialFocus\r\n              mode=\"range\"\r\n              defaultMonth={value?.from}\r\n              selected={value}\r\n              onSelect={(range) => {\r\n                // Only call onChange if range is not undefined\r\n                if (range) {\r\n                  onChange(range);\r\n                }\r\n              }}\r\n              numberOfMonths={isMobile ? 1 : 2}\r\n            />\r\n          </div>\r\n        </PopoverContent>\r\n      </Popover>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAGA;AACA;AACA;AACA;;;AAXA;;;;;;;;AAuBO,SAAS,gBAAgB,EAC9B,KAAK,EACL,QAAQ,EACR,SAAS,EACY;;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;2DAAgB;oBACpB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA,gBAAgB;YAChB;YAEA,uCAAuC;YACvC,OAAO,gBAAgB,CAAC,UAAU;YAElC,UAAU;YACV;6CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;oCAAG,EAAE;IACL,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;kBAC/B,cAAA,6LAAC,sIAAA,CAAA,UAAO;;8BACN,6LAAC,sIAAA,CAAA,iBAAc;oBAAC,OAAO;8BACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBACL,IAAG;wBACH,SAAS;wBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA,CAAC,SAAS;;0CAGZ,6LAAC,6MAAA,CAAA,WAAY;gCAAC,WAAU;;;;;;4BACvB,OAAO,OACN,MAAM,EAAE,iBACN;;oCACG,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE;oCAAa;oCAAG;oCACnC,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,EAAE,EAAE;;+CAGpB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE,6BAGrB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAIZ,6LAAC,sIAAA,CAAA,iBAAc;oBAAC,WAAU;oBAAa,OAAM;8BAC3C,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;4BACP,YAAY;4BACZ,MAAK;4BACL,cAAc,OAAO;4BACrB,UAAU;4BACV,UAAU,CAAC;gCACT,+CAA+C;gCAC/C,IAAI,OAAO;oCACT,SAAS;gCACX;4BACF;4BACA,gBAAgB,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;GApEgB;KAAA", "debugId": null}}, {"offset": {"line": 5259, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 5293, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 5542, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/branches/api/branch-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { Branch, PaginatedResponse, UpdateBranchStatusRequest } from \"@/types\";\r\nimport { CreateBranchRequest, UpdateBranchRequest } from \"@/types/branch\";\r\n\r\nexport const branchService = {\r\n  getBranches: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Branch>> => {\r\n    try {\r\n      console.log(\"Calling API: /branches with params:\", params);\r\n      let response;\r\n\r\n      try {\r\n        // Try the Next.js API route first\r\n        response = await apiClient.get<any>(\"/branches\", { params });\r\n      } catch (error) {\r\n        console.warn(\r\n          \"Error fetching from Next.js API route, trying direct backend:\",\r\n          error\r\n        );\r\n        // If that fails, try the backend directly\r\n        const API_URL = process.env.NEXT_PUBLIC_API_URL;\r\n        const token =\r\n          localStorage.getItem(\"token\") || localStorage.getItem(\"accessToken\");\r\n\r\n        if (!token) {\r\n          throw new Error(\"No authentication token available\");\r\n        }\r\n\r\n        const backendResponse = await fetch(\r\n          `${API_URL}/branches${\r\n            params ? `?${new URLSearchParams(params)}` : \"\"\r\n          }`,\r\n          {\r\n            headers: {\r\n              Authorization: `Bearer ${token}`,\r\n              \"Content-Type\": \"application/json\",\r\n            },\r\n          }\r\n        );\r\n\r\n        if (!backendResponse.ok) {\r\n          throw new Error(`Backend API error: ${backendResponse.status}`);\r\n        }\r\n\r\n        response = await backendResponse.json();\r\n      }\r\n\r\n      console.log(\"Raw branches API response:\", response);\r\n\r\n      // Map API response to our Branch type\r\n      const mapApiBranchToBranch = (apiBranch: any): Branch => ({\r\n        ...apiBranch,\r\n      });\r\n\r\n      // If response is an array, convert to paginated format with mapped branches\r\n      if (Array.isArray(response)) {\r\n        console.log(\r\n          \"Branches response is an array, converting to paginated format\"\r\n        );\r\n        const mappedBranches = response.map(mapApiBranchToBranch);\r\n        return {\r\n          data: mappedBranches,\r\n          pagination: {\r\n            total: mappedBranches.length,\r\n            page: 1,\r\n            limit: mappedBranches.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        console.log(\"Branches response is already in paginated format\");\r\n        const mappedBranches = response.data.map(mapApiBranchToBranch);\r\n        return {\r\n          data: mappedBranches,\r\n          pagination: response.pagination || {\r\n            total: mappedBranches.length,\r\n            page: 1,\r\n            limit: mappedBranches.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If response has data property but it's not an array, wrap it\r\n      if (response && response.data && !Array.isArray(response.data)) {\r\n        console.log(\r\n          \"Branches response has data property but it's not an array, wrapping it\"\r\n        );\r\n        return {\r\n          data: [mapApiBranchToBranch(response.data)],\r\n          pagination: response.pagination || {\r\n            total: 1,\r\n            page: 1,\r\n            limit: 1,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If response itself is not an array but contains branches directly\r\n      if (response && !Array.isArray(response) && !response.data) {\r\n        console.log(\"Response contains branches directly, converting to array\");\r\n        // Try to extract branches from the response\r\n        const branches = Object.values(response).filter(\r\n          (item) =>\r\n            typeof item === \"object\" &&\r\n            item !== null &&\r\n            \"id\" in item &&\r\n            \"name\" in item\r\n        );\r\n\r\n        if (branches.length > 0) {\r\n          const mappedBranches = branches.map(mapApiBranchToBranch);\r\n          return {\r\n            data: mappedBranches,\r\n            pagination: {\r\n              total: mappedBranches.length,\r\n              page: 1,\r\n              limit: mappedBranches.length,\r\n              totalPages: 1,\r\n            },\r\n          };\r\n        }\r\n      }\r\n\r\n      // Default fallback\r\n      console.log(\"Using default fallback for branches response\");\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error in getBranches:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getBranchById: async (id: number): Promise<Branch> => {\r\n    return apiClient.get(`/branches/${id}`);\r\n  },\r\n\r\n  createBranch: async (branch: CreateBranchRequest): Promise<Branch> => {\r\n    // Send exactly what the API expects according to the guide\r\n    const apiRequest = {\r\n      tenant_id: branch.tenant_id,\r\n      name: branch.name,\r\n      location: branch.location,\r\n      region_id: branch.region_id,\r\n      is_hq: false, // Always set to false as requested\r\n      status: \"active\", // Default status\r\n    };\r\n\r\n    // Add optional fields if they exist\r\n    if (branch.phone) {\r\n      apiRequest.phone_number = branch.phone;\r\n    }\r\n    if (branch.email) {\r\n      apiRequest.email = branch.email;\r\n    }\r\n\r\n    console.log(\"Creating branch with data:\", apiRequest);\r\n    return apiClient.post(\"/branches\", apiRequest);\r\n  },\r\n\r\n  updateBranch: async (\r\n    id: number,\r\n    branch: UpdateBranchRequest\r\n  ): Promise<Branch> => {\r\n    // Ensure we're sending the correct fields to the API\r\n    const apiRequest = {\r\n      name: branch.name,\r\n      location: branch.location,\r\n      region_id: branch.region_id,\r\n      level: branch.level,\r\n    };\r\n\r\n    console.log(\"Updating branch with data:\", apiRequest);\r\n    return apiClient.put(`/branches/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteBranch: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/branches/${id}`);\r\n  },\r\n\r\n  updateBranchStatus: async (\r\n    id: number,\r\n    status: UpdateBranchStatusRequest\r\n  ): Promise<Branch> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      status: status.status, // 'active' or 'inactive'\r\n    };\r\n\r\n    return apiClient.put(`/branches/${id}/status`, apiRequest);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAqBwB;AArBxB;;AAIO,MAAM,gBAAgB;IAC3B,aAAa,OACX;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,uCAAuC;YACnD,IAAI;YAEJ,IAAI;gBACF,kCAAkC;gBAClC,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,aAAa;oBAAE;gBAAO;YAC5D,EAAE,OAAO,OAAO;gBACd,QAAQ,IAAI,CACV,iEACA;gBAEF,0CAA0C;gBAC1C,MAAM;gBACN,MAAM,QACJ,aAAa,OAAO,CAAC,YAAY,aAAa,OAAO,CAAC;gBAExD,IAAI,CAAC,OAAO;oBACV,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,kBAAkB,MAAM,MAC5B,GAAG,QAAQ,SAAS,EAClB,SAAS,CAAC,CAAC,EAAE,IAAI,gBAAgB,SAAS,GAAG,IAC7C,EACF;oBACE,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,OAAO;wBAChC,gBAAgB;oBAClB;gBACF;gBAGF,IAAI,CAAC,gBAAgB,EAAE,EAAE;oBACvB,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,gBAAgB,MAAM,EAAE;gBAChE;gBAEA,WAAW,MAAM,gBAAgB,IAAI;YACvC;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,sCAAsC;YACtC,MAAM,uBAAuB,CAAC,YAA2B,CAAC;oBACxD,GAAG,SAAS;gBACd,CAAC;YAED,4EAA4E;YAC5E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,QAAQ,GAAG,CACT;gBAEF,MAAM,iBAAiB,SAAS,GAAG,CAAC;gBACpC,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,eAAe,MAAM;wBAC5B,MAAM;wBACN,OAAO,eAAe,MAAM;wBAC5B,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,QAAQ,GAAG,CAAC;gBACZ,MAAM,iBAAiB,SAAS,IAAI,CAAC,GAAG,CAAC;gBACzC,OAAO;oBACL,MAAM;oBACN,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,eAAe,MAAM;wBAC5B,MAAM;wBACN,OAAO,eAAe,MAAM;wBAC5B,YAAY;oBACd;gBACF;YACF;YAEA,+DAA+D;YAC/D,IAAI,YAAY,SAAS,IAAI,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC9D,QAAQ,GAAG,CACT;gBAEF,OAAO;oBACL,MAAM;wBAAC,qBAAqB,SAAS,IAAI;qBAAE;oBAC3C,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO;wBACP,MAAM;wBACN,OAAO;wBACP,YAAY;oBACd;gBACF;YACF;YAEA,oEAAoE;YACpE,IAAI,YAAY,CAAC,MAAM,OAAO,CAAC,aAAa,CAAC,SAAS,IAAI,EAAE;gBAC1D,QAAQ,GAAG,CAAC;gBACZ,4CAA4C;gBAC5C,MAAM,WAAW,OAAO,MAAM,CAAC,UAAU,MAAM,CAC7C,CAAC,OACC,OAAO,SAAS,YAChB,SAAS,QACT,QAAQ,QACR,UAAU;gBAGd,IAAI,SAAS,MAAM,GAAG,GAAG;oBACvB,MAAM,iBAAiB,SAAS,GAAG,CAAC;oBACpC,OAAO;wBACL,MAAM;wBACN,YAAY;4BACV,OAAO,eAAe,MAAM;4BAC5B,MAAM;4BACN,OAAO,eAAe,MAAM;4BAC5B,YAAY;wBACd;oBACF;gBACF;YACF;YAEA,mBAAmB;YACnB,QAAQ,GAAG,CAAC;YACZ,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,eAAe,OAAO;QACpB,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;IACxC;IAEA,cAAc,OAAO;QACnB,2DAA2D;QAC3D,MAAM,aAAa;YACjB,WAAW,OAAO,SAAS;YAC3B,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,OAAO;YACP,QAAQ;QACV;QAEA,oCAAoC;QACpC,IAAI,OAAO,KAAK,EAAE;YAChB,WAAW,YAAY,GAAG,OAAO,KAAK;QACxC;QACA,IAAI,OAAO,KAAK,EAAE;YAChB,WAAW,KAAK,GAAG,OAAO,KAAK;QACjC;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,aAAa;IACrC;IAEA,cAAc,OACZ,IACA;QAEA,qDAAqD;QACrD,MAAM,aAAa;YACjB,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;YAC3B,OAAO,OAAO,KAAK;QACrB;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;IAC1C;IAEA,cAAc,OAAO;QACnB,OAAO,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IAC3C;IAEA,oBAAoB,OAClB,IACA;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,QAAQ,OAAO,MAAM;QACvB;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE;IACjD;AACF", "debugId": null}}, {"offset": {"line": 5722, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/branches/hooks/use-branches.ts"], "sourcesContent": ["import { UpdateBranchRequest, UpdateBranchStatusRequest } from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport { branchService } from \"../api/branch-service\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { CreateBranchRequest } from \"@/types/branch\";\r\n\r\nexport function useBranches(\r\n  params?: Record<string, any>,\r\n  options?: { enabled?: boolean }\r\n) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n\r\n  return useQuery({\r\n    queryKey: [\"branches\", params],\r\n    queryFn: () => branchService.getBranches(params),\r\n    // Only run query when authentication is ready and enabled option is true (if provided)\r\n    enabled: isInitialized && !!accessToken && (options?.enabled !== false),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useBranch(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"branches\", id],\r\n    queryFn: () => branchService.getBranchById(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n\r\nexport function useCreateBranch() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (branch: CreateBranchRequest) =>\r\n      branchService.createBranch(branch),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch created\", {\r\n        description: \"The branch has been created successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error creating branch\", {\r\n        description:\r\n          error.message || \"An error occurred while creating the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateBranch(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (branch: UpdateBranchRequest) =>\r\n      branchService.updateBranch(id, branch),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch updated\", {\r\n        description: \"The branch has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating branch\", {\r\n        description:\r\n          error.message || \"An error occurred while updating the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteBranch() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => branchService.deleteBranch(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch deleted\", {\r\n        description: \"The branch has been deleted successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error deleting branch\", {\r\n        description:\r\n          error.message || \"An error occurred while deleting the branch.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateBranchStatus(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (status: UpdateBranchStatusRequest) =>\r\n      branchService.updateBranchStatus(id, status),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"branches\"] });\r\n      toast.success(\"Branch status updated\", {\r\n        description: \"The branch status has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating branch status\", {\r\n        description:\r\n          error.message ||\r\n          \"An error occurred while updating the branch status.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAGO,SAAS,YACd,MAA4B,EAC5B,OAA+B;;IAE/B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAEnD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAO;QAC9B,OAAO;oCAAE,IAAM,0JAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;;QACzC,uFAAuF;QACvF,SAAS,iBAAiB,CAAC,CAAC,eAAgB,SAAS,YAAY;QACjE,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,MAAM;oCAAE,CAAC;gBACP,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;oBACF;gBACF;gBACA,OAAO;YACT;;IACF;AACF;GA/BgB;;QAIyB,wIAAA,CAAA,gBAAa;QAE7C,8KAAA,CAAA,WAAQ;;;AA2BV,SAAS,UAAU,EAAU;;IAClC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAG;QAC1B,OAAO;kCAAE,IAAM,0JAAA,CAAA,gBAAa,CAAC,aAAa,CAAC;;QAC3C,SAAS,CAAC,CAAC;IACb;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ;;;AAOV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;;QAC7B,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IAnBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS,gBAAgB,EAAU;;IACxC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,IAAI;;QACjC,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IApBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2CAAE,CAAC,KAAe,0JAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;;QACvD,SAAS;2CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;2CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IAlBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,SAAS,sBAAsB,EAAU;;IAC9C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,SACX,0JAAA,CAAA,gBAAa,CAAC,kBAAkB,CAAC,IAAI;;QACvC,SAAS;iDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,yBAAyB;oBACrC,aAAa;gBACf;YACF;;QACA,OAAO;iDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,gCAAgC;oBAC1C,aACE,MAAM,OAAO,IACb;gBACJ;YACF;;IACF;AACF;IArBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 5958, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/employees/api/employee-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse } from \"@/types/api\";\r\nimport { Employee, EmployeeFormValues } from \"../types\";\r\n\r\nexport const employeeService = {\r\n  getEmployees: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Employee>> => {\r\n    try {\r\n      const response = await apiClient.get<any>(\"/employees\", { params });\r\n\r\n      // Check if the response is an array (as per Swagger)\r\n      if (Array.isArray(response)) {\r\n        // Convert array to paginated response format\r\n        return {\r\n          data: response,\r\n          pagination: {\r\n            total: response.length,\r\n            page: 1,\r\n            limit: response.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If it's already in paginated format with data and meta properties\r\n      if (response && response.data && response.meta) {\r\n        return {\r\n          data: response.data,\r\n          pagination: {\r\n            total: response.meta.total,\r\n            page: response.meta.page,\r\n            limit: response.meta.limit,\r\n            totalPages: response.meta.totalPages,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Default fallback\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching employees:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getEmployeeById: async (id: number): Promise<Employee> => {\r\n    try {\r\n      const response = await apiClient.get<Employee>(`/employees/${id}`);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Error fetching employee with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  createEmployee: async (data: EmployeeFormValues): Promise<Employee> => {\r\n    try {\r\n      const response = await apiClient.post<Employee>(\"/employees\", data);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(\"Error creating employee:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  updateEmployee: async (id: number, data: EmployeeFormValues): Promise<Employee> => {\r\n    try {\r\n      const response = await apiClient.put<Employee>(`/employees/${id}`, data);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Error updating employee with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  deleteEmployee: async (id: number): Promise<void> => {\r\n    try {\r\n      await apiClient.delete(`/employees/${id}`);\r\n    } catch (error) {\r\n      console.error(`Error deleting employee with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  getEmployeesByBranch: async (branchId: number): Promise<Employee[]> => {\r\n    try {\r\n      const response = await apiClient.get<Employee[]>(`/employees/branch/${branchId}`);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Error fetching employees for branch ${branchId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAIO,MAAM,kBAAkB;IAC7B,cAAc,OACZ;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,cAAc;gBAAE;YAAO;YAEjE,qDAAqD;YACrD,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,6CAA6C;gBAC7C,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,SAAS,MAAM;wBACtB,MAAM;wBACN,OAAO,SAAS,MAAM;wBACtB,YAAY;oBACd;gBACF;YACF;YAEA,oEAAoE;YACpE,IAAI,YAAY,SAAS,IAAI,IAAI,SAAS,IAAI,EAAE;gBAC9C,OAAO;oBACL,MAAM,SAAS,IAAI;oBACnB,YAAY;wBACV,OAAO,SAAS,IAAI,CAAC,KAAK;wBAC1B,MAAM,SAAS,IAAI,CAAC,IAAI;wBACxB,OAAO,SAAS,IAAI,CAAC,KAAK;wBAC1B,YAAY,SAAS,IAAI,CAAC,UAAU;oBACtC;gBACF;YACF;YAEA,mBAAmB;YACnB,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI;YACjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAW,cAAc;YAC9D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,gBAAgB,OAAO,IAAY;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI,EAAE;YACnE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,sBAAsB,OAAO;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAa,CAAC,kBAAkB,EAAE,UAAU;YAChF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,SAAS,CAAC,CAAC,EAAE;YAClE,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 6072, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/employees/hooks/use-employees.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { employeeService } from \"../api/employee-service\";\r\nimport { Employee, EmployeeFormValues } from \"../types\";\r\nimport { toast } from \"sonner\";\r\n\r\n// Define query keys for better cache management\r\nconst employeeKeys = {\r\n  all: [\"employees\"] as const,\r\n  lists: () => [...employeeKeys.all, \"list\"] as const,\r\n  list: (filters: Record<string, any>) => [...employeeKeys.lists(), filters] as const,\r\n  details: () => [...employeeKeys.all, \"detail\"] as const,\r\n  detail: (id: number) => [...employeeKeys.details(), id] as const,\r\n  branches: () => [...employeeKeys.all, \"branches\"] as const,\r\n  branch: (branchId: number) => [...employeeKeys.branches(), branchId] as const,\r\n};\r\n\r\nexport const useEmployees = (params?: Record<string, any>) => {\r\n  return useQuery({\r\n    queryKey: employeeKeys.list(params || {}),\r\n    queryFn: () => employeeService.getEmployees(params),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n  });\r\n};\r\n\r\nexport const useEmployee = (id: number) => {\r\n  return useQuery({\r\n    queryKey: employeeKeys.detail(id),\r\n    queryFn: () => employeeService.getEmployeeById(id),\r\n    enabled: !!id,\r\n    retry: 1,\r\n  });\r\n};\r\n\r\nexport const useEmployeesByBranch = (branchId: number) => {\r\n  return useQuery({\r\n    queryKey: employeeKeys.branch(branchId),\r\n    queryFn: () => employeeService.getEmployeesByBranch(branchId),\r\n    enabled: !!branchId,\r\n    retry: 1,\r\n  });\r\n};\r\n\r\nexport const useCreateEmployee = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: EmployeeFormValues) => employeeService.createEmployee(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: employeeKeys.lists() });\r\n      toast.success(\"Employee created successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      const errorMessage = error.response?.data?.message || error.message || \"Failed to create employee\";\r\n      toast.error(errorMessage);\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateEmployee = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: EmployeeFormValues) => employeeService.updateEmployee(id, data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: employeeKeys.lists() });\r\n      queryClient.invalidateQueries({ queryKey: employeeKeys.detail(id) });\r\n      toast.success(\"Employee updated successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      const errorMessage = error.response?.data?.message || error.message || \"Failed to update employee\";\r\n      toast.error(errorMessage);\r\n    },\r\n  });\r\n};\r\n\r\nexport const useDeleteEmployee = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => employeeService.deleteEmployee(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: employeeKeys.lists() });\r\n      toast.success(\"Employee deleted successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      const errorMessage = error.response?.data?.message || error.message || \"Failed to delete employee\";\r\n      toast.error(errorMessage);\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAAA;AAAA;AACA;AAEA;;;;;AAEA,gDAAgD;AAChD,MAAM,eAAe;IACnB,KAAK;QAAC;KAAY;IAClB,OAAO,IAAM;eAAI,aAAa,GAAG;YAAE;SAAO;IAC1C,MAAM,CAAC,UAAiC;eAAI,aAAa,KAAK;YAAI;SAAQ;IAC1E,SAAS,IAAM;eAAI,aAAa,GAAG;YAAE;SAAS;IAC9C,QAAQ,CAAC,KAAe;eAAI,aAAa,OAAO;YAAI;SAAG;IACvD,UAAU,IAAM;eAAI,aAAa,GAAG;YAAE;SAAW;IACjD,QAAQ,CAAC,WAAqB;eAAI,aAAa,QAAQ;YAAI;SAAS;AACtE;AAEO,MAAM,eAAe,CAAC;;IAC3B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,IAAI,CAAC,UAAU,CAAC;QACvC,OAAO;qCAAE,IAAM,6JAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;;QAC5C,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;IACxB;AACF;GATa;;QACJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,cAAc,CAAC;;IAC1B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,MAAM,CAAC;QAC9B,OAAO;oCAAE,IAAM,6JAAA,CAAA,kBAAe,CAAC,eAAe,CAAC;;QAC/C,SAAS,CAAC,CAAC;QACX,OAAO;IACT;AACF;IAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AAQV,MAAM,uBAAuB,CAAC;;IACnC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,MAAM,CAAC;QAC9B,OAAO;6CAAE,IAAM,6JAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC;;QACpD,SAAS,CAAC,CAAC;QACX,OAAO;IACT;AACF;IAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AAQV,MAAM,oBAAoB;;IAC/B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE,CAAC,OAA6B,6JAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;;QACzE,SAAS;6CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK;gBAAG;gBAC/D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;6CAAE,CAAC;gBACR,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;gBACvE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAda;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,MAAM,oBAAoB,CAAC;;IAChC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE,CAAC,OAA6B,6JAAA,CAAA,kBAAe,CAAC,cAAc,CAAC,IAAI;;QAC7E,SAAS;6CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK;gBAAG;gBAC/D,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,MAAM,CAAC;gBAAI;gBAClE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;6CAAE,CAAC;gBACR,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;gBACvE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAfa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAcb,MAAM,oBAAoB;;IAC/B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;6CAAE,CAAC,KAAe,6JAAA,CAAA,kBAAe,CAAC,cAAc,CAAC;;QAC3D,SAAS;6CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,aAAa,KAAK;gBAAG;gBAC/D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;6CAAE,CAAC;gBACR,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;gBACvE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAda;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 6268, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/command.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Command as CommandPrimitive } from \"cmdk\"\r\nimport { SearchIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@/components/ui/dialog\"\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot=\"command\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = \"Command Palette\",\r\n  description = \"Search for a command to run...\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string\r\n  description?: string\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className=\"sr-only\">\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent className=\"overflow-hidden p-0\">\r\n        <Command className=\"[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  )\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot=\"command-input-wrapper\"\r\n      className=\"flex h-9 items-center gap-2 border-b px-3\"\r\n    >\r\n      <SearchIcon className=\"size-4 shrink-0 opacity-50\" />\r\n      <CommandPrimitive.Input\r\n        data-slot=\"command-input\"\r\n        className={cn(\r\n          \"placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot=\"command-list\"\r\n      className={cn(\r\n        \"max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot=\"command-empty\"\r\n      className=\"py-6 text-center text-sm\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot=\"command-group\"\r\n      className={cn(\r\n        \"text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot=\"command-separator\"\r\n      className={cn(\"bg-border -mx-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot=\"command-item\"\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"command-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,6LAAC,yIAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,GAAG,OAIJ;IACC,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,6LAAC,qIAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,6LAAC,qIAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,6LAAC,qIAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,6LAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,6LAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;MAtBS;AAwBT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;;0BAEV,6LAAC,6MAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,6LAAC,yIAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,yIAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 6473, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/employees/components/employee-selector.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { useEmployees } from '../hooks/use-employees';\r\nimport { Employee } from '../types';\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList,\r\n} from \"@/components/ui/command\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { Button } from '@/components/ui/button';\r\nimport { Check, ChevronsUpDown, Loader2 } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface EmployeeSelectorProps {\r\n  value?: number;\r\n  onValueChange: (value: number | undefined) => void;\r\n  placeholder?: string;\r\n  includeAllOption?: boolean;\r\n  branchId?: number;\r\n}\r\n\r\nexport function EmployeeSelector({\r\n  value,\r\n  onValueChange,\r\n  placeholder = \"Select an employee\",\r\n  includeAllOption = false,\r\n  branchId,\r\n}: EmployeeSelectorProps) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n\r\n  // Fetch employees with optional branch filter\r\n  const params: Record<string, any> = {\r\n    search: searchTerm,\r\n    status: 'active'\r\n  };\r\n  \r\n  if (branchId) {\r\n    params.branch_id = branchId;\r\n  }\r\n  \r\n  const { data: employeesResponse, isLoading } = useEmployees(params);\r\n  const employees = employeesResponse?.data || [];\r\n\r\n  // Find the selected employee\r\n  const selectedEmployee = value \r\n    ? employees.find(employee => employee.id === value) \r\n    : undefined;\r\n\r\n  const handleSelect = (value: string) => {\r\n    if (value === 'all') {\r\n      onValueChange(undefined);\r\n    } else {\r\n      onValueChange(parseInt(value, 10));\r\n    }\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          className=\"w-full justify-between\"\r\n        >\r\n          {value && selectedEmployee\r\n            ? selectedEmployee.name\r\n            : placeholder}\r\n          {isLoading ? (\r\n            <Loader2 className=\"ml-2 h-4 w-4 animate-spin\" />\r\n          ) : (\r\n            <ChevronsUpDown className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\r\n          )}\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-[300px] p-0\">\r\n        <Command>\r\n          <CommandInput\r\n            placeholder=\"Search employees...\"\r\n            value={searchTerm}\r\n            onValueChange={setSearchTerm}\r\n          />\r\n          <CommandList>\r\n            <CommandEmpty>No employees found.</CommandEmpty>\r\n            <CommandGroup>\r\n              {includeAllOption && (\r\n                <CommandItem\r\n                  value=\"all\"\r\n                  onSelect={handleSelect}\r\n                  className=\"cursor-pointer\"\r\n                >\r\n                  <Check\r\n                    className={cn(\r\n                      \"mr-2 h-4 w-4\",\r\n                      !value ? \"opacity-100\" : \"opacity-0\"\r\n                    )}\r\n                  />\r\n                  All Employees\r\n                </CommandItem>\r\n              )}\r\n              {employees.map((employee) => (\r\n                <CommandItem\r\n                  key={employee.id}\r\n                  value={employee.id.toString()}\r\n                  onSelect={handleSelect}\r\n                  className=\"cursor-pointer\"\r\n                >\r\n                  <Check\r\n                    className={cn(\r\n                      \"mr-2 h-4 w-4\",\r\n                      value === employee.id ? \"opacity-100\" : \"opacity-0\"\r\n                    )}\r\n                  />\r\n                  {employee.name}\r\n                </CommandItem>\r\n              ))}\r\n            </CommandGroup>\r\n          </CommandList>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAQA;AAKA;AACA;AAAA;AAAA;AACA;;;AApBA;;;;;;;;AA8BO,SAAS,iBAAiB,EAC/B,KAAK,EACL,aAAa,EACb,cAAc,oBAAoB,EAClC,mBAAmB,KAAK,EACxB,QAAQ,EACc;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,8CAA8C;IAC9C,MAAM,SAA8B;QAClC,QAAQ;QACR,QAAQ;IACV;IAEA,IAAI,UAAU;QACZ,OAAO,SAAS,GAAG;IACrB;IAEA,MAAM,EAAE,MAAM,iBAAiB,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD,EAAE;IAC5D,MAAM,YAAY,mBAAmB,QAAQ,EAAE;IAE/C,6BAA6B;IAC7B,MAAM,mBAAmB,QACrB,UAAU,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK,SAC3C;IAEJ,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU,OAAO;YACnB,cAAc;QAChB,OAAO;YACL,cAAc,SAAS,OAAO;QAChC;QACA,QAAQ;IACV;IAEA,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,WAAU;;wBAET,SAAS,mBACN,iBAAiB,IAAI,GACrB;wBACH,0BACC,6LAAC,oNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;iDAEnB,6LAAC,iOAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAIhC,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,6LAAC,sIAAA,CAAA,UAAO;;sCACN,6LAAC,sIAAA,CAAA,eAAY;4BACX,aAAY;4BACZ,OAAO;4BACP,eAAe;;;;;;sCAEjB,6LAAC,sIAAA,CAAA,cAAW;;8CACV,6LAAC,sIAAA,CAAA,eAAY;8CAAC;;;;;;8CACd,6LAAC,sIAAA,CAAA,eAAY;;wCACV,kCACC,6LAAC,sIAAA,CAAA,cAAW;4CACV,OAAM;4CACN,UAAU;4CACV,WAAU;;8DAEV,6LAAC,uMAAA,CAAA,QAAK;oDACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,CAAC,QAAQ,gBAAgB;;;;;;gDAE3B;;;;;;;wCAIL,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,sIAAA,CAAA,cAAW;gDAEV,OAAO,SAAS,EAAE,CAAC,QAAQ;gDAC3B,UAAU;gDACV,WAAU;;kEAEV,6LAAC,uMAAA,CAAA,QAAK;wDACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,UAAU,SAAS,EAAE,GAAG,gBAAgB;;;;;;oDAG3C,SAAS,IAAI;;+CAXT,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBlC;GAvGgB;;QAoBiC,4JAAA,CAAA,eAAY;;;KApB7C", "debugId": null}}, {"offset": {"line": 6666, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/payment-methods/api/payment-method-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport type {\r\n  PaymentMethod,\r\n  CreatePaymentMethodRequest,\r\n  UpdatePaymentMethodRequest,\r\n  PaymentMethodFilters,\r\n  PaginatedPaymentMethodResponse,\r\n} from \"@/types/payment-method\";\r\n\r\n/**\r\n * Payment Method Service\r\n * Handles API calls for payment methods\r\n */\r\nconst paymentMethodService = {\r\n  /**\r\n   * Get all payment methods with optional filters\r\n   */\r\n  getPaymentMethods: async (\r\n    filters?: PaymentMethodFilters\r\n  ): Promise<PaginatedPaymentMethodResponse> => {\r\n    try {\r\n      const response = await apiClient.get<PaginatedPaymentMethodResponse | PaymentMethod[]>(\r\n        \"/payment-methods\",\r\n        {\r\n          params: filters,\r\n        }\r\n      );\r\n\r\n      // Handle case where API returns an array directly instead of paginated response\r\n      if (Array.isArray(response)) {\r\n        return {\r\n          data: response,\r\n          meta: {\r\n            total: response.length,\r\n            page: 1,\r\n            limit: response.length,\r\n            offset: 0,\r\n            pages: 1\r\n          }\r\n        };\r\n      }\r\n\r\n      return response;\r\n    } catch (error) {\r\n      // Silent error handling, will be handled by the component\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a payment method by ID\r\n   */\r\n  getPaymentMethodById: async (id: number): Promise<PaymentMethod> => {\r\n    const response = await apiClient.get<PaymentMethod>(\r\n      `/payment-methods/${id}`\r\n    );\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Create a new payment method\r\n   */\r\n  createPaymentMethod: async (\r\n    paymentMethod: CreatePaymentMethodRequest\r\n  ): Promise<PaymentMethod> => {\r\n    const response = await apiClient.post<PaymentMethod>(\r\n      \"/payment-methods\",\r\n      paymentMethod\r\n    );\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Update a payment method\r\n   */\r\n  updatePaymentMethod: async (\r\n    id: number,\r\n    paymentMethod: UpdatePaymentMethodRequest\r\n  ): Promise<PaymentMethod> => {\r\n    const response = await apiClient.put<PaymentMethod>(\r\n      `/payment-methods/${id}`,\r\n      paymentMethod\r\n    );\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Delete a payment method\r\n   */\r\n  deletePaymentMethod: async (id: number): Promise<void> => {\r\n    await apiClient.delete(`/payment-methods/${id}`);\r\n  },\r\n\r\n  /**\r\n   * Update payment method status\r\n   */\r\n  updatePaymentMethodStatus: async (\r\n    id: number,\r\n    isActive: boolean\r\n  ): Promise<PaymentMethod> => {\r\n    const response = await apiClient.patch<PaymentMethod>(\r\n      `/payment-methods/${id}/status`,\r\n      { is_active: isActive }\r\n    );\r\n    return response;\r\n  },\r\n};\r\n\r\nexport default paymentMethodService;\r\n"], "names": [], "mappings": ";;;AAAA;;AASA;;;CAGC,GACD,MAAM,uBAAuB;IAC3B;;GAEC,GACD,mBAAmB,OACjB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,oBACA;gBACE,QAAQ;YACV;YAGF,gFAAgF;YAChF,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,OAAO;oBACL,MAAM;oBACN,MAAM;wBACJ,OAAO,SAAS,MAAM;wBACtB,MAAM;wBACN,OAAO,SAAS,MAAM;wBACtB,QAAQ;wBACR,OAAO;oBACT;gBACF;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,0DAA0D;YAC1D,MAAM;QACR;IACF;IAEA;;GAEC,GACD,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,iBAAiB,EAAE,IAAI;QAE1B,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,OACnB;QAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CACnC,oBACA;QAEF,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,OACnB,IACA;QAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,iBAAiB,EAAE,IAAI,EACxB;QAEF,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,OAAO;QAC1B,MAAM,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,IAAI;IACjD;IAEA;;GAEC,GACD,2BAA2B,OACzB,IACA;QAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,KAAK,CACpC,CAAC,iBAAiB,EAAE,GAAG,OAAO,CAAC,EAC/B;YAAE,WAAW;QAAS;QAExB,OAAO;IACT;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 6743, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/payment-methods/hooks/use-payment-methods.ts"], "sourcesContent": ["import type {\r\n  CreatePaymentMethodRequest,\r\n  PaymentMethodFilters,\r\n  UpdatePaymentMethodRequest,\r\n} from \"@/types/payment-method\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport paymentMethodService from \"../api/payment-method-service\";\r\n\r\n/**\r\n * Hook to fetch all payment methods with optional filters\r\n */\r\nexport const usePaymentMethods = (filters?: PaymentMethodFilters) => {\r\n  return useQuery({\r\n    queryKey: [\"payment-methods\", filters],\r\n    queryFn: () => paymentMethodService.getPaymentMethods(filters),\r\n    staleTime: 5 * 60 * 1000, // 5 minutes\r\n    retry: 3,\r\n    refetchOnWindowFocus: false,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch a single payment method by ID\r\n */\r\nexport const usePaymentMethod = (id: number) => {\r\n  return useQuery({\r\n    queryKey: [\"payment-method\", id],\r\n    queryFn: () => paymentMethodService.getPaymentMethodById(id),\r\n    enabled: !!id,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to create a new payment method\r\n */\r\nexport const useCreatePaymentMethod = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (paymentMethod: CreatePaymentMethodRequest) =>\r\n      paymentMethodService.createPaymentMethod(paymentMethod),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"payment-methods\"] });\r\n      toast.success(\"Payment method created successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.message || \"An error occurred while creating the payment method\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to update a payment method\r\n */\r\nexport const useUpdatePaymentMethod = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (paymentMethod: UpdatePaymentMethodRequest) =>\r\n      paymentMethodService.updatePaymentMethod(id, paymentMethod),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"payment-methods\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"payment-method\", id] });\r\n      toast.success(\"Payment method updated successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.message || \"An error occurred while updating the payment method\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to delete a payment method\r\n */\r\nexport const useDeletePaymentMethod = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => paymentMethodService.deletePaymentMethod(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"payment-methods\"] });\r\n      toast.success(\"Payment method deleted successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.message || \"An error occurred while deleting the payment method\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to update payment method status\r\n */\r\nexport const useUpdatePaymentMethodStatus = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (isActive: boolean) =>\r\n      paymentMethodService.updatePaymentMethodStatus(id, isActive),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"payment-methods\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"payment-method\", id] });\r\n      toast.success(\"Payment method status updated successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.message ||\r\n          \"An error occurred while updating the payment method status\"\r\n      );\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAKA;AAAA;AAAA;AACA;AACA;;;;;AAKO,MAAM,oBAAoB,CAAC;;IAChC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAmB;SAAQ;QACtC,OAAO;0CAAE,IAAM,+KAAA,CAAA,UAAoB,CAAC,iBAAiB,CAAC;;QACtD,WAAW,IAAI,KAAK;QACpB,OAAO;QACP,sBAAsB;IACxB;AACF;GARa;;QACJ,8KAAA,CAAA,WAAQ;;;AAYV,MAAM,mBAAmB,CAAC;;IAC/B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAkB;SAAG;QAChC,OAAO;yCAAE,IAAM,+KAAA,CAAA,UAAoB,CAAC,oBAAoB,CAAC;;QACzD,SAAS,CAAC,CAAC;IACb;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,yBAAyB;;IACpC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;kDAAE,CAAC,gBACX,+KAAA,CAAA,UAAoB,CAAC,mBAAmB,CAAC;;QAC3C,SAAS;kDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAkB;gBAAC;gBAC9D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;kDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,OAAO,IAAI;YAErB;;IACF;AACF;IAhBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,MAAM,yBAAyB,CAAC;;IACrC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;kDAAE,CAAC,gBACX,+KAAA,CAAA,UAAoB,CAAC,mBAAmB,CAAC,IAAI;;QAC/C,SAAS;kDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAkB;gBAAC;gBAC9D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAkB;qBAAG;gBAAC;gBACjE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;kDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,OAAO,IAAI;YAErB;;IACF;AACF;IAjBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmBb,MAAM,yBAAyB;;IACpC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;kDAAE,CAAC,KAAe,+KAAA,CAAA,UAAoB,CAAC,mBAAmB,CAAC;;QACrE,SAAS;kDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAkB;gBAAC;gBAC9D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;kDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,OAAO,IAAI;YAErB;;IACF;AACF;IAfa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,MAAM,+BAA+B,CAAC;;IAC3C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;wDAAE,CAAC,WACX,+KAAA,CAAA,UAAoB,CAAC,yBAAyB,CAAC,IAAI;;QACrD,SAAS;wDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAkB;gBAAC;gBAC9D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAkB;qBAAG;gBAAC;gBACjE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;wDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,OAAO,IACX;YAEN;;IACF;AACF;IAlBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 6939, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/payment-methods/components/payment-method-selector.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { usePaymentMethods } from '../hooks/use-payment-methods';\r\nimport { PaymentMethod } from '@/types/payment-method';\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList,\r\n} from \"@/components/ui/command\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { Button } from '@/components/ui/button';\r\nimport { Check, ChevronsUpDown, Loader2 } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport apiClient from '@/lib/api-client';\r\n\r\ninterface PaymentMethodSelectorProps {\r\n  value?: number;\r\n  onValueChange: (value: number | undefined) => void;\r\n  placeholder?: string;\r\n  includeAllOption?: boolean;\r\n}\r\n\r\nexport function PaymentMethodSelector({\r\n  value,\r\n  onValueChange,\r\n  placeholder = \"Select payment method...\",\r\n  includeAllOption = true\r\n}: PaymentMethodSelectorProps) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n\r\n  // State for fallback payment methods\r\n  const [fallbackMethods, setFallbackMethods] = useState<PaymentMethod[]>([]);\r\n  const [isFallbackLoading, setIsFallbackLoading] = useState(false);\r\n\r\n  // Fetch payment methods\r\n  const { data: paymentMethodsResponse, isLoading: isApiLoading, error } = usePaymentMethods({\r\n    search: searchTerm,\r\n    is_active: true // Only fetch active payment methods\r\n  });\r\n\r\n  // No debug logs in production\r\n\r\n  // Fallback to direct API call if the hook fails\r\n  useEffect(() => {\r\n    const fetchFallbackMethods = async () => {\r\n      if (error && !fallbackMethods.length) {\r\n        try {\r\n          setIsFallbackLoading(true);\r\n          const response = await apiClient.get<PaymentMethod[]>('/payment-methods');\r\n          if (Array.isArray(response)) {\r\n            setFallbackMethods(response);\r\n          } else if (response && response.data) {\r\n            setFallbackMethods(response.data);\r\n          }\r\n        } catch (err) {\r\n          // Silent error handling, will use hardcoded methods\r\n        } finally {\r\n          setIsFallbackLoading(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    fetchFallbackMethods();\r\n  }, [error]);\r\n\r\n  // Hardcoded fallback payment methods as a last resort\r\n  const hardcodedMethods: PaymentMethod[] = [\r\n    { id: 1, name: \"Cash\", code: \"CASH\", description: \"Cash payment\", requires_reference: false, is_active: true, created_at: \"\", updated_at: \"\", deleted_at: null, tenant_id: null, Tenant: null },\r\n    { id: 2, name: \"M-Pesa\", code: \"MPESA\", description: \"M-Pesa payment\", requires_reference: true, is_active: true, created_at: \"\", updated_at: \"\", deleted_at: null, tenant_id: null, Tenant: null },\r\n    { id: 3, name: \"Credit Card\", code: \"CARD\", description: \"Credit card payment\", requires_reference: true, is_active: true, created_at: \"\", updated_at: \"\", deleted_at: null, tenant_id: null, Tenant: null },\r\n    { id: 4, name: \"Bank Transfer\", code: \"BANK\", description: \"Bank transfer payment\", requires_reference: true, is_active: true, created_at: \"\", updated_at: \"\", deleted_at: null, tenant_id: null, Tenant: null },\r\n    { id: 5, name: \"Credit\", code: \"CREDIT\", description: \"Credit payment\", requires_reference: true, is_active: true, created_at: \"\", updated_at: \"\", deleted_at: null, tenant_id: null, Tenant: null }\r\n  ];\r\n\r\n  const isLoading = isApiLoading || isFallbackLoading;\r\n  const paymentMethods = paymentMethodsResponse?.data?.length\r\n    ? paymentMethodsResponse.data\r\n    : fallbackMethods.length\r\n      ? fallbackMethods\r\n      : hardcodedMethods;\r\n\r\n  // Find the selected payment method\r\n  const selectedPaymentMethod = value\r\n    ? paymentMethods.find(method => method.id === value)\r\n    : undefined;\r\n\r\n  const handleSelect = (value: string) => {\r\n    if (value === 'all') {\r\n      onValueChange(undefined);\r\n    } else {\r\n      onValueChange(parseInt(value, 10));\r\n    }\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          className=\"w-full justify-between\"\r\n        >\r\n          {selectedPaymentMethod\r\n            ? selectedPaymentMethod.name\r\n            : value === undefined && includeAllOption\r\n              ? \"All Payment Methods\"\r\n              : placeholder}\r\n          <ChevronsUpDown className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-[300px] p-0\">\r\n        <Command>\r\n          <CommandInput\r\n            placeholder=\"Search payment methods...\"\r\n            value={searchTerm}\r\n            onValueChange={setSearchTerm}\r\n          />\r\n          <CommandList>\r\n            {isLoading ? (\r\n              <div className=\"flex items-center justify-center p-4\">\r\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\r\n                <span>Loading payment methods...</span>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                <CommandEmpty>No payment methods found.</CommandEmpty>\r\n                <CommandGroup>\r\n                  {includeAllOption && (\r\n                    <CommandItem\r\n                      key=\"all\"\r\n                      value=\"all\"\r\n                      onSelect={() => handleSelect('all')}\r\n                    >\r\n                      <Check\r\n                        className={cn(\r\n                          \"mr-2 h-4 w-4\",\r\n                          value === undefined ? \"opacity-100\" : \"opacity-0\"\r\n                        )}\r\n                      />\r\n                      <span>All Payment Methods</span>\r\n                    </CommandItem>\r\n                  )}\r\n                  {paymentMethods.map((method) => (\r\n                    <CommandItem\r\n                      key={method.id}\r\n                      value={method.id.toString()}\r\n                      onSelect={() => handleSelect(method.id.toString())}\r\n                    >\r\n                      <Check\r\n                        className={cn(\r\n                          \"mr-2 h-4 w-4\",\r\n                          value === method.id ? \"opacity-100\" : \"opacity-0\"\r\n                        )}\r\n                      />\r\n                      <span>{method.name}</span>\r\n                    </CommandItem>\r\n                  ))}\r\n                </CommandGroup>\r\n              </>\r\n            )}\r\n          </CommandList>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;AAQA;AAKA;AACA;AAAA;AAAA;AACA;AACA;;;;;;;;;;;AASO,SAAS,sBAAsB,EACpC,KAAK,EACL,aAAa,EACb,cAAc,0BAA0B,EACxC,mBAAmB,IAAI,EACI;;IAC3B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qCAAqC;IACrC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC1E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,wBAAwB;IACxB,MAAM,EAAE,MAAM,sBAAsB,EAAE,WAAW,YAAY,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,oBAAiB,AAAD,EAAE;QACzF,QAAQ;QACR,WAAW,KAAK,oCAAoC;IACtD;IAEA,8BAA8B;IAE9B,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM;wEAAuB;oBAC3B,IAAI,SAAS,CAAC,gBAAgB,MAAM,EAAE;wBACpC,IAAI;4BACF,qBAAqB;4BACrB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAkB;4BACtD,IAAI,MAAM,OAAO,CAAC,WAAW;gCAC3B,mBAAmB;4BACrB,OAAO,IAAI,YAAY,SAAS,IAAI,EAAE;gCACpC,mBAAmB,SAAS,IAAI;4BAClC;wBACF,EAAE,OAAO,KAAK;wBACZ,oDAAoD;wBACtD,SAAU;4BACR,qBAAqB;wBACvB;oBACF;gBACF;;YAEA;QACF;0CAAG;QAAC;KAAM;IAEV,sDAAsD;IACtD,MAAM,mBAAoC;QACxC;YAAE,IAAI;YAAG,MAAM;YAAQ,MAAM;YAAQ,aAAa;YAAgB,oBAAoB;YAAO,WAAW;YAAM,YAAY;YAAI,YAAY;YAAI,YAAY;YAAM,WAAW;YAAM,QAAQ;QAAK;QAC9L;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAS,aAAa;YAAkB,oBAAoB;YAAM,WAAW;YAAM,YAAY;YAAI,YAAY;YAAI,YAAY;YAAM,WAAW;YAAM,QAAQ;QAAK;QAClM;YAAE,IAAI;YAAG,MAAM;YAAe,MAAM;YAAQ,aAAa;YAAuB,oBAAoB;YAAM,WAAW;YAAM,YAAY;YAAI,YAAY;YAAI,YAAY;YAAM,WAAW;YAAM,QAAQ;QAAK;QAC3M;YAAE,IAAI;YAAG,MAAM;YAAiB,MAAM;YAAQ,aAAa;YAAyB,oBAAoB;YAAM,WAAW;YAAM,YAAY;YAAI,YAAY;YAAI,YAAY;YAAM,WAAW;YAAM,QAAQ;QAAK;QAC/M;YAAE,IAAI;YAAG,MAAM;YAAU,MAAM;YAAU,aAAa;YAAkB,oBAAoB;YAAM,WAAW;YAAM,YAAY;YAAI,YAAY;YAAI,YAAY;YAAM,WAAW;YAAM,QAAQ;QAAK;KACpM;IAED,MAAM,YAAY,gBAAgB;IAClC,MAAM,iBAAiB,wBAAwB,MAAM,SACjD,uBAAuB,IAAI,GAC3B,gBAAgB,MAAM,GACpB,kBACA;IAEN,mCAAmC;IACnC,MAAM,wBAAwB,QAC1B,eAAe,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK,SAC5C;IAEJ,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU,OAAO;YACnB,cAAc;QAChB,OAAO;YACL,cAAc,SAAS,OAAO;QAChC;QACA,QAAQ;IACV;IAEA,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,WAAU;;wBAET,wBACG,sBAAsB,IAAI,GAC1B,UAAU,aAAa,mBACrB,wBACA;sCACN,6LAAC,iOAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,6LAAC,sIAAA,CAAA,UAAO;;sCACN,6LAAC,sIAAA,CAAA,eAAY;4BACX,aAAY;4BACZ,OAAO;4BACP,eAAe;;;;;;sCAEjB,6LAAC,sIAAA,CAAA,cAAW;sCACT,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAK;;;;;;;;;;;qDAGR;;kDACE,6LAAC,sIAAA,CAAA,eAAY;kDAAC;;;;;;kDACd,6LAAC,sIAAA,CAAA,eAAY;;4CACV,kCACC,6LAAC,sIAAA,CAAA,cAAW;gDAEV,OAAM;gDACN,UAAU,IAAM,aAAa;;kEAE7B,6LAAC,uMAAA,CAAA,QAAK;wDACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,UAAU,YAAY,gBAAgB;;;;;;kEAG1C,6LAAC;kEAAK;;;;;;;+CAVF;;;;;4CAaP,eAAe,GAAG,CAAC,CAAC,uBACnB,6LAAC,sIAAA,CAAA,cAAW;oDAEV,OAAO,OAAO,EAAE,CAAC,QAAQ;oDACzB,UAAU,IAAM,aAAa,OAAO,EAAE,CAAC,QAAQ;;sEAE/C,6LAAC,uMAAA,CAAA,QAAK;4DACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,UAAU,OAAO,EAAE,GAAG,gBAAgB;;;;;;sEAG1C,6LAAC;sEAAM,OAAO,IAAI;;;;;;;mDAVb,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBpC;GAlJgB;;QAc2D,8KAAA,CAAA,oBAAiB;;;KAd5E", "debugId": null}}, {"offset": {"line": 7257, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/pos/api/pos-session-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport {\r\n  PosSession,\r\n  CreatePosSessionRequest,\r\n  ClosePosSessionRequest,\r\n  PosSessionFilters,\r\n} from \"@/types\";\r\n\r\n/**\r\n * Helper function to fetch DSA sales data for a session\r\n */\r\nasync function fetchDsaSalesData(sessionId: number, branchId: number) {\r\n  try {\r\n    // Fetch users with DSA role in this branch\r\n    const dsaUsers = await apiClient.get(\"/users\", {\r\n      params: {\r\n        branch_id: branchId,\r\n        role_name: \"dsa\",\r\n      },\r\n    });\r\n\r\n    // If no DSA users, return empty data\r\n    if (!Array.isArray(dsaUsers) || dsaUsers.length === 0) {\r\n      return {\r\n        dsaSalesByUser: [],\r\n        totalDsaSales: 0,\r\n      };\r\n    }\r\n\r\n    // For each DSA user, fetch their sales during this session\r\n    const dsaSalesByUser = await Promise.all(\r\n      dsaUsers.map(async (user: any) => {\r\n        let userSales;\r\n        try {\r\n          // First try with pos_session_id\r\n          userSales = await apiClient.get(\"/sales\", {\r\n            params: {\r\n              user_id: user.id,\r\n              pos_session_id: sessionId,\r\n              branch_id: branchId,\r\n            },\r\n          });\r\n        } catch (error) {\r\n          // If the API doesn't support filtering by pos_session_id, return empty data\r\n          console.warn(\r\n            \"Error fetching DSA sales with pos_session_id, returning empty data\"\r\n          );\r\n          userSales = [];\r\n        }\r\n\r\n        // Calculate total sales for this user\r\n        let total = 0;\r\n        const sales = [];\r\n\r\n        if (Array.isArray(userSales)) {\r\n          for (const sale of userSales) {\r\n            const amount = parseFloat(sale.total_amount) || 0;\r\n            total += amount;\r\n\r\n            sales.push({\r\n              id: sale.id,\r\n              amount,\r\n              date: sale.created_at,\r\n              paymentMethod: sale.payment_method || \"Unknown\",\r\n            });\r\n          }\r\n        }\r\n\r\n        return {\r\n          id: user.id,\r\n          name: user.name || `DSA #${user.id}`,\r\n          total,\r\n          sales,\r\n        };\r\n      })\r\n    );\r\n\r\n    // Filter out users with no sales\r\n    const dsaUsersWithSales = dsaSalesByUser.filter((user) => user.total > 0);\r\n\r\n    // Calculate total DSA sales\r\n    const totalDsaSales = dsaUsersWithSales.reduce(\r\n      (sum, user) => sum + user.total,\r\n      0\r\n    );\r\n\r\n    return {\r\n      dsaSalesByUser: dsaUsersWithSales,\r\n      totalDsaSales,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"Error fetching DSA sales data:\", error);\r\n    return {\r\n      dsaSalesByUser: [],\r\n      totalDsaSales: 0,\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Shift Closing Data interface\r\n */\r\ninterface ShiftClosingData {\r\n  session: {\r\n    id: number;\r\n    opening_cash_balance: number;\r\n    opening_mpesa_balance: number;\r\n    opening_mpesa_float: number;\r\n    start_time: string;\r\n    user: {\r\n      id: number;\r\n      name: string;\r\n    };\r\n    branch: {\r\n      id: number;\r\n      name: string;\r\n    };\r\n  };\r\n  sales: {\r\n    total: number;\r\n    transaction_count: number;\r\n    by_payment_method: {\r\n      [key: string]: {\r\n        name: string;\r\n        amount: number;\r\n        count: number;\r\n      };\r\n    };\r\n    cash_sales: number;\r\n    non_cash_sales: number;\r\n  };\r\n  mpesa_services: {\r\n    deposits: number;\r\n    withdrawals: number;\r\n  };\r\n  mobile_money_services: {\r\n    opening_float: number;\r\n    float_in: number;\r\n    float_out: number;\r\n    total_deposits: number;\r\n    total_withdrawals: number;\r\n    expected_float: number;\r\n  };\r\n  banking: {\r\n    total: number;\r\n    items: Array<{\r\n      id: number;\r\n      amount: number;\r\n      banking_method: string;\r\n      reference_number: string;\r\n      status: string;\r\n      transaction_date: string;\r\n      bank: {\r\n        id: number;\r\n        name: string;\r\n        code: string;\r\n        type: string;\r\n      };\r\n    }>;\r\n  };\r\n  expenses: {\r\n    total: number;\r\n    items: any[];\r\n  };\r\n}\r\n\r\n/**\r\n * Session Breakdown Data interface\r\n */\r\ninterface SessionBreakdownData {\r\n  // Session data\r\n  session: {\r\n    id: number;\r\n    user_id: number;\r\n    branch_id: number;\r\n    start_time: string;\r\n    end_time: string | null;\r\n    opening_cash_balance: string;\r\n    opening_mpesa_balance?: string;\r\n    opening_mpesa_float?: string;\r\n    cash_paid_in: string;\r\n    cash_paid_out: string;\r\n    status: \"open\" | \"closed\";\r\n    user_name?: string;\r\n    branch_name?: string;\r\n  };\r\n\r\n  // Reconciliation data\r\n  reconciliation?: {\r\n    id: number;\r\n    closing_cash_balance: string;\r\n    closing_mpesa_balance: string;\r\n    closing_mpesa_float?: string;\r\n    cash_payments: string;\r\n    mpesa_payments: string;\r\n    total_sales: string;\r\n    discrepancies: string;\r\n    total_variance: string;\r\n    notes: string;\r\n    created_at: string;\r\n  };\r\n\r\n  // Sales data\r\n  salesByPaymentMethod: {\r\n    [key: string]: number;\r\n  };\r\n  totalSales: number;\r\n  totalCashSales: number;\r\n  totalNonCashSales: number;\r\n\r\n  // MPESA services\r\n  mpesaServices: {\r\n    deposits: number;\r\n    withdrawals: number;\r\n  };\r\n\r\n  // Mobile money services\r\n  mobileMoneyServices: {\r\n    deposits: number;\r\n    withdrawals: number;\r\n    float_in: number;\r\n    float_out: number;\r\n  };\r\n\r\n  // Banking transactions\r\n  bankingTransactions: any[];\r\n\r\n  // Expenses\r\n  totalExpenses: number;\r\n\r\n  // DSA sales\r\n  dsaSales: {\r\n    dsaSalesByUser: Array<{\r\n      id: number;\r\n      name: string;\r\n      total: number;\r\n      sales: Array<{\r\n        id: number;\r\n        amount: number;\r\n        date: string;\r\n        paymentMethod: string;\r\n      }>;\r\n    }>;\r\n    totalDsaSales: number;\r\n  };\r\n}\r\n\r\n/**\r\n * POS Session Service\r\n * Handles API calls for POS sessions\r\n */\r\nconst posSessionService = {\r\n  /**\r\n   * Get shift closing data for a session\r\n   */\r\n  getShiftClosingData: async (id: number): Promise<any> => {\r\n    try {\r\n      const response = await apiClient.get(\r\n        `/pos-sessions/${id}/shift-closing-data`\r\n      );\r\n      console.log(\"Shift closing data response:\", response);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(\"Error fetching shift closing data:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get all POS sessions with optional filters\r\n   */\r\n  getSessions: async (filters?: PosSessionFilters): Promise<PosSession[]> => {\r\n    const response = await apiClient.get<PosSession[]>(\"/pos-sessions\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get a POS session by ID\r\n   */\r\n  getSessionById: async (id: number): Promise<PosSession> => {\r\n    const response = await apiClient.get<PosSession>(`/pos-sessions/${id}`);\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Create a new POS session\r\n   */\r\n  createSession: async (data: CreatePosSessionRequest): Promise<PosSession> => {\r\n    const response = await apiClient.post<PosSession>(\"/pos-sessions\", data);\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Close a POS session\r\n   */\r\n  closeSession: async (\r\n    id: number,\r\n    data: ClosePosSessionRequest\r\n  ): Promise<PosSession> => {\r\n    const response = await apiClient.post<PosSession>(\r\n      `/pos-sessions/${id}/close`,\r\n      data\r\n    );\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get detailed breakdown data for a session\r\n   */\r\n  getSessionBreakdown: async (id: number): Promise<SessionBreakdownData> => {\r\n    try {\r\n      // First get the session details to get the time range\r\n      const session = await posSessionService.getSessionById(id);\r\n\r\n      if (!session) {\r\n        throw new Error(`Session with ID ${id} not found`);\r\n      }\r\n\r\n      // Prepare date filters for API calls\r\n      const startTime = session.start_time;\r\n      const endTime = session.end_time || new Date().toISOString();\r\n      const branchId = session.branch_id;\r\n\r\n      // Fetch sales data for this session\r\n      const salesData = await apiClient.get(\"/sales\", {\r\n        params: {\r\n          pos_session_id: id,\r\n          include_items: true,\r\n        },\r\n      });\r\n\r\n      // Fetch MPESA transactions for this session\r\n      let mpesaData;\r\n      try {\r\n        // First try with pos_session_id\r\n        mpesaData = await apiClient.get(\"/mpesa-transactions\", {\r\n          params: {\r\n            pos_session_id: id,\r\n            branch_id: branchId,\r\n            limit: 1000,\r\n          },\r\n        });\r\n      } catch (error) {\r\n        // Fallback to time-based filtering if pos_session_id is not supported\r\n        console.warn(\r\n          \"Falling back to time-based filtering for MPESA transactions\"\r\n        );\r\n        mpesaData = await apiClient.get(\"/mpesa-transactions\", {\r\n          params: {\r\n            branch_id: branchId,\r\n            start_date: startTime,\r\n            end_date: endTime,\r\n            limit: 1000,\r\n          },\r\n        });\r\n      }\r\n\r\n      // Fetch expenses for this session\r\n      let expensesData;\r\n      try {\r\n        // First try with pos_session_id\r\n        expensesData = await apiClient.get(\"/expenses\", {\r\n          params: {\r\n            pos_session_id: id,\r\n            branch_id: branchId,\r\n            status: \"approved\",\r\n            limit: 1000,\r\n          },\r\n        });\r\n      } catch (error) {\r\n        // Fallback to time-based filtering if pos_session_id is not supported\r\n        console.warn(\"Falling back to time-based filtering for expenses\");\r\n        expensesData = await apiClient.get(\"/expenses\", {\r\n          params: {\r\n            branch_id: branchId,\r\n            start_date: startTime,\r\n            end_date: endTime,\r\n            status: \"approved\",\r\n            limit: 1000,\r\n          },\r\n        });\r\n      }\r\n\r\n      // Process sales data to get sales by payment method\r\n      const salesByPaymentMethod: Record<string, number> = {};\r\n      let totalSales = 0;\r\n      let totalCashSales = 0;\r\n      let totalNonCashSales = 0;\r\n\r\n      // Group sales by payment method\r\n      if (Array.isArray(salesData)) {\r\n        salesData.forEach((sale: any) => {\r\n          const paymentMethod = sale.payment_method || \"Unknown\";\r\n          const amount = parseFloat(sale.total_amount) || 0;\r\n\r\n          salesByPaymentMethod[paymentMethod] =\r\n            (salesByPaymentMethod[paymentMethod] || 0) + amount;\r\n          totalSales += amount;\r\n\r\n          if (paymentMethod.toLowerCase() === \"cash\") {\r\n            totalCashSales += amount;\r\n          } else {\r\n            totalNonCashSales += amount;\r\n          }\r\n        });\r\n      }\r\n\r\n      // Process MPESA transactions\r\n      const mpesaServices = {\r\n        deposits: 0,\r\n        withdrawals: 0,\r\n      };\r\n\r\n      const mobileMoneyServices = {\r\n        deposits: 0,\r\n        withdrawals: 0,\r\n        float_in: 0,\r\n        float_out: 0,\r\n      };\r\n\r\n      if (mpesaData && mpesaData.data && Array.isArray(mpesaData.data)) {\r\n        mpesaData.data.forEach((transaction: any) => {\r\n          const amount = parseFloat(transaction.amount) || 0;\r\n\r\n          if (transaction.transaction_type === \"deposit\") {\r\n            mpesaServices.deposits += amount;\r\n          } else if (transaction.transaction_type === \"withdrawal\") {\r\n            mpesaServices.withdrawals += amount;\r\n          } else if (transaction.transaction_type === \"float_in\") {\r\n            mobileMoneyServices.float_in += amount;\r\n          } else if (transaction.transaction_type === \"float_out\") {\r\n            mobileMoneyServices.float_out += amount;\r\n          }\r\n\r\n          // Mobile money services\r\n          if (transaction.service_type === \"mobile_money\") {\r\n            if (transaction.transaction_type === \"deposit\") {\r\n              mobileMoneyServices.deposits += amount;\r\n            } else if (transaction.transaction_type === \"withdrawal\") {\r\n              mobileMoneyServices.withdrawals += amount;\r\n            }\r\n          }\r\n        });\r\n      }\r\n\r\n      // Process expenses\r\n      let totalExpenses = 0;\r\n      if (\r\n        expensesData &&\r\n        expensesData.expenses &&\r\n        Array.isArray(expensesData.expenses)\r\n      ) {\r\n        expensesData.expenses.forEach((expense: any) => {\r\n          totalExpenses +=\r\n            parseFloat(expense.approved_amount || expense.amount) || 0;\r\n        });\r\n      }\r\n\r\n      // Fetch DSA sales data\r\n      const dsaSalesData = await fetchDsaSalesData(id, branchId);\r\n\r\n      // Extract session data\r\n      const sessionData = {\r\n        id: session.id,\r\n        user_id: session.user_id,\r\n        branch_id: session.branch_id,\r\n        start_time: session.start_time,\r\n        end_time: session.end_time,\r\n        opening_cash_balance: session.opening_cash_balance,\r\n        opening_mpesa_balance: session.opening_mpesa_balance || \"0\",\r\n        opening_mpesa_float: session.opening_mpesa_float || \"0\",\r\n        cash_paid_in: session.cash_paid_in,\r\n        cash_paid_out: session.cash_paid_out,\r\n        status: session.status,\r\n        user_name: session.User?.name || \"Unknown\",\r\n        branch_name: session.Branch?.name || \"Unknown\",\r\n      };\r\n\r\n      // Extract reconciliation data if available\r\n      const reconciliationData = session.PosSessionReconciliation\r\n        ? {\r\n            id: session.PosSessionReconciliation.id,\r\n            closing_cash_balance:\r\n              session.PosSessionReconciliation.closing_cash_balance,\r\n            closing_mpesa_balance:\r\n              session.PosSessionReconciliation.closing_mpesa_balance,\r\n            closing_mpesa_float:\r\n              session.PosSessionReconciliation.closing_mpesa_float || \"0\",\r\n            cash_payments: session.PosSessionReconciliation.cash_payments,\r\n            mpesa_payments: session.PosSessionReconciliation.mpesa_payments,\r\n            total_sales: session.PosSessionReconciliation.total_sales,\r\n            discrepancies: session.PosSessionReconciliation.discrepancies,\r\n            total_variance: session.PosSessionReconciliation.total_variance,\r\n            notes: session.PosSessionReconciliation.notes,\r\n            created_at: session.PosSessionReconciliation.created_at,\r\n          }\r\n        : undefined;\r\n\r\n      return {\r\n        session: sessionData,\r\n        reconciliation: reconciliationData,\r\n        salesByPaymentMethod,\r\n        totalSales,\r\n        totalCashSales,\r\n        totalNonCashSales,\r\n        mpesaServices,\r\n        mobileMoneyServices,\r\n        bankingTransactions: [], // This would need another API call if needed\r\n        totalExpenses,\r\n        dsaSales: dsaSalesData,\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching session breakdown:\", error);\r\n      // Return empty data structure instead of mock data\r\n      return {\r\n        session: {\r\n          id: 0,\r\n          user_id: 0,\r\n          branch_id: 0,\r\n          start_time: \"\",\r\n          end_time: null,\r\n          opening_cash_balance: \"0\",\r\n          opening_mpesa_balance: \"0\",\r\n          opening_mpesa_float: \"0\",\r\n          cash_paid_in: \"0\",\r\n          cash_paid_out: \"0\",\r\n          status: \"open\",\r\n          user_name: \"Unknown\",\r\n          branch_name: \"Unknown\",\r\n        },\r\n        reconciliation: undefined,\r\n        salesByPaymentMethod: {},\r\n        totalSales: 0,\r\n        totalCashSales: 0,\r\n        totalNonCashSales: 0,\r\n        mpesaServices: {\r\n          deposits: 0,\r\n          withdrawals: 0,\r\n        },\r\n        mobileMoneyServices: {\r\n          deposits: 0,\r\n          withdrawals: 0,\r\n          float_in: 0,\r\n          float_out: 0,\r\n        },\r\n        bankingTransactions: [],\r\n        totalExpenses: 0,\r\n        dsaSales: {\r\n          dsaSalesByUser: [],\r\n          totalDsaSales: 0,\r\n        },\r\n      };\r\n    }\r\n  },\r\n};\r\n\r\nexport default posSessionService;\r\n"], "names": [], "mappings": ";;;AAAA;;AAQA;;CAEC,GACD,eAAe,kBAAkB,SAAiB,EAAE,QAAgB;IAClE,IAAI;QACF,2CAA2C;QAC3C,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,UAAU;YAC7C,QAAQ;gBACN,WAAW;gBACX,WAAW;YACb;QACF;QAEA,qCAAqC;QACrC,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,KAAK,GAAG;YACrD,OAAO;gBACL,gBAAgB,EAAE;gBAClB,eAAe;YACjB;QACF;QAEA,2DAA2D;QAC3D,MAAM,iBAAiB,MAAM,QAAQ,GAAG,CACtC,SAAS,GAAG,CAAC,OAAO;YAClB,IAAI;YACJ,IAAI;gBACF,gCAAgC;gBAChC,YAAY,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,UAAU;oBACxC,QAAQ;wBACN,SAAS,KAAK,EAAE;wBAChB,gBAAgB;wBAChB,WAAW;oBACb;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,4EAA4E;gBAC5E,QAAQ,IAAI,CACV;gBAEF,YAAY,EAAE;YAChB;YAEA,sCAAsC;YACtC,IAAI,QAAQ;YACZ,MAAM,QAAQ,EAAE;YAEhB,IAAI,MAAM,OAAO,CAAC,YAAY;gBAC5B,KAAK,MAAM,QAAQ,UAAW;oBAC5B,MAAM,SAAS,WAAW,KAAK,YAAY,KAAK;oBAChD,SAAS;oBAET,MAAM,IAAI,CAAC;wBACT,IAAI,KAAK,EAAE;wBACX;wBACA,MAAM,KAAK,UAAU;wBACrB,eAAe,KAAK,cAAc,IAAI;oBACxC;gBACF;YACF;YAEA,OAAO;gBACL,IAAI,KAAK,EAAE;gBACX,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBACpC;gBACA;YACF;QACF;QAGF,iCAAiC;QACjC,MAAM,oBAAoB,eAAe,MAAM,CAAC,CAAC,OAAS,KAAK,KAAK,GAAG;QAEvE,4BAA4B;QAC5B,MAAM,gBAAgB,kBAAkB,MAAM,CAC5C,CAAC,KAAK,OAAS,MAAM,KAAK,KAAK,EAC/B;QAGF,OAAO;YACL,gBAAgB;YAChB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;YACL,gBAAgB,EAAE;YAClB,eAAe;QACjB;IACF;AACF;AAsJA;;;CAGC,GACD,MAAM,oBAAoB;IACxB;;GAEC,GACD,qBAAqB,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,mBAAmB,CAAC;YAE1C,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAe,iBAAiB;YAClE,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAa,CAAC,cAAc,EAAE,IAAI;QACtE,OAAO;IACT;IAEA;;GAEC,GACD,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAa,iBAAiB;QACnE,OAAO;IACT;IAEA;;GAEC,GACD,cAAc,OACZ,IACA;QAEA,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,cAAc,EAAE,GAAG,MAAM,CAAC,EAC3B;QAEF,OAAO;IACT;IAEA;;GAEC,GACD,qBAAqB,OAAO;QAC1B,IAAI;YACF,sDAAsD;YACtD,MAAM,UAAU,MAAM,kBAAkB,cAAc,CAAC;YAEvD,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,GAAG,UAAU,CAAC;YACnD;YAEA,qCAAqC;YACrC,MAAM,YAAY,QAAQ,UAAU;YACpC,MAAM,UAAU,QAAQ,QAAQ,IAAI,IAAI,OAAO,WAAW;YAC1D,MAAM,WAAW,QAAQ,SAAS;YAElC,oCAAoC;YACpC,MAAM,YAAY,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,UAAU;gBAC9C,QAAQ;oBACN,gBAAgB;oBAChB,eAAe;gBACjB;YACF;YAEA,4CAA4C;YAC5C,IAAI;YACJ,IAAI;gBACF,gCAAgC;gBAChC,YAAY,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,uBAAuB;oBACrD,QAAQ;wBACN,gBAAgB;wBAChB,WAAW;wBACX,OAAO;oBACT;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,sEAAsE;gBACtE,QAAQ,IAAI,CACV;gBAEF,YAAY,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,uBAAuB;oBACrD,QAAQ;wBACN,WAAW;wBACX,YAAY;wBACZ,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;YAEA,kCAAkC;YAClC,IAAI;YACJ,IAAI;gBACF,gCAAgC;gBAChC,eAAe,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,aAAa;oBAC9C,QAAQ;wBACN,gBAAgB;wBAChB,WAAW;wBACX,QAAQ;wBACR,OAAO;oBACT;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,sEAAsE;gBACtE,QAAQ,IAAI,CAAC;gBACb,eAAe,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,aAAa;oBAC9C,QAAQ;wBACN,WAAW;wBACX,YAAY;wBACZ,UAAU;wBACV,QAAQ;wBACR,OAAO;oBACT;gBACF;YACF;YAEA,oDAAoD;YACpD,MAAM,uBAA+C,CAAC;YACtD,IAAI,aAAa;YACjB,IAAI,iBAAiB;YACrB,IAAI,oBAAoB;YAExB,gCAAgC;YAChC,IAAI,MAAM,OAAO,CAAC,YAAY;gBAC5B,UAAU,OAAO,CAAC,CAAC;oBACjB,MAAM,gBAAgB,KAAK,cAAc,IAAI;oBAC7C,MAAM,SAAS,WAAW,KAAK,YAAY,KAAK;oBAEhD,oBAAoB,CAAC,cAAc,GACjC,CAAC,oBAAoB,CAAC,cAAc,IAAI,CAAC,IAAI;oBAC/C,cAAc;oBAEd,IAAI,cAAc,WAAW,OAAO,QAAQ;wBAC1C,kBAAkB;oBACpB,OAAO;wBACL,qBAAqB;oBACvB;gBACF;YACF;YAEA,6BAA6B;YAC7B,MAAM,gBAAgB;gBACpB,UAAU;gBACV,aAAa;YACf;YAEA,MAAM,sBAAsB;gBAC1B,UAAU;gBACV,aAAa;gBACb,UAAU;gBACV,WAAW;YACb;YAEA,IAAI,aAAa,UAAU,IAAI,IAAI,MAAM,OAAO,CAAC,UAAU,IAAI,GAAG;gBAChE,UAAU,IAAI,CAAC,OAAO,CAAC,CAAC;oBACtB,MAAM,SAAS,WAAW,YAAY,MAAM,KAAK;oBAEjD,IAAI,YAAY,gBAAgB,KAAK,WAAW;wBAC9C,cAAc,QAAQ,IAAI;oBAC5B,OAAO,IAAI,YAAY,gBAAgB,KAAK,cAAc;wBACxD,cAAc,WAAW,IAAI;oBAC/B,OAAO,IAAI,YAAY,gBAAgB,KAAK,YAAY;wBACtD,oBAAoB,QAAQ,IAAI;oBAClC,OAAO,IAAI,YAAY,gBAAgB,KAAK,aAAa;wBACvD,oBAAoB,SAAS,IAAI;oBACnC;oBAEA,wBAAwB;oBACxB,IAAI,YAAY,YAAY,KAAK,gBAAgB;wBAC/C,IAAI,YAAY,gBAAgB,KAAK,WAAW;4BAC9C,oBAAoB,QAAQ,IAAI;wBAClC,OAAO,IAAI,YAAY,gBAAgB,KAAK,cAAc;4BACxD,oBAAoB,WAAW,IAAI;wBACrC;oBACF;gBACF;YACF;YAEA,mBAAmB;YACnB,IAAI,gBAAgB;YACpB,IACE,gBACA,aAAa,QAAQ,IACrB,MAAM,OAAO,CAAC,aAAa,QAAQ,GACnC;gBACA,aAAa,QAAQ,CAAC,OAAO,CAAC,CAAC;oBAC7B,iBACE,WAAW,QAAQ,eAAe,IAAI,QAAQ,MAAM,KAAK;gBAC7D;YACF;YAEA,uBAAuB;YACvB,MAAM,eAAe,MAAM,kBAAkB,IAAI;YAEjD,uBAAuB;YACvB,MAAM,cAAc;gBAClB,IAAI,QAAQ,EAAE;gBACd,SAAS,QAAQ,OAAO;gBACxB,WAAW,QAAQ,SAAS;gBAC5B,YAAY,QAAQ,UAAU;gBAC9B,UAAU,QAAQ,QAAQ;gBAC1B,sBAAsB,QAAQ,oBAAoB;gBAClD,uBAAuB,QAAQ,qBAAqB,IAAI;gBACxD,qBAAqB,QAAQ,mBAAmB,IAAI;gBACpD,cAAc,QAAQ,YAAY;gBAClC,eAAe,QAAQ,aAAa;gBACpC,QAAQ,QAAQ,MAAM;gBACtB,WAAW,QAAQ,IAAI,EAAE,QAAQ;gBACjC,aAAa,QAAQ,MAAM,EAAE,QAAQ;YACvC;YAEA,2CAA2C;YAC3C,MAAM,qBAAqB,QAAQ,wBAAwB,GACvD;gBACE,IAAI,QAAQ,wBAAwB,CAAC,EAAE;gBACvC,sBACE,QAAQ,wBAAwB,CAAC,oBAAoB;gBACvD,uBACE,QAAQ,wBAAwB,CAAC,qBAAqB;gBACxD,qBACE,QAAQ,wBAAwB,CAAC,mBAAmB,IAAI;gBAC1D,eAAe,QAAQ,wBAAwB,CAAC,aAAa;gBAC7D,gBAAgB,QAAQ,wBAAwB,CAAC,cAAc;gBAC/D,aAAa,QAAQ,wBAAwB,CAAC,WAAW;gBACzD,eAAe,QAAQ,wBAAwB,CAAC,aAAa;gBAC7D,gBAAgB,QAAQ,wBAAwB,CAAC,cAAc;gBAC/D,OAAO,QAAQ,wBAAwB,CAAC,KAAK;gBAC7C,YAAY,QAAQ,wBAAwB,CAAC,UAAU;YACzD,IACA;YAEJ,OAAO;gBACL,SAAS;gBACT,gBAAgB;gBAChB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,qBAAqB,EAAE;gBACvB;gBACA,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,mDAAmD;YACnD,OAAO;gBACL,SAAS;oBACP,IAAI;oBACJ,SAAS;oBACT,WAAW;oBACX,YAAY;oBACZ,UAAU;oBACV,sBAAsB;oBACtB,uBAAuB;oBACvB,qBAAqB;oBACrB,cAAc;oBACd,eAAe;oBACf,QAAQ;oBACR,WAAW;oBACX,aAAa;gBACf;gBACA,gBAAgB;gBAChB,sBAAsB,CAAC;gBACvB,YAAY;gBACZ,gBAAgB;gBAChB,mBAAmB;gBACnB,eAAe;oBACb,UAAU;oBACV,aAAa;gBACf;gBACA,qBAAqB;oBACnB,UAAU;oBACV,aAAa;oBACb,UAAU;oBACV,WAAW;gBACb;gBACA,qBAAqB,EAAE;gBACvB,eAAe;gBACf,UAAU;oBACR,gBAAgB,EAAE;oBAClB,eAAe;gBACjB;YACF;QACF;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 7603, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/pos/hooks/use-pos-sessions.ts"], "sourcesContent": ["import {\r\n  ClosePosSessionRequest,\r\n  CreatePosSessionRequest,\r\n  PosSessionFilters,\r\n} from \"@/types\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport posSessionService from \"../api/pos-session-service\";\r\n\r\n/**\r\n * Hook to fetch all POS sessions with optional filters\r\n */\r\nexport const usePosSessions = (filters?: PosSessionFilters) => {\r\n  return useQuery({\r\n    queryKey: [\"pos-sessions\", filters],\r\n    queryFn: () => posSessionService.getSessions(filters),\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to fetch a single POS session by ID\r\n */\r\nexport const usePosSession = (id: number) => {\r\n  return useQuery({\r\n    queryKey: [\"pos-session\", id],\r\n    queryFn: () => posSessionService.getSessionById(id),\r\n    enabled: !!id,\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to create a new POS session\r\n */\r\nexport const useCreatePosSession = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: CreatePosSessionRequest) =>\r\n      posSessionService.createSession(data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"pos-sessions\"] });\r\n      toast.success(\"POS session created successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.message || \"Failed to create POS session. Please try again.\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to close a POS session\r\n */\r\nexport const useClosePosSession = (id: number) => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (data: ClosePosSessionRequest) =>\r\n      posSessionService.closeSession(id, data),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"pos-sessions\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"pos-session\", id] });\r\n      toast.success(\"POS session closed successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.message || \"Failed to close POS session. Please try again.\"\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;AAKA;AAAA;AAAA;AACA;AACA;;;;;AAKO,MAAM,iBAAiB,CAAC;;IAC7B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAgB;SAAQ;QACnC,OAAO;uCAAE,IAAM,6JAAA,CAAA,UAAiB,CAAC,WAAW,CAAC;;IAC/C;AACF;GALa;;QACJ,8KAAA,CAAA,WAAQ;;;AASV,MAAM,gBAAgB,CAAC;;IAC5B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAe;SAAG;QAC7B,OAAO;sCAAE,IAAM,6JAAA,CAAA,UAAiB,CAAC,cAAc,CAAC;;QAChD,SAAS,CAAC,CAAC;IACb;AACF;IANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,sBAAsB;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;+CAAE,CAAC,OACX,6JAAA,CAAA,UAAiB,CAAC,aAAa,CAAC;;QAClC,SAAS;+CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAe;gBAAC;gBAC3D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;+CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,OAAO,IAAI;YAErB;;IACF;AACF;IAhBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,MAAM,qBAAqB,CAAC;;IACjC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;8CAAE,CAAC,OACX,6JAAA,CAAA,UAAiB,CAAC,YAAY,CAAC,IAAI;;QACrC,SAAS;8CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAe;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAe;qBAAG;gBAAC;gBAC9D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;8CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,OAAO,IAAI;YAErB;;IACF;AACF;IAjBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 7728, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/regions/api/region-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse } from \"@/types/api\";\r\n\r\nexport interface Region {\r\n  id: number;\r\n  name: string;\r\n  code?: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\nexport const regionService = {\r\n  getRegions: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Region>> => {\r\n    try {\r\n      const response = await apiClient.get<any>(\"/regions\", { params });\r\n\r\n      // If response is an array, convert to paginated format\r\n      if (Array.isArray(response)) {\r\n        return {\r\n          data: response,\r\n          pagination: {\r\n            total: response.length,\r\n            page: 1,\r\n            limit: response.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        return {\r\n          data: response.data,\r\n          pagination: response.pagination || {\r\n            total: response.data.length,\r\n            page: 1,\r\n            limit: response.data.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Default fallback\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error in getRegions:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getRegionById: async (id: number): Promise<Region> => {\r\n    return apiClient.get(`/regions/${id}`);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAYO,MAAM,gBAAgB;IAC3B,YAAY,OACV;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,YAAY;gBAAE;YAAO;YAE/D,uDAAuD;YACvD,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,SAAS,MAAM;wBACtB,MAAM;wBACN,OAAO,SAAS,MAAM;wBACtB,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,OAAO;oBACL,MAAM,SAAS,IAAI;oBACnB,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,SAAS,IAAI,CAAC,MAAM;wBAC3B,MAAM;wBACN,OAAO,SAAS,IAAI,CAAC,MAAM;wBAC3B,YAAY;oBACd;gBACF;YACF;YAEA,mBAAmB;YACnB,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,eAAe,OAAO;QACpB,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;IACvC;AACF", "debugId": null}}, {"offset": {"line": 7800, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/regions/hooks/use-regions.ts"], "sourcesContent": ["import { useQuery } from \"@tanstack/react-query\";\r\nimport { regionService } from \"../api/region-service\";\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\n\r\nexport function useRegions(params?: Record<string, any>) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n\r\n  return useQuery({\r\n    queryKey: [\"regions\", params],\r\n    queryFn: () => regionService.getRegions(params),\r\n    // Only run query when authentication is ready\r\n    enabled: isInitialized && !!accessToken,\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If data exists but data.data is undefined, provide a default\r\n      if (!data.data) {\r\n        return {\r\n          ...data,\r\n          data: [],\r\n          pagination: data.pagination || {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useRegion(id: number) {\r\n  return useQuery({\r\n    queryKey: [\"regions\", id],\r\n    queryFn: () => regionService.getRegionById(id),\r\n    enabled: !!id,\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEO,SAAS,WAAW,MAA4B;;IACrD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAEnD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAO;QAC7B,OAAO;mCAAE,IAAM,yJAAA,CAAA,gBAAa,CAAC,UAAU,CAAC;;QACxC,8CAA8C;QAC9C,SAAS,iBAAiB,CAAC,CAAC;QAC5B,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,MAAM;mCAAE,CAAC;gBACP,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;oBACF;gBACF;gBAEA,+DAA+D;gBAC/D,IAAI,CAAC,KAAK,IAAI,EAAE;oBACd,OAAO;wBACL,GAAG,IAAI;wBACP,MAAM,EAAE;wBACR,YAAY,KAAK,UAAU,IAAI;4BAC7B,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;oBACF;gBACF;gBAEA,OAAO;YACT;;IACF;AACF;GA3CgB;;QACyB,wIAAA,CAAA,gBAAa;QAE7C,8KAAA,CAAA,WAAQ;;;AA0CV,SAAS,UAAU,EAAU;;IAClC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAW;SAAG;QACzB,OAAO;kCAAE,IAAM,yJAAA,CAAA,gBAAa,CAAC,aAAa,CAAC;;QAC3C,SAAS,CAAC,CAAC;IACb;AACF;IANgB;;QACP,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 7893, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/api/product-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse } from \"@/types/api\";\r\nimport {\r\n  Product,\r\n  CreateProductRequest,\r\n  UpdateProductRequest,\r\n  UpdateProductStatusRequest,\r\n} from \"@/types/product\";\r\nimport { toast } from \"sonner\";\r\n\r\nexport const productService = {\r\n  /**\r\n   * Get products without stock items at headquarters\r\n   * @param params Query parameters\r\n   * @returns Paginated list of products without stock items at headquarters\r\n   */\r\n  getProductsWithoutHQStock: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Product>> => {\r\n    try {\r\n      // Use the regular products endpoint with a filter for products without HQ stock\r\n      // This is a workaround until the backend endpoint is fixed\r\n      const allProducts = await productService.getProducts({\r\n        ...params,\r\n        limit: 1000, // Get a large number of products to filter client-side\r\n      });\r\n\r\n      // Get all stock items for headquarters\r\n      let stockItems: any[] = [];\r\n      try {\r\n        const stockItemsResponse = await apiClient.get<any>(\"/stock-items\", {\r\n          params: {\r\n            branch_id: 1, // Headquarters\r\n            limit: 1000,\r\n          }\r\n        });\r\n\r\n        if (stockItemsResponse && stockItemsResponse.data) {\r\n          stockItems = stockItemsResponse.data;\r\n        }\r\n      } catch (stockError) {\r\n        console.error(\"Error fetching stock items:\", stockError);\r\n        // Continue with empty stock items\r\n      }\r\n\r\n      // Create a set of product IDs that already have stock items at headquarters\r\n      const productsWithHQStock = new Set(\r\n        stockItems.map((item: any) => item.product_id)\r\n      );\r\n\r\n      // Filter out products that already have stock items at headquarters\r\n      const filteredProducts = allProducts.data.filter(\r\n        (product) => !productsWithHQStock.has(product.id)\r\n      );\r\n\r\n      // Return the filtered products with pagination\r\n      return {\r\n        data: filteredProducts,\r\n        pagination: {\r\n          total: filteredProducts.length,\r\n          page: parseInt(params?.page || \"1\", 10),\r\n          limit: parseInt(params?.limit || \"10\", 10),\r\n          totalPages: Math.ceil(filteredProducts.length / parseInt(params?.limit || \"10\", 10)),\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching products without HQ stock:\", error);\r\n      // Show a toast error message\r\n      try {\r\n        // @ts-ignore - toast is imported elsewhere\r\n        toast.error(\"Error loading products\", {\r\n          description: \"Could not load products without headquarters stock. Please try again later.\",\r\n        });\r\n      } catch (toastError) {\r\n        // Ignore toast errors\r\n      }\r\n\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getProducts: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Product>> => {\r\n    try {\r\n      // Extract branch_procurable parameter for client-side filtering\r\n      const branchProcurable = params?.branch_procurable;\r\n\r\n      // Create a new params object without branch_procurable\r\n      const apiParams = { ...params };\r\n      if (apiParams.branch_procurable !== undefined) {\r\n        delete apiParams.branch_procurable;\r\n      }\r\n\r\n      console.log(\"Fetching products with params:\", apiParams);\r\n      const response = await apiClient.get<any>(\"/products\", { params: apiParams });\r\n      console.log(\"API response for products:\", response);\r\n\r\n      // Map API response to our Product type\r\n      const mapApiProductToProduct = (apiProduct: any): Product => {\r\n        // Convert string prices to numbers for UI\r\n        const sellingPrice = apiProduct.suggested_selling_price\r\n          ? parseFloat(apiProduct.suggested_selling_price)\r\n          : 0;\r\n\r\n        // Calculate stock quantity from stock array if available\r\n        const stockQuantity =\r\n          apiProduct.stock?.reduce(\r\n            (total: number, item: any) => total + item.quantity,\r\n            0\r\n          ) || 0;\r\n\r\n        // Ensure category hierarchy is properly handled\r\n        let categoryHierarchy = apiProduct.categoryHierarchy || [];\r\n\r\n        // If categoryHierarchy is not available but ProductCategory is, create a simple hierarchy\r\n        if (\r\n          (!categoryHierarchy || categoryHierarchy.length === 0) &&\r\n          apiProduct.ProductCategory\r\n        ) {\r\n          const category = apiProduct.ProductCategory;\r\n\r\n          // If the category has a parent, include it in the hierarchy\r\n          if (category.Parent) {\r\n            categoryHierarchy = [\r\n              {\r\n                id: category.Parent.id,\r\n                name: category.Parent.name,\r\n                description: category.Parent.description,\r\n                level: category.Parent.level || 0,\r\n              },\r\n              {\r\n                id: category.id,\r\n                name: category.name,\r\n                description: category.description,\r\n                level: category.level || 1,\r\n              },\r\n            ];\r\n          } else {\r\n            // If no parent, just include the category itself\r\n            categoryHierarchy = [\r\n              {\r\n                id: category.id,\r\n                name: category.name,\r\n                description: category.description,\r\n                level: category.level || 0,\r\n              },\r\n            ];\r\n          }\r\n        }\r\n\r\n        return {\r\n          ...apiProduct,\r\n          // Map API fields to UI fields\r\n          price: sellingPrice, // Map suggested_selling_price to price for UI compatibility\r\n          stock_quantity: stockQuantity,\r\n          status: apiProduct.is_active ? \"active\" : \"inactive\", // Derive status from is_active\r\n          has_variants: apiProduct.has_serial, // Map has_serial to has_variants for UI compatibility\r\n          categoryHierarchy: categoryHierarchy, // Ensure category hierarchy is available\r\n        };\r\n      };\r\n\r\n      let mappedProducts: Product[] = [];\r\n\r\n      // If response is an array, convert to paginated format with mapped products\r\n      if (Array.isArray(response)) {\r\n        mappedProducts = response.map(mapApiProductToProduct);\r\n      }\r\n      // Check if response is already in paginated format\r\n      else if (response && response.data && Array.isArray(response.data)) {\r\n        mappedProducts = response.data.map(mapApiProductToProduct);\r\n      }\r\n\r\n      // Apply client-side filtering for branch_procurable if needed\r\n      if (branchProcurable === true) {\r\n        console.log(\"Filtering products for branch_procurable=true\");\r\n        mappedProducts = mappedProducts.filter(product => product.branch_procurable === true);\r\n      }\r\n\r\n      console.log(\"Filtered products:\", mappedProducts);\r\n\r\n      // Return the filtered products with pagination\r\n      return {\r\n        data: mappedProducts,\r\n        pagination: response.pagination || {\r\n          total: mappedProducts.length,\r\n          page: 1,\r\n          limit: mappedProducts.length,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching products:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getProductById: async (id: number): Promise<Product | null> => {\r\n    try {\r\n      const response = await apiClient.get(`/products/${id}`);\r\n      return response;\r\n    } catch (error: any) {\r\n      console.error(`Error fetching product with ID ${id}:`, error);\r\n\r\n      // If the error is a 404, return null instead of throwing\r\n      if (error.response && error.response.status === 404) {\r\n        console.warn(`Product with ID ${id} not found`);\r\n        return null;\r\n      }\r\n\r\n      // For other errors, rethrow\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  createProduct: async (product: CreateProductRequest): Promise<Product> => {\r\n    // Create a properly structured API request with only the required fields\r\n    const apiRequest: Record<string, any> = {\r\n      // Required fields\r\n      tenant_id: product.tenant_id,\r\n      name: product.name,\r\n      sku: product.sku,\r\n      has_serial: product.has_serial,\r\n      suggested_buying_price: product.suggested_buying_price,\r\n      suggested_selling_price: product.suggested_selling_price,\r\n    };\r\n\r\n    // Optional fields\r\n    if (product.category_id !== undefined)\r\n      apiRequest.category_id = product.category_id;\r\n    if (product.brand_id !== undefined) apiRequest.brand_id = product.brand_id;\r\n    if (product.brand_type_id !== undefined)\r\n      apiRequest.brand_type_id = product.brand_type_id;\r\n    if (product.description !== undefined)\r\n      apiRequest.description = product.description;\r\n    if (product.barcode !== undefined)\r\n      apiRequest.barcode = product.barcode;\r\n    if (product.default_wholesale_price !== undefined)\r\n      apiRequest.default_wholesale_price = product.default_wholesale_price;\r\n    if (product.warranty_period !== undefined)\r\n      apiRequest.warranty_period = product.warranty_period;\r\n    if (product.can_assign_to_dsa !== undefined)\r\n      apiRequest.can_assign_to_dsa = product.can_assign_to_dsa;\r\n\r\n    console.log(\"Creating product with API request:\", apiRequest);\r\n    return apiClient.post(\"/products\", apiRequest);\r\n  },\r\n\r\n  updateProduct: async (\r\n    id: number,\r\n    product: UpdateProductRequest\r\n  ): Promise<Product> => {\r\n    // Create a properly structured API request\r\n    const apiRequest: Record<string, any> = {};\r\n\r\n    // Basic information\r\n    if (product.name !== undefined) apiRequest.name = product.name;\r\n    if (product.sku !== undefined) apiRequest.sku = product.sku;\r\n    if (product.description !== undefined) apiRequest.description = product.description;\r\n    if (product.barcode !== undefined) apiRequest.barcode = product.barcode;\r\n    if (product.status !== undefined) apiRequest.is_active = product.status === 'active';\r\n    if (product.is_active !== undefined) apiRequest.is_active = product.is_active;\r\n\r\n    // Categorization\r\n    if (product.category_id !== undefined) apiRequest.category_id = product.category_id;\r\n    if (product.brand_id !== undefined) apiRequest.brand_id = product.brand_id;\r\n    if (product.brand_type_id !== undefined) apiRequest.brand_type_id = product.brand_type_id;\r\n\r\n    // Inventory tracking\r\n    if (product.has_serial !== undefined) {\r\n      apiRequest.has_serial = product.has_serial;\r\n    } else if (product.has_variants !== undefined) {\r\n      apiRequest.has_serial = product.has_variants;\r\n    }\r\n\r\n    if (product.has_variants !== undefined) apiRequest.has_variants = product.has_variants;\r\n    if (product.is_parent !== undefined) apiRequest.is_parent = product.is_parent;\r\n    if (product.parent_id !== undefined) apiRequest.parent_id = product.parent_id;\r\n\r\n    // Pricing fields\r\n    if (product.suggested_buying_price !== undefined) {\r\n      apiRequest.suggested_buying_price = product.suggested_buying_price;\r\n    } else if (product.buying_price !== undefined) {\r\n      apiRequest.suggested_buying_price = product.buying_price;\r\n    }\r\n\r\n    if (product.suggested_selling_price !== undefined) {\r\n      apiRequest.suggested_selling_price = product.suggested_selling_price;\r\n    } else if (product.selling_price !== undefined) {\r\n      apiRequest.suggested_selling_price = product.selling_price;\r\n    } else if (product.price !== undefined) {\r\n      apiRequest.suggested_selling_price = product.price;\r\n    }\r\n\r\n    if (product.default_wholesale_price !== undefined) {\r\n      apiRequest.default_wholesale_price = product.default_wholesale_price;\r\n    }\r\n\r\n    // VAT-related fields\r\n    if (product.vat_rate_id !== undefined) apiRequest.vat_rate_id = product.vat_rate_id;\r\n    if (product.is_vat_inclusive !== undefined) apiRequest.is_vat_inclusive = product.is_vat_inclusive;\r\n    if (product.is_vat_exempt !== undefined) apiRequest.is_vat_exempt = product.is_vat_exempt;\r\n    if (product.vat_exemption_reason !== undefined) apiRequest.vat_exemption_reason = product.vat_exemption_reason;\r\n\r\n    // Inventory management\r\n    if (product.reorder_level !== undefined) apiRequest.reorder_level = product.reorder_level;\r\n    if (product.reorder_quantity !== undefined) apiRequest.reorder_quantity = product.reorder_quantity;\r\n\r\n    // Other fields\r\n    if (product.warranty_period !== undefined) apiRequest.warranty_period = product.warranty_period;\r\n\r\n    // DSA-related fields\r\n    if (product.can_assign_to_dsa !== undefined) apiRequest.can_assign_to_dsa = product.can_assign_to_dsa;\r\n\r\n    console.log(\"Updating product with API request:\", apiRequest);\r\n    return apiClient.put(`/products/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteProduct: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/products/${id}`);\r\n  },\r\n\r\n  updateProductStatus: async (\r\n    id: number,\r\n    status: UpdateProductStatusRequest\r\n  ): Promise<Product> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      status: status.status, // 'active' or 'inactive'\r\n    };\r\n\r\n    return apiClient.put(`/products/${id}/status`, apiRequest);\r\n  },\r\n\r\n  uploadProductImage: async (\r\n    id: number,\r\n    file: File\r\n  ): Promise<{ image_url: string }> => {\r\n    const formData = new FormData();\r\n    formData.append(\"image\", file);\r\n\r\n    return apiClient.post(`/products/${id}/image`, formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n    });\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAQA;;;AAEO,MAAM,iBAAiB;IAC5B;;;;GAIC,GACD,2BAA2B,OACzB;QAEA,IAAI;YACF,gFAAgF;YAChF,2DAA2D;YAC3D,MAAM,cAAc,MAAM,eAAe,WAAW,CAAC;gBACnD,GAAG,MAAM;gBACT,OAAO;YACT;YAEA,uCAAuC;YACvC,IAAI,aAAoB,EAAE;YAC1B,IAAI;gBACF,MAAM,qBAAqB,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,gBAAgB;oBAClE,QAAQ;wBACN,WAAW;wBACX,OAAO;oBACT;gBACF;gBAEA,IAAI,sBAAsB,mBAAmB,IAAI,EAAE;oBACjD,aAAa,mBAAmB,IAAI;gBACtC;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,kCAAkC;YACpC;YAEA,4EAA4E;YAC5E,MAAM,sBAAsB,IAAI,IAC9B,WAAW,GAAG,CAAC,CAAC,OAAc,KAAK,UAAU;YAG/C,oEAAoE;YACpE,MAAM,mBAAmB,YAAY,IAAI,CAAC,MAAM,CAC9C,CAAC,UAAY,CAAC,oBAAoB,GAAG,CAAC,QAAQ,EAAE;YAGlD,+CAA+C;YAC/C,OAAO;gBACL,MAAM;gBACN,YAAY;oBACV,OAAO,iBAAiB,MAAM;oBAC9B,MAAM,SAAS,QAAQ,QAAQ,KAAK;oBACpC,OAAO,SAAS,QAAQ,SAAS,MAAM;oBACvC,YAAY,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG,SAAS,QAAQ,SAAS,MAAM;gBAClF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,6BAA6B;YAC7B,IAAI;gBACF,2CAA2C;gBAC3C,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0BAA0B;oBACpC,aAAa;gBACf;YACF,EAAE,OAAO,YAAY;YACnB,sBAAsB;YACxB;YAEA,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,aAAa,OACX;QAEA,IAAI;YACF,gEAAgE;YAChE,MAAM,mBAAmB,QAAQ;YAEjC,uDAAuD;YACvD,MAAM,YAAY;gBAAE,GAAG,MAAM;YAAC;YAC9B,IAAI,UAAU,iBAAiB,KAAK,WAAW;gBAC7C,OAAO,UAAU,iBAAiB;YACpC;YAEA,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,aAAa;gBAAE,QAAQ;YAAU;YAC3E,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,uCAAuC;YACvC,MAAM,yBAAyB,CAAC;gBAC9B,0CAA0C;gBAC1C,MAAM,eAAe,WAAW,uBAAuB,GACnD,WAAW,WAAW,uBAAuB,IAC7C;gBAEJ,yDAAyD;gBACzD,MAAM,gBACJ,WAAW,KAAK,EAAE,OAChB,CAAC,OAAe,OAAc,QAAQ,KAAK,QAAQ,EACnD,MACG;gBAEP,gDAAgD;gBAChD,IAAI,oBAAoB,WAAW,iBAAiB,IAAI,EAAE;gBAE1D,0FAA0F;gBAC1F,IACE,CAAC,CAAC,qBAAqB,kBAAkB,MAAM,KAAK,CAAC,KACrD,WAAW,eAAe,EAC1B;oBACA,MAAM,WAAW,WAAW,eAAe;oBAE3C,4DAA4D;oBAC5D,IAAI,SAAS,MAAM,EAAE;wBACnB,oBAAoB;4BAClB;gCACE,IAAI,SAAS,MAAM,CAAC,EAAE;gCACtB,MAAM,SAAS,MAAM,CAAC,IAAI;gCAC1B,aAAa,SAAS,MAAM,CAAC,WAAW;gCACxC,OAAO,SAAS,MAAM,CAAC,KAAK,IAAI;4BAClC;4BACA;gCACE,IAAI,SAAS,EAAE;gCACf,MAAM,SAAS,IAAI;gCACnB,aAAa,SAAS,WAAW;gCACjC,OAAO,SAAS,KAAK,IAAI;4BAC3B;yBACD;oBACH,OAAO;wBACL,iDAAiD;wBACjD,oBAAoB;4BAClB;gCACE,IAAI,SAAS,EAAE;gCACf,MAAM,SAAS,IAAI;gCACnB,aAAa,SAAS,WAAW;gCACjC,OAAO,SAAS,KAAK,IAAI;4BAC3B;yBACD;oBACH;gBACF;gBAEA,OAAO;oBACL,GAAG,UAAU;oBACb,8BAA8B;oBAC9B,OAAO;oBACP,gBAAgB;oBAChB,QAAQ,WAAW,SAAS,GAAG,WAAW;oBAC1C,cAAc,WAAW,UAAU;oBACnC,mBAAmB;gBACrB;YACF;YAEA,IAAI,iBAA4B,EAAE;YAElC,4EAA4E;YAC5E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,iBAAiB,SAAS,GAAG,CAAC;YAChC,OAEK,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAClE,iBAAiB,SAAS,IAAI,CAAC,GAAG,CAAC;YACrC;YAEA,8DAA8D;YAC9D,IAAI,qBAAqB,MAAM;gBAC7B,QAAQ,GAAG,CAAC;gBACZ,iBAAiB,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,iBAAiB,KAAK;YAClF;YAEA,QAAQ,GAAG,CAAC,sBAAsB;YAElC,+CAA+C;YAC/C,OAAO;gBACL,MAAM;gBACN,YAAY,SAAS,UAAU,IAAI;oBACjC,OAAO,eAAe,MAAM;oBAC5B,MAAM;oBACN,OAAO,eAAe,MAAM;oBAC5B,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;YACtD,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YAEvD,yDAAyD;YACzD,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACnD,QAAQ,IAAI,CAAC,CAAC,gBAAgB,EAAE,GAAG,UAAU,CAAC;gBAC9C,OAAO;YACT;YAEA,4BAA4B;YAC5B,MAAM;QACR;IACF;IAEA,eAAe,OAAO;QACpB,yEAAyE;QACzE,MAAM,aAAkC;YACtC,kBAAkB;YAClB,WAAW,QAAQ,SAAS;YAC5B,MAAM,QAAQ,IAAI;YAClB,KAAK,QAAQ,GAAG;YAChB,YAAY,QAAQ,UAAU;YAC9B,wBAAwB,QAAQ,sBAAsB;YACtD,yBAAyB,QAAQ,uBAAuB;QAC1D;QAEA,kBAAkB;QAClB,IAAI,QAAQ,WAAW,KAAK,WAC1B,WAAW,WAAW,GAAG,QAAQ,WAAW;QAC9C,IAAI,QAAQ,QAAQ,KAAK,WAAW,WAAW,QAAQ,GAAG,QAAQ,QAAQ;QAC1E,IAAI,QAAQ,aAAa,KAAK,WAC5B,WAAW,aAAa,GAAG,QAAQ,aAAa;QAClD,IAAI,QAAQ,WAAW,KAAK,WAC1B,WAAW,WAAW,GAAG,QAAQ,WAAW;QAC9C,IAAI,QAAQ,OAAO,KAAK,WACtB,WAAW,OAAO,GAAG,QAAQ,OAAO;QACtC,IAAI,QAAQ,uBAAuB,KAAK,WACtC,WAAW,uBAAuB,GAAG,QAAQ,uBAAuB;QACtE,IAAI,QAAQ,eAAe,KAAK,WAC9B,WAAW,eAAe,GAAG,QAAQ,eAAe;QACtD,IAAI,QAAQ,iBAAiB,KAAK,WAChC,WAAW,iBAAiB,GAAG,QAAQ,iBAAiB;QAE1D,QAAQ,GAAG,CAAC,sCAAsC;QAClD,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,aAAa;IACrC;IAEA,eAAe,OACb,IACA;QAEA,2CAA2C;QAC3C,MAAM,aAAkC,CAAC;QAEzC,oBAAoB;QACpB,IAAI,QAAQ,IAAI,KAAK,WAAW,WAAW,IAAI,GAAG,QAAQ,IAAI;QAC9D,IAAI,QAAQ,GAAG,KAAK,WAAW,WAAW,GAAG,GAAG,QAAQ,GAAG;QAC3D,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;QACnF,IAAI,QAAQ,OAAO,KAAK,WAAW,WAAW,OAAO,GAAG,QAAQ,OAAO;QACvE,IAAI,QAAQ,MAAM,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,MAAM,KAAK;QAC5E,IAAI,QAAQ,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,SAAS;QAE7E,iBAAiB;QACjB,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;QACnF,IAAI,QAAQ,QAAQ,KAAK,WAAW,WAAW,QAAQ,GAAG,QAAQ,QAAQ;QAC1E,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,aAAa,GAAG,QAAQ,aAAa;QAEzF,qBAAqB;QACrB,IAAI,QAAQ,UAAU,KAAK,WAAW;YACpC,WAAW,UAAU,GAAG,QAAQ,UAAU;QAC5C,OAAO,IAAI,QAAQ,YAAY,KAAK,WAAW;YAC7C,WAAW,UAAU,GAAG,QAAQ,YAAY;QAC9C;QAEA,IAAI,QAAQ,YAAY,KAAK,WAAW,WAAW,YAAY,GAAG,QAAQ,YAAY;QACtF,IAAI,QAAQ,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,SAAS;QAC7E,IAAI,QAAQ,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,SAAS;QAE7E,iBAAiB;QACjB,IAAI,QAAQ,sBAAsB,KAAK,WAAW;YAChD,WAAW,sBAAsB,GAAG,QAAQ,sBAAsB;QACpE,OAAO,IAAI,QAAQ,YAAY,KAAK,WAAW;YAC7C,WAAW,sBAAsB,GAAG,QAAQ,YAAY;QAC1D;QAEA,IAAI,QAAQ,uBAAuB,KAAK,WAAW;YACjD,WAAW,uBAAuB,GAAG,QAAQ,uBAAuB;QACtE,OAAO,IAAI,QAAQ,aAAa,KAAK,WAAW;YAC9C,WAAW,uBAAuB,GAAG,QAAQ,aAAa;QAC5D,OAAO,IAAI,QAAQ,KAAK,KAAK,WAAW;YACtC,WAAW,uBAAuB,GAAG,QAAQ,KAAK;QACpD;QAEA,IAAI,QAAQ,uBAAuB,KAAK,WAAW;YACjD,WAAW,uBAAuB,GAAG,QAAQ,uBAAuB;QACtE;QAEA,qBAAqB;QACrB,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;QACnF,IAAI,QAAQ,gBAAgB,KAAK,WAAW,WAAW,gBAAgB,GAAG,QAAQ,gBAAgB;QAClG,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,aAAa,GAAG,QAAQ,aAAa;QACzF,IAAI,QAAQ,oBAAoB,KAAK,WAAW,WAAW,oBAAoB,GAAG,QAAQ,oBAAoB;QAE9G,uBAAuB;QACvB,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,aAAa,GAAG,QAAQ,aAAa;QACzF,IAAI,QAAQ,gBAAgB,KAAK,WAAW,WAAW,gBAAgB,GAAG,QAAQ,gBAAgB;QAElG,eAAe;QACf,IAAI,QAAQ,eAAe,KAAK,WAAW,WAAW,eAAe,GAAG,QAAQ,eAAe;QAE/F,qBAAqB;QACrB,IAAI,QAAQ,iBAAiB,KAAK,WAAW,WAAW,iBAAiB,GAAG,QAAQ,iBAAiB;QAErG,QAAQ,GAAG,CAAC,sCAAsC;QAClD,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;IAC1C;IAEA,eAAe,OAAO;QACpB,OAAO,8HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IAC3C;IAEA,qBAAqB,OACnB,IACA;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,QAAQ,OAAO,MAAM;QACvB;QAEA,OAAO,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE;IACjD;IAEA,oBAAoB,OAClB,IACA;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,OAAO,8HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,UAAU;YACvD,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 8189, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/hooks/use-products.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  CreateProductRequest,\r\n  UpdateProductRequest,\r\n  UpdateProductStatusRequest,\r\n} from \"@/types/product\";\r\nimport { useMutation, useQuery, useQueryClient } from \"@tanstack/react-query\";\r\nimport { toast } from \"sonner\";\r\nimport { productService } from \"../api/product-service\";\r\n\r\nexport function useProducts(params?: Record<string, any>) {\r\n  return useQuery({\r\n    queryKey: [\"products\", params],\r\n    queryFn: () => productService.getProducts(params),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\n/**\r\n * Hook to fetch products without stock items at headquarters\r\n */\r\nexport function useProductsWithoutHQStock(params?: Record<string, any>) {\r\n  return useQuery({\r\n    queryKey: [\"products-without-hq-stock\", params],\r\n    queryFn: () => productService.getProductsWithoutHQStock(params),\r\n    // In v5, this is the equivalent of keepPreviousData\r\n    placeholderData: \"keep\" as any,\r\n    retry: 1,\r\n    refetchOnWindowFocus: false,\r\n    // Provide a default value for data to prevent undefined errors\r\n    select: (data) => {\r\n      if (!data) {\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            total: 0,\r\n            page: 1,\r\n            limit: 0,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n      return data;\r\n    },\r\n  });\r\n}\r\n\r\nexport function useProduct(id: number, options?: any) {\r\n  return useQuery({\r\n    queryKey: [\"products\", id],\r\n    queryFn: async () => {\r\n      try {\r\n        return await productService.getProductById(id);\r\n      } catch (error: any) {\r\n        console.error(`Error fetching product with ID ${id}:`, error);\r\n        // Return null instead of throwing an error\r\n        return null;\r\n      }\r\n    },\r\n    enabled: !!id,\r\n    ...options,\r\n  });\r\n}\r\n\r\nexport function useCreateProduct() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (product: CreateProductRequest) =>\r\n      productService.createProduct(product),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"products\"] });\r\n      toast.success(\"Product created\", {\r\n        description: \"The product has been created successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error creating product\", {\r\n        description:\r\n          error.message || \"An error occurred while creating the product.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateProduct(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (product: UpdateProductRequest) =>\r\n      productService.updateProduct(id, product),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"products\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"products\"] });\r\n      toast.success(\"Product updated\", {\r\n        description: \"The product has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating product\", {\r\n        description:\r\n          error.message || \"An error occurred while updating the product.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteProduct() {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (id: number) => productService.deleteProduct(id),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"products\"] });\r\n      toast.success(\"Product deleted\", {\r\n        description: \"The product has been deleted successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error deleting product\", {\r\n        description:\r\n          error.message || \"An error occurred while deleting the product.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateProductStatus(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (status: UpdateProductStatusRequest) =>\r\n      productService.updateProductStatus(id, status),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"products\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"products\"] });\r\n      toast.success(\"Product status updated\", {\r\n        description: \"The product status has been updated successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error updating product status\", {\r\n        description:\r\n          error.message ||\r\n          \"An error occurred while updating the product status.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUploadProductImage(id: number) {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (file: File) => productService.uploadProductImage(id, file),\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: [\"products\", id] });\r\n      queryClient.invalidateQueries({ queryKey: [\"products\"] });\r\n      toast.success(\"Image uploaded\", {\r\n        description: \"The product image has been uploaded successfully.\",\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\"Error uploading image\", {\r\n        description:\r\n          error.message || \"An error occurred while uploading the image.\",\r\n      });\r\n    },\r\n  });\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAOA;AAAA;AAAA;AACA;AACA;;AATA;;;;AAWO,SAAS,YAAY,MAA4B;;IACtD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAO;QAC9B,OAAO;oCAAE,IAAM,2JAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;;QAC1C,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,MAAM;oCAAE,CAAC;gBACP,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;oBACF;gBACF;gBACA,OAAO;YACT;;IACF;AACF;GAxBgB;;QACP,8KAAA,CAAA,WAAQ;;;AA4BV,SAAS,0BAA0B,MAA4B;;IACpE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAA6B;SAAO;QAC/C,OAAO;kDAAE,IAAM,2JAAA,CAAA,iBAAc,CAAC,yBAAyB,CAAC;;QACxD,oDAAoD;QACpD,iBAAiB;QACjB,OAAO;QACP,sBAAsB;QACtB,+DAA+D;QAC/D,MAAM;kDAAE,CAAC;gBACP,IAAI,CAAC,MAAM;oBACT,OAAO;wBACL,MAAM,EAAE;wBACR,YAAY;4BACV,OAAO;4BACP,MAAM;4BACN,OAAO;4BACP,YAAY;wBACd;oBACF;gBACF;gBACA,OAAO;YACT;;IACF;AACF;IAxBgB;;QACP,8KAAA,CAAA,WAAQ;;;AAyBV,SAAS,WAAW,EAAU,EAAE,OAAa;;IAClD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAY;SAAG;QAC1B,OAAO;mCAAE;gBACP,IAAI;oBACF,OAAO,MAAM,2JAAA,CAAA,iBAAc,CAAC,cAAc,CAAC;gBAC7C,EAAE,OAAO,OAAY;oBACnB,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;oBACvD,2CAA2C;oBAC3C,OAAO;gBACT;YACF;;QACA,SAAS,CAAC,CAAC;QACX,GAAG,OAAO;IACZ;AACF;IAfgB;;QACP,8KAAA,CAAA,WAAQ;;;AAgBV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,CAAC,UACX,2JAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;;QAC/B,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;oBAC/B,aAAa;gBACf;YACF;;QACA,OAAO;4CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0BAA0B;oBACpC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IAnBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAkBb,SAAS,iBAAiB,EAAU;;IACzC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,CAAC,UACX,2JAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,IAAI;;QACnC,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;oBAC/B,aAAa;gBACf;YACF;;QACA,OAAO;4CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0BAA0B;oBACpC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IApBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmBb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,CAAC,KAAe,2JAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;;QACzD,SAAS;4CAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,mBAAmB;oBAC/B,aAAa;gBACf;YACF;;QACA,OAAO;4CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0BAA0B;oBACpC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IAlBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAiBb,SAAS,uBAAuB,EAAU;;IAC/C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;kDAAE,CAAC,SACX,2JAAA,CAAA,iBAAc,CAAC,mBAAmB,CAAC,IAAI;;QACzC,SAAS;kDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,0BAA0B;oBACtC,aAAa;gBACf;YACF;;QACA,OAAO;kDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iCAAiC;oBAC3C,aACE,MAAM,OAAO,IACb;gBACJ;YACF;;IACF;AACF;IArBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAoBb,SAAS,sBAAsB,EAAU;;IAC9C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,CAAC,OAAe,2JAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,IAAI;;QAClE,SAAS;iDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAY;qBAAG;gBAAC;gBAC3D,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa;gBACf;YACF;;QACA,OAAO;iDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,yBAAyB;oBACnC,aACE,MAAM,OAAO,IAAI;gBACrB;YACF;;IACF;AACF;IAnBgB;;QACM,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 8509, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/product-selector.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { Check, ChevronsUpDown, Loader2 } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList,\r\n} from \"@/components/ui/command\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { useProducts } from \"@/features/products/hooks/use-products\";\r\n\r\ninterface ProductSelectorProps {\r\n  value?: number;\r\n  onValueChange: (value: number | undefined) => void;\r\n  placeholder?: string;\r\n  disabled?: boolean;\r\n  includeAllOption?: boolean;\r\n}\r\n\r\nexport function ProductSelector({\r\n  value,\r\n  onValueChange,\r\n  placeholder = \"Select product...\",\r\n  disabled = false,\r\n  includeAllOption = true,\r\n}: ProductSelectorProps) {\r\n  const [open, setOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n\r\n  const { data: productsResponse, isLoading } = useProducts({\r\n    search: searchTerm,\r\n    page: 1,\r\n    limit: 50,\r\n  });\r\n\r\n  const products = productsResponse?.data || [];\r\n  const selectedProduct = products.find(p => p.id === value);\r\n\r\n  const handleSelect = (productId: number | undefined) => {\r\n    onValueChange(productId);\r\n    setOpen(false);\r\n  };\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          role=\"combobox\"\r\n          aria-expanded={open}\r\n          className=\"w-full justify-between\"\r\n          disabled={disabled}\r\n        >\r\n          {selectedProduct ? (\r\n            <span className=\"truncate\">\r\n              {selectedProduct.name}\r\n            </span>\r\n          ) : (\r\n            <span className=\"text-muted-foreground\">{placeholder}</span>\r\n          )}\r\n          <ChevronsUpDown className=\"ml-2 h-4 w-4 shrink-0 opacity-50\" />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className=\"w-[300px] p-0\">\r\n        <Command>\r\n          <CommandInput\r\n            placeholder=\"Search products...\"\r\n            value={searchTerm}\r\n            onValueChange={setSearchTerm}\r\n          />\r\n          <CommandList>\r\n            {isLoading ? (\r\n              <div className=\"flex items-center justify-center p-4\">\r\n                <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\r\n                <span>Loading products...</span>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                <CommandEmpty>No products found.</CommandEmpty>\r\n                <CommandGroup>\r\n                  {includeAllOption && (\r\n                    <CommandItem\r\n                      value=\"all-products\"\r\n                      onSelect={() => handleSelect(undefined)}\r\n                    >\r\n                      <Check\r\n                        className={cn(\r\n                          \"mr-2 h-4 w-4\",\r\n                          !value ? \"opacity-100\" : \"opacity-0\"\r\n                        )}\r\n                      />\r\n                      All Products\r\n                    </CommandItem>\r\n                  )}\r\n                  {products.map((product) => (\r\n                    <CommandItem\r\n                      key={product.id}\r\n                      value={product.id.toString()}\r\n                      onSelect={() => handleSelect(product.id)}\r\n                    >\r\n                      <Check\r\n                        className={cn(\r\n                          \"mr-2 h-4 w-4\",\r\n                          value === product.id ? \"opacity-100\" : \"opacity-0\"\r\n                        )}\r\n                      />\r\n                      <div className=\"flex flex-col\">\r\n                        <span className=\"truncate\">{product.name}</span>\r\n                        <span className=\"text-xs text-muted-foreground truncate\">\r\n                          SKU: {product.sku}\r\n                        </span>\r\n                      </div>\r\n                    </CommandItem>\r\n                  ))}\r\n                </CommandGroup>\r\n              </>\r\n            )}\r\n          </CommandList>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAQA;AAKA;;;AAnBA;;;;;;;;AA6BO,SAAS,gBAAgB,EAC9B,KAAK,EACL,aAAa,EACb,cAAc,mBAAmB,EACjC,WAAW,KAAK,EAChB,mBAAmB,IAAI,EACF;;IACrB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,EAAE,MAAM,gBAAgB,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD,EAAE;QACxD,QAAQ;QACR,MAAM;QACN,OAAO;IACT;IAEA,MAAM,WAAW,kBAAkB,QAAQ,EAAE;IAC7C,MAAM,kBAAkB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEpD,MAAM,eAAe,CAAC;QACpB,cAAc;QACd,QAAQ;IACV;IAEA,qBACE,6LAAC,sIAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,iBAAe;oBACf,WAAU;oBACV,UAAU;;wBAET,gCACC,6LAAC;4BAAK,WAAU;sCACb,gBAAgB,IAAI;;;;;iDAGvB,6LAAC;4BAAK,WAAU;sCAAyB;;;;;;sCAE3C,6LAAC,iOAAA,CAAA,iBAAc;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAG9B,6LAAC,sIAAA,CAAA,iBAAc;gBAAC,WAAU;0BACxB,cAAA,6LAAC,sIAAA,CAAA,UAAO;;sCACN,6LAAC,sIAAA,CAAA,eAAY;4BACX,aAAY;4BACZ,OAAO;4BACP,eAAe;;;;;;sCAEjB,6LAAC,sIAAA,CAAA,cAAW;sCACT,0BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6LAAC;kDAAK;;;;;;;;;;;qDAGR;;kDACE,6LAAC,sIAAA,CAAA,eAAY;kDAAC;;;;;;kDACd,6LAAC,sIAAA,CAAA,eAAY;;4CACV,kCACC,6LAAC,sIAAA,CAAA,cAAW;gDACV,OAAM;gDACN,UAAU,IAAM,aAAa;;kEAE7B,6LAAC,uMAAA,CAAA,QAAK;wDACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,CAAC,QAAQ,gBAAgB;;;;;;oDAE3B;;;;;;;4CAIL,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,sIAAA,CAAA,cAAW;oDAEV,OAAO,QAAQ,EAAE,CAAC,QAAQ;oDAC1B,UAAU,IAAM,aAAa,QAAQ,EAAE;;sEAEvC,6LAAC,uMAAA,CAAA,QAAK;4DACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,UAAU,QAAQ,EAAE,GAAG,gBAAgB;;;;;;sEAG3C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAY,QAAQ,IAAI;;;;;;8EACxC,6LAAC;oEAAK,WAAU;;wEAAyC;wEACjD,QAAQ,GAAG;;;;;;;;;;;;;;mDAbhB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BrC;GAvGgB;;QAUgC,0JAAA,CAAA,cAAW;;;KAV3C", "debugId": null}}, {"offset": {"line": 8751, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/report-filters.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { DateRangePicker } from \"@/components/ui/date-range-picker\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { useBranches } from \"@/features/branches/hooks/use-branches\";\r\nimport { EmployeeSelector } from \"@/features/employees/components/employee-selector\";\r\nimport { PaymentMethodSelector } from \"@/features/payment-methods/components/payment-method-selector\";\r\nimport { usePaymentMethods } from \"@/features/payment-methods/hooks/use-payment-methods\";\r\nimport { usePosSessions } from \"@/features/pos/hooks/use-pos-sessions\";\r\nimport { useRegions } from \"@/features/regions/hooks/use-regions\";\r\nimport { ProductSelector } from \"./product-selector\";\r\nimport {\r\n  DATE_RANGE_OPTIONS,\r\n  ReportFilterParams,\r\n  TIME_RANGE_OPTIONS,\r\n} from \"@/types\";\r\nimport { endOfDay, format, startOfDay, subDays } from \"date-fns\";\r\nimport { FilterIcon, RefreshCw } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\nimport { DateRange } from \"react-day-picker\";\r\n\r\ninterface ReportFiltersProps {\r\n  filters: ReportFilterParams;\r\n  onFilterChange: (filters: ReportFilterParams) => void;\r\n  showTimeFilter?: boolean;\r\n  showSessionFilter?: boolean;\r\n  showUserFilter?: boolean;\r\n  showBranchFilter?: boolean;\r\n  showRegionFilter?: boolean;\r\n  showPaymentMethodFilter?: boolean;\r\n  showStatusFilter?: boolean;\r\n  showDsaFilter?: boolean;\r\n  showProductFilter?: boolean;\r\n  showCategoryFilter?: boolean;\r\n  showLocationFilter?: boolean;\r\n  showBankingMethodFilter?: boolean;\r\n  showTransactionTypeFilter?: boolean;\r\n}\r\n\r\nexport function ReportFilters({\r\n  filters,\r\n  onFilterChange,\r\n  showTimeFilter = true,\r\n  showSessionFilter = false,\r\n  showUserFilter = true,\r\n  showBranchFilter = true,\r\n  showRegionFilter = false,\r\n  showPaymentMethodFilter = true,\r\n  showStatusFilter = false,\r\n  showDsaFilter = false,\r\n  showProductFilter = false,\r\n  showCategoryFilter = false,\r\n  showLocationFilter = false,\r\n  showBankingMethodFilter = false,\r\n  showTransactionTypeFilter = false,\r\n}: ReportFiltersProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [dateRange, setDateRange] = useState<DateRange>({\r\n    from: filters.start_date\r\n      ? new Date(filters.start_date)\r\n      : subDays(new Date(), 7),\r\n    to: filters.end_date ? new Date(filters.end_date) : new Date(),\r\n  });\r\n  const [selectedDateRange, setSelectedDateRange] =\r\n    useState<string>(\"this_week\");\r\n  // Time filter removed\r\n\r\n  const { data: branchesResponse } = useBranches();\r\n  const { data: paymentMethodsResponse } = usePaymentMethods();\r\n  const { data: posSessionsResponse } = usePosSessions();\r\n  const { data: regionsResponse } = useRegions();\r\n\r\n  // Filter branches by selected region\r\n  const filteredBranches = branchesResponse?.data?.filter((branch) => {\r\n    if (!filters.region_id) return true; // Show all branches if no region selected\r\n    return branch.region_id === filters.region_id;\r\n  }) || [];\r\n\r\n  const handleDateRangeChange = (range: DateRange) => {\r\n    setDateRange(range);\r\n    if (range?.from && range?.to) {\r\n      onFilterChange({\r\n        ...filters,\r\n        start_date: format(range.from, \"yyyy-MM-dd\"),\r\n        end_date: format(range.to, \"yyyy-MM-dd\"),\r\n      });\r\n    }\r\n  };\r\n\r\n  const handlePredefinedDateRange = (value: string) => {\r\n    setSelectedDateRange(value);\r\n    let from: Date | undefined;\r\n    let to: Date | undefined;\r\n\r\n    const today = new Date();\r\n\r\n    switch (value) {\r\n      case \"today\":\r\n        from = startOfDay(today);\r\n        to = endOfDay(today);\r\n        break;\r\n      case \"yesterday\":\r\n        from = startOfDay(subDays(today, 1));\r\n        to = endOfDay(subDays(today, 1));\r\n        break;\r\n      case \"this_week\":\r\n        from = startOfDay(subDays(today, today.getDay()));\r\n        to = endOfDay(today);\r\n        break;\r\n      case \"last_week\":\r\n        from = startOfDay(subDays(today, today.getDay() + 7));\r\n        to = endOfDay(subDays(today, today.getDay() + 1));\r\n        break;\r\n      case \"this_month\":\r\n        from = startOfDay(new Date(today.getFullYear(), today.getMonth(), 1));\r\n        to = endOfDay(today);\r\n        break;\r\n      case \"last_month\":\r\n        from = startOfDay(\r\n          new Date(today.getFullYear(), today.getMonth() - 1, 1)\r\n        );\r\n        to = endOfDay(new Date(today.getFullYear(), today.getMonth(), 0));\r\n        break;\r\n      case \"this_year\":\r\n        from = startOfDay(new Date(today.getFullYear(), 0, 1));\r\n        to = endOfDay(today);\r\n        break;\r\n      case \"last_year\":\r\n        from = startOfDay(new Date(today.getFullYear() - 1, 0, 1));\r\n        to = endOfDay(new Date(today.getFullYear() - 1, 11, 31));\r\n        break;\r\n      case \"custom\":\r\n        // Keep the current date range\r\n        return;\r\n      default:\r\n        from = subDays(today, 7);\r\n        to = today;\r\n    }\r\n\r\n    setDateRange({ from, to });\r\n\r\n    if (from && to) {\r\n      onFilterChange({\r\n        ...filters,\r\n        start_date: format(from, \"yyyy-MM-dd\"),\r\n        end_date: format(to, \"yyyy-MM-dd\"),\r\n      });\r\n    }\r\n  };\r\n\r\n  // Time filter removed\r\n\r\n  const handleBranchChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      branch_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleRegionChange = (value: string) => {\r\n    const regionId = value === \"all\" ? undefined : parseInt(value, 10);\r\n\r\n    onFilterChange({\r\n      ...filters,\r\n      region_id: regionId,\r\n      // Reset branch filter when region changes\r\n      branch_id: undefined,\r\n    });\r\n  };\r\n\r\n  const handleEmployeeChange = (value: number | undefined) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      employee_id: value,\r\n    });\r\n  };\r\n\r\n  const handlePaymentMethodChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      payment_method_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleSessionChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      pos_session_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleStatusChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      status: value === \"all\" ? undefined : value,\r\n    });\r\n  };\r\n\r\n  const handleDsaChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      is_dsa: value === \"all\" ? undefined : value === \"true\",\r\n    });\r\n  };\r\n\r\n  const handleProductChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      product_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleCategoryChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      category_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleLocationChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      location_id: value === \"all\" ? undefined : parseInt(value, 10),\r\n    });\r\n  };\r\n\r\n  const handleBankingMethodChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      banking_method: value === \"all\" ? undefined : value,\r\n    });\r\n  };\r\n\r\n  const handleTransactionTypeChange = (value: string) => {\r\n    onFilterChange({\r\n      ...filters,\r\n      transaction_type: value === \"all\" ? undefined : value,\r\n    });\r\n  };\r\n\r\n  const handleReset = () => {\r\n    setSelectedDateRange(\"this_week\");\r\n    const today = new Date();\r\n    const from = subDays(today, 7);\r\n    const to = today;\r\n    setDateRange({ from, to });\r\n\r\n    // Reset all filters to default values\r\n    onFilterChange({\r\n      start_date: format(from, \"yyyy-MM-dd\"),\r\n      end_date: format(to, \"yyyy-MM-dd\"),\r\n      branch_id: undefined,\r\n      region_id: undefined,\r\n      payment_method_id: undefined,\r\n      employee_id: undefined,\r\n      pos_session_id: undefined,\r\n      status: undefined,\r\n      is_dsa: undefined,\r\n    });\r\n  };\r\n\r\n  return (\r\n    <Card className=\"mb-6\">\r\n      <CardContent className=\"p-4\">\r\n        <div className=\"flex flex-col space-y-4 md:flex-row md:flex-wrap md:items-end md:gap-4 md:space-y-0\">\r\n          {/* Date Range Filter */}\r\n          <div className=\"flex-1 space-y-2\">\r\n            <Label>Date Range</Label>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              <Select\r\n                value={selectedDateRange}\r\n                onValueChange={handlePredefinedDateRange}\r\n              >\r\n                <SelectTrigger className=\"w-[180px]\">\r\n                  <SelectValue placeholder=\"Select date range\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {DATE_RANGE_OPTIONS.map((option) => (\r\n                    <SelectItem key={option.value} value={option.value}>\r\n                      {option.label}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n\r\n              <DateRangePicker\r\n                value={dateRange}\r\n                onChange={handleDateRangeChange}\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Branch Filter - Moved outside of More Filters */}\r\n          {showBranchFilter && (\r\n            <div className=\"space-y-2\">\r\n              <Label>Branch</Label>\r\n              <Select\r\n                value={filters.branch_id ? filters.branch_id.toString() : \"all\"}\r\n                onValueChange={handleBranchChange}\r\n              >\r\n                <SelectTrigger className=\"w-[180px]\">\r\n                  <SelectValue placeholder=\"All Branches\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All Branches</SelectItem>\r\n                  {filteredBranches.map((branch) => (\r\n                    <SelectItem key={branch.id} value={branch.id.toString()}>\r\n                      {branch.name}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n          )}\r\n\r\n          {/* Payment Method Filter - Moved outside of More Filters */}\r\n          {showPaymentMethodFilter && (\r\n            <div className=\"space-y-2\">\r\n              <Label>Payment Method</Label>\r\n              <div className=\"w-[220px]\">\r\n                <PaymentMethodSelector\r\n                  value={filters.payment_method_id}\r\n                  onValueChange={(value) =>\r\n                    handlePaymentMethodChange(\r\n                      value !== undefined ? value.toString() : \"all\"\r\n                    )\r\n                  }\r\n                  placeholder=\"All Payment Methods\"\r\n                  includeAllOption={true}\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Product Filter */}\r\n          {showProductFilter && (\r\n            <div className=\"space-y-2\">\r\n              <Label>Product</Label>\r\n              <div className=\"w-[220px]\">\r\n                <ProductSelector\r\n                  value={filters.product_id}\r\n                  onValueChange={(value) =>\r\n                    handleProductChange(value?.toString() || \"all\")\r\n                  }\r\n                  placeholder=\"All Products\"\r\n                  includeAllOption={true}\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Region Filter */}\r\n          {showRegionFilter && (\r\n            <div className=\"space-y-2\">\r\n              <Label>Region</Label>\r\n              <Select\r\n                value={filters.region_id ? filters.region_id.toString() : \"all\"}\r\n                onValueChange={handleRegionChange}\r\n              >\r\n                <SelectTrigger className=\"w-[180px]\">\r\n                  <SelectValue placeholder=\"All Regions\" />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  <SelectItem value=\"all\">All Regions</SelectItem>\r\n                  {regionsResponse?.data?.map((region) => (\r\n                    <SelectItem key={region.id} value={region.id.toString()}>\r\n                      {region.name}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </div>\r\n          )}\r\n\r\n          {/* Employee Filter - Moved outside of More Filters */}\r\n          {showUserFilter && (\r\n            <div className=\"space-y-2\">\r\n              <Label>Employee</Label>\r\n              <div className=\"w-[220px]\">\r\n                <EmployeeSelector\r\n                  value={filters.employee_id}\r\n                  onValueChange={handleEmployeeChange}\r\n                  placeholder=\"All Employees\"\r\n                  includeAllOption={true}\r\n                  branchId={filters.branch_id}\r\n                />\r\n              </div>\r\n            </div>\r\n          )}\r\n\r\n          {/* Time Range Filter removed */}\r\n\r\n          {/* More Filters Button */}\r\n          <div className=\"flex space-x-2\">\r\n            <Popover open={isOpen} onOpenChange={setIsOpen}>\r\n              <PopoverTrigger asChild>\r\n                <Button variant=\"outline\">\r\n                  <FilterIcon className=\"mr-2 h-4 w-4\" />\r\n                  More Filters\r\n                </Button>\r\n              </PopoverTrigger>\r\n              <PopoverContent className=\"w-80 p-4\" align=\"end\">\r\n                <div className=\"grid gap-4\">\r\n                  {/* Branch Filter - Moved outside */}\r\n                  {/* Employee Filter - Moved outside */}\r\n\r\n                  {/* Session Filter */}\r\n                  {showSessionFilter && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label>POS Session</Label>\r\n                      <Select\r\n                        value={\r\n                          filters.pos_session_id\r\n                            ? filters.pos_session_id.toString()\r\n                            : \"all\"\r\n                        }\r\n                        onValueChange={handleSessionChange}\r\n                      >\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder=\"All Sessions\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"all\">All Sessions</SelectItem>\r\n                          {posSessionsResponse?.map((session) => (\r\n                            <SelectItem\r\n                              key={session.id}\r\n                              value={session.id.toString()}\r\n                            >\r\n                              Session #{session.id}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Status Filter */}\r\n                  {showStatusFilter && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label>Status</Label>\r\n                      <Select\r\n                        value={filters.status || \"all\"}\r\n                        onValueChange={handleStatusChange}\r\n                      >\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder=\"All Statuses\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"all\">All Statuses</SelectItem>\r\n                          <SelectItem value=\"completed\">Completed</SelectItem>\r\n                          <SelectItem value=\"pending\">Pending</SelectItem>\r\n                          <SelectItem value=\"cancelled\">Cancelled</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* DSA Filter */}\r\n                  {showDsaFilter && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label>Sale Type</Label>\r\n                      <Select\r\n                        value={\r\n                          filters.is_dsa !== undefined\r\n                            ? filters.is_dsa.toString()\r\n                            : \"all\"\r\n                        }\r\n                        onValueChange={handleDsaChange}\r\n                      >\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder=\"All Sales\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"all\">All Sales</SelectItem>\r\n                          <SelectItem value=\"true\">DSA Sales</SelectItem>\r\n                          <SelectItem value=\"false\">Regular Sales</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Banking Method Filter */}\r\n                  {showBankingMethodFilter && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label>Banking Method</Label>\r\n                      <Select\r\n                        value={filters.banking_method || \"all\"}\r\n                        onValueChange={handleBankingMethodChange}\r\n                      >\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder=\"All Methods\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"all\">All Methods</SelectItem>\r\n                          <SelectItem value=\"bank\">Bank</SelectItem>\r\n                          <SelectItem value=\"agent\">Agent</SelectItem>\r\n                          <SelectItem value=\"mpesa\">M-Pesa</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                  )}\r\n\r\n                  {/* Transaction Type Filter */}\r\n                  {showTransactionTypeFilter && (\r\n                    <div className=\"space-y-2\">\r\n                      <Label>Transaction Type</Label>\r\n                      <Select\r\n                        value={filters.transaction_type || \"all\"}\r\n                        onValueChange={handleTransactionTypeChange}\r\n                      >\r\n                        <SelectTrigger>\r\n                          <SelectValue placeholder=\"All Types\" />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"all\">All Types</SelectItem>\r\n                          <SelectItem value=\"deposit\">Deposit</SelectItem>\r\n                          <SelectItem value=\"withdrawal\">Withdrawal</SelectItem>\r\n                          <SelectItem value=\"transfer\">Transfer</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </PopoverContent>\r\n            </Popover>\r\n\r\n            <Button variant=\"outline\" onClick={handleReset}>\r\n              <RefreshCw className=\"mr-2 h-4 w-4\" />\r\n              Reset\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAKA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAKA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AAhCA;;;;;;;;;;;;;;;;;;AAqDO,SAAS,cAAc,EAC5B,OAAO,EACP,cAAc,EACd,iBAAiB,IAAI,EACrB,oBAAoB,KAAK,EACzB,iBAAiB,IAAI,EACrB,mBAAmB,IAAI,EACvB,mBAAmB,KAAK,EACxB,0BAA0B,IAAI,EAC9B,mBAAmB,KAAK,EACxB,gBAAgB,KAAK,EACrB,oBAAoB,KAAK,EACzB,qBAAqB,KAAK,EAC1B,qBAAqB,KAAK,EAC1B,0BAA0B,KAAK,EAC/B,4BAA4B,KAAK,EACd;;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM,QAAQ,UAAU,GACpB,IAAI,KAAK,QAAQ,UAAU,IAC3B,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,QAAQ;QACxB,IAAI,QAAQ,QAAQ,GAAG,IAAI,KAAK,QAAQ,QAAQ,IAAI,IAAI;IAC1D;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAC7C,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnB,sBAAsB;IAEtB,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,CAAA,GAAA,0JAAA,CAAA,cAAW,AAAD;IAC7C,MAAM,EAAE,MAAM,sBAAsB,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,oBAAiB,AAAD;IACzD,MAAM,EAAE,MAAM,mBAAmB,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,iBAAc,AAAD;IACnD,MAAM,EAAE,MAAM,eAAe,EAAE,GAAG,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD;IAE3C,qCAAqC;IACrC,MAAM,mBAAmB,kBAAkB,MAAM,OAAO,CAAC;QACvD,IAAI,CAAC,QAAQ,SAAS,EAAE,OAAO,MAAM,0CAA0C;QAC/E,OAAO,OAAO,SAAS,KAAK,QAAQ,SAAS;IAC/C,MAAM,EAAE;IAER,MAAM,wBAAwB,CAAC;QAC7B,aAAa;QACb,IAAI,OAAO,QAAQ,OAAO,IAAI;YAC5B,eAAe;gBACb,GAAG,OAAO;gBACV,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,EAAE;gBAC/B,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,EAAE,EAAE;YAC7B;QACF;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,qBAAqB;QACrB,IAAI;QACJ,IAAI;QAEJ,MAAM,QAAQ,IAAI;QAElB,OAAQ;YACN,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE;gBAClB,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;gBACd;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBACjC,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBAC7B;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,MAAM,MAAM;gBAC7C,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;gBACd;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,MAAM,MAAM,KAAK;gBAClD,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,MAAM,MAAM,KAAK;gBAC9C;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,IAAI;gBAClE,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;gBACd;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EACd,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,KAAK,GAAG;gBAEtD,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK,MAAM,WAAW,IAAI,MAAM,QAAQ,IAAI;gBAC9D;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK,MAAM,WAAW,IAAI,GAAG;gBACnD,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE;gBACd;YACF,KAAK;gBACH,OAAO,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK,MAAM,WAAW,KAAK,GAAG,GAAG;gBACvD,KAAK,CAAA,GAAA,0IAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,KAAK,MAAM,WAAW,KAAK,GAAG,IAAI;gBACpD;YACF,KAAK;gBACH,8BAA8B;gBAC9B;YACF;gBACE,OAAO,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBACtB,KAAK;QACT;QAEA,aAAa;YAAE;YAAM;QAAG;QAExB,IAAI,QAAQ,IAAI;YACd,eAAe;gBACb,GAAG,OAAO;gBACV,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;gBACzB,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI;YACvB;QACF;IACF;IAEA,sBAAsB;IAEtB,MAAM,qBAAqB,CAAC;QAC1B,eAAe;YACb,GAAG,OAAO;YACV,WAAW,UAAU,QAAQ,YAAY,SAAS,OAAO;QAC3D;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,WAAW,UAAU,QAAQ,YAAY,SAAS,OAAO;QAE/D,eAAe;YACb,GAAG,OAAO;YACV,WAAW;YACX,0CAA0C;YAC1C,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe;YACb,GAAG,OAAO;YACV,aAAa;QACf;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,eAAe;YACb,GAAG,OAAO;YACV,mBAAmB,UAAU,QAAQ,YAAY,SAAS,OAAO;QACnE;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,eAAe;YACb,GAAG,OAAO;YACV,gBAAgB,UAAU,QAAQ,YAAY,SAAS,OAAO;QAChE;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,eAAe;YACb,GAAG,OAAO;YACV,QAAQ,UAAU,QAAQ,YAAY;QACxC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,eAAe;YACb,GAAG,OAAO;YACV,QAAQ,UAAU,QAAQ,YAAY,UAAU;QAClD;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,eAAe;YACb,GAAG,OAAO;YACV,YAAY,UAAU,QAAQ,YAAY,SAAS,OAAO;QAC5D;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe;YACb,GAAG,OAAO;YACV,aAAa,UAAU,QAAQ,YAAY,SAAS,OAAO;QAC7D;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe;YACb,GAAG,OAAO;YACV,aAAa,UAAU,QAAQ,YAAY,SAAS,OAAO;QAC7D;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,eAAe;YACb,GAAG,OAAO;YACV,gBAAgB,UAAU,QAAQ,YAAY;QAChD;IACF;IAEA,MAAM,8BAA8B,CAAC;QACnC,eAAe;YACb,GAAG,OAAO;YACV,kBAAkB,UAAU,QAAQ,YAAY;QAClD;IACF;IAEA,MAAM,cAAc;QAClB,qBAAqB;QACrB,MAAM,QAAQ,IAAI;QAClB,MAAM,OAAO,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QAC5B,MAAM,KAAK;QACX,aAAa;YAAE;YAAM;QAAG;QAExB,sCAAsC;QACtC,eAAe;YACb,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YACzB,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI;YACrB,WAAW;YACX,WAAW;YACX,mBAAmB;YACnB,aAAa;YACb,gBAAgB;YAChB,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,OAAO;wCACP,eAAe;;0DAEf,6LAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,6LAAC,qIAAA,CAAA,gBAAa;0DACX,0HAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAC,uBACvB,6LAAC,qIAAA,CAAA,aAAU;wDAAoB,OAAO,OAAO,KAAK;kEAC/C,OAAO,KAAK;uDADE,OAAO,KAAK;;;;;;;;;;;;;;;;kDAOnC,6LAAC,sJAAA,CAAA,kBAAe;wCACd,OAAO;wCACP,UAAU;;;;;;;;;;;;;;;;;;oBAMf,kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,QAAQ,KAAK;gCAC1D,eAAe;;kDAEf,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0DACZ,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;4CACvB,iBAAiB,GAAG,CAAC,CAAC,uBACrB,6LAAC,qIAAA,CAAA,aAAU;oDAAiB,OAAO,OAAO,EAAE,CAAC,QAAQ;8DAClD,OAAO,IAAI;mDADG,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;oBAUnC,yCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,wLAAA,CAAA,wBAAqB;oCACpB,OAAO,QAAQ,iBAAiB;oCAChC,eAAe,CAAC,QACd,0BACE,UAAU,YAAY,MAAM,QAAQ,KAAK;oCAG7C,aAAY;oCACZ,kBAAkB;;;;;;;;;;;;;;;;;oBAOzB,mCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mKAAA,CAAA,kBAAe;oCACd,OAAO,QAAQ,UAAU;oCACzB,eAAe,CAAC,QACd,oBAAoB,OAAO,cAAc;oCAE3C,aAAY;oCACZ,kBAAkB;;;;;;;;;;;;;;;;;oBAOzB,kCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,QAAQ,KAAK;gCAC1D,eAAe;;kDAEf,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,gBAAa;;0DACZ,6LAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;4CACvB,iBAAiB,MAAM,IAAI,CAAC,uBAC3B,6LAAC,qIAAA,CAAA,aAAU;oDAAiB,OAAO,OAAO,EAAE,CAAC,QAAQ;8DAClD,OAAO,IAAI;mDADG,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;oBAUnC,gCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,sKAAA,CAAA,mBAAgB;oCACf,OAAO,QAAQ,WAAW;oCAC1B,eAAe;oCACf,aAAY;oCACZ,kBAAkB;oCAClB,UAAU,QAAQ,SAAS;;;;;;;;;;;;;;;;;kCASnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,sIAAA,CAAA,UAAO;gCAAC,MAAM;gCAAQ,cAAc;;kDACnC,6LAAC,sIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;;8DACd,6LAAC,6MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAI3C,6LAAC,sIAAA,CAAA,iBAAc;wCAAC,WAAU;wCAAW,OAAM;kDACzC,cAAA,6LAAC;4CAAI,WAAU;;gDAKZ,mCACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,OACE,QAAQ,cAAc,GAClB,QAAQ,cAAc,CAAC,QAAQ,KAC/B;4DAEN,eAAe;;8EAEf,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;wEACvB,qBAAqB,IAAI,CAAC,wBACzB,6LAAC,qIAAA,CAAA,aAAU;gFAET,OAAO,QAAQ,EAAE,CAAC,QAAQ;;oFAC3B;oFACW,QAAQ,EAAE;;+EAHf,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;gDAY1B,kCACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,OAAO,QAAQ,MAAM,IAAI;4DACzB,eAAe;;8EAEf,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;sFACxB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAY;;;;;;sFAC9B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAU;;;;;;sFAC5B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAY;;;;;;;;;;;;;;;;;;;;;;;;gDAOrC,+BACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,OACE,QAAQ,MAAM,KAAK,YACf,QAAQ,MAAM,CAAC,QAAQ,KACvB;4DAEN,eAAe;;8EAEf,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;sFACxB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAO;;;;;;sFACzB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAQ;;;;;;;;;;;;;;;;;;;;;;;;gDAOjC,yCACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,OAAO,QAAQ,cAAc,IAAI;4DACjC,eAAe;;8EAEf,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;sFACxB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAO;;;;;;sFACzB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAQ;;;;;;sFAC1B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAQ;;;;;;;;;;;;;;;;;;;;;;;;gDAOjC,2CACC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;sEAAC;;;;;;sEACP,6LAAC,qIAAA,CAAA,SAAM;4DACL,OAAO,QAAQ,gBAAgB,IAAI;4DACnC,eAAe;;8EAEf,6LAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;8EAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sFACZ,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAM;;;;;;sFACxB,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAU;;;;;;sFAC5B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAa;;;;;;sFAC/B,6LAAC,qIAAA,CAAA,aAAU;4EAAC,OAAM;sFAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS3C,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;GAhfgB;;QA4BqB,0JAAA,CAAA,cAAW;QACL,8KAAA,CAAA,oBAAiB;QACpB,4JAAA,CAAA,iBAAc;QAClB,wJAAA,CAAA,aAAU;;;KA/B9B", "debugId": null}}, {"offset": {"line": 9775, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/pagination.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport {\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n  MoreHorizontalIcon,\r\n} from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button, buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Pagination({ className, ...props }: React.ComponentProps<\"nav\">) {\r\n  return (\r\n    <nav\r\n      role=\"navigation\"\r\n      aria-label=\"pagination\"\r\n      data-slot=\"pagination\"\r\n      className={cn(\"mx-auto flex w-full justify-center\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"pagination-content\"\r\n      className={cn(\"flex flex-row items-center gap-1\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationItem({ ...props }: React.ComponentProps<\"li\">) {\r\n  return <li data-slot=\"pagination-item\" {...props} />\r\n}\r\n\r\ntype PaginationLinkProps = {\r\n  isActive?: boolean\r\n} & Pick<React.ComponentProps<typeof Button>, \"size\"> &\r\n  React.ComponentProps<\"a\">\r\n\r\nfunction PaginationLink({\r\n  className,\r\n  isActive,\r\n  size = \"icon\",\r\n  ...props\r\n}: PaginationLinkProps) {\r\n  return (\r\n    <a\r\n      aria-current={isActive ? \"page\" : undefined}\r\n      data-slot=\"pagination-link\"\r\n      data-active={isActive}\r\n      className={cn(\r\n        buttonVariants({\r\n          variant: isActive ? \"outline\" : \"ghost\",\r\n          size,\r\n        }),\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction PaginationPrevious({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to previous page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pl-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <ChevronLeftIcon />\r\n      <span className=\"hidden sm:block\">Previous</span>\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationNext({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof PaginationLink>) {\r\n  return (\r\n    <PaginationLink\r\n      aria-label=\"Go to next page\"\r\n      size=\"default\"\r\n      className={cn(\"gap-1 px-2.5 sm:pr-2.5\", className)}\r\n      {...props}\r\n    >\r\n      <span className=\"hidden sm:block\">Next</span>\r\n      <ChevronRightIcon />\r\n    </PaginationLink>\r\n  )\r\n}\r\n\r\nfunction PaginationEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      aria-hidden\r\n      data-slot=\"pagination-ellipsis\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontalIcon className=\"size-4\" />\r\n      <span className=\"sr-only\">More pages</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationLink,\r\n  PaginationItem,\r\n  PaginationPrevious,\r\n  PaginationNext,\r\n  PaginationEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AAAA;AAAA;AAMA;AACA;;;;;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,MAAK;QACL,cAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;KAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,EAAE,GAAG,OAAmC;IAC9D,qBAAO,6LAAC;QAAG,aAAU;QAAmB,GAAG,KAAK;;;;;;AAClD;MAFS;AAST,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,MAAM,EACb,GAAG,OACiB;IACpB,qBACE,6LAAC;QACC,gBAAc,WAAW,SAAS;QAClC,aAAU;QACV,eAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YACb,SAAS,WAAW,YAAY;YAChC;QACF,IACA;QAED,GAAG,KAAK;;;;;;AAGf;MArBS;AAuBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,6LAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,6LAAC,2NAAA,CAAA,kBAAe;;;;;0BAChB,6LAAC;gBAAK,WAAU;0BAAkB;;;;;;;;;;;;AAGxC;MAfS;AAiBT,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACyC;IAC5C,qBACE,6LAAC;QACC,cAAW;QACX,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;QACvC,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BAAkB;;;;;;0BAClC,6LAAC,6NAAA,CAAA,mBAAgB;;;;;;;;;;;AAGvB;MAfS;AAiBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,6LAAC;QACC,aAAW;QACX,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,6LAAC,uNAAA,CAAA,qBAAkB;gBAAC,WAAU;;;;;;0BAC9B,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;MAfS", "debugId": null}}, {"offset": {"line": 9952, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/data-pagination.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useMemo } from \"react\";\r\nimport {\r\n  Pagination,\r\n  PaginationContent,\r\n  PaginationEllipsis,\r\n  PaginationItem,\r\n  PaginationLink,\r\n  PaginationNext,\r\n  PaginationPrevious,\r\n} from \"@/components/ui/pagination\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { ChevronFirst, ChevronLast } from \"lucide-react\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nexport interface DataPaginationProps {\r\n  // Required props\r\n  currentPage: number;\r\n  totalPages: number;\r\n  onPageChange: (page: number) => void;\r\n\r\n  // Optional props\r\n  pageSize?: number;\r\n  pageSizes?: number[];\r\n  onPageSizeChange?: (pageSize: number) => void;\r\n  totalItems?: number;\r\n  isLoading?: boolean;\r\n  showPageSizeSelector?: boolean;\r\n  showItemsInfo?: boolean;\r\n  showFirstLastButtons?: boolean;\r\n  maxPageButtons?: number;\r\n  className?: string;\r\n  compact?: boolean;\r\n  ariaLabel?: string;\r\n}\r\n\r\nexport function DataPagination({\r\n  currentPage,\r\n  totalPages,\r\n  onPageChange,\r\n  pageSize = 10,\r\n  pageSizes = [10, 25, 50, 100, 250, 500, 1000],\r\n  onPageSizeChange,\r\n  totalItems,\r\n  isLoading = false,\r\n  showPageSizeSelector = true,\r\n  showItemsInfo = true,\r\n  showFirstLastButtons = true,\r\n  maxPageButtons = 5,\r\n  className,\r\n  compact = false,\r\n  ariaLabel = \"Pagination\",\r\n}: DataPaginationProps) {\r\n  // Don't render pagination if there's only one page and no page size selector\r\n  if (totalPages <= 1 && (!showPageSizeSelector || !onPageSizeChange)) {\r\n    return null;\r\n  }\r\n\r\n  // Calculate the current range of items being displayed\r\n  const itemRange = useMemo(() => {\r\n    if (!totalItems) return null;\r\n\r\n    const start = totalItems === 0 ? 0 : (currentPage - 1) * pageSize + 1;\r\n    const end = Math.min(currentPage * pageSize, totalItems);\r\n\r\n    return { start, end };\r\n  }, [currentPage, pageSize, totalItems]);\r\n\r\n  // Generate page numbers to display\r\n  const pageNumbers = useMemo(() => {\r\n    const numbers: (number | string)[] = [];\r\n\r\n    if (totalPages <= maxPageButtons) {\r\n      // If total pages is less than or equal to maxPageButtons, show all pages\r\n      for (let i = 1; i <= totalPages; i++) {\r\n        numbers.push(i);\r\n      }\r\n    } else {\r\n      // Always include first page\r\n      numbers.push(1);\r\n\r\n      // Calculate start and end of page numbers to show\r\n      let start = Math.max(2, currentPage - Math.floor(maxPageButtons / 2) + 1);\r\n      let end = Math.min(totalPages - 1, start + maxPageButtons - 3);\r\n\r\n      // Adjust if we're at the beginning\r\n      if (currentPage <= Math.floor(maxPageButtons / 2)) {\r\n        end = maxPageButtons - 2;\r\n        start = 2;\r\n      }\r\n\r\n      // Adjust if we're at the end\r\n      if (currentPage > totalPages - Math.floor(maxPageButtons / 2)) {\r\n        start = Math.max(2, totalPages - maxPageButtons + 2);\r\n        end = totalPages - 1;\r\n      }\r\n\r\n      // Add ellipsis before middle pages if needed\r\n      if (start > 2) {\r\n        numbers.push(\"ellipsis-start\");\r\n      }\r\n\r\n      // Add middle pages\r\n      for (let i = start; i <= end; i++) {\r\n        numbers.push(i);\r\n      }\r\n\r\n      // Add ellipsis after middle pages if needed\r\n      if (end < totalPages - 1) {\r\n        numbers.push(\"ellipsis-end\");\r\n      }\r\n\r\n      // Always include last page if more than one page\r\n      if (totalPages > 1) {\r\n        numbers.push(totalPages);\r\n      }\r\n    }\r\n\r\n    return numbers;\r\n  }, [currentPage, totalPages, maxPageButtons]);\r\n\r\n  // Handle page click with validation\r\n  const handlePageClick = (page: number, e: React.MouseEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Only trigger page change if it's not the current page and not loading\r\n    if (page !== currentPage && !isLoading) {\r\n      // Force the page to be within valid range\r\n      const validPage = Math.max(1, Math.min(page, totalPages));\r\n      onPageChange(validPage);\r\n    }\r\n  };\r\n\r\n  // Handle page size change\r\n  const handlePageSizeChange = (value: string) => {\r\n    if (onPageSizeChange) {\r\n      onPageSizeChange(parseInt(value, 10));\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"flex flex-col gap-4 w-full\",\r\n        compact\r\n          ? \"sm:flex-row sm:items-center sm:justify-between\"\r\n          : \"md:flex-row md:items-center md:justify-between\",\r\n        className\r\n      )}\r\n      aria-label={ariaLabel}\r\n    >\r\n      {/* Page size selector */}\r\n      {showPageSizeSelector && onPageSizeChange && (\r\n        <div className=\"flex items-center gap-2\">\r\n          <span className=\"text-sm text-muted-foreground\">Show</span>\r\n          <Select\r\n            value={pageSize.toString()}\r\n            onValueChange={handlePageSizeChange}\r\n            disabled={isLoading}\r\n          >\r\n            <SelectTrigger\r\n              className=\"h-8 w-[70px]\"\r\n              aria-label=\"Select page size\"\r\n            >\r\n              <SelectValue placeholder={pageSize.toString()} />\r\n            </SelectTrigger>\r\n            <SelectContent>\r\n              {pageSizes.map((size) => (\r\n                <SelectItem key={size} value={size.toString()}>\r\n                  {size}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n          <span className=\"text-sm text-muted-foreground\">per page</span>\r\n        </div>\r\n      )}\r\n\r\n      {/* Pagination controls - Always show if we have pagination data */}\r\n      {totalPages >= 1 && (\r\n        <div className=\"bg-muted/50 rounded-md p-1\">\r\n          <Pagination>\r\n            <PaginationContent className=\"flex-wrap justify-center\">\r\n              {/* First page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(1, e)}\r\n                    disabled={currentPage === 1 || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to first page\"\r\n                  >\r\n                    <ChevronFirst className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n\r\n              {/* Previous page button */}\r\n              <PaginationItem>\r\n                <PaginationPrevious\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage - 1, e)}\r\n                  aria-disabled={currentPage === 1 || isLoading}\r\n                  className={cn(\r\n                    currentPage === 1 || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Page numbers */}\r\n              {!compact &&\r\n                pageNumbers.map((pageNumber, index) => {\r\n                  if (typeof pageNumber === \"string\") {\r\n                    return (\r\n                      <PaginationItem key={pageNumber}>\r\n                        <PaginationEllipsis />\r\n                      </PaginationItem>\r\n                    );\r\n                  }\r\n\r\n                  return (\r\n                    <PaginationItem key={`page-${pageNumber}`}>\r\n                      <PaginationLink\r\n                        href=\"#\"\r\n                        onClick={(e) => handlePageClick(pageNumber, e)}\r\n                        isActive={currentPage === pageNumber}\r\n                        aria-current={\r\n                          currentPage === pageNumber ? \"page\" : undefined\r\n                        }\r\n                      >\r\n                        {pageNumber}\r\n                      </PaginationLink>\r\n                    </PaginationItem>\r\n                  );\r\n                })}\r\n\r\n              {/* Compact view shows current/total instead of page numbers */}\r\n              {compact && (\r\n                <div className=\"flex items-center mx-2\">\r\n                  <span className=\"text-sm\">\r\n                    Page <span className=\"font-bold\">{currentPage}</span> of{\" \"}\r\n                    <span className=\"font-medium\">{totalPages}</span>\r\n                    {isLoading && (\r\n                      <span className=\"ml-1 text-muted-foreground\">\r\n                        (Loading...)\r\n                      </span>\r\n                    )}\r\n                  </span>\r\n                </div>\r\n              )}\r\n\r\n              {/* Next page button */}\r\n              <PaginationItem>\r\n                <PaginationNext\r\n                  href=\"#\"\r\n                  onClick={(e) => handlePageClick(currentPage + 1, e)}\r\n                  aria-disabled={currentPage === totalPages || isLoading}\r\n                  className={cn(\r\n                    currentPage === totalPages || isLoading\r\n                      ? \"pointer-events-none opacity-50\"\r\n                      : \"\",\r\n                    \"hover:bg-primary/10 transition-colors\"\r\n                  )}\r\n                />\r\n              </PaginationItem>\r\n\r\n              {/* Last page button */}\r\n              {showFirstLastButtons && (\r\n                <PaginationItem>\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"icon\"\r\n                    onClick={(e) => handlePageClick(totalPages, e)}\r\n                    disabled={currentPage === totalPages || isLoading}\r\n                    className=\"h-8 w-8\"\r\n                    aria-label=\"Go to last page\"\r\n                  >\r\n                    <ChevronLast className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </PaginationItem>\r\n              )}\r\n            </PaginationContent>\r\n          </Pagination>\r\n        </div>\r\n      )}\r\n\r\n      {/* Items info */}\r\n      {showItemsInfo && totalItems !== undefined && (\r\n        <div className=\"text-sm text-muted-foreground whitespace-nowrap\">\r\n          {totalItems === 0 ? (\r\n            \"No items\"\r\n          ) : (\r\n            <>\r\n              Showing {itemRange?.start} to {itemRange?.end} of {totalItems}{\" \"}\r\n              items\r\n            </>\r\n          )}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AAOA;AACA;AAAA;AACA;;;AArBA;;;;;;;AA4CO,SAAS,eAAe,EAC7B,WAAW,EACX,UAAU,EACV,YAAY,EACZ,WAAW,EAAE,EACb,YAAY;IAAC;IAAI;IAAI;IAAI;IAAK;IAAK;IAAK;CAAK,EAC7C,gBAAgB,EAChB,UAAU,EACV,YAAY,KAAK,EACjB,uBAAuB,IAAI,EAC3B,gBAAgB,IAAI,EACpB,uBAAuB,IAAI,EAC3B,iBAAiB,CAAC,EAClB,SAAS,EACT,UAAU,KAAK,EACf,YAAY,YAAY,EACJ;;IACpB,6EAA6E;IAC7E,IAAI,cAAc,KAAK,CAAC,CAAC,wBAAwB,CAAC,gBAAgB,GAAG;QACnE,OAAO;IACT;IAEA,uDAAuD;IACvD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE;YACxB,IAAI,CAAC,YAAY,OAAO;YAExB,MAAM,QAAQ,eAAe,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,WAAW;YACpE,MAAM,MAAM,KAAK,GAAG,CAAC,cAAc,UAAU;YAE7C,OAAO;gBAAE;gBAAO;YAAI;QACtB;4CAAG;QAAC;QAAa;QAAU;KAAW;IAEtC,mCAAmC;IACnC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE;YAC1B,MAAM,UAA+B,EAAE;YAEvC,IAAI,cAAc,gBAAgB;gBAChC,yEAAyE;gBACzE,IAAK,IAAI,IAAI,GAAG,KAAK,YAAY,IAAK;oBACpC,QAAQ,IAAI,CAAC;gBACf;YACF,OAAO;gBACL,4BAA4B;gBAC5B,QAAQ,IAAI,CAAC;gBAEb,kDAAkD;gBAClD,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,cAAc,KAAK,KAAK,CAAC,iBAAiB,KAAK;gBACvE,IAAI,MAAM,KAAK,GAAG,CAAC,aAAa,GAAG,QAAQ,iBAAiB;gBAE5D,mCAAmC;gBACnC,IAAI,eAAe,KAAK,KAAK,CAAC,iBAAiB,IAAI;oBACjD,MAAM,iBAAiB;oBACvB,QAAQ;gBACV;gBAEA,6BAA6B;gBAC7B,IAAI,cAAc,aAAa,KAAK,KAAK,CAAC,iBAAiB,IAAI;oBAC7D,QAAQ,KAAK,GAAG,CAAC,GAAG,aAAa,iBAAiB;oBAClD,MAAM,aAAa;gBACrB;gBAEA,6CAA6C;gBAC7C,IAAI,QAAQ,GAAG;oBACb,QAAQ,IAAI,CAAC;gBACf;gBAEA,mBAAmB;gBACnB,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,IAAK;oBACjC,QAAQ,IAAI,CAAC;gBACf;gBAEA,4CAA4C;gBAC5C,IAAI,MAAM,aAAa,GAAG;oBACxB,QAAQ,IAAI,CAAC;gBACf;gBAEA,iDAAiD;gBACjD,IAAI,aAAa,GAAG;oBAClB,QAAQ,IAAI,CAAC;gBACf;YACF;YAEA,OAAO;QACT;8CAAG;QAAC;QAAa;QAAY;KAAe;IAE5C,oCAAoC;IACpC,MAAM,kBAAkB,CAAC,MAAc;QACrC,EAAE,cAAc;QAEhB,wEAAwE;QACxE,IAAI,SAAS,eAAe,CAAC,WAAW;YACtC,0CAA0C;YAC1C,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM;YAC7C,aAAa;QACf;IACF;IAEA,0BAA0B;IAC1B,MAAM,uBAAuB,CAAC;QAC5B,IAAI,kBAAkB;YACpB,iBAAiB,SAAS,OAAO;QACnC;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8BACA,UACI,mDACA,kDACJ;QAEF,cAAY;;YAGX,wBAAwB,kCACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAU;kCAAgC;;;;;;kCAChD,6LAAC,qIAAA,CAAA,SAAM;wBACL,OAAO,SAAS,QAAQ;wBACxB,eAAe;wBACf,UAAU;;0CAEV,6LAAC,qIAAA,CAAA,gBAAa;gCACZ,WAAU;gCACV,cAAW;0CAEX,cAAA,6LAAC,qIAAA,CAAA,cAAW;oCAAC,aAAa,SAAS,QAAQ;;;;;;;;;;;0CAE7C,6LAAC,qIAAA,CAAA,gBAAa;0CACX,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,qIAAA,CAAA,aAAU;wCAAY,OAAO,KAAK,QAAQ;kDACxC;uCADc;;;;;;;;;;;;;;;;kCAMvB,6LAAC;wBAAK,WAAU;kCAAgC;;;;;;;;;;;;YAKnD,cAAc,mBACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,yIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,yIAAA,CAAA,oBAAiB;wBAAC,WAAU;;4BAE1B,sCACC,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,GAAG;oCACnC,UAAU,gBAAgB,KAAK;oCAC/B,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAM9B,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,yIAAA,CAAA,qBAAkB;oCACjB,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,KAAK;oCACpC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,KAAK,YACjB,mCACA,IACJ;;;;;;;;;;;4BAML,CAAC,WACA,YAAY,GAAG,CAAC,CAAC,YAAY;gCAC3B,IAAI,OAAO,eAAe,UAAU;oCAClC,qBACE,6LAAC,yIAAA,CAAA,iBAAc;kDACb,cAAA,6LAAC,yIAAA,CAAA,qBAAkB;;;;;uCADA;;;;;gCAIzB;gCAEA,qBACE,6LAAC,yIAAA,CAAA,iBAAc;8CACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;wCACb,MAAK;wCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;wCAC5C,UAAU,gBAAgB;wCAC1B,gBACE,gBAAgB,aAAa,SAAS;kDAGvC;;;;;;mCATgB,CAAC,KAAK,EAAE,YAAY;;;;;4BAa7C;4BAGD,yBACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;;wCAAU;sDACnB,6LAAC;4CAAK,WAAU;sDAAa;;;;;;wCAAmB;wCAAI;sDACzD,6LAAC;4CAAK,WAAU;sDAAe;;;;;;wCAC9B,2BACC,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;0CASrD,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,yIAAA,CAAA,iBAAc;oCACb,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,cAAc,GAAG;oCACjD,iBAAe,gBAAgB,cAAc;oCAC7C,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBAAgB,cAAc,YAC1B,mCACA,IACJ;;;;;;;;;;;4BAML,sCACC,6LAAC,yIAAA,CAAA,iBAAc;0CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,CAAC,IAAM,gBAAgB,YAAY;oCAC5C,UAAU,gBAAgB,cAAc;oCACxC,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUpC,iBAAiB,eAAe,2BAC/B,6LAAC;gBAAI,WAAU;0BACZ,eAAe,IACd,2BAEA;;wBAAE;wBACS,WAAW;wBAAM;wBAAK,WAAW;wBAAI;wBAAK;wBAAY;wBAAI;;;;;;;;;;;;;;AAQjF;GA9QgB;KAAA", "debugId": null}}, {"offset": {"line": 10354, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-debounced-search.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useRef, useCallback } from \"react\";\r\n\r\ninterface UseDebouncedSearchOptions {\r\n  initialValue?: string;\r\n  delay?: number;\r\n  minLength?: number;\r\n  onSearch?: (value: string) => void;\r\n  onTyping?: (value: string) => void;\r\n  onClear?: () => void;\r\n}\r\n\r\n/**\r\n * Hook for debounced search input\r\n *\r\n * @param options Configuration options\r\n * @returns Search state and handlers\r\n */\r\nexport function useDebouncedSearch({\r\n  initialValue = \"\",\r\n  delay = 300,\r\n  minLength = 0,\r\n  onSearch,\r\n  onTyping,\r\n  onClear,\r\n}: UseDebouncedSearchOptions = {}) {\r\n  const [inputValue, setInputValue] = useState(initialValue);\r\n  const [debouncedValue, setDebouncedValue] = useState(initialValue);\r\n  const [isSearching, setIsSearching] = useState(false);\r\n  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);\r\n  const previousValueRef = useRef(initialValue);\r\n\r\n  // Clear the debounce timer on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (debounceTimerRef.current) {\r\n        clearTimeout(debounceTimerRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  // Update debounced value after delay\r\n  useEffect(() => {\r\n    // Skip if the value hasn't changed\r\n    if (inputValue === previousValueRef.current) {\r\n      return;\r\n    }\r\n\r\n    // Call onTyping callback immediately\r\n    if (onTyping) {\r\n      onTyping(inputValue);\r\n    }\r\n\r\n    // Clear any existing timer\r\n    if (debounceTimerRef.current) {\r\n      clearTimeout(debounceTimerRef.current);\r\n    }\r\n\r\n    // Set searching state\r\n    setIsSearching(inputValue.length >= minLength);\r\n\r\n    // Set a new timer\r\n    debounceTimerRef.current = setTimeout(() => {\r\n      setDebouncedValue(inputValue);\r\n      previousValueRef.current = inputValue;\r\n\r\n      // Only trigger search if value meets minimum length\r\n      if (inputValue.length >= minLength) {\r\n        if (onSearch) {\r\n          onSearch(inputValue);\r\n        }\r\n      } else if (inputValue === \"\" && onClear) {\r\n        onClear();\r\n      }\r\n\r\n      setIsSearching(false);\r\n    }, delay);\r\n  }, [inputValue, delay, minLength, onSearch, onTyping, onClear]);\r\n\r\n  // Handle input change\r\n  const handleChange = useCallback((value: string) => {\r\n    setInputValue(value);\r\n  }, []);\r\n\r\n  // Clear search\r\n  const clearSearch = useCallback(() => {\r\n    setInputValue(\"\");\r\n    setDebouncedValue(\"\");\r\n    previousValueRef.current = \"\";\r\n\r\n    if (onClear) {\r\n      onClear();\r\n    }\r\n  }, [onClear]);\r\n\r\n  // Reset to initial value\r\n  const resetSearch = useCallback(() => {\r\n    setInputValue(initialValue);\r\n    setDebouncedValue(initialValue);\r\n    previousValueRef.current = initialValue;\r\n\r\n    if (initialValue === \"\" && onClear) {\r\n      onClear();\r\n    } else if (initialValue.length >= minLength && onSearch) {\r\n      onSearch(initialValue);\r\n    }\r\n  }, [initialValue, minLength, onSearch, onClear]);\r\n\r\n  // Force immediate search\r\n  const forceSearch = useCallback(() => {\r\n    if (debounceTimerRef.current) {\r\n      clearTimeout(debounceTimerRef.current);\r\n    }\r\n\r\n    setDebouncedValue(inputValue);\r\n    previousValueRef.current = inputValue;\r\n\r\n    if (inputValue.length >= minLength) {\r\n      if (onSearch) {\r\n        onSearch(inputValue);\r\n      }\r\n    } else if (inputValue === \"\" && onClear) {\r\n      onClear();\r\n    }\r\n\r\n    setIsSearching(false);\r\n  }, [inputValue, minLength, onSearch, onClear]);\r\n\r\n  return {\r\n    inputValue,\r\n    debouncedValue,\r\n    isSearching,\r\n    handleChange,\r\n    clearSearch,\r\n    resetSearch,\r\n    setInputValue,\r\n    forceSearch,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAmBO,SAAS,mBAAmB,EACjC,eAAe,EAAE,EACjB,QAAQ,GAAG,EACX,YAAY,CAAC,EACb,QAAQ,EACR,QAAQ,EACR,OAAO,EACmB,GAAG,CAAC,CAAC;;IAC/B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACvD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAEhC,sCAAsC;IACtC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;gDAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;uCAAG,EAAE;IAEL,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,mCAAmC;YACnC,IAAI,eAAe,iBAAiB,OAAO,EAAE;gBAC3C;YACF;YAEA,qCAAqC;YACrC,IAAI,UAAU;gBACZ,SAAS;YACX;YAEA,2BAA2B;YAC3B,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,sBAAsB;YACtB,eAAe,WAAW,MAAM,IAAI;YAEpC,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;gDAAW;oBACpC,kBAAkB;oBAClB,iBAAiB,OAAO,GAAG;oBAE3B,oDAAoD;oBACpD,IAAI,WAAW,MAAM,IAAI,WAAW;wBAClC,IAAI,UAAU;4BACZ,SAAS;wBACX;oBACF,OAAO,IAAI,eAAe,MAAM,SAAS;wBACvC;oBACF;oBAEA,eAAe;gBACjB;+CAAG;QACL;uCAAG;QAAC;QAAY;QAAO;QAAW;QAAU;QAAU;KAAQ;IAE9D,sBAAsB;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YAChC,cAAc;QAChB;uDAAG,EAAE;IAEL,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC9B,cAAc;YACd,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;YAE3B,IAAI,SAAS;gBACX;YACF;QACF;sDAAG;QAAC;KAAQ;IAEZ,yBAAyB;IACzB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC9B,cAAc;YACd,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;YAE3B,IAAI,iBAAiB,MAAM,SAAS;gBAClC;YACF,OAAO,IAAI,aAAa,MAAM,IAAI,aAAa,UAAU;gBACvD,SAAS;YACX;QACF;sDAAG;QAAC;QAAc;QAAW;QAAU;KAAQ;IAE/C,yBAAyB;IACzB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE;YAC9B,IAAI,iBAAiB,OAAO,EAAE;gBAC5B,aAAa,iBAAiB,OAAO;YACvC;YAEA,kBAAkB;YAClB,iBAAiB,OAAO,GAAG;YAE3B,IAAI,WAAW,MAAM,IAAI,WAAW;gBAClC,IAAI,UAAU;oBACZ,SAAS;gBACX;YACF,OAAO,IAAI,eAAe,MAAM,SAAS;gBACvC;YACF;YAEA,eAAe;QACjB;sDAAG;QAAC;QAAY;QAAW;QAAU;KAAQ;IAE7C,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAxHgB", "debugId": null}}, {"offset": {"line": 10503, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/search-input.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useCallback, forwardRef } from \"react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Search, X } from \"lucide-react\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { useDebouncedSearch } from \"@/hooks/use-debounced-search\";\r\n\r\nexport interface SearchInputProps {\r\n  /**\r\n   * Placeholder text for the search input\r\n   */\r\n  placeholder?: string;\r\n  /**\r\n   * Initial search value\r\n   */\r\n  value?: string;\r\n  /**\r\n   * Search callback function\r\n   */\r\n  onSearch?: (query: string) => void;\r\n  /**\r\n   * Clear callback function\r\n   */\r\n  onClear?: () => void;\r\n  /**\r\n   * Debounce delay in milliseconds\r\n   */\r\n  debounceMs?: number;\r\n  /**\r\n   * Minimum length before triggering search\r\n   */\r\n  minLength?: number;\r\n  /**\r\n   * Whether to show search button\r\n   */\r\n  showSearchButton?: boolean;\r\n  /**\r\n   * Whether to show clear button\r\n   */\r\n  showClearButton?: boolean;\r\n  /**\r\n   * Search mode: 'debounced' | 'manual' | 'realtime'\r\n   */\r\n  mode?: \"debounced\" | \"manual\" | \"realtime\";\r\n  /**\r\n   * Additional class names\r\n   */\r\n  className?: string;\r\n  /**\r\n   * Input class names\r\n   */\r\n  inputClassName?: string;\r\n  /**\r\n   * Button class names\r\n   */\r\n  buttonClassName?: string;\r\n  /**\r\n   * Whether the search is loading\r\n   */\r\n  isLoading?: boolean;\r\n  /**\r\n   * Disabled state\r\n   */\r\n  disabled?: boolean;\r\n  /**\r\n   * Size variant\r\n   */\r\n  size?: \"sm\" | \"default\" | \"lg\";\r\n}\r\n\r\n/**\r\n * Standardized search input component with consistent behavior\r\n */\r\nexport const SearchInput = forwardRef<HTMLInputElement, SearchInputProps>(\r\n  (\r\n    {\r\n      placeholder = \"Search...\",\r\n      value = \"\",\r\n      onSearch,\r\n      onClear,\r\n      debounceMs = 300,\r\n      minLength = 0,\r\n      showSearchButton = false,\r\n      showClearButton = true,\r\n      mode = \"debounced\",\r\n      className,\r\n      inputClassName,\r\n      buttonClassName,\r\n      isLoading = false,\r\n      disabled = false,\r\n      size = \"default\",\r\n    },\r\n    ref\r\n  ) => {\r\n    const [manualValue, setManualValue] = useState(value);\r\n\r\n    // Use debounced search for debounced mode\r\n    const {\r\n      inputValue,\r\n      handleChange,\r\n      clearSearch,\r\n      forceSearch,\r\n      isSearching,\r\n    } = useDebouncedSearch({\r\n      initialValue: value,\r\n      delay: debounceMs,\r\n      minLength,\r\n      onSearch: mode === \"debounced\" ? onSearch : undefined,\r\n      onClear,\r\n    });\r\n\r\n    // Handle different search modes\r\n    const currentValue = mode === \"manual\" ? manualValue : inputValue;\r\n    const currentIsLoading = mode === \"debounced\" ? isSearching : isLoading;\r\n\r\n    const handleInputChange = useCallback(\r\n      (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const newValue = e.target.value;\r\n\r\n        if (mode === \"manual\") {\r\n          setManualValue(newValue);\r\n        } else if (mode === \"realtime\") {\r\n          onSearch?.(newValue);\r\n        } else {\r\n          handleChange(newValue);\r\n        }\r\n      },\r\n      [mode, handleChange, onSearch]\r\n    );\r\n\r\n    const handleSearchClick = useCallback(() => {\r\n      if (mode === \"manual\") {\r\n        onSearch?.(manualValue);\r\n      } else {\r\n        forceSearch();\r\n      }\r\n    }, [mode, manualValue, onSearch, forceSearch]);\r\n\r\n    const handleClearClick = useCallback(() => {\r\n      if (mode === \"manual\") {\r\n        setManualValue(\"\");\r\n        onClear?.();\r\n      } else {\r\n        clearSearch();\r\n      }\r\n    }, [mode, clearSearch, onClear]);\r\n\r\n    const handleKeyDown = useCallback(\r\n      (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n        if (e.key === \"Enter\") {\r\n          e.preventDefault();\r\n          handleSearchClick();\r\n        }\r\n      },\r\n      [handleSearchClick]\r\n    );\r\n\r\n    const sizeClasses = {\r\n      sm: \"h-8 text-sm\",\r\n      default: \"h-9\",\r\n      lg: \"h-10\",\r\n    };\r\n\r\n    const buttonSizeClasses = {\r\n      sm: \"h-8 px-2\",\r\n      default: \"h-9 px-3\",\r\n      lg: \"h-10 px-4\",\r\n    };\r\n\r\n    return (\r\n      <div className={cn(\"flex items-center gap-2\", className)}>\r\n        <div className=\"relative flex-1\">\r\n          <Search className=\"absolute left-2.5 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\r\n          <Input\r\n            ref={ref}\r\n            type=\"search\"\r\n            placeholder={placeholder}\r\n            value={currentValue}\r\n            onChange={handleInputChange}\r\n            onKeyDown={handleKeyDown}\r\n            disabled={disabled || currentIsLoading}\r\n            className={cn(\r\n              \"pl-8\",\r\n              showClearButton && currentValue && \"pr-8\",\r\n              sizeClasses[size],\r\n              inputClassName\r\n            )}\r\n          />\r\n          {showClearButton && currentValue && (\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleClearClick}\r\n              disabled={disabled || currentIsLoading}\r\n              className=\"absolute right-2.5 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground disabled:opacity-50\"\r\n            >\r\n              <X className=\"h-4 w-4\" />\r\n              <span className=\"sr-only\">Clear search</span>\r\n            </button>\r\n          )}\r\n        </div>\r\n\r\n        {showSearchButton && (\r\n          <Button\r\n            type=\"button\"\r\n            variant=\"outline\"\r\n            onClick={handleSearchClick}\r\n            disabled={disabled || currentIsLoading}\r\n            className={cn(buttonSizeClasses[size], buttonClassName)}\r\n          >\r\n            {currentIsLoading ? (\r\n              <span className=\"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\r\n            ) : (\r\n              <Search className=\"h-4 w-4\" />\r\n            )}\r\n            <span className=\"sr-only\">Search</span>\r\n          </Button>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nSearchInput.displayName = \"SearchInput\";\r\n\r\n/**\r\n * Simple search input for basic use cases\r\n */\r\nexport function SimpleSearchInput({\r\n  placeholder = \"Search...\",\r\n  onSearch,\r\n  className,\r\n  ...props\r\n}: {\r\n  placeholder?: string;\r\n  onSearch?: (query: string) => void;\r\n  className?: string;\r\n} & Omit<SearchInputProps, \"onSearch\">) {\r\n  return (\r\n    <SearchInput\r\n      placeholder={placeholder}\r\n      onSearch={onSearch}\r\n      mode=\"debounced\"\r\n      showClearButton={true}\r\n      className={cn(\"max-w-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\n/**\r\n * Manual search input with search button\r\n */\r\nexport function ManualSearchInput({\r\n  placeholder = \"Search...\",\r\n  onSearch,\r\n  className,\r\n  ...props\r\n}: {\r\n  placeholder?: string;\r\n  onSearch?: (query: string) => void;\r\n  className?: string;\r\n} & Omit<SearchInputProps, \"onSearch\" | \"mode\" | \"showSearchButton\">) {\r\n  return (\r\n    <SearchInput\r\n      placeholder={placeholder}\r\n      onSearch={onSearch}\r\n      mode=\"manual\"\r\n      showSearchButton={true}\r\n      showClearButton={true}\r\n      className={className}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\n/**\r\n * Real-time search input for instant filtering\r\n */\r\nexport function RealtimeSearchInput({\r\n  placeholder = \"Search...\",\r\n  onSearch,\r\n  className,\r\n  ...props\r\n}: {\r\n  placeholder?: string;\r\n  onSearch?: (query: string) => void;\r\n  className?: string;\r\n} & Omit<SearchInputProps, \"onSearch\" | \"mode\">) {\r\n  return (\r\n    <SearchInput\r\n      placeholder={placeholder}\r\n      onSearch={onSearch}\r\n      mode=\"realtime\"\r\n      showClearButton={true}\r\n      className={cn(\"max-w-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\n/**\r\n * Search input configurations for different use cases\r\n */\r\nexport const SearchConfigs = {\r\n  /**\r\n   * For large datasets that need server-side search\r\n   */\r\n  serverSide: {\r\n    mode: \"debounced\" as const,\r\n    debounceMs: 500,\r\n    minLength: 2,\r\n    showSearchButton: false,\r\n    showClearButton: true,\r\n  },\r\n\r\n  /**\r\n   * For client-side filtering of small datasets\r\n   */\r\n  clientSide: {\r\n    mode: \"realtime\" as const,\r\n    debounceMs: 100,\r\n    minLength: 0,\r\n    showSearchButton: false,\r\n    showClearButton: true,\r\n  },\r\n\r\n  /**\r\n   * For manual search with explicit search action\r\n   */\r\n  manual: {\r\n    mode: \"manual\" as const,\r\n    showSearchButton: true,\r\n    showClearButton: true,\r\n  },\r\n\r\n  /**\r\n   * For global search functionality\r\n   */\r\n  global: {\r\n    mode: \"debounced\" as const,\r\n    debounceMs: 300,\r\n    minLength: 1,\r\n    showSearchButton: false,\r\n    showClearButton: true,\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;;AA2EO,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,KAClC,CACE,EACE,cAAc,WAAW,EACzB,QAAQ,EAAE,EACV,QAAQ,EACR,OAAO,EACP,aAAa,GAAG,EAChB,YAAY,CAAC,EACb,mBAAmB,KAAK,EACxB,kBAAkB,IAAI,EACtB,OAAO,WAAW,EAClB,SAAS,EACT,cAAc,EACd,eAAe,EACf,YAAY,KAAK,EACjB,WAAW,KAAK,EAChB,OAAO,SAAS,EACjB,EACD;;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,0CAA0C;IAC1C,MAAM,EACJ,UAAU,EACV,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW,EACZ,GAAG,CAAA,GAAA,6IAAA,CAAA,qBAAkB,AAAD,EAAE;QACrB,cAAc;QACd,OAAO;QACP;QACA,UAAU,SAAS,cAAc,WAAW;QAC5C;IACF;IAEA,gCAAgC;IAChC,MAAM,eAAe,SAAS,WAAW,cAAc;IACvD,MAAM,mBAAmB,SAAS,cAAc,cAAc;IAE9D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAClC,CAAC;YACC,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;YAE/B,IAAI,SAAS,UAAU;gBACrB,eAAe;YACjB,OAAO,IAAI,SAAS,YAAY;gBAC9B,WAAW;YACb,OAAO;gBACL,aAAa;YACf;QACF;qDACA;QAAC;QAAM;QAAc;KAAS;IAGhC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACpC,IAAI,SAAS,UAAU;gBACrB,WAAW;YACb,OAAO;gBACL;YACF;QACF;qDAAG;QAAC;QAAM;QAAa;QAAU;KAAY;IAE7C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YACnC,IAAI,SAAS,UAAU;gBACrB,eAAe;gBACf;YACF,OAAO;gBACL;YACF;QACF;oDAAG;QAAC;QAAM;QAAa;KAAQ;IAE/B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAC9B,CAAC;YACC,IAAI,EAAE,GAAG,KAAK,SAAS;gBACrB,EAAE,cAAc;gBAChB;YACF;QACF;iDACA;QAAC;KAAkB;IAGrB,MAAM,cAAc;QAClB,IAAI;QACJ,SAAS;QACT,IAAI;IACN;IAEA,MAAM,oBAAoB;QACxB,IAAI;QACJ,SAAS;QACT,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;;0BAC5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAClB,6LAAC,oIAAA,CAAA,QAAK;wBACJ,KAAK;wBACL,MAAK;wBACL,aAAa;wBACb,OAAO;wBACP,UAAU;wBACV,WAAW;wBACX,UAAU,YAAY;wBACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,mBAAmB,gBAAgB,QACnC,WAAW,CAAC,KAAK,EACjB;;;;;;oBAGH,mBAAmB,8BAClB,6LAAC;wBACC,MAAK;wBACL,SAAS;wBACT,UAAU,YAAY;wBACtB,WAAU;;0CAEV,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;YAK/B,kCACC,6LAAC,qIAAA,CAAA,SAAM;gBACL,MAAK;gBACL,SAAQ;gBACR,SAAS;gBACT,UAAU,YAAY;gBACtB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB,CAAC,KAAK,EAAE;;oBAEtC,iCACC,6LAAC;wBAAK,WAAU;;;;;6CAEhB,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;kCAEpB,6LAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;;AAKpC;;QApHM,6IAAA,CAAA,qBAAkB;;;KA9Bb;AAqJb,YAAY,WAAW,GAAG;AAKnB,SAAS,kBAAkB,EAChC,cAAc,WAAW,EACzB,QAAQ,EACR,SAAS,EACT,GAAG,OAKiC;IACpC,qBACE,6LAAC;QACC,aAAa;QACb,UAAU;QACV,MAAK;QACL,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MApBgB;AAyBT,SAAS,kBAAkB,EAChC,cAAc,WAAW,EACzB,QAAQ,EACR,SAAS,EACT,GAAG,OAK+D;IAClE,qBACE,6LAAC;QACC,aAAa;QACb,UAAU;QACV,MAAK;QACL,kBAAkB;QAClB,iBAAiB;QACjB,WAAW;QACV,GAAG,KAAK;;;;;;AAGf;MArBgB;AA0BT,SAAS,oBAAoB,EAClC,cAAc,WAAW,EACzB,QAAQ,EACR,SAAS,EACT,GAAG,OAK0C;IAC7C,qBACE,6LAAC;QACC,aAAa;QACb,UAAU;QACV,MAAK;QACL,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;MApBgB;AAyBT,MAAM,gBAAgB;IAC3B;;GAEC,GACD,YAAY;QACV,MAAM;QACN,YAAY;QACZ,WAAW;QACX,kBAAkB;QAClB,iBAAiB;IACnB;IAEA;;GAEC,GACD,YAAY;QACV,MAAM;QACN,YAAY;QACZ,WAAW;QACX,kBAAkB;QAClB,iBAAiB;IACnB;IAEA;;GAEC,GACD,QAAQ;QACN,MAAM;QACN,kBAAkB;QAClB,iBAAiB;IACnB;IAEA;;GAEC,GACD,QAAQ;QACN,MAAM;QACN,YAAY;QACZ,WAAW;QACX,kBAAkB;QAClB,iBAAiB;IACnB;AACF", "debugId": null}}, {"offset": {"line": 10808, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 10946, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/export-utils.ts"], "sourcesContent": ["import { Table } from \"@tanstack/react-table\";\r\nimport * as XLSX from \"xlsx\";\r\nimport { format } from \"date-fns\";\r\n\r\n/**\r\n * Download table data as Excel file\r\n */\r\nexport function downloadTableAsExcel<T>(\r\n  table: Table<T>,\r\n  filename: string = \"export\"\r\n) {\r\n  // Get visible columns\r\n  const visibleColumns = table.getVisibleLeafColumns();\r\n\r\n  // Create header row with column names\r\n  const headers = visibleColumns.map((column) => {\r\n    const headerContent = column.columnDef.header;\r\n    return typeof headerContent === \"string\" ? headerContent : column.id;\r\n  });\r\n\r\n  // Get all rows data\r\n  const rows = table.getRowModel().rows;\r\n\r\n  // Create data array with header row\r\n  const data = [headers];\r\n\r\n  // Add data rows\r\n  rows.forEach((row) => {\r\n    const rowData: any[] = [];\r\n    visibleColumns.forEach((column) => {\r\n      // Get cell value\r\n      const cell = row.getVisibleCells().find((c) => c.column.id === column.id);\r\n      if (cell) {\r\n        // Try to get the raw value if possible\r\n        const value = cell.getValue();\r\n        rowData.push(value);\r\n      } else {\r\n        rowData.push(\"\");\r\n      }\r\n    });\r\n    data.push(rowData);\r\n  });\r\n\r\n  // Create worksheet\r\n  const ws = XLSX.utils.aoa_to_sheet(data);\r\n\r\n  // Create workbook\r\n  const wb = XLSX.utils.book_new();\r\n  XLSX.utils.book_append_sheet(wb, ws, \"Sheet1\");\r\n\r\n  // Generate Excel file and trigger download\r\n  XLSX.writeFile(wb, `${filename}.xlsx`);\r\n}\r\n\r\n/**\r\n * Export array of objects to Excel with automatic timestamp\r\n */\r\nexport function exportDataToExcel<T extends Record<string, any>>(\r\n  data: T[],\r\n  filename: string,\r\n  options: {\r\n    sheetName?: string;\r\n    excludeColumns?: string[];\r\n    includeTimestamp?: boolean;\r\n    customHeaders?: Record<string, string>;\r\n  } = {}\r\n) {\r\n  const {\r\n    sheetName = \"Sheet1\",\r\n    excludeColumns = [],\r\n    includeTimestamp = true,\r\n    customHeaders = {},\r\n  } = options;\r\n\r\n  if (!data || data.length === 0) {\r\n    throw new Error(\"No data to export\");\r\n  }\r\n\r\n  // Get all unique keys from the data\r\n  const allKeys = Array.from(\r\n    new Set(data.flatMap((item) => Object.keys(item)))\r\n  ).filter((key) => !excludeColumns.includes(key));\r\n\r\n  // Create headers with custom names if provided\r\n  const headers = allKeys.map(\r\n    (key) => customHeaders[key] || formatHeaderName(key)\r\n  );\r\n\r\n  // Create data rows\r\n  const rows = data.map((item) =>\r\n    allKeys.map((key) => formatCellValue(item[key]))\r\n  );\r\n\r\n  // Create worksheet data\r\n  const worksheetData = [headers, ...rows];\r\n  const ws = XLSX.utils.aoa_to_sheet(worksheetData);\r\n\r\n  // Auto-size columns\r\n  const colWidths = headers.map((header, index) => {\r\n    const maxLength = Math.max(\r\n      header.length,\r\n      ...rows.map((row) => String(row[index] || \"\").length)\r\n    );\r\n    return { wch: Math.min(Math.max(maxLength + 2, 10), 50) };\r\n  });\r\n  ws[\"!cols\"] = colWidths;\r\n\r\n  // Create workbook\r\n  const wb = XLSX.utils.book_new();\r\n  XLSX.utils.book_append_sheet(wb, ws, sheetName);\r\n\r\n  // Generate filename with timestamp if requested\r\n  const finalFilename = includeTimestamp\r\n    ? `${filename}_${format(new Date(), \"yyyy-MM-dd_HH-mm-ss\")}`\r\n    : filename;\r\n\r\n  // Download file\r\n  XLSX.writeFile(wb, `${finalFilename}.xlsx`);\r\n}\r\n\r\n/**\r\n * Format header names from camelCase/snake_case to readable format\r\n */\r\nfunction formatHeaderName(key: string): string {\r\n  return key\r\n    .replace(/([A-Z])/g, \" $1\") // Add space before capital letters\r\n    .replace(/_/g, \" \") // Replace underscores with spaces\r\n    .replace(/\\b\\w/g, (l) => l.toUpperCase()) // Capitalize first letter of each word\r\n    .trim();\r\n}\r\n\r\n/**\r\n * Format cell values for Excel export\r\n */\r\nfunction formatCellValue(value: any): any {\r\n  if (value === null || value === undefined) {\r\n    return \"\";\r\n  }\r\n\r\n  if (typeof value === \"object\") {\r\n    // Handle dates\r\n    if (value instanceof Date) {\r\n      return format(value, \"yyyy-MM-dd HH:mm:ss\");\r\n    }\r\n\r\n    // Handle objects with name property (like relations)\r\n    if (value.name) {\r\n      return value.name;\r\n    }\r\n\r\n    // Convert other objects to JSON string\r\n    return JSON.stringify(value);\r\n  }\r\n\r\n  return value;\r\n}\r\n\r\n/**\r\n * Export table data with custom formatting\r\n */\r\nexport function exportTableData(\r\n  data: any[],\r\n  columns: Array<{\r\n    key: string;\r\n    header: string;\r\n    format?: (value: any) => string;\r\n  }>,\r\n  filename: string\r\n) {\r\n  if (!data || data.length === 0) {\r\n    throw new Error(\"No data to export\");\r\n  }\r\n\r\n  // Create headers\r\n  const headers = columns.map((col) => col.header);\r\n\r\n  // Create data rows with custom formatting\r\n  const rows = data.map((item) =>\r\n    columns.map((col) => {\r\n      const value = getNestedValue(item, col.key);\r\n      return col.format ? col.format(value) : formatCellValue(value);\r\n    })\r\n  );\r\n\r\n  // Create worksheet\r\n  const ws = XLSX.utils.aoa_to_sheet([headers, ...rows]);\r\n\r\n  // Auto-size columns\r\n  const colWidths = headers.map((header, index) => {\r\n    const maxLength = Math.max(\r\n      header.length,\r\n      ...rows.map((row) => String(row[index] || \"\").length)\r\n    );\r\n    return { wch: Math.min(Math.max(maxLength + 2, 10), 50) };\r\n  });\r\n  ws[\"!cols\"] = colWidths;\r\n\r\n  // Create workbook\r\n  const wb = XLSX.utils.book_new();\r\n  XLSX.utils.book_append_sheet(wb, ws, \"Export\");\r\n\r\n  // Download with timestamp\r\n  const finalFilename = `${filename}_${format(\r\n    new Date(),\r\n    \"yyyy-MM-dd_HH-mm-ss\"\r\n  )}`;\r\n  XLSX.writeFile(wb, `${finalFilename}.xlsx`);\r\n}\r\n\r\n/**\r\n * Get nested object value by key path\r\n */\r\nfunction getNestedValue(obj: any, path: string): any {\r\n  return path.split(\".\").reduce((current, key) => current?.[key], obj);\r\n}\r\n\r\n/**\r\n * Format date for export\r\n */\r\nexport function formatDateForExport(date: Date | string): string {\r\n  if (!date) return \"\";\r\n  const d = typeof date === \"string\" ? new Date(date) : date;\r\n  return d.toLocaleDateString();\r\n}\r\n\r\n/**\r\n * Format currency for export\r\n */\r\nexport function formatCurrencyForExport(value: number | string): string {\r\n  if (value === null || value === undefined) return \"\";\r\n  const num = typeof value === \"string\" ? parseFloat(value) : value;\r\n  return num.toFixed(2);\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;;;AAKO,SAAS,qBACd,KAAe,EACf,WAAmB,QAAQ;IAE3B,sBAAsB;IACtB,MAAM,iBAAiB,MAAM,qBAAqB;IAElD,sCAAsC;IACtC,MAAM,UAAU,eAAe,GAAG,CAAC,CAAC;QAClC,MAAM,gBAAgB,OAAO,SAAS,CAAC,MAAM;QAC7C,OAAO,OAAO,kBAAkB,WAAW,gBAAgB,OAAO,EAAE;IACtE;IAEA,oBAAoB;IACpB,MAAM,OAAO,MAAM,WAAW,GAAG,IAAI;IAErC,oCAAoC;IACpC,MAAM,OAAO;QAAC;KAAQ;IAEtB,gBAAgB;IAChB,KAAK,OAAO,CAAC,CAAC;QACZ,MAAM,UAAiB,EAAE;QACzB,eAAe,OAAO,CAAC,CAAC;YACtB,iBAAiB;YACjB,MAAM,OAAO,IAAI,eAAe,GAAG,IAAI,CAAC,CAAC,IAAM,EAAE,MAAM,CAAC,EAAE,KAAK,OAAO,EAAE;YACxE,IAAI,MAAM;gBACR,uCAAuC;gBACvC,MAAM,QAAQ,KAAK,QAAQ;gBAC3B,QAAQ,IAAI,CAAC;YACf,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;QACF;QACA,KAAK,IAAI,CAAC;IACZ;IAEA,mBAAmB;IACnB,MAAM,KAAK,gIAAA,CAAA,QAAU,CAAC,YAAY,CAAC;IAEnC,kBAAkB;IAClB,MAAM,KAAK,gIAAA,CAAA,QAAU,CAAC,QAAQ;IAC9B,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,IAAI,IAAI;IAErC,2CAA2C;IAC3C,CAAA,GAAA,gIAAA,CAAA,YAAc,AAAD,EAAE,IAAI,GAAG,SAAS,KAAK,CAAC;AACvC;AAKO,SAAS,kBACd,IAAS,EACT,QAAgB,EAChB,UAKI,CAAC,CAAC;IAEN,MAAM,EACJ,YAAY,QAAQ,EACpB,iBAAiB,EAAE,EACnB,mBAAmB,IAAI,EACvB,gBAAgB,CAAC,CAAC,EACnB,GAAG;IAEJ,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,MAAM,IAAI,MAAM;IAClB;IAEA,oCAAoC;IACpC,MAAM,UAAU,MAAM,IAAI,CACxB,IAAI,IAAI,KAAK,OAAO,CAAC,CAAC,OAAS,OAAO,IAAI,CAAC,SAC3C,MAAM,CAAC,CAAC,MAAQ,CAAC,eAAe,QAAQ,CAAC;IAE3C,+CAA+C;IAC/C,MAAM,UAAU,QAAQ,GAAG,CACzB,CAAC,MAAQ,aAAa,CAAC,IAAI,IAAI,iBAAiB;IAGlD,mBAAmB;IACnB,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC,OACrB,QAAQ,GAAG,CAAC,CAAC,MAAQ,gBAAgB,IAAI,CAAC,IAAI;IAGhD,wBAAwB;IACxB,MAAM,gBAAgB;QAAC;WAAY;KAAK;IACxC,MAAM,KAAK,gIAAA,CAAA,QAAU,CAAC,YAAY,CAAC;IAEnC,oBAAoB;IACpB,MAAM,YAAY,QAAQ,GAAG,CAAC,CAAC,QAAQ;QACrC,MAAM,YAAY,KAAK,GAAG,CACxB,OAAO,MAAM,KACV,KAAK,GAAG,CAAC,CAAC,MAAQ,OAAO,GAAG,CAAC,MAAM,IAAI,IAAI,MAAM;QAEtD,OAAO;YAAE,KAAK,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,YAAY,GAAG,KAAK;QAAI;IAC1D;IACA,EAAE,CAAC,QAAQ,GAAG;IAEd,kBAAkB;IAClB,MAAM,KAAK,gIAAA,CAAA,QAAU,CAAC,QAAQ;IAC9B,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,IAAI,IAAI;IAErC,gDAAgD;IAChD,MAAM,gBAAgB,mBAClB,GAAG,SAAS,CAAC,EAAE,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,QAAQ,wBAAwB,GAC1D;IAEJ,gBAAgB;IAChB,CAAA,GAAA,gIAAA,CAAA,YAAc,AAAD,EAAE,IAAI,GAAG,cAAc,KAAK,CAAC;AAC5C;AAEA;;CAEC,GACD,SAAS,iBAAiB,GAAW;IACnC,OAAO,IACJ,OAAO,CAAC,YAAY,OAAO,mCAAmC;KAC9D,OAAO,CAAC,MAAM,KAAK,kCAAkC;KACrD,OAAO,CAAC,SAAS,CAAC,IAAM,EAAE,WAAW,IAAI,uCAAuC;KAChF,IAAI;AACT;AAEA;;CAEC,GACD,SAAS,gBAAgB,KAAU;IACjC,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,eAAe;QACf,IAAI,iBAAiB,MAAM;YACzB,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,OAAO;QACvB;QAEA,qDAAqD;QACrD,IAAI,MAAM,IAAI,EAAE;YACd,OAAO,MAAM,IAAI;QACnB;QAEA,uCAAuC;QACvC,OAAO,KAAK,SAAS,CAAC;IACxB;IAEA,OAAO;AACT;AAKO,SAAS,gBACd,IAAW,EACX,OAIE,EACF,QAAgB;IAEhB,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,MAAM,IAAI,MAAM;IAClB;IAEA,iBAAiB;IACjB,MAAM,UAAU,QAAQ,GAAG,CAAC,CAAC,MAAQ,IAAI,MAAM;IAE/C,0CAA0C;IAC1C,MAAM,OAAO,KAAK,GAAG,CAAC,CAAC,OACrB,QAAQ,GAAG,CAAC,CAAC;YACX,MAAM,QAAQ,eAAe,MAAM,IAAI,GAAG;YAC1C,OAAO,IAAI,MAAM,GAAG,IAAI,MAAM,CAAC,SAAS,gBAAgB;QAC1D;IAGF,mBAAmB;IACnB,MAAM,KAAK,gIAAA,CAAA,QAAU,CAAC,YAAY,CAAC;QAAC;WAAY;KAAK;IAErD,oBAAoB;IACpB,MAAM,YAAY,QAAQ,GAAG,CAAC,CAAC,QAAQ;QACrC,MAAM,YAAY,KAAK,GAAG,CACxB,OAAO,MAAM,KACV,KAAK,GAAG,CAAC,CAAC,MAAQ,OAAO,GAAG,CAAC,MAAM,IAAI,IAAI,MAAM;QAEtD,OAAO;YAAE,KAAK,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,YAAY,GAAG,KAAK;QAAI;IAC1D;IACA,EAAE,CAAC,QAAQ,GAAG;IAEd,kBAAkB;IAClB,MAAM,KAAK,gIAAA,CAAA,QAAU,CAAC,QAAQ;IAC9B,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,IAAI,IAAI;IAErC,0BAA0B;IAC1B,MAAM,gBAAgB,GAAG,SAAS,CAAC,EAAE,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EACxC,IAAI,QACJ,wBACC;IACH,CAAA,GAAA,gIAAA,CAAA,YAAc,AAAD,EAAE,IAAI,GAAG,cAAc,KAAK,CAAC;AAC5C;AAEA;;CAEC,GACD,SAAS,eAAe,GAAQ,EAAE,IAAY;IAC5C,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,SAAS,MAAQ,SAAS,CAAC,IAAI,EAAE;AAClE;AAKO,SAAS,oBAAoB,IAAmB;IACrD,IAAI,CAAC,MAAM,OAAO;IAClB,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,EAAE,kBAAkB;AAC7B;AAKO,SAAS,wBAAwB,KAAsB;IAC5D,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAClD,MAAM,MAAM,OAAO,UAAU,WAAW,WAAW,SAAS;IAC5D,OAAO,IAAI,OAAO,CAAC;AACrB", "debugId": null}}, {"offset": {"line": 11111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/reports/components/report-data-table.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  <PERSON>,\r\n  CardContent,\r\n  CardDescription,\r\n  Card<PERSON>ooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\nimport { DataPagination } from \"@/components/ui/data-pagination\";\r\nimport { SearchInput } from \"@/components/ui/search-input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { downloadTableAsExcel } from \"@/lib/export-utils\";\r\nimport {\r\n  ColumnDef,\r\n  ColumnFiltersState,\r\n  SortingState,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable,\r\n} from \"@tanstack/react-table\";\r\nimport { ArrowDown, ArrowUp, Download } from \"lucide-react\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\ninterface DataTableProps<TData, TValue> {\r\n  columns: ColumnDef<TData, TValue>[];\r\n  data: TData[];\r\n  title?: string;\r\n  description?: string;\r\n  searchPlaceholder?: string;\r\n  searchColumn?: string;\r\n  exportFilename?: string;\r\n  showExport?: boolean;\r\n  showSearch?: boolean;\r\n  showPagination?: boolean;\r\n  rowClassName?: (row: any) => string;\r\n  // Mobile responsiveness props\r\n  mobileBreakpoint?: number;\r\n  enableMobileCards?: boolean;\r\n  // Server-side pagination props\r\n  pagination?: {\r\n    currentPage: number;\r\n    totalPages: number;\r\n    onPageChange: (page: number) => void;\r\n    pageSize?: number;\r\n    onPageSizeChange?: (pageSize: number) => void;\r\n    totalItems?: number;\r\n    isLoading?: boolean;\r\n  };\r\n  isLoading?: boolean;\r\n}\r\n\r\nexport function ReportDataTable<TData, TValue>({\r\n  columns,\r\n  data,\r\n  title,\r\n  description,\r\n  searchPlaceholder = \"Search...\",\r\n  searchColumn,\r\n  exportFilename = \"export\",\r\n  showExport = true,\r\n  showSearch = true,\r\n  showPagination = true,\r\n  rowClassName,\r\n  mobileBreakpoint = 768,\r\n  enableMobileCards = true,\r\n  pagination,\r\n  isLoading = false,\r\n}: DataTableProps<TData, TValue>) {\r\n  const [sorting, setSorting] = useState<SortingState>([]);\r\n  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);\r\n  const [isMobile, setIsMobile] = useState(false);\r\n\r\n  // Check for mobile view\r\n  useEffect(() => {\r\n    const checkMobile = () => {\r\n      setIsMobile(window.innerWidth < mobileBreakpoint);\r\n    };\r\n\r\n    checkMobile();\r\n    window.addEventListener(\"resize\", checkMobile);\r\n    return () => window.removeEventListener(\"resize\", checkMobile);\r\n  }, [mobileBreakpoint]);\r\n\r\n  const table = useReactTable({\r\n    data,\r\n    columns,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    // Use server-side pagination if pagination prop is provided\r\n    ...(pagination\r\n      ? {\r\n          manualPagination: true,\r\n          pageCount: pagination.totalPages,\r\n        }\r\n      : {\r\n          getPaginationRowModel: getPaginationRowModel(),\r\n        }),\r\n    onSortingChange: setSorting,\r\n    getSortedRowModel: getSortedRowModel(),\r\n    onColumnFiltersChange: setColumnFilters,\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    state: {\r\n      sorting,\r\n      columnFilters,\r\n      ...(pagination\r\n        ? {\r\n            pagination: {\r\n              pageIndex: pagination.currentPage - 1,\r\n              pageSize: pagination.pageSize || 10,\r\n            },\r\n          }\r\n        : {}),\r\n    },\r\n  });\r\n\r\n  const handleExport = () => {\r\n    downloadTableAsExcel(table, exportFilename);\r\n  };\r\n\r\n  // Render mobile card for a row\r\n  const renderMobileCard = (row: any, index: number) => {\r\n    return (\r\n      <Card key={`mobile-card-${index}`} className=\"mb-4\">\r\n        <CardContent className=\"p-4\">\r\n          <div className=\"space-y-2\">\r\n            {table.getAllColumns().map((column) => {\r\n              const cell = row\r\n                .getVisibleCells()\r\n                .find((c: any) => c.column.id === column.id);\r\n              if (!cell) return null;\r\n\r\n              const value = flexRender(\r\n                cell.column.columnDef.cell,\r\n                cell.getContext()\r\n              );\r\n\r\n              // Skip empty values\r\n              if (!value || value === \"-\" || value === \"\") return null;\r\n\r\n              return (\r\n                <div\r\n                  key={column.id}\r\n                  className=\"flex justify-between items-center\"\r\n                >\r\n                  <span className=\"text-sm font-medium text-muted-foreground\">\r\n                    {typeof column.columnDef.header === \"string\"\r\n                      ? column.columnDef.header\r\n                      : column.id}\r\n                    :\r\n                  </span>\r\n                  <span className=\"text-sm\">{value}</span>\r\n                </div>\r\n              );\r\n            })}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Card>\r\n      {(title || description || showSearch || showExport) && (\r\n        <CardHeader className=\"space-y-1\">\r\n          {title && <CardTitle>{title}</CardTitle>}\r\n          {description && <CardDescription>{description}</CardDescription>}\r\n          {(showSearch || showExport) && (\r\n            <div className=\"flex items-center justify-between\">\r\n              {showSearch && searchColumn && (\r\n                <SearchInput\r\n                  placeholder={searchPlaceholder}\r\n                  value={\r\n                    (table\r\n                      .getColumn(searchColumn)\r\n                      ?.getFilterValue() as string) ?? \"\"\r\n                  }\r\n                  onSearch={(value) =>\r\n                    table.getColumn(searchColumn)?.setFilterValue(value)\r\n                  }\r\n                  onClear={() =>\r\n                    table.getColumn(searchColumn)?.setFilterValue(\"\")\r\n                  }\r\n                  mode=\"realtime\"\r\n                  className=\"max-w-sm\"\r\n                />\r\n              )}\r\n              {showExport && (\r\n                <Button variant=\"outline\" onClick={handleExport}>\r\n                  <Download className=\"mr-2 h-4 w-4\" />\r\n                  Export\r\n                </Button>\r\n              )}\r\n            </div>\r\n          )}\r\n        </CardHeader>\r\n      )}\r\n      <CardContent>\r\n        {/* Mobile Card Layout */}\r\n        {isMobile && enableMobileCards ? (\r\n          <div className=\"space-y-4\">\r\n            {isLoading || pagination?.isLoading ? (\r\n              // Show skeleton cards when loading\r\n              Array.from({ length: pagination?.pageSize || 10 }).map(\r\n                (_, index) => (\r\n                  <Card key={`skeleton-card-${index}`} className=\"mb-4\">\r\n                    <CardContent className=\"p-4\">\r\n                      <div className=\"space-y-2\">\r\n                        <div className=\"h-4 bg-muted animate-pulse rounded w-3/4\" />\r\n                        <div className=\"h-4 bg-muted animate-pulse rounded w-1/2\" />\r\n                        <div className=\"h-4 bg-muted animate-pulse rounded w-2/3\" />\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                )\r\n              )\r\n            ) : table.getRowModel().rows?.length ? (\r\n              table\r\n                .getRowModel()\r\n                .rows.map((row, index) => renderMobileCard(row, index))\r\n            ) : (\r\n              <div className=\"text-center py-8 text-muted-foreground\">\r\n                No data available\r\n              </div>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          /* Desktop Table Layout */\r\n          <div className=\"rounded-md border overflow-x-auto\">\r\n            <Table>\r\n              <TableHeader>\r\n                {table.getHeaderGroups().map((headerGroup) => (\r\n                  <TableRow key={headerGroup.id}>\r\n                    {headerGroup.headers.map((header) => {\r\n                      return (\r\n                        <TableHead\r\n                          key={header.id}\r\n                          className=\"whitespace-nowrap\"\r\n                        >\r\n                          {header.isPlaceholder ? null : header.column.getCanSort() ? (\r\n                            <div\r\n                              className=\"flex items-center space-x-1 cursor-pointer\"\r\n                              onClick={header.column.getToggleSortingHandler()}\r\n                            >\r\n                              <span>\r\n                                {flexRender(\r\n                                  header.column.columnDef.header,\r\n                                  header.getContext()\r\n                                )}\r\n                              </span>\r\n                              {header.column.getIsSorted() === \"asc\" ? (\r\n                                <ArrowUp className=\"h-4 w-4\" />\r\n                              ) : header.column.getIsSorted() === \"desc\" ? (\r\n                                <ArrowDown className=\"h-4 w-4\" />\r\n                              ) : null}\r\n                            </div>\r\n                          ) : (\r\n                            flexRender(\r\n                              header.column.columnDef.header,\r\n                              header.getContext()\r\n                            )\r\n                          )}\r\n                        </TableHead>\r\n                      );\r\n                    })}\r\n                  </TableRow>\r\n                ))}\r\n              </TableHeader>\r\n              <TableBody>\r\n                {isLoading || pagination?.isLoading ? (\r\n                  // Show skeleton rows when loading\r\n                  Array.from({ length: pagination?.pageSize || 10 }).map(\r\n                    (_, index) => (\r\n                      <TableRow key={`skeleton-${index}`}>\r\n                        {columns.map((_, colIndex) => (\r\n                          <TableCell key={`skeleton-cell-${colIndex}`}>\r\n                            <div className=\"h-4 bg-muted animate-pulse rounded\" />\r\n                          </TableCell>\r\n                        ))}\r\n                      </TableRow>\r\n                    )\r\n                  )\r\n                ) : table.getRowModel().rows?.length ? (\r\n                  table.getRowModel().rows.map((row) => (\r\n                    <TableRow\r\n                      key={row.original.uniqueKey || row.id}\r\n                      data-state={row.getIsSelected() && \"selected\"}\r\n                      className={rowClassName ? rowClassName(row) : \"\"}\r\n                    >\r\n                      {row.getVisibleCells().map((cell) => (\r\n                        <TableCell key={cell.id}>\r\n                          {flexRender(\r\n                            cell.column.columnDef.cell,\r\n                            cell.getContext()\r\n                          )}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  ))\r\n                ) : (\r\n                  <TableRow>\r\n                    <TableCell\r\n                      colSpan={columns.length}\r\n                      className=\"h-24 text-center\"\r\n                    >\r\n                      No results.\r\n                    </TableCell>\r\n                  </TableRow>\r\n                )}\r\n              </TableBody>\r\n            </Table>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n      {showPagination && (\r\n        <CardFooter className=\"py-4\">\r\n          {pagination ? (\r\n            // Server-side pagination\r\n            <DataPagination\r\n              currentPage={pagination.currentPage}\r\n              totalPages={pagination.totalPages}\r\n              onPageChange={pagination.onPageChange}\r\n              pageSize={pagination.pageSize || 10}\r\n              onPageSizeChange={pagination.onPageSizeChange}\r\n              totalItems={pagination.totalItems || 0}\r\n              isLoading={pagination.isLoading || isLoading}\r\n              showPageSizeSelector={true}\r\n              showItemsInfo={true}\r\n              showFirstLastButtons={true}\r\n            />\r\n          ) : (\r\n            // Client-side pagination (fallback)\r\n            <div className=\"flex items-center justify-between space-x-2 w-full\">\r\n              <div className=\"flex-1 text-sm text-muted-foreground\">\r\n                Showing{\" \"}\r\n                <strong>\r\n                  {table.getState().pagination.pageIndex *\r\n                    table.getState().pagination.pageSize +\r\n                    1}\r\n                  -\r\n                  {Math.min(\r\n                    (table.getState().pagination.pageIndex + 1) *\r\n                      table.getState().pagination.pageSize,\r\n                    table.getFilteredRowModel().rows.length\r\n                  )}\r\n                </strong>{\" \"}\r\n                of <strong>{table.getFilteredRowModel().rows.length}</strong>{\" \"}\r\n                results\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={() => table.previousPage()}\r\n                  disabled={!table.getCanPreviousPage()}\r\n                >\r\n                  Previous\r\n                </Button>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={() => table.nextPage()}\r\n                  disabled={!table.getCanNextPage()}\r\n                >\r\n                  Next\r\n                </Button>\r\n                <Select\r\n                  value={table.getState().pagination.pageSize.toString()}\r\n                  onValueChange={(value) => {\r\n                    table.setPageSize(Number(value));\r\n                  }}\r\n                >\r\n                  <SelectTrigger className=\"h-8 w-[70px]\">\r\n                    <SelectValue\r\n                      placeholder={table.getState().pagination.pageSize}\r\n                    />\r\n                  </SelectTrigger>\r\n                  <SelectContent side=\"top\">\r\n                    {[10, 20, 30, 40, 50].map((pageSize) => (\r\n                      <SelectItem key={pageSize} value={pageSize.toString()}>\r\n                        {pageSize}\r\n                      </SelectItem>\r\n                    ))}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n            </div>\r\n          )}\r\n        </CardFooter>\r\n      )}\r\n    </Card>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AACA;AAOA;AAQA;AACA;AAAA;AAWA;AAAA;AAAA;AACA;;;AAzCA;;;;;;;;;;;AAuEO,SAAS,gBAA+B,EAC7C,OAAO,EACP,IAAI,EACJ,KAAK,EACL,WAAW,EACX,oBAAoB,WAAW,EAC/B,YAAY,EACZ,iBAAiB,QAAQ,EACzB,aAAa,IAAI,EACjB,aAAa,IAAI,EACjB,iBAAiB,IAAI,EACrB,YAAY,EACZ,mBAAmB,GAAG,EACtB,oBAAoB,IAAI,EACxB,UAAU,EACV,YAAY,KAAK,EACa;;IAC9B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,wBAAwB;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;yDAAc;oBAClB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;oCAAG;QAAC;KAAiB;IAErB,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B;QACA;QACA,iBAAiB,CAAA,GAAA,wKAAA,CAAA,kBAAe,AAAD;QAC/B,4DAA4D;QAC5D,GAAI,aACA;YACE,kBAAkB;YAClB,WAAW,WAAW,UAAU;QAClC,IACA;YACE,uBAAuB,CAAA,GAAA,wKAAA,CAAA,wBAAqB,AAAD;QAC7C,CAAC;QACL,iBAAiB;QACjB,mBAAmB,CAAA,GAAA,wKAAA,CAAA,oBAAiB,AAAD;QACnC,uBAAuB;QACvB,qBAAqB,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD;QACvC,OAAO;YACL;YACA;YACA,GAAI,aACA;gBACE,YAAY;oBACV,WAAW,WAAW,WAAW,GAAG;oBACpC,UAAU,WAAW,QAAQ,IAAI;gBACnC;YACF,IACA,CAAC,CAAC;QACR;IACF;IAEA,MAAM,eAAe;QACnB,CAAA,GAAA,gIAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO;IAC9B;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB,CAAC,KAAU;QAClC,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAA8B,WAAU;sBAC3C,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAI,WAAU;8BACZ,MAAM,aAAa,GAAG,GAAG,CAAC,CAAC;wBAC1B,MAAM,OAAO,IACV,eAAe,GACf,IAAI,CAAC,CAAC,IAAW,EAAE,MAAM,CAAC,EAAE,KAAK,OAAO,EAAE;wBAC7C,IAAI,CAAC,MAAM,OAAO;wBAElB,MAAM,QAAQ,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACrB,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;wBAGjB,oBAAoB;wBACpB,IAAI,CAAC,SAAS,UAAU,OAAO,UAAU,IAAI,OAAO;wBAEpD,qBACE,6LAAC;4BAEC,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;;wCACb,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK,WAChC,OAAO,SAAS,CAAC,MAAM,GACvB,OAAO,EAAE;wCAAC;;;;;;;8CAGhB,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;2BATtB,OAAO,EAAE;;;;;oBAYpB;;;;;;;;;;;WA/BK,CAAC,YAAY,EAAE,OAAO;;;;;IAoCrC;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;YACF,CAAC,SAAS,eAAe,cAAc,UAAU,mBAChD,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;oBACnB,uBAAS,6LAAC,mIAAA,CAAA,YAAS;kCAAE;;;;;;oBACrB,6BAAe,6LAAC,mIAAA,CAAA,kBAAe;kCAAE;;;;;;oBACjC,CAAC,cAAc,UAAU,mBACxB,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,8BACb,6LAAC,8IAAA,CAAA,cAAW;gCACV,aAAa;gCACb,OACE,AAAC,MACE,SAAS,CAAC,eACT,oBAA+B;gCAErC,UAAU,CAAC,QACT,MAAM,SAAS,CAAC,eAAe,eAAe;gCAEhD,SAAS,IACP,MAAM,SAAS,CAAC,eAAe,eAAe;gCAEhD,MAAK;gCACL,WAAU;;;;;;4BAGb,4BACC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;;kDACjC,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAQjD,6LAAC,mIAAA,CAAA,cAAW;0BAET,YAAY,kCACX,6LAAC;oBAAI,WAAU;8BACZ,aAAa,YAAY,YACxB,mCAAmC;oBACnC,MAAM,IAAI,CAAC;wBAAE,QAAQ,YAAY,YAAY;oBAAG,GAAG,GAAG,CACpD,CAAC,GAAG,sBACF,6LAAC,mIAAA,CAAA,OAAI;4BAAgC,WAAU;sCAC7C,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;2BALV,CAAC,cAAc,EAAE,OAAO;;;;oCAWrC,MAAM,WAAW,GAAG,IAAI,EAAE,SAC5B,MACG,WAAW,GACX,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,QAAU,iBAAiB,KAAK,wBAElD,6LAAC;wBAAI,WAAU;kCAAyC;;;;;;;;;;2BAM5D,wBAAwB,iBACxB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;;0CACJ,6LAAC,oIAAA,CAAA,cAAW;0CACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,6LAAC,oIAAA,CAAA,WAAQ;kDACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC;4CACxB,qBACE,6LAAC,oIAAA,CAAA,YAAS;gDAER,WAAU;0DAET,OAAO,aAAa,GAAG,OAAO,OAAO,MAAM,CAAC,UAAU,mBACrD,6LAAC;oDACC,WAAU;oDACV,SAAS,OAAO,MAAM,CAAC,uBAAuB;;sEAE9C,6LAAC;sEACE,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACR,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;;;;;;wDAGpB,OAAO,MAAM,CAAC,WAAW,OAAO,sBAC/B,6LAAC,+MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;mEACjB,OAAO,MAAM,CAAC,WAAW,OAAO,uBAClC,6LAAC,mNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;mEACnB;;;;;;2DAGN,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;+CAvBhB,OAAO,EAAE;;;;;wCA4BpB;uCAhCa,YAAY,EAAE;;;;;;;;;;0CAoCjC,6LAAC,oIAAA,CAAA,YAAS;0CACP,aAAa,YAAY,YACxB,kCAAkC;gCAClC,MAAM,IAAI,CAAC;oCAAE,QAAQ,YAAY,YAAY;gCAAG,GAAG,GAAG,CACpD,CAAC,GAAG,sBACF,6LAAC,oIAAA,CAAA,WAAQ;kDACN,QAAQ,GAAG,CAAC,CAAC,GAAG,yBACf,6LAAC,oIAAA,CAAA,YAAS;0DACR,cAAA,6LAAC;oDAAI,WAAU;;;;;;+CADD,CAAC,cAAc,EAAE,UAAU;;;;;uCAFhC,CAAC,SAAS,EAAE,OAAO;;;;gDASpC,MAAM,WAAW,GAAG,IAAI,EAAE,SAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC5B,6LAAC,oIAAA,CAAA,WAAQ;wCAEP,cAAY,IAAI,aAAa,MAAM;wCACnC,WAAW,eAAe,aAAa,OAAO;kDAE7C,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,6LAAC,oIAAA,CAAA,YAAS;0DACP,CAAA,GAAA,yLAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;+CAHH,KAAK,EAAE;;;;;uCALpB,IAAI,QAAQ,CAAC,SAAS,IAAI,IAAI,EAAE;;;;8DAezC,6LAAC,oIAAA,CAAA,WAAQ;8CACP,cAAA,6LAAC,oIAAA,CAAA,YAAS;wCACR,SAAS,QAAQ,MAAM;wCACvB,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUd,gCACC,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACnB,aACC,yBAAyB;8BACzB,6LAAC,iJAAA,CAAA,iBAAc;oBACb,aAAa,WAAW,WAAW;oBACnC,YAAY,WAAW,UAAU;oBACjC,cAAc,WAAW,YAAY;oBACrC,UAAU,WAAW,QAAQ,IAAI;oBACjC,kBAAkB,WAAW,gBAAgB;oBAC7C,YAAY,WAAW,UAAU,IAAI;oBACrC,WAAW,WAAW,SAAS,IAAI;oBACnC,sBAAsB;oBACtB,eAAe;oBACf,sBAAsB;;;;;2BAGxB,oCAAoC;8BACpC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;gCAAuC;gCAC5C;8CACR,6LAAC;;wCACE,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GACpC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,GACpC;wCAAE;wCAEH,KAAK,GAAG,CACP,CAAC,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG,CAAC,IACxC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EACtC,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;;;;;;;gCAEjC;gCAAI;8CACX,6LAAC;8CAAQ,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;;;;;;gCAAW;gCAAI;;;;;;;sCAGpE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,MAAM,YAAY;oCACjC,UAAU,CAAC,MAAM,kBAAkB;8CACpC;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,MAAM,QAAQ;oCAC7B,UAAU,CAAC,MAAM,cAAc;8CAChC;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ;oCACpD,eAAe,CAAC;wCACd,MAAM,WAAW,CAAC,OAAO;oCAC3B;;sDAEA,6LAAC,qIAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;gDACV,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;;;;;;;;;;;sDAGrD,6LAAC,qIAAA,CAAA,gBAAa;4CAAC,MAAK;sDACjB;gDAAC;gDAAI;gDAAI;gDAAI;gDAAI;6CAAG,CAAC,GAAG,CAAC,CAAC,yBACzB,6LAAC,qIAAA,CAAA,aAAU;oDAAgB,OAAO,SAAS,QAAQ;8DAChD;mDADc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAavC;GAnVgB;;QAgCA,yLAAA,CAAA,gBAAa;;;KAhCb", "debugId": null}}, {"offset": {"line": 11671, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/sale-items/api/sale-items-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\n\r\nexport interface SaleItemFilters {\r\n  page?: number;\r\n  limit?: number;\r\n  branch_id?: number;\r\n  region_id?: number;\r\n  start_date?: string;\r\n  end_date?: string;\r\n  product_id?: number;\r\n  search?: string;\r\n  format?: 'json' | 'excel';\r\n}\r\n\r\nexport interface SaleItem {\r\n  id: number;\r\n  product_id: number;\r\n  product_name: string;\r\n  product_sku: string;\r\n  category_name: string;\r\n  quantity: number;\r\n  unit_price: number;\r\n  total_price: number;\r\n  sale_id: number;\r\n  sale_reference: string;\r\n  branch_id: number;\r\n  branch_name: string;\r\n  region_id: number;\r\n  region_name: string;\r\n  user_name: string;\r\n  created_at: string;\r\n  sale_date: string;\r\n}\r\n\r\nexport interface SaleItemsResponse {\r\n  data: SaleItem[];\r\n  pagination: {\r\n    page: number;\r\n    limit: number;\r\n    total: number;\r\n    totalPages: number;\r\n  };\r\n}\r\n\r\n/**\r\n * Sale Items Service\r\n * Handles API calls for sale items\r\n */\r\nconst saleItemsService = {\r\n  /**\r\n   * Get sale items with filtering and pagination\r\n   */\r\n  getSaleItems: async (filters?: SaleItemFilters): Promise<SaleItemsResponse> => {\r\n    const response = await apiClient.get<SaleItemsResponse>(\"/sale-items\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Export sale items to Excel\r\n   */\r\n  exportSaleItems: async (filters?: SaleItemFilters): Promise<Blob> => {\r\n    // Remove pagination parameters for export to get all data\r\n    const { page, limit, ...exportFilters } = filters || {};\r\n\r\n    const response = await apiClient.get(\"/sale-items\", {\r\n      params: { ...exportFilters, format: 'excel' },\r\n      responseType: 'blob',\r\n    });\r\n    return response as Blob;\r\n  },\r\n};\r\n\r\nexport default saleItemsService;\r\n"], "names": [], "mappings": ";;;AAAA;;AA4CA;;;CAGC,GACD,MAAM,mBAAmB;IACvB;;GAEC,GACD,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAoB,eAAe;YACrE,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,iBAAiB,OAAO;QACtB,0DAA0D;QAC1D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,eAAe,GAAG,WAAW,CAAC;QAEtD,MAAM,WAAW,MAAM,8HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,eAAe;YAClD,QAAQ;gBAAE,GAAG,aAAa;gBAAE,QAAQ;YAAQ;YAC5C,cAAc;QAChB;QACA,OAAO;IACT;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 11713, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/sale-items/hooks/use-sale-items.ts"], "sourcesContent": ["import { useQuery, useMutation } from \"@tanstack/react-query\";\r\nimport saleItemsService, { SaleItemFilters } from \"../api/sale-items-service\";\r\nimport { toast } from \"sonner\";\r\n\r\n/**\r\n * Hook to fetch sale items with filtering and pagination\r\n */\r\nexport const useSaleItems = (filters?: SaleItemFilters) => {\r\n  return useQuery({\r\n    queryKey: [\"sale-items\", filters],\r\n    queryFn: () => saleItemsService.getSaleItems(filters),\r\n    enabled: true, // Always enabled since we want to show data by default\r\n  });\r\n};\r\n\r\n/**\r\n * Hook to export sale items to Excel\r\n */\r\nexport const useSaleItemsExport = () => {\r\n  return useMutation({\r\n    mutationFn: (filters?: SaleItemFilters) => saleItemsService.exportSaleItems(filters),\r\n    onSuccess: (blob) => {\r\n      // Create download link\r\n      const url = window.URL.createObjectURL(blob);\r\n      const link = document.createElement('a');\r\n      link.href = url;\r\n      link.download = `sale-items-${new Date().toISOString().split('T')[0]}.xlsx`;\r\n      document.body.appendChild(link);\r\n      link.click();\r\n      document.body.removeChild(link);\r\n      window.URL.revokeObjectURL(url);\r\n      \r\n      toast.success(\"Sale items exported successfully\");\r\n    },\r\n    onError: (error: any) => {\r\n      console.error(\"Export failed:\", error);\r\n      toast.error(\"Failed to export sale items\");\r\n    },\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;;;;;AAKO,MAAM,eAAe,CAAC;;IAC3B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAc;SAAQ;QACjC,OAAO;qCAAE,IAAM,sKAAA,CAAA,UAAgB,CAAC,YAAY,CAAC;;QAC7C,SAAS;IACX;AACF;GANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,qBAAqB;;IAChC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;8CAAE,CAAC,UAA8B,sKAAA,CAAA,UAAgB,CAAC,eAAe,CAAC;;QAC5E,SAAS;8CAAE,CAAC;gBACV,uBAAuB;gBACvB,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,OAAO,SAAS,aAAa,CAAC;gBACpC,KAAK,IAAI,GAAG;gBACZ,KAAK,QAAQ,GAAG,CAAC,WAAW,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;gBAC3E,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,KAAK,KAAK;gBACV,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;gBAE3B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;8CAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IArBa;;QACJ,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 11785, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/reports/sales-by-item/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState } from \"react\";\r\nimport { MainLayout } from \"@/components/layouts/main-layout\";\r\nimport { ReportFilters } from \"@/features/reports/components/report-filters\";\r\nimport { ReportDataTable } from \"@/features/reports/components/report-data-table\";\r\nimport { useSaleItems, useSaleItemsExport } from \"@/features/sale-items/hooks/use-sale-items\";\r\nimport { SaleItemFilters } from \"@/features/sale-items/api/sale-items-service\";\r\nimport { Button } from \"@/components/ui/button\";\r\n\r\nimport { format, subDays } from \"date-fns\";\r\nimport { formatCurrency } from \"@/lib/utils\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { Download } from \"lucide-react\";\r\n\r\nexport default function SalesByItemPage() {\r\n  // Initialize with default filters (last 7 days)\r\n  const today = new Date();\r\n  const sevenDaysAgo = subDays(today, 7);\r\n\r\n  const [filters, setFilters] = useState<SaleItemFilters>({\r\n    start_date: format(sevenDaysAgo, \"yyyy-MM-dd\"),\r\n    end_date: format(today, \"yyyy-MM-dd\"),\r\n    page: 1,\r\n    limit: 50,\r\n  });\r\n\r\n  const { data, isLoading, error } = useSaleItems(filters);\r\n  const exportMutation = useSaleItemsExport();\r\n\r\n  const handleFilterChange = (newFilters: any) => {\r\n    setFilters(prev => ({\r\n      ...prev,\r\n      ...newFilters,\r\n      page: 1, // Reset to first page when filters change\r\n    }));\r\n  };\r\n\r\n  const handleExport = async () => {\r\n    try {\r\n      await exportMutation.mutateAsync(filters);\r\n    } catch (error) {\r\n      console.error(\"Export failed:\", error);\r\n    }\r\n  };\r\n\r\n  // Define columns for the sale items table\r\n  const columns = [\r\n    {\r\n      accessorKey: \"product_name\",\r\n      header: \"Product\",\r\n      cell: ({ row }) => (\r\n        <div>\r\n          <div className=\"font-medium\">{row.original.product_name}</div>\r\n          <div className=\"text-sm text-muted-foreground\">\r\n            SKU: {row.original.product_sku}\r\n          </div>\r\n        </div>\r\n      ),\r\n    },\r\n    {\r\n      accessorKey: \"category_name\",\r\n      header: \"Category\",\r\n    },\r\n    {\r\n      accessorKey: \"quantity\",\r\n      header: \"Quantity\",\r\n      cell: ({ row }) => row.original.quantity.toLocaleString(),\r\n    },\r\n    {\r\n      accessorKey: \"unit_price\",\r\n      header: \"Unit Price\",\r\n      cell: ({ row }) => formatCurrency(row.original.unit_price),\r\n    },\r\n    {\r\n      accessorKey: \"total_price\",\r\n      header: \"Total Price\",\r\n      cell: ({ row }) => formatCurrency(row.original.total_price),\r\n    },\r\n    {\r\n      accessorKey: \"sale_reference\",\r\n      header: \"Sale Reference\",\r\n    },\r\n    {\r\n      accessorKey: \"branch_name\",\r\n      header: \"Branch\",\r\n    },\r\n    {\r\n      accessorKey: \"region_name\",\r\n      header: \"Region\",\r\n    },\r\n    {\r\n      accessorKey: \"user_name\",\r\n      header: \"User\",\r\n    },\r\n    {\r\n      accessorKey: \"sale_date\",\r\n      header: \"Sale Date\",\r\n      cell: ({ row }) => {\r\n        const date = new Date(row.original.sale_date);\r\n        return format(date, \"MMM dd, yyyy HH:mm\");\r\n      },\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <MainLayout>\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex justify-between items-center\">\r\n          <div>\r\n            <h1 className=\"text-2xl font-bold tracking-tight\">Sales by Item</h1>\r\n            <p className=\"text-muted-foreground\">\r\n              Individual sale items with detailed information\r\n            </p>\r\n          </div>\r\n          <Button\r\n            onClick={handleExport}\r\n            disabled={exportMutation.isPending}\r\n            variant=\"outline\"\r\n          >\r\n            {exportMutation.isPending ? (\r\n              <>\r\n                <div className=\"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600\" />\r\n                Exporting...\r\n              </>\r\n            ) : (\r\n              <>\r\n                <Download className=\"mr-2 h-4 w-4\" />\r\n                Export to Excel\r\n              </>\r\n            )}\r\n          </Button>\r\n        </div>\r\n\r\n        <ReportFilters\r\n          filters={filters}\r\n          onFilterChange={handleFilterChange}\r\n          showTimeFilter={false}\r\n          showBranchFilter={true}\r\n          showRegionFilter={true}\r\n          showPaymentMethodFilter={false}\r\n          showStatusFilter={false}\r\n          showDsaFilter={false}\r\n          showProductFilter={false}\r\n          showUserFilter={false}\r\n          showCategoryFilter={false}\r\n        />\r\n\r\n        {isLoading ? (\r\n          <div className=\"space-y-4\">\r\n            <Skeleton className=\"h-96\" />\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"rounded-md bg-destructive/10 p-4 text-destructive\">\r\n            Error loading sale items data. Please try again.\r\n          </div>\r\n        ) : data ? (\r\n          <ReportDataTable\r\n            columns={columns}\r\n            data={data.data}\r\n            title=\"Sale Items\"\r\n            description={`Showing ${data.data.length} of ${data.pagination.total} sale items`}\r\n            searchColumn=\"product_name\"\r\n            searchPlaceholder=\"Search by product name...\"\r\n            exportFilename=\"sale-items-report\"\r\n            showExport={false} // We have our own export button\r\n          />\r\n        ) : null}\r\n      </div>\r\n    </MainLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAAA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;AAee,SAAS;;IACtB,gDAAgD;IAChD,MAAM,QAAQ,IAAI;IAClB,MAAM,eAAe,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;IAEpC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;QACtD,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;QACjC,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,OAAO;QACxB,MAAM;QACN,OAAO;IACT;IAEA,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE;IAChD,MAAM,iBAAiB,CAAA,GAAA,oKAAA,CAAA,qBAAkB,AAAD;IAExC,MAAM,qBAAqB,CAAC;QAC1B,WAAW,CAAA,OAAQ,CAAC;gBAClB,GAAG,IAAI;gBACP,GAAG,UAAU;gBACb,MAAM;YACR,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,eAAe,WAAW,CAAC;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,0CAA0C;IAC1C,MAAM,UAAU;QACd;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,iBACZ,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;sCAAe,IAAI,QAAQ,CAAC,YAAY;;;;;;sCACvD,6LAAC;4BAAI,WAAU;;gCAAgC;gCACvC,IAAI,QAAQ,CAAC,WAAW;;;;;;;;;;;;;QAItC;QACA;YACE,aAAa;YACb,QAAQ;QACV;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,IAAI,QAAQ,CAAC,QAAQ,CAAC,cAAc;QACzD;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,QAAQ,CAAC,UAAU;QAC3D;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,QAAQ,CAAC,WAAW;QAC5D;QACA;YACE,aAAa;YACb,QAAQ;QACV;QACA;YACE,aAAa;YACb,QAAQ;QACV;QACA;YACE,aAAa;YACb,QAAQ;QACV;QACA;YACE,aAAa;YACb,QAAQ;QACV;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,KAAK,IAAI,QAAQ,CAAC,SAAS;gBAC5C,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YACtB;QACF;KACD;IAED,qBACE,6LAAC,kJAAA,CAAA,aAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,eAAe,SAAS;4BAClC,SAAQ;sCAEP,eAAe,SAAS,iBACvB;;kDACE,6LAAC;wCAAI,WAAU;;;;;;oCAAsF;;6DAIvG;;kDACE,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;8BAO7C,6LAAC,iKAAA,CAAA,gBAAa;oBACZ,SAAS;oBACT,gBAAgB;oBAChB,gBAAgB;oBAChB,kBAAkB;oBAClB,kBAAkB;oBAClB,yBAAyB;oBACzB,kBAAkB;oBAClB,eAAe;oBACf,mBAAmB;oBACnB,gBAAgB;oBAChB,oBAAoB;;;;;;gBAGrB,0BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;2BAEpB,sBACF,6LAAC;oBAAI,WAAU;8BAAoD;;;;;2BAGjE,qBACF,6LAAC,uKAAA,CAAA,kBAAe;oBACd,SAAS;oBACT,MAAM,KAAK,IAAI;oBACf,OAAM;oBACN,aAAa,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC;oBACjF,cAAa;oBACb,mBAAkB;oBAClB,gBAAe;oBACf,YAAY;;;;;2BAEZ;;;;;;;;;;;;AAIZ;GA5JwB;;QAYa,oKAAA,CAAA,eAAY;QACxB,oKAAA,CAAA,qBAAkB;;;KAbnB", "debugId": null}}]}