{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/auth/hooks/use-permissions.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCurrentUser } from \"./use-auth\";\r\nimport { usePermissionContext } from \"../context/permission-context\";\r\n\r\n// Define constants for resources and actions\r\nexport const RESOURCES = {\r\n  USERS: \"users\",\r\n  ROLES: \"roles\",\r\n  PERMISSIONS: \"permissions\",\r\n  PRODUCTS: \"products\",\r\n  CATEGORIES: \"categories\",\r\n  BRANDS: \"brands\",\r\n  INVENTORY: \"inventory\",\r\n  STOCK_ITEMS: \"stock_items\",\r\n  BRANCHES: \"branches\",\r\n  EMPLOYEES: \"employees\",\r\n  BANKING: \"banking\",\r\n  EXPENSES: \"expenses\",\r\n  EXPENSE_FIRST_APPROVAL: \"expense_first_approval\",\r\n  EXPENSE_SECOND_APPROVAL: \"expense_second_approval\",\r\n  SALES: \"sales\",\r\n  POS_SESSIONS: \"pos_sessions\",\r\n  CUSTOMERS: \"customers\",\r\n  PROFILE: \"profile\",\r\n  STOCK_LOCATIONS: \"stock_locations\",\r\n};\r\n\r\nexport const ACTIONS = {\r\n  CREATE: \"create\",\r\n  READ: \"read\",\r\n  UPDATE: \"update\",\r\n  DELETE: \"delete\",\r\n};\r\n\r\nexport const SCOPE = {\r\n  ANY: \"any\",\r\n  OWN: \"own\",\r\n};\r\n\r\nexport function usePermissions() {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const {\r\n    permissions,\r\n    isLoading: isPermissionsLoading,\r\n    hasPermission,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n  } = usePermissionContext();\r\n\r\n  // Check if the user is a company admin\r\n  const isCompanyAdmin = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the company_admin role\r\n    return (\r\n      user.role_name === \"company_admin\" ||\r\n      user.role_name === \"tenant_admin\" ||\r\n      user.role_name === \"super_admin\"\r\n    );\r\n  };\r\n\r\n  // Check if the user is a branch manager\r\n  const isBranchManager = (): boolean => {\r\n    if (!user) return false;\r\n\r\n    // Check if the user has the branch_manager role\r\n    return user.role_name === \"branch_manager\";\r\n  };\r\n\r\n  // Check if the user can manage stock locations (create, update, delete)\r\n  const canManageStockLocations = (): boolean => {\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.CREATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.UPDATE, SCOPE.ANY) ||\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.DELETE, SCOPE.ANY) ||\r\n      isCompanyAdmin()\r\n    );\r\n  };\r\n\r\n  // Check if the user can view stock locations\r\n  const canViewStockLocations = (): boolean => {\r\n    // Check for explicit permission or fallback to any authenticated user\r\n    return (\r\n      hasPermission(RESOURCES.STOCK_LOCATIONS, ACTIONS.READ, SCOPE.ANY) ||\r\n      !!user\r\n    );\r\n  };\r\n\r\n  // Check if the user can perform an action on a resource\r\n  const can = (\r\n    resource: string,\r\n    action: string,\r\n    scope: \"any\" | \"own\" = \"any\"\r\n  ): boolean => {\r\n    return hasPermission(resource, action, scope);\r\n  };\r\n\r\n  // Check if the user has any of the specified permissions\r\n  const hasAnyPermission = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.some(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  // Check if the user has all of the specified permissions\r\n  const hasAllPermissions = (\r\n    permissions: Array<{\r\n      resource: string;\r\n      action: string;\r\n      scope?: \"any\" | \"own\";\r\n    }>\r\n  ): boolean => {\r\n    return permissions.every(({ resource, action, scope = \"any\" }) =>\r\n      hasPermission(resource, action, scope)\r\n    );\r\n  };\r\n\r\n  return {\r\n    isCompanyAdmin,\r\n    isBranchManager,\r\n    canManageStockLocations,\r\n    canViewStockLocations,\r\n    can,\r\n    hasPermission,\r\n    hasAnyPermission,\r\n    hasAllPermissions,\r\n    refreshPermissions,\r\n    logAvailableGrants,\r\n    permissions, // Expose the raw permissions for debugging\r\n    isLoading: isUserLoading || isPermissionsLoading,\r\n    RESOURCES,\r\n    ACTIONS,\r\n    SCOPE,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;AAMO,MAAM,YAAY;IACvB,OAAO;IACP,OAAO;IACP,aAAa;IACb,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,WAAW;IACX,aAAa;IACb,UAAU;IACV,WAAW;IACX,SAAS;IACT,UAAU;IACV,wBAAwB;IACxB,yBAAyB;IACzB,OAAO;IACP,cAAc;IACd,WAAW;IACX,SAAS;IACT,iBAAiB;AACnB;AAEO,MAAM,UAAU;IACrB,QAAQ;IACR,MAAM;IACN,QAAQ;IACR,QAAQ;AACV;AAEO,MAAM,QAAQ;IACnB,KAAK;IACL,KAAK;AACP;AAEO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EACJ,WAAW,EACX,WAAW,oBAAoB,EAC/B,aAAa,EACb,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,4JAAA,CAAA,uBAAoB,AAAD;IAEvB,uCAAuC;IACvC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,+CAA+C;QAC/C,OACE,KAAK,SAAS,KAAK,mBACnB,KAAK,SAAS,KAAK,kBACnB,KAAK,SAAS,KAAK;IAEvB;IAEA,wCAAwC;IACxC,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAElB,gDAAgD;QAChD,OAAO,KAAK,SAAS,KAAK;IAC5B;IAEA,wEAAwE;IACxE,MAAM,0BAA0B;QAC9B,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE,cAAc,UAAU,eAAe,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,KAClE;IAEJ;IAEA,6CAA6C;IAC7C,MAAM,wBAAwB;QAC5B,sEAAsE;QACtE,OACE,cAAc,UAAU,eAAe,EAAE,QAAQ,IAAI,EAAE,MAAM,GAAG,KAChE,CAAC,CAAC;IAEN;IAEA,wDAAwD;IACxD,MAAM,MAAM,CACV,UACA,QACA,QAAuB,KAAK;QAE5B,OAAO,cAAc,UAAU,QAAQ;IACzC;IAEA,yDAAyD;IACzD,MAAM,mBAAmB,CACvB;QAMA,OAAO,YAAY,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC1D,cAAc,UAAU,QAAQ;IAEpC;IAEA,yDAAyD;IACzD,MAAM,oBAAoB,CACxB;QAMA,OAAO,YAAY,KAAK,CAAC,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,GAC3D,cAAc,UAAU,QAAQ;IAEpC;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,WAAW,iBAAiB;QAC5B;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/lib/route-permissions.ts"], "sourcesContent": ["/**\r\n * Route Permission Mapping\r\n *\r\n * This file maps routes to the permissions required to access them.\r\n * Each key represents a route pattern, and the value is the permission required.\r\n */\r\n\r\nimport { NavigationPermission } from \"./navigation-permissions\";\r\n\r\nexport const routePermissions: Record<string, NavigationPermission> = {\r\n  // Dashboard routes\r\n  \"/dashboard\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/company\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/branch\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/finance\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/operations\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/stock\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/float\": { resource: \"dashboard\", action: \"view\" },\r\n  \"/dashboard/pos\": { resource: \"dashboard\", action: \"view\" },\r\n\r\n  // User management routes\r\n  \"/users\": { resource: \"users\", action: \"view\" },\r\n  \"/users/create\": { resource: \"users\", action: \"create\" },\r\n  \"/users/[id]\": { resource: \"users\", action: \"view\" },\r\n  \"/users/[id]/edit\": { resource: \"users\", action: \"update\" },\r\n\r\n  // Role management routes\r\n  \"/roles\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/create\": { resource: \"roles\", action: \"create\" },\r\n  \"/roles/[id]\": { resource: \"roles\", action: \"view\" },\r\n  \"/roles/[id]/edit\": { resource: \"roles\", action: \"update\" },\r\n\r\n  // RBAC routes\r\n  \"/rbac\": { resource: \"permissions\", action: \"manage\" },\r\n\r\n  // Branch management routes\r\n  \"/branches\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/create\": { resource: \"branches\", action: \"create\" },\r\n  \"/branches/[id]\": { resource: \"branches\", action: \"view\" },\r\n  \"/branches/[id]/edit\": { resource: \"branches\", action: \"update\" },\r\n\r\n  // Location management routes\r\n  \"/locations\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/locations/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/locations/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n  \"/location-guides\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/create\": { resource: \"locations\", action: \"create\" },\r\n  \"/location-guides/[id]\": { resource: \"locations\", action: \"view\" },\r\n  \"/location-guides/[id]/edit\": { resource: \"locations\", action: \"update\" },\r\n\r\n  // Employee management routes\r\n  \"/employees\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/create\": { resource: \"employees\", action: \"create\" },\r\n  \"/employees/[id]\": { resource: \"employees\", action: \"view\" },\r\n  \"/employees/[id]/edit\": { resource: \"employees\", action: \"update\" },\r\n\r\n  // Product management routes\r\n  \"/products\": { resource: \"products\", action: \"view\" },\r\n  \"/products/create\": { resource: \"products\", action: \"create\" },\r\n  \"/products/[id]\": { resource: \"products\", action: \"view\" },\r\n  \"/products/[id]/edit\": { resource: \"products\", action: \"update\" },\r\n\r\n  // Category management routes\r\n  \"/categories\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/create\": { resource: \"categories\", action: \"create\" },\r\n  \"/categories/[id]\": { resource: \"categories\", action: \"view\" },\r\n  \"/categories/[id]/edit\": { resource: \"categories\", action: \"update\" },\r\n\r\n  // Brand management routes\r\n  \"/brands\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/create\": { resource: \"brands\", action: \"create\" },\r\n  \"/brands/[id]\": { resource: \"brands\", action: \"view\" },\r\n  \"/brands/[id]/edit\": { resource: \"brands\", action: \"update\" },\r\n\r\n  // Inventory management routes\r\n  \"/inventory\": { resource: \"inventory\", action: \"view\" },\r\n  \"/inventory/transfers\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/bulk-transfer\": { resource: \"inventory\", action: \"transfer\" },\r\n  \"/inventory/reports\": { resource: \"inventory\", action: \"report\" },\r\n  \"/inventory/stock-cards\": { resource: \"inventory\", action: \"view\" },\r\n\r\n  // Banking routes\r\n  \"/banking\": { resource: \"banking\", action: \"view\" },\r\n  \"/banking/summary\": { resource: \"banking\", action: \"view\" },\r\n\r\n  // MPESA routes\r\n  \"/mpesa\": { resource: \"mpesa\", action: \"view\" },\r\n  \"/mpesa/transactions\": { resource: \"mpesa\", action: \"view\" },\r\n\r\n  // Float management routes\r\n  \"/float\": { resource: \"float\", action: \"view\" },\r\n  \"/float/movements\": { resource: \"float\", action: \"view\" },\r\n  \"/float/reconciliations\": { resource: \"float\", action: \"view\" },\r\n  \"/float/topups\": { resource: \"float\", action: \"view\" },\r\n\r\n  // DSA routes\r\n  \"/dsa\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/agents\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/assignments\": { resource: \"dsa\", action: \"view\" },\r\n  \"/dsa/reconciliations\": { resource: \"dsa\", action: \"view\" },\r\n\r\n  // Phone repairs routes\r\n  \"/phone-repairs\": { resource: \"phone_repairs\", action: \"view\" },\r\n  \"/phone-repairs/[id]\": { resource: \"phone_repairs\", action: \"view\" },\r\n\r\n  // Expense management routes\r\n  \"/expenses\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/create\": { resource: \"expenses\", action: \"create\" },\r\n  \"/expenses/[id]\": { resource: \"expenses\", action: \"view\" },\r\n  \"/expenses/[id]/edit\": { resource: \"expenses\", action: \"update\" },\r\n  \"/expense-analytics\": { resource: \"expenses\", action: \"view\" },\r\n\r\n  // Credit Note routes\r\n  \"/credit-notes\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/new\": { resource: \"invoices\", action: \"create\" },\r\n  \"/credit-notes/[id]\": { resource: \"invoices\", action: \"view\" },\r\n  \"/credit-notes/[id]/edit\": { resource: \"invoices\", action: \"update\" },\r\n\r\n  // Report routes\r\n  \"/reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-summary\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-item\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-category\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-employee\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/sales-by-payment-type\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-banking\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/cash-status\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/running-balances\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/mpesa-transactions\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/phone-repairs\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/expense-reports\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/dsa-sales\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/receipts\": { resource: \"reports\", action: \"view\" },\r\n  \"/reports/shifts\": { resource: \"reports\", action: \"view\" },\r\n\r\n  // Settings routes\r\n  \"/settings\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/system\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/company\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/payment-methods\": { resource: \"settings\", action: \"manage\" },\r\n  \"/settings/health\": { resource: \"settings\", action: \"view\" },\r\n  \"/settings/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // Profile route\r\n  \"/profile\": { resource: \"profile\", action: \"view\" },\r\n\r\n  // POS routes\r\n  \"/pos\": { resource: \"pos\", action: \"view\" },\r\n  \"/pos/sessions\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]\": { resource: \"pos_sessions\", action: \"view\" },\r\n  \"/pos/sessions/[id]/edit\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/close\": { resource: \"pos_sessions\", action: \"update\" },\r\n  \"/pos/sessions/[id]/shift-closing\": {\r\n    resource: \"pos_sessions\",\r\n    action: \"view\",\r\n  },\r\n\r\n  // Sales routes\r\n  \"/sales\": { resource: \"sales\", action: \"view\" },\r\n  \"/sales/[id]\": { resource: \"sales\", action: \"view\" },\r\n\r\n  // Customer routes\r\n  \"/customers\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/create\": { resource: \"customers\", action: \"create\" },\r\n  \"/customers/[id]\": { resource: \"customers\", action: \"view\" },\r\n  \"/customers/[id]/edit\": { resource: \"customers\", action: \"update\" },\r\n\r\n  // Procurement routes\r\n  \"/procurement\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests\": { resource: \"procurement\", action: \"view\" },\r\n  \"/procurement/requests/new\": { resource: \"procurement\", action: \"create\" },\r\n  \"/procurement/receipts\": { resource: \"procurement\", action: \"view\" },\r\n\r\n  // Supplier routes\r\n  \"/suppliers\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/create\": { resource: \"suppliers\", action: \"create\" },\r\n  \"/suppliers/[id]\": { resource: \"suppliers\", action: \"view\" },\r\n  \"/suppliers/[id]/edit\": { resource: \"suppliers\", action: \"update\" },\r\n\r\n  // Tenant routes (Super Admin only)\r\n  \"/tenants\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/create\": { resource: \"tenants\", action: \"create\" },\r\n  \"/tenants/[id]\": { resource: \"tenants\", action: \"view\" },\r\n  \"/tenants/[id]/edit\": { resource: \"tenants\", action: \"update\" },\r\n\r\n  // Super Admin routes\r\n  \"/super-admin\": { resource: \"tenants\", action: \"view\" },\r\n\r\n  // Debug routes\r\n  \"/permission-debug\": { resource: \"settings\", action: \"manage\" },\r\n};\r\n\r\n/**\r\n * Get the permission required for a route\r\n * @param route The route to check\r\n * @returns The permission required for the route, or null if no permission is required\r\n */\r\nexport function getRoutePermission(route: string): NavigationPermission | null {\r\n  // First try exact match\r\n  if (routePermissions[route]) {\r\n    return routePermissions[route];\r\n  }\r\n\r\n  // Then try dynamic routes\r\n  for (const [pattern, permission] of Object.entries(routePermissions)) {\r\n    if (pattern.includes(\"[\") && matchDynamicRoute(route, pattern)) {\r\n      return permission;\r\n    }\r\n  }\r\n\r\n  return null;\r\n}\r\n\r\n/**\r\n * Check if a route matches a dynamic route pattern\r\n * @param route The route to check\r\n * @param pattern The pattern to match against\r\n * @returns Whether the route matches the pattern\r\n */\r\nfunction matchDynamicRoute(route: string, pattern: string): boolean {\r\n  const routeParts = route.split(\"/\");\r\n  const patternParts = pattern.split(\"/\");\r\n\r\n  if (routeParts.length !== patternParts.length) {\r\n    return false;\r\n  }\r\n\r\n  for (let i = 0; i < patternParts.length; i++) {\r\n    if (patternParts[i].startsWith(\"[\") && patternParts[i].endsWith(\"]\")) {\r\n      // This is a dynamic part, so it matches anything\r\n      continue;\r\n    }\r\n\r\n    if (patternParts[i] !== routeParts[i]) {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  return true;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;CAKC;;;;AAIM,MAAM,mBAAyD;IACpE,mBAAmB;IACnB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC7D,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC9D,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,kBAAkB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,yBAAyB;IACzB,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAS;IACvD,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IACnD,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAS;IAE1D,cAAc;IACd,SAAS;QAAE,UAAU;QAAe,QAAQ;IAAS;IAErD,2BAA2B;IAC3B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAClE,oBAAoB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC5D,2BAA2B;QAAE,UAAU;QAAa,QAAQ;IAAS;IACrE,yBAAyB;QAAE,UAAU;QAAa,QAAQ;IAAO;IACjE,8BAA8B;QAAE,UAAU;QAAa,QAAQ;IAAS;IAExE,6BAA6B;IAC7B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEhE,6BAA6B;IAC7B,eAAe;QAAE,UAAU;QAAc,QAAQ;IAAO;IACxD,sBAAsB;QAAE,UAAU;QAAc,QAAQ;IAAS;IACjE,oBAAoB;QAAE,UAAU;QAAc,QAAQ;IAAO;IAC7D,yBAAyB;QAAE,UAAU;QAAc,QAAQ;IAAS;IAEpE,0BAA0B;IAC1B,WAAW;QAAE,UAAU;QAAU,QAAQ;IAAO;IAChD,kBAAkB;QAAE,UAAU;QAAU,QAAQ;IAAS;IACzD,gBAAgB;QAAE,UAAU;QAAU,QAAQ;IAAO;IACrD,qBAAqB;QAAE,UAAU;QAAU,QAAQ;IAAS;IAE5D,8BAA8B;IAC9B,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAW;IACpE,4BAA4B;QAAE,UAAU;QAAa,QAAQ;IAAW;IACxE,sBAAsB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAChE,0BAA0B;QAAE,UAAU;QAAa,QAAQ;IAAO;IAElE,iBAAiB;IACjB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,oBAAoB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE1D,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,uBAAuB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAE3D,0BAA0B;IAC1B,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,oBAAoB;QAAE,UAAU;QAAS,QAAQ;IAAO;IACxD,0BAA0B;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9D,iBAAiB;QAAE,UAAU;QAAS,QAAQ;IAAO;IAErD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,eAAe;QAAE,UAAU;QAAO,QAAQ;IAAO;IACjD,oBAAoB;QAAE,UAAU;QAAO,QAAQ;IAAO;IACtD,wBAAwB;QAAE,UAAU;QAAO,QAAQ;IAAO;IAE1D,uBAAuB;IACvB,kBAAkB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAC9D,uBAAuB;QAAE,UAAU;QAAiB,QAAQ;IAAO;IAEnE,4BAA4B;IAC5B,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,kBAAkB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACzD,uBAAuB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAChE,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAE7D,qBAAqB;IACrB,iBAAiB;QAAE,UAAU;QAAY,QAAQ;IAAO;IACxD,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,sBAAsB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC7D,2BAA2B;QAAE,UAAU;QAAY,QAAQ;IAAS;IAEpE,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,8BAA8B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACpE,kCAAkC;QAAE,UAAU;QAAW,QAAQ;IAAO;IACxE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,wBAAwB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC9D,6BAA6B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACnE,+BAA+B;QAAE,UAAU;QAAW,QAAQ;IAAO;IACrE,0BAA0B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAChE,4BAA4B;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClE,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC5D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAC3D,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEzD,kBAAkB;IAClB,aAAa;QAAE,UAAU;QAAY,QAAQ;IAAO;IACpD,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC7D,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;IAC9D,6BAA6B;QAAE,UAAU;QAAY,QAAQ;IAAS;IACtE,oBAAoB;QAAE,UAAU;QAAY,QAAQ;IAAO;IAC3D,qBAAqB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAE3D,gBAAgB;IAChB,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAElD,aAAa;IACb,QAAQ;QAAE,UAAU;QAAO,QAAQ;IAAO;IAC1C,iBAAiB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IAC5D,sBAAsB;QAAE,UAAU;QAAgB,QAAQ;IAAO;IACjE,2BAA2B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACxE,4BAA4B;QAAE,UAAU;QAAgB,QAAQ;IAAS;IACzE,oCAAoC;QAClC,UAAU;QACV,QAAQ;IACV;IAEA,eAAe;IACf,UAAU;QAAE,UAAU;QAAS,QAAQ;IAAO;IAC9C,eAAe;QAAE,UAAU;QAAS,QAAQ;IAAO;IAEnD,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAC1D,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IACnE,6BAA6B;QAAE,UAAU;QAAe,QAAQ;IAAS;IACzE,yBAAyB;QAAE,UAAU;QAAe,QAAQ;IAAO;IAEnE,kBAAkB;IAClB,cAAc;QAAE,UAAU;QAAa,QAAQ;IAAO;IACtD,qBAAqB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAC/D,mBAAmB;QAAE,UAAU;QAAa,QAAQ;IAAO;IAC3D,wBAAwB;QAAE,UAAU;QAAa,QAAQ;IAAS;IAElE,mCAAmC;IACnC,YAAY;QAAE,UAAU;QAAW,QAAQ;IAAO;IAClD,mBAAmB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAC3D,iBAAiB;QAAE,UAAU;QAAW,QAAQ;IAAO;IACvD,sBAAsB;QAAE,UAAU;QAAW,QAAQ;IAAS;IAE9D,qBAAqB;IACrB,gBAAgB;QAAE,UAAU;QAAW,QAAQ;IAAO;IAEtD,eAAe;IACf,qBAAqB;QAAE,UAAU;QAAY,QAAQ;IAAS;AAChE;AAOO,SAAS,mBAAmB,KAAa;IAC9C,wBAAwB;IACxB,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,OAAO,gBAAgB,CAAC,MAAM;IAChC;IAEA,0BAA0B;IAC1B,KAAK,MAAM,CAAC,SAAS,WAAW,IAAI,OAAO,OAAO,CAAC,kBAAmB;QACpE,IAAI,QAAQ,QAAQ,CAAC,QAAQ,kBAAkB,OAAO,UAAU;YAC9D,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,KAAa,EAAE,OAAe;IACvD,MAAM,aAAa,MAAM,KAAK,CAAC;IAC/B,MAAM,eAAe,QAAQ,KAAK,CAAC;IAEnC,IAAI,WAAW,MAAM,KAAK,aAAa,MAAM,EAAE;QAC7C,OAAO;IACT;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC5C,IAAI,YAAY,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM;YAEpE;QACF;QAEA,IAAI,YAAY,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 673, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/role-guard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect, useState } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\nimport { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\nimport { getRoutePermission } from \"@/lib/route-permissions\";\r\nimport { hasRouteAccess, getDashboardByRole } from \"@/lib/rbac-config\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\nfunction mapActionToGrantAction(action: string): string {\r\n  switch (action) {\r\n    case \"view\":\r\n      return \"read:any\";\r\n    case \"create\":\r\n      return \"create:any\";\r\n    case \"update\":\r\n      return \"update:any\";\r\n    case \"delete\":\r\n      return \"delete:any\";\r\n    case \"manage\":\r\n      return \"update:any\"; // Manage maps to update:any\r\n    case \"transfer\":\r\n      return \"update:any\"; // Transfer maps to update:any\r\n    case \"report\":\r\n      return \"read:any\"; // Report maps to read:any\r\n    default:\r\n      return `${action}:any`;\r\n  }\r\n}\r\n\r\ninterface RoleGuardProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function RoleGuard({ children }: RoleGuardProps) {\r\n  const { data: user, isLoading: isUserLoading } = useCurrentUser();\r\n  const { hasPermission, isLoading: isPermissionsLoading } = usePermissions();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Track if this is the initial mount\r\n  const [isInitialMount, setIsInitialMount] = useState(true);\r\n\r\n  // Effect to mark initial mount as complete\r\n  useEffect(() => {\r\n    setIsInitialMount(false);\r\n  }, []);\r\n\r\n  // Effect to check access rights\r\n  useEffect(() => {\r\n    // Skip check if on auth pages\r\n    if (\r\n      pathname.startsWith(\"/login\") ||\r\n      pathname.startsWith(\"/forgot-password\") ||\r\n      pathname.startsWith(\"/reset-password\")\r\n    ) {\r\n      setLoading(\"role\", false);\r\n      return;\r\n    }\r\n\r\n    // Only show loading indicator on initial mount or when user data is loading\r\n    // and we haven't shown the loading screen before in this session\r\n    if (isInitialMount && isUserLoading && !hasShownLoading.role) {\r\n      setLoading(\"role\", true);\r\n      return;\r\n    } else {\r\n      setLoading(\"role\", false);\r\n    }\r\n\r\n    // If user data and permissions are loaded, check access rights\r\n    if (!isUserLoading && !isPermissionsLoading && user) {\r\n      // Safely access role_name with type checking\r\n      const roleName =\r\n        user && typeof user === \"object\" && \"role_name\" in user\r\n          ? user.role_name\r\n          : \"\";\r\n\r\n      // First try permission-based access control\r\n      const routePermission = getRoutePermission(pathname);\r\n\r\n      let hasAccess = true;\r\n      let accessMethod = \"default\";\r\n\r\n      // Use RBAC-based access control\r\n      hasAccess = hasRouteAccess(pathname, roleName);\r\n      accessMethod = \"rbac\";\r\n\r\n      // Add detailed debug logging\r\n      console.log(`[RoleGuard] Route: ${pathname}`);\r\n      console.log(`[RoleGuard] User role: ${roleName}`);\r\n      console.log(`[RoleGuard] Using RBAC-based access control`);\r\n      console.log(\r\n        `[RoleGuard] Access ${hasAccess ? \"GRANTED\" : \"DENIED\"} by RBAC check`\r\n      );\r\n\r\n      // If we have a permission mapping, check it too (for debugging purposes only)\r\n      if (routePermission) {\r\n        // Map the action to the format used in grants (e.g., \"view\" -> \"read:any\")\r\n        const grantAction = mapActionToGrantAction(routePermission.action);\r\n\r\n        // Check if user has the required permission (but don't use the result)\r\n        const permissionAccess = hasPermission(\r\n          routePermission.resource,\r\n          grantAction,\r\n          routePermission.scope\r\n        );\r\n\r\n        // Log the permission check result for debugging\r\n        console.log(\r\n          `[RoleGuard] Permission check (not used): ${\r\n            routePermission.resource\r\n          }:${routePermission.action}:${routePermission.scope || \"any\"}`\r\n        );\r\n        console.log(`[RoleGuard] Mapped to grant action: ${grantAction}`);\r\n        console.log(\r\n          `[RoleGuard] Permission would be ${\r\n            permissionAccess ? \"GRANTED\" : \"DENIED\"\r\n          } (for future reference)`\r\n        );\r\n      }\r\n\r\n      if (!hasAccess) {\r\n        console.log(\r\n          `[RoleGuard] Access denied for ${pathname}, redirecting to dashboard (method: ${accessMethod})`\r\n        );\r\n\r\n        // Redirect to the appropriate dashboard based on role using RBAC configuration\r\n        const dashboardRoute = getDashboardByRole(roleName);\r\n        console.log(\r\n          `[RoleGuard] Redirecting to ${dashboardRoute} for role ${roleName}`\r\n        );\r\n        router.replace(dashboardRoute);\r\n      }\r\n\r\n      // User data is loaded, we can stop checking and mark as shown\r\n      setLoading(\"role\", false);\r\n      markLoadingShown(\"role\");\r\n    } else if (!isUserLoading && !isPermissionsLoading) {\r\n      // No user data and not loading, stop checking\r\n      setLoading(\"role\", false);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [\r\n    user,\r\n    isUserLoading,\r\n    isPermissionsLoading,\r\n    pathname,\r\n    router,\r\n    isInitialMount,\r\n    hasShownLoading.role,\r\n    hasPermission,\r\n  ]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in checking state\r\n  useEffect(() => {\r\n    let timeoutId: NodeJS.Timeout | null = null;\r\n\r\n    if (isLoading.role) {\r\n      timeoutId = setTimeout(() => {\r\n        setLoading(\"role\", false);\r\n        markLoadingShown(\"role\");\r\n      }, 1500); // 1.5 second timeout for better UX\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.role]);\r\n\r\n  // Show loading state only when actively checking role access\r\n  if (isLoading.role) {\r\n    return <LoadingScreen message=\"Checking access...\" />;\r\n  }\r\n\r\n  // Don't block rendering, the useEffect will handle redirects\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAaA;;;;CAIC,GACD,SAAS,uBAAuB,MAAc;IAC5C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO,cAAc,4BAA4B;QACnD,KAAK;YACH,OAAO,cAAc,8BAA8B;QACrD,KAAK;YACH,OAAO,YAAY,0BAA0B;QAC/C;YACE,OAAO,GAAG,OAAO,IAAI,CAAC;IAC1B;AACF;AAMO,SAAS,UAAU,EAAE,QAAQ,EAAkB;IACpD,MAAM,EAAE,MAAM,IAAI,EAAE,WAAW,aAAa,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAC9D,MAAM,EAAE,aAAa,EAAE,WAAW,oBAAoB,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD;IAEX,qCAAqC;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kBAAkB;IACpB,GAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8BAA8B;QAC9B,IACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,uBACpB,SAAS,UAAU,CAAC,oBACpB;YACA,WAAW,QAAQ;YACnB;QACF;QAEA,4EAA4E;QAC5E,iEAAiE;QACjE,IAAI,kBAAkB,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;YAC5D,WAAW,QAAQ;YACnB;QACF,OAAO;YACL,WAAW,QAAQ;QACrB;QAEA,+DAA+D;QAC/D,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,MAAM;YACnD,6CAA6C;YAC7C,MAAM,WACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;YAEN,4CAA4C;YAC5C,MAAM,kBAAkB,CAAA,GAAA,kIAAA,CAAA,qBAAkB,AAAD,EAAE;YAE3C,IAAI,YAAY;YAChB,IAAI,eAAe;YAEnB,gCAAgC;YAChC,YAAY,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;YACrC,eAAe;YAEf,6BAA6B;YAC7B,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;YAC5C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,UAAU;YAChD,QAAQ,GAAG,CAAC,CAAC,2CAA2C,CAAC;YACzD,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,YAAY,YAAY,SAAS,cAAc,CAAC;YAGxE,8EAA8E;YAC9E,IAAI,iBAAiB;gBACnB,2EAA2E;gBAC3E,MAAM,cAAc,uBAAuB,gBAAgB,MAAM;gBAEjE,uEAAuE;gBACvE,MAAM,mBAAmB,cACvB,gBAAgB,QAAQ,EACxB,aACA,gBAAgB,KAAK;gBAGvB,gDAAgD;gBAChD,QAAQ,GAAG,CACT,CAAC,yCAAyC,EACxC,gBAAgB,QAAQ,CACzB,CAAC,EAAE,gBAAgB,MAAM,CAAC,CAAC,EAAE,gBAAgB,KAAK,IAAI,OAAO;gBAEhE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,aAAa;gBAChE,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAC/B,mBAAmB,YAAY,SAChC,uBAAuB,CAAC;YAE7B;YAEA,IAAI,CAAC,WAAW;gBACd,QAAQ,GAAG,CACT,CAAC,8BAA8B,EAAE,SAAS,oCAAoC,EAAE,aAAa,CAAC,CAAC;gBAGjG,+EAA+E;gBAC/E,MAAM,iBAAiB,CAAA,GAAA,4HAAA,CAAA,qBAAkB,AAAD,EAAE;gBAC1C,QAAQ,GAAG,CACT,CAAC,2BAA2B,EAAE,eAAe,UAAU,EAAE,UAAU;gBAErE,OAAO,OAAO,CAAC;YACjB;YAEA,8DAA8D;YAC9D,WAAW,QAAQ;YACnB,iBAAiB;QACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;YAClD,8CAA8C;YAC9C,WAAW,QAAQ;QACrB;IACA,uDAAuD;IACzD,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA,gBAAgB,IAAI;QACpB;KACD;IAED,kEAAkE;IAClE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAmC;QAEvC,IAAI,UAAU,IAAI,EAAE;YAClB,YAAY,WAAW;gBACrB,WAAW,QAAQ;gBACnB,iBAAiB;YACnB,GAAG,OAAO,mCAAmC;QAC/C;QAEA,OAAO;YACL,IAAI,WAAW,aAAa;QAC9B;IACA,uDAAuD;IACzD,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6DAA6D;IAC7D,IAAI,UAAU,IAAI,EAAE;QAClB,qBAAO,8OAAC,sJAAA,CAAA,gBAAa;YAAC,SAAQ;;;;;;IAChC;IAEA,6DAA6D;IAC7D,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/hooks/use-mobile.ts"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nconst MO<PERSON>LE_BREAKPOINT = 768\r\n\r\nexport function useIsMobile() {\r\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\r\n\r\n  React.useEffect(() => {\r\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\r\n    const onChange = () => {\r\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    }\r\n    mql.addEventListener(\"change\", onChange)\r\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\r\n    return () => mql.removeEventListener(\"change\", onChange)\r\n  }, [])\r\n\r\n  return !!isMobile\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;AAEnB,SAAS;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAuB;IAEpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,MAAM,OAAO,UAAU,CAAC,CAAC,YAAY,EAAE,oBAAoB,EAAE,GAAG,CAAC;QACvE,MAAM,WAAW;YACf,YAAY,OAAO,UAAU,GAAG;QAClC;QACA,IAAI,gBAAgB,CAAC,UAAU;QAC/B,YAAY,OAAO,UAAU,GAAG;QAChC,OAAO,IAAM,IAAI,mBAAmB,CAAC,UAAU;IACjD,GAAG,EAAE;IAEL,OAAO,CAAC,CAAC;AACX", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\r\n  return (\r\n    <input\r\n      type={type}\r\n      data-slot=\"input\"\r\n      className={cn(\r\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\r\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Separator({\r\n  className,\r\n  orientation = \"horizontal\",\r\n  decorative = true,\r\n  ...props\r\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\r\n  return (\r\n    <SeparatorPrimitive.Root\r\n      data-slot=\"separator-root\"\r\n      decorative={decorative}\r\n      orientation={orientation}\r\n      className={cn(\r\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Separator }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\r\nimport { XIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\r\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\r\n}\r\n\r\nfunction SheetTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\r\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\r\n}\r\n\r\nfunction SheetClose({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\r\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\r\n}\r\n\r\nfunction SheetPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\r\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\r\n}\r\n\r\nfunction SheetOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\r\n  return (\r\n    <SheetPrimitive.Overlay\r\n      data-slot=\"sheet-overlay\"\r\n      className={cn(\r\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetContent({\r\n  className,\r\n  children,\r\n  side = \"right\",\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\r\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\r\n}) {\r\n  return (\r\n    <SheetPortal>\r\n      <SheetOverlay />\r\n      <SheetPrimitive.Content\r\n        data-slot=\"sheet-content\"\r\n        className={cn(\r\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\r\n          side === \"right\" &&\r\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\r\n          side === \"left\" &&\r\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\r\n          side === \"top\" &&\r\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\r\n          side === \"bottom\" &&\r\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\r\n          <XIcon className=\"size-4\" />\r\n          <span className=\"sr-only\">Close</span>\r\n        </SheetPrimitive.Close>\r\n      </SheetPrimitive.Content>\r\n    </SheetPortal>\r\n  )\r\n}\r\n\r\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-header\"\r\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sheet-footer\"\r\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\r\n  return (\r\n    <SheetPrimitive.Title\r\n      data-slot=\"sheet-title\"\r\n      className={cn(\"text-foreground font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SheetDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\r\n  return (\r\n    <SheetPrimitive.Description\r\n      data-slot=\"sheet-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Sheet,\r\n  SheetTrigger,\r\n  SheetClose,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetFooter,\r\n  SheetTitle,\r\n  SheetDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\r\n\r\nfunction Skeleton({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"skeleton\"\r\n      className={cn(\"bg-accent animate-pulse rounded-md\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Skeleton }\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction TooltipProvider({\r\n  delayDuration = 0,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\r\n  return (\r\n    <TooltipPrimitive.Provider\r\n      data-slot=\"tooltip-provider\"\r\n      delayDuration={delayDuration}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction Tooltip({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\r\n  return (\r\n    <TooltipProvider>\r\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\r\n    </TooltipProvider>\r\n  )\r\n}\r\n\r\nfunction TooltipTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\r\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\r\n}\r\n\r\nfunction TooltipContent({\r\n  className,\r\n  sideOffset = 0,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\r\n  return (\r\n    <TooltipPrimitive.Portal>\r\n      <TooltipPrimitive.Content\r\n        data-slot=\"tooltip-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\r\n      </TooltipPrimitive.Content>\r\n    </TooltipPrimitive.Portal>\r\n  )\r\n}\r\n\r\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { Slot } from \"@radix-ui/react-slot\";\r\nimport { VariantProps, cva } from \"class-variance-authority\";\r\nimport { PanelLeftIcon } from \"lucide-react\";\r\n\r\nimport { useIsMobile } from \"@/hooks/use-mobile\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Separator } from \"@/components/ui/separator\";\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetDescription,\r\n  SheetHeader,\r\n  SheetTitle,\r\n} from \"@/components/ui/sheet\";\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\n\r\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\r\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\r\nconst SIDEBAR_WIDTH = \"16rem\";\r\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\r\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\r\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\r\n\r\ntype SidebarContextProps = {\r\n  state: \"expanded\" | \"collapsed\";\r\n  open: boolean;\r\n  setOpen: (open: boolean) => void;\r\n  openMobile: boolean;\r\n  setOpenMobile: (open: boolean) => void;\r\n  isMobile: boolean;\r\n  toggleSidebar: () => void;\r\n};\r\n\r\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null);\r\n\r\nfunction useSidebar() {\r\n  const context = React.useContext(SidebarContext);\r\n  if (!context) {\r\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\");\r\n  }\r\n\r\n  return context;\r\n}\r\n\r\nfunction SidebarProvider({\r\n  defaultOpen = true,\r\n  open: openProp,\r\n  onOpenChange: setOpenProp,\r\n  className,\r\n  style,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  defaultOpen?: boolean;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n}) {\r\n  const isMobile = useIsMobile();\r\n  const [openMobile, setOpenMobile] = React.useState(false);\r\n\r\n  // This is the internal state of the sidebar.\r\n  // We use openProp and setOpenProp for control from outside the component.\r\n  const [_open, _setOpen] = React.useState(defaultOpen);\r\n  const open = openProp ?? _open;\r\n  const setOpen = React.useCallback(\r\n    (value: boolean | ((value: boolean) => boolean)) => {\r\n      const openState = typeof value === \"function\" ? value(open) : value;\r\n      if (setOpenProp) {\r\n        setOpenProp(openState);\r\n      } else {\r\n        _setOpen(openState);\r\n      }\r\n\r\n      // This sets the cookie to keep the sidebar state.\r\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`;\r\n    },\r\n    [setOpenProp, open]\r\n  );\r\n\r\n  // Helper to toggle the sidebar.\r\n  const toggleSidebar = React.useCallback(() => {\r\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open);\r\n  }, [isMobile, setOpen, setOpenMobile]);\r\n\r\n  // Adds a keyboard shortcut to toggle the sidebar.\r\n  React.useEffect(() => {\r\n    const handleKeyDown = (event: KeyboardEvent) => {\r\n      if (\r\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\r\n        (event.metaKey || event.ctrlKey)\r\n      ) {\r\n        event.preventDefault();\r\n        toggleSidebar();\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n    return () => window.removeEventListener(\"keydown\", handleKeyDown);\r\n  }, [toggleSidebar]);\r\n\r\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\r\n  // This makes it easier to style the sidebar with Tailwind classes.\r\n  const state = open ? \"expanded\" : \"collapsed\";\r\n\r\n  const contextValue = React.useMemo<SidebarContextProps>(\r\n    () => ({\r\n      state,\r\n      open,\r\n      setOpen,\r\n      isMobile,\r\n      openMobile,\r\n      setOpenMobile,\r\n      toggleSidebar,\r\n    }),\r\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\r\n  );\r\n\r\n  return (\r\n    <SidebarContext.Provider value={contextValue}>\r\n      <TooltipProvider delayDuration={0}>\r\n        <div\r\n          data-slot=\"sidebar-wrapper\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH,\r\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\r\n              ...style,\r\n            } as React.CSSProperties\r\n          }\r\n          className={cn(\r\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\r\n            className\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </TooltipProvider>\r\n    </SidebarContext.Provider>\r\n  );\r\n}\r\n\r\nfunction Sidebar({\r\n  side = \"left\",\r\n  variant = \"sidebar\",\r\n  collapsible = \"offcanvas\",\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  side?: \"left\" | \"right\";\r\n  variant?: \"sidebar\" | \"floating\" | \"inset\";\r\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\";\r\n}) {\r\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\r\n\r\n  if (collapsible === \"none\") {\r\n    return (\r\n      <div\r\n        data-slot=\"sidebar\"\r\n        className={cn(\r\n          \"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isMobile) {\r\n    return (\r\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\r\n        <SheetContent\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar\"\r\n          data-mobile=\"true\"\r\n          className=\"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\"\r\n          style={\r\n            {\r\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\r\n            } as React.CSSProperties\r\n          }\r\n          side={side}\r\n        >\r\n          <SheetHeader className=\"sr-only\">\r\n            <SheetTitle>Sidebar</SheetTitle>\r\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\r\n          </SheetHeader>\r\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className=\"group peer text-sidebar-foreground hidden md:block\"\r\n      data-state={state}\r\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\r\n      data-variant={variant}\r\n      data-side={side}\r\n      data-slot=\"sidebar\"\r\n    >\r\n      {/* This is what handles the sidebar gap on desktop */}\r\n      <div\r\n        data-slot=\"sidebar-gap\"\r\n        className={cn(\r\n          \"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\",\r\n          \"group-data-[collapsible=offcanvas]:w-0\",\r\n          \"group-data-[side=right]:rotate-180\",\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\"\r\n        )}\r\n      />\r\n      <div\r\n        data-slot=\"sidebar-container\"\r\n        className={cn(\r\n          \"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\",\r\n          side === \"left\"\r\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\r\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\r\n          // Adjust the padding for floating and inset variants.\r\n          variant === \"floating\" || variant === \"inset\"\r\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\"\r\n            : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\",\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        <div\r\n          data-sidebar=\"sidebar\"\r\n          data-slot=\"sidebar-inner\"\r\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\r\n        >\r\n          {children}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarTrigger({\r\n  className,\r\n  onClick,\r\n  ...props\r\n}: React.ComponentProps<typeof Button>) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <Button\r\n      data-sidebar=\"trigger\"\r\n      data-slot=\"sidebar-trigger\"\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      className={cn(\"size-7\", className)}\r\n      onClick={(event) => {\r\n        onClick?.(event);\r\n        toggleSidebar();\r\n      }}\r\n      {...props}\r\n    >\r\n      <PanelLeftIcon />\r\n      <span className=\"sr-only\">Toggle Sidebar</span>\r\n    </Button>\r\n  );\r\n}\r\n\r\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\r\n  const { toggleSidebar } = useSidebar();\r\n\r\n  return (\r\n    <button\r\n      data-sidebar=\"rail\"\r\n      data-slot=\"sidebar-rail\"\r\n      aria-label=\"Toggle Sidebar\"\r\n      tabIndex={-1}\r\n      onClick={toggleSidebar}\r\n      title=\"Toggle Sidebar\"\r\n      className={cn(\r\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\r\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\r\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\r\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\r\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\r\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\r\n  return (\r\n    <main\r\n      data-slot=\"sidebar-inset\"\r\n      className={cn(\r\n        \"bg-background relative flex w-full flex-1 flex-col\",\r\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Input>) {\r\n  return (\r\n    <Input\r\n      data-slot=\"sidebar-input\"\r\n      data-sidebar=\"input\"\r\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-header\"\r\n      data-sidebar=\"header\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-footer\"\r\n      data-sidebar=\"footer\"\r\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof Separator>) {\r\n  return (\r\n    <Separator\r\n      data-slot=\"sidebar-separator\"\r\n      data-sidebar=\"separator\"\r\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-content\"\r\n      data-sidebar=\"content\"\r\n      className={cn(\r\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group\"\r\n      data-sidebar=\"group\"\r\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupLabel({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"div\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-label\"\r\n      data-sidebar=\"group-label\"\r\n      className={cn(\r\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupAction({\r\n  className,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"sidebar-group-action\"\r\n      data-sidebar=\"group-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarGroupContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-group-content\"\r\n      data-sidebar=\"group-content\"\r\n      className={cn(\"w-full text-sm\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu\"\r\n      data-sidebar=\"menu\"\r\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-item\"\r\n      data-sidebar=\"menu-item\"\r\n      className={cn(\"group/menu-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nconst sidebarMenuButtonVariants = cva(\r\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\r\n        outline:\r\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\r\n      },\r\n      size: {\r\n        default: \"h-8 text-sm\",\r\n        sm: \"h-7 text-xs\",\r\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n);\r\n\r\nfunction SidebarMenuButton({\r\n  asChild = false,\r\n  isActive = false,\r\n  variant = \"default\",\r\n  size = \"default\",\r\n  tooltip,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  isActive?: boolean;\r\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>;\r\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n  const { isMobile, state } = useSidebar();\r\n\r\n  // Use useMemo to prevent recreating the button on every render\r\n  const button = React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-button\"\r\n      data-sidebar=\"menu-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, variant, className, props]);\r\n\r\n  // Use useMemo for tooltipContent to prevent recalculation on every render\r\n  const tooltipContent = React.useMemo(() => {\r\n    if (!tooltip) return null;\r\n    return typeof tooltip === \"string\" ? { children: tooltip } : tooltip;\r\n  }, [tooltip]);\r\n\r\n  if (!tooltip) {\r\n    return button;\r\n  }\r\n\r\n  // Use useMemo for the hidden prop to prevent recalculation on every render\r\n  const isHidden = React.useMemo(() => {\r\n    return state !== \"collapsed\" || isMobile;\r\n  }, [state, isMobile]);\r\n\r\n  return (\r\n    <Tooltip>\r\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\r\n      <TooltipContent\r\n        side=\"right\"\r\n        align=\"center\"\r\n        hidden={isHidden}\r\n        {...tooltipContent}\r\n      />\r\n    </Tooltip>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuAction({\r\n  className,\r\n  asChild = false,\r\n  showOnHover = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> & {\r\n  asChild?: boolean;\r\n  showOnHover?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"button\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-action\"\r\n      data-sidebar=\"menu-action\"\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        // Increases the hit area of the button on mobile.\r\n        \"after:absolute after:-inset-2 md:after:hidden\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        showOnHover &&\r\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, className, showOnHover, props]);\r\n}\r\n\r\nfunction SidebarMenuBadge({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"div\">) {\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <div\r\n      data-slot=\"sidebar-menu-badge\"\r\n      data-sidebar=\"menu-badge\"\r\n      className={cn(\r\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\r\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\r\n        \"peer-data-[size=sm]/menu-button:top-1\",\r\n        \"peer-data-[size=default]/menu-button:top-1.5\",\r\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [className, props]);\r\n}\r\n\r\nfunction SidebarMenuSkeleton({\r\n  className,\r\n  showIcon = false,\r\n  ...props\r\n}: React.ComponentProps<\"div\"> & {\r\n  showIcon?: boolean;\r\n}) {\r\n  // Random width between 50 to 90%.\r\n  const width = React.useMemo(() => {\r\n    return `${Math.floor(Math.random() * 40) + 50}%`;\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      data-slot=\"sidebar-menu-skeleton\"\r\n      data-sidebar=\"menu-skeleton\"\r\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\r\n      {...props}\r\n    >\r\n      {showIcon && (\r\n        <Skeleton\r\n          className=\"size-4 rounded-md\"\r\n          data-sidebar=\"menu-skeleton-icon\"\r\n        />\r\n      )}\r\n      <Skeleton\r\n        className=\"h-4 max-w-(--skeleton-width) flex-1\"\r\n        data-sidebar=\"menu-skeleton-text\"\r\n        style={\r\n          {\r\n            \"--skeleton-width\": width,\r\n          } as React.CSSProperties\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\r\n  return (\r\n    <ul\r\n      data-slot=\"sidebar-menu-sub\"\r\n      data-sidebar=\"menu-sub\"\r\n      className={cn(\r\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"sidebar-menu-sub-item\"\r\n      data-sidebar=\"menu-sub-item\"\r\n      className={cn(\"group/menu-sub-item relative\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction SidebarMenuSubButton({\r\n  asChild = false,\r\n  size = \"md\",\r\n  isActive = false,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean;\r\n  size?: \"sm\" | \"md\";\r\n  isActive?: boolean;\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\";\r\n\r\n  // Use useMemo to prevent recreating the component on every render\r\n  return React.useMemo(() => (\r\n    <Comp\r\n      data-slot=\"sidebar-menu-sub-button\"\r\n      data-sidebar=\"menu-sub-button\"\r\n      data-size={size}\r\n      data-active={isActive}\r\n      className={cn(\r\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\r\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\r\n        size === \"sm\" && \"text-xs\",\r\n        size === \"md\" && \"text-sm\",\r\n        \"group-data-[collapsible=icon]:hidden\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  ), [Comp, size, isActive, className, props]);\r\n}\r\n\r\nexport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupAction,\r\n  SidebarGroupContent,\r\n  SidebarGroupLabel,\r\n  SidebarHeader,\r\n  SidebarInput,\r\n  SidebarInset,\r\n  SidebarMenu,\r\n  SidebarMenuAction,\r\n  SidebarMenuBadge,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSkeleton,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n  SidebarProvider,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n  SidebarTrigger,\r\n  useSidebar,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AApBA;;;;;;;;;;;;;;AA2BA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB,KAAK,KAAK,KAAK;AAC9C,MAAM,gBAAgB;AACtB,MAAM,uBAAuB;AAC7B,MAAM,qBAAqB;AAC3B,MAAM,4BAA4B;AAYlC,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA8B;AAEvE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,SAAS,gBAAgB,EACvB,cAAc,IAAI,EAClB,MAAM,QAAQ,EACd,cAAc,WAAW,EACzB,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,WAAW,CAAA,GAAA,6HAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEnD,6CAA6C;IAC7C,0EAA0E;IAC1E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,OAAO,YAAY;IACzB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAC9B,CAAC;QACC,MAAM,YAAY,OAAO,UAAU,aAAa,MAAM,QAAQ;QAC9D,IAAI,aAAa;YACf,YAAY;QACd,OAAO;YACL,SAAS;QACX;QAEA,kDAAkD;QAClD,SAAS,MAAM,GAAG,GAAG,oBAAoB,CAAC,EAAE,UAAU,kBAAkB,EAAE,wBAAwB;IACpG,GACA;QAAC;QAAa;KAAK;IAGrB,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACtC,OAAO,WAAW,cAAc,CAAC,OAAS,CAAC,QAAQ,QAAQ,CAAC,OAAS,CAAC;IACxE,GAAG;QAAC;QAAU;QAAS;KAAc;IAErC,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,gBAAgB,CAAC;YACrB,IACE,MAAM,GAAG,KAAK,6BACd,CAAC,MAAM,OAAO,IAAI,MAAM,OAAO,GAC/B;gBACA,MAAM,cAAc;gBACpB;YACF;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;KAAc;IAElB,yEAAyE;IACzE,mEAAmE;IACnE,MAAM,QAAQ,OAAO,aAAa;IAElC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAC/B,IAAM,CAAC;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF,CAAC,GACD;QAAC;QAAO;QAAM;QAAS;QAAU;QAAY;QAAe;KAAc;IAG5E,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;kBAC9B,cAAA,8OAAC,mIAAA,CAAA,kBAAe;YAAC,eAAe;sBAC9B,cAAA,8OAAC;gBACC,aAAU;gBACV,OACE;oBACE,mBAAmB;oBACnB,wBAAwB;oBACxB,GAAG,KAAK;gBACV;gBAEF,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mFACA;gBAED,GAAG,KAAK;0BAER;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,QAAQ,EACf,OAAO,MAAM,EACb,UAAU,SAAS,EACnB,cAAc,WAAW,EACzB,SAAS,EACT,QAAQ,EACR,GAAG,OAKJ;IACC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEvD,IAAI,gBAAgB,QAAQ;QAC1B,qBACE,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;YAED,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,IAAI,UAAU;QACZ,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,MAAM;YAAY,cAAc;YAAgB,GAAG,KAAK;sBAC7D,cAAA,8OAAC,iIAAA,CAAA,eAAY;gBACX,gBAAa;gBACb,aAAU;gBACV,eAAY;gBACZ,WAAU;gBACV,OACE;oBACE,mBAAmB;gBACrB;gBAEF,MAAM;;kCAEN,8OAAC,iIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,iIAAA,CAAA,aAAU;0CAAC;;;;;;0CACZ,8OAAC,iIAAA,CAAA,mBAAgB;0CAAC;;;;;;;;;;;;kCAEpB,8OAAC;wBAAI,WAAU;kCAA+B;;;;;;;;;;;;;;;;;IAItD;IAEA,qBACE,8OAAC;QACC,WAAU;QACV,cAAY;QACZ,oBAAkB,UAAU,cAAc,cAAc;QACxD,gBAAc;QACd,aAAW;QACX,aAAU;;0BAGV,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2FACA,0CACA,sCACA,YAAY,cAAc,YAAY,UAClC,qFACA;;;;;;0BAGR,8OAAC;gBACC,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wHACA,SAAS,SACL,mFACA,oFACJ,sDAAsD;gBACtD,YAAY,cAAc,YAAY,UAClC,6FACA,2HACJ;gBAED,GAAG,KAAK;0BAET,cAAA,8OAAC;oBACC,gBAAa;oBACb,aAAU;oBACV,WAAU;8BAET;;;;;;;;;;;;;;;;;AAKX;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,OAAO,EACP,GAAG,OACiC;IACpC,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,gBAAa;QACb,aAAU;QACV,SAAQ;QACR,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;QACxB,SAAS,CAAC;YACR,UAAU;YACV;QACF;QACC,GAAG,KAAK;;0BAET,8OAAC,oNAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAuC;IAC1E,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,qBACE,8OAAC;QACC,gBAAa;QACb,aAAU;QACV,cAAW;QACX,UAAU,CAAC;QACX,SAAS;QACT,OAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mPACA,4EACA,0HACA,2JACA,6DACA,6DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAqC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA,mNACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACgC;IACnC,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EAAE,SAAS,EAAE,GAAG,OAAoC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACoC;IACvC,qBACE,8OAAC,qIAAA,CAAA,YAAS;QACR,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACiD;IACpD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4OACA,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,UAAU,KAAK,EACf,GAAG,OACoD;IACvD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8RACA,kDAAkD;QAClD,iDACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAmC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAmC;IAC1E,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,4BAA4B,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EAClC,+rBACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,SACE;QACJ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,kBAAkB,EACzB,UAAU,KAAK,EACf,WAAW,KAAK,EAChB,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,OAAO,EACP,SAAS,EACT,GAAG,OAK6C;IAChD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;IAE5B,+DAA+D;IAC/D,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBAC3B,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,aAAW;YACX,eAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B;gBAAE;gBAAS;YAAK,IAAI;YAC3D,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAM;QAAU;QAAS;QAAW;KAAM;IAEpD,0EAA0E;IAC1E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACnC,IAAI,CAAC,SAAS,OAAO;QACrB,OAAO,OAAO,YAAY,WAAW;YAAE,UAAU;QAAQ,IAAI;IAC/D,GAAG;QAAC;KAAQ;IAEZ,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,2EAA2E;IAC3E,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC7B,OAAO,UAAU,eAAe;IAClC,GAAG;QAAC;QAAO;KAAS;IAEpB,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,mIAAA,CAAA,iBAAc;gBAAC,OAAO;0BAAE;;;;;;0BACzB,8OAAC,mIAAA,CAAA,iBAAc;gBACb,MAAK;gBACL,OAAM;gBACN,QAAQ;gBACP,GAAG,cAAc;;;;;;;;;;;;AAI1B;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,UAAU,KAAK,EACf,cAAc,KAAK,EACnB,GAAG,OAIJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oVACA,kDAAkD;YAClD,iDACA,yCACA,gDACA,2CACA,wCACA,eACE,4LACF;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAW;QAAa;KAAM;AAC1C;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA,4HACA,yCACA,gDACA,2CACA,wCACA;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAW;KAAM;AACvB;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,WAAW,KAAK,EAChB,GAAG,OAGJ;IACC,kCAAkC;IAClC,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC1B,OAAO,GAAG,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC;IAClD,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAC5D,GAAG,KAAK;;YAER,0BACC,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;;;;;;0BAGjB,8OAAC,oIAAA,CAAA,WAAQ;gBACP,WAAU;gBACV,gBAAa;gBACb,OACE;oBACE,oBAAoB;gBACtB;;;;;;;;;;;;AAKV;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kGACA,wCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;QAC7C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,UAAU,KAAK,EACf,OAAO,IAAI,EACX,WAAW,KAAK,EAChB,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,kEAAkE;IAClE,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,kBACnB,8OAAC;YACC,aAAU;YACV,gBAAa;YACb,aAAW;YACX,eAAa;YACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA,0FACA,SAAS,QAAQ,WACjB,SAAS,QAAQ,WACjB,wCACA;YAED,GAAG,KAAK;;;;;kBAEV;QAAC;QAAM;QAAM;QAAU;QAAW;KAAM;AAC7C", "debugId": null}}, {"offset": {"line": 1881, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/navigation-link.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\nimport { ReactNode, MouseEvent, useMemo } from \"react\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\ninterface NavigationLinkProps {\r\n  href: string;\r\n  children: ReactNode;\r\n  className?: string;\r\n  onClick?: (e: MouseEvent<HTMLAnchorElement>) => void;\r\n  replace?: boolean;\r\n  prefetch?: boolean;\r\n  scroll?: boolean;\r\n  exact?: boolean;\r\n}\r\n\r\n/**\r\n * NavigationLink component that ensures client-side navigation\r\n * This component wraps Next.js Link component and provides a consistent way to navigate\r\n */\r\nexport function NavigationLink({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(className)}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n      aria-current={isActive ? \"page\" : undefined}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n\r\n/**\r\n * NavigationButton component that looks like a button but navigates like a link\r\n */\r\nexport function NavigationButton({\r\n  href,\r\n  children,\r\n  className,\r\n  onClick,\r\n  replace = false,\r\n  prefetch = true,\r\n  scroll = true,\r\n  exact = false,\r\n}: NavigationLinkProps) {\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Use useMemo to prevent recalculating isActive on every render\r\n  const isActive = useMemo(() => {\r\n    return exact ? pathname === href : pathname.startsWith(href);\r\n  }, [pathname, href, exact]);\r\n\r\n  const handleClick = (e: MouseEvent<HTMLAnchorElement>) => {\r\n    // Allow the default onClick handler to run if provided\r\n    if (onClick) {\r\n      onClick(e);\r\n    }\r\n\r\n    // Prevent default only if we're handling navigation ourselves\r\n    if (replace) {\r\n      e.preventDefault();\r\n      router.replace(href);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Link\r\n      href={href}\r\n      className={cn(\r\n        \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\r\n        className\r\n      )}\r\n      onClick={handleClick}\r\n      prefetch={prefetch}\r\n      scroll={scroll}\r\n      data-active={isActive}\r\n    >\r\n      {children}\r\n    </Link>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAsBO,SAAS,eAAe,EAC7B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;IACzD,GAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;QACb,gBAAc,WAAW,SAAS;kBAEjC;;;;;;AAGP;AAKO,SAAS,iBAAiB,EAC/B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,WAAW,IAAI,EACf,SAAS,IAAI,EACb,QAAQ,KAAK,EACO;IACpB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,gEAAgE;IAChE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO,QAAQ,aAAa,OAAO,SAAS,UAAU,CAAC;IACzD,GAAG;QAAC;QAAU;QAAM;KAAM;IAE1B,MAAM,cAAc,CAAC;QACnB,uDAAuD;QACvD,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,8DAA8D;QAC9D,IAAI,SAAS;YACX,EAAE,cAAc;YAChB,OAAO,OAAO,CAAC;QACjB;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;QAEF,SAAS;QACT,UAAU;QACV,QAAQ;QACR,eAAa;kBAEZ;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\r\n\r\nfunction Collapsible({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.Root>) {\r\n  return <CollapsiblePrimitive.Root data-slot=\"collapsible\" {...props} />\r\n}\r\n\r\nfunction CollapsibleTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleTrigger>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleTrigger\r\n      data-slot=\"collapsible-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CollapsibleContent({\r\n  ...props\r\n}: React.ComponentProps<typeof CollapsiblePrimitive.CollapsibleContent>) {\r\n  return (\r\n    <CollapsiblePrimitive.CollapsibleContent\r\n      data-slot=\"collapsible-content\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AAIA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,uKAAA,CAAA,OAAyB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AACrE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACkE;IACrE,qBACE,8OAAC,uKAAA,CAAA,qBAAuC;QACtC,aAAU;QACT,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2022, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-main.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\nimport { ChevronRight, CircleIcon, type LucideIcon } from \"lucide-react\";\r\n\r\nimport {\r\n  Collapsible,\r\n  CollapsibleContent,\r\n  CollapsibleTrigger,\r\n} from \"@/components/ui/collapsible\";\r\nimport {\r\n  SidebarGroup,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarMenuSub,\r\n  SidebarMenuSubButton,\r\n  SidebarMenuSubItem,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavMain({\r\n  items,\r\n}: {\r\n  items: {\r\n    title: string;\r\n    url: string;\r\n    icon?: LucideIcon;\r\n    isActive?: boolean;\r\n    items?: {\r\n      title: string;\r\n      url: string;\r\n    }[];\r\n  }[];\r\n}) {\r\n  return (\r\n    <SidebarGroup>\r\n      <SidebarMenu>\r\n        {items.map((item) => {\r\n          // Special case for Dashboard - make it a direct link\r\n          if (item.title === \"Dashboard\") {\r\n            return (\r\n              <SidebarMenuItem key={item.title}>\r\n                <SidebarMenuButton\r\n                  asChild\r\n                  tooltip={item.title}\r\n                  isActive={item.isActive}\r\n                  className=\"nav-main-item\"\r\n                >\r\n                  <NavigationLink href={item.url}>\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                  </NavigationLink>\r\n                </SidebarMenuButton>\r\n              </SidebarMenuItem>\r\n            );\r\n          }\r\n\r\n          // For all other items, use the collapsible dropdown\r\n          return (\r\n            <Collapsible\r\n              key={item.title}\r\n              asChild\r\n              defaultOpen={item.isActive}\r\n              className=\"group/collapsible mb-2\"\r\n            >\r\n              <SidebarMenuItem>\r\n                <CollapsibleTrigger asChild>\r\n                  <SidebarMenuButton\r\n                    tooltip={item.title}\r\n                    isActive={item.isActive}\r\n                    className=\"nav-main-item\"\r\n                  >\r\n                    <div className=\"relative\">\r\n                      {item.icon && <item.icon className=\"h-3.5 w-3.5\" />}\r\n                      {item.isActive && (\r\n                        <CircleIcon className=\"absolute -top-1 -right-1 h-2 w-2 text-primary fill-primary\" />\r\n                      )}\r\n                    </div>\r\n                    <span className=\"text-sm nav-main-item-text\">\r\n                      {item.title}\r\n                    </span>\r\n                    <ChevronRight className=\"ml-auto h-2 w-2 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90 nav-main-item-chevron\" />\r\n                  </SidebarMenuButton>\r\n                </CollapsibleTrigger>\r\n                <CollapsibleContent>\r\n                  <SidebarMenuSub className=\"nav-main-sub mt-2\">\r\n                    {item.items?.map((subItem) => (\r\n                      <SidebarMenuSubItem key={subItem.title}>\r\n                        <SidebarMenuSubButton asChild>\r\n                          <NavigationLink href={subItem.url} exact={true}>\r\n                            {/* No circle icon for child items */}\r\n                            <span className=\"text-xs\">{subItem.title}</span>\r\n                          </NavigationLink>\r\n                        </SidebarMenuSubButton>\r\n                      </SidebarMenuSubItem>\r\n                    ))}\r\n                  </SidebarMenuSub>\r\n                </CollapsibleContent>\r\n              </SidebarMenuItem>\r\n            </Collapsible>\r\n          );\r\n        })}\r\n      </SidebarMenu>\r\n    </SidebarGroup>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAEA;AAKA;AAVA;;;;;;AAoBO,SAAS,QAAQ,EACtB,KAAK,EAYN;IACC,qBACE,8OAAC,mIAAA,CAAA,eAAY;kBACX,cAAA,8OAAC,mIAAA,CAAA,cAAW;sBACT,MAAM,GAAG,CAAC,CAAC;gBACV,qDAAqD;gBACrD,IAAI,KAAK,KAAK,KAAK,aAAa;oBAC9B,qBACE,8OAAC,mIAAA,CAAA,kBAAe;kCACd,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,OAAO;4BACP,SAAS,KAAK,KAAK;4BACnB,UAAU,KAAK,QAAQ;4BACvB,WAAU;sCAEV,cAAA,8OAAC,8IAAA,CAAA,iBAAc;gCAAC,MAAM,KAAK,GAAG;;kDAC5B,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;4CAClC,KAAK,QAAQ,kBACZ,8OAAC,0MAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAG1B,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;uBAfG,KAAK,KAAK;;;;;gBAqBpC;gBAEA,oDAAoD;gBACpD,qBACE,8OAAC,uIAAA,CAAA,cAAW;oBAEV,OAAO;oBACP,aAAa,KAAK,QAAQ;oBAC1B,WAAU;8BAEV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;;0CACd,8OAAC,uIAAA,CAAA,qBAAkB;gCAAC,OAAO;0CACzB,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;oCAChB,SAAS,KAAK,KAAK;oCACnB,UAAU,KAAK,QAAQ;oCACvB,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,IAAI,kBAAI,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDAClC,KAAK,QAAQ,kBACZ,8OAAC,0MAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;sDAG1B,8OAAC;4CAAK,WAAU;sDACb,KAAK,KAAK;;;;;;sDAEb,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG5B,8OAAC,uIAAA,CAAA,qBAAkB;0CACjB,cAAA,8OAAC,mIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,KAAK,KAAK,EAAE,IAAI,CAAC,wBAChB,8OAAC,mIAAA,CAAA,qBAAkB;sDACjB,cAAA,8OAAC,mIAAA,CAAA,uBAAoB;gDAAC,OAAO;0DAC3B,cAAA,8OAAC,8IAAA,CAAA,iBAAc;oDAAC,MAAM,QAAQ,GAAG;oDAAE,OAAO;8DAExC,cAAA,8OAAC;wDAAK,WAAU;kEAAW,QAAQ,KAAK;;;;;;;;;;;;;;;;2CAJrB,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;mBA3BzC,KAAK,KAAK;;;;;YAyCrB;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 2234, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Avatar({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\r\n  return (\r\n    <AvatarPrimitive.Root\r\n      data-slot=\"avatar\"\r\n      className={cn(\r\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarImage({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\r\n  return (\r\n    <AvatarPrimitive.Image\r\n      data-slot=\"avatar-image\"\r\n      className={cn(\"aspect-square size-full\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AvatarFallback({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\r\n  return (\r\n    <AvatarPrimitive.Fallback\r\n      data-slot=\"avatar-fallback\"\r\n      className={cn(\r\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback }\r\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2286, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction DropdownMenu({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\r\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Trigger\r\n      data-slot=\"dropdown-menu-trigger\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuContent({\r\n  className,\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Portal>\r\n      <DropdownMenuPrimitive.Content\r\n        data-slot=\"dropdown-menu-content\"\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </DropdownMenuPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuItem({\r\n  className,\r\n  inset,\r\n  variant = \"default\",\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\r\n  inset?: boolean\r\n  variant?: \"default\" | \"destructive\"\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Item\r\n      data-slot=\"dropdown-menu-item\"\r\n      data-inset={inset}\r\n      data-variant={variant}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuCheckboxItem({\r\n  className,\r\n  children,\r\n  checked,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.CheckboxItem\r\n      data-slot=\"dropdown-menu-checkbox-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      checked={checked}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.CheckboxItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioGroup\r\n      data-slot=\"dropdown-menu-radio-group\"\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuRadioItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\r\n  return (\r\n    <DropdownMenuPrimitive.RadioItem\r\n      data-slot=\"dropdown-menu-radio-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\r\n        <DropdownMenuPrimitive.ItemIndicator>\r\n          <CircleIcon className=\"size-2 fill-current\" />\r\n        </DropdownMenuPrimitive.ItemIndicator>\r\n      </span>\r\n      {children}\r\n    </DropdownMenuPrimitive.RadioItem>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuLabel({\r\n  className,\r\n  inset,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.Label\r\n      data-slot=\"dropdown-menu-label\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\r\n  return (\r\n    <DropdownMenuPrimitive.Separator\r\n      data-slot=\"dropdown-menu-separator\"\r\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"dropdown-menu-shortcut\"\r\n      className={cn(\r\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSub({\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\r\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\r\n}\r\n\r\nfunction DropdownMenuSubTrigger({\r\n  className,\r\n  inset,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n  inset?: boolean\r\n}) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubTrigger\r\n      data-slot=\"dropdown-menu-sub-trigger\"\r\n      data-inset={inset}\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <ChevronRightIcon className=\"ml-auto size-4\" />\r\n    </DropdownMenuPrimitive.SubTrigger>\r\n  )\r\n}\r\n\r\nfunction DropdownMenuSubContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\r\n  return (\r\n    <DropdownMenuPrimitive.SubContent\r\n      data-slot=\"dropdown-menu-sub-content\"\r\n      className={cn(\r\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuPortal,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuLabel,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioGroup,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuSubContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2548, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/nav-user.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Chev<PERSON>UpDown, LogOut } from \"lucide-react\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport {\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  useSidebar,\r\n} from \"@/components/ui/sidebar\";\r\n\r\nexport function NavUser({\r\n  user,\r\n}: {\r\n  user: {\r\n    name: string;\r\n    email: string;\r\n    avatar: string;\r\n  };\r\n}) {\r\n  const { isMobile } = useSidebar();\r\n\r\n  return (\r\n    <SidebarMenu>\r\n      <SidebarMenuItem>\r\n        <DropdownMenu>\r\n          <DropdownMenuTrigger asChild>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground nav-user\"\r\n            >\r\n              <Avatar className=\"h-6 w-6 rounded-lg nav-user-avatar\">\r\n                <AvatarImage src={user.avatar} alt={user.name} />\r\n                <AvatarFallback className=\"rounded-lg text-xs\">\r\n                  {user.name.charAt(0)}\r\n                </AvatarFallback>\r\n              </Avatar>\r\n              <div className=\"grid flex-1 text-left leading-tight nav-user-details\">\r\n                <span className=\"truncate text-xs font-medium\">\r\n                  {user.name}\r\n                </span>\r\n                <span className=\"truncate text-[10px]\">{user.email}</span>\r\n              </div>\r\n              <ChevronsUpDown className=\"ml-auto h-3 w-3 nav-user-chevron\" />\r\n            </SidebarMenuButton>\r\n          </DropdownMenuTrigger>\r\n          <DropdownMenuContent\r\n            className=\"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg\"\r\n            side={isMobile ? \"bottom\" : \"right\"}\r\n            align=\"end\"\r\n            sideOffset={4}\r\n          >\r\n            <DropdownMenuLabel className=\"p-0 font-normal\">\r\n              <div className=\"flex items-center gap-2 px-1 py-1.5 text-left text-sm\">\r\n                <Avatar className=\"h-8 w-8 rounded-lg\">\r\n                  <AvatarImage src={user.avatar} alt={user.name} />\r\n                  <AvatarFallback className=\"rounded-lg\">CN</AvatarFallback>\r\n                </Avatar>\r\n                <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                  <span className=\"truncate font-medium\">{user.name}</span>\r\n                  <span className=\"truncate text-xs\">{user.email}</span>\r\n                </div>\r\n              </div>\r\n            </DropdownMenuLabel>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuGroup>\r\n              <DropdownMenuItem>\r\n                <BadgeCheck className=\"mr-2 h-3.5 w-3.5\" />\r\n                <span className=\"text-xs\">Account</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuGroup>\r\n            <DropdownMenuSeparator />\r\n            <DropdownMenuItem>\r\n              <LogOut className=\"mr-2 h-3.5 w-3.5\" />\r\n              <span className=\"text-xs\">Log out</span>\r\n            </DropdownMenuItem>\r\n          </DropdownMenuContent>\r\n        </DropdownMenu>\r\n      </SidebarMenuItem>\r\n    </SidebarMenu>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AASA;AAdA;;;;;;AAqBO,SAAS,QAAQ,EACtB,IAAI,EAOL;IACC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAE9B,qBACE,8OAAC,mIAAA,CAAA,cAAW;kBACV,cAAA,8OAAC,mIAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC,4IAAA,CAAA,eAAY;;kCACX,8OAAC,4IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,8OAAC,mIAAA,CAAA,oBAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,kIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,MAAM;4CAAE,KAAK,KAAK,IAAI;;;;;;sDAC7C,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB,KAAK,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;;8CAGtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;sDAAwB,KAAK,KAAK;;;;;;;;;;;;8CAEpD,8OAAC,8NAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,4IAAA,CAAA,sBAAmB;wBAClB,WAAU;wBACV,MAAM,WAAW,WAAW;wBAC5B,OAAM;wBACN,YAAY;;0CAEZ,8OAAC,4IAAA,CAAA,oBAAiB;gCAAC,WAAU;0CAC3B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,KAAK,MAAM;oDAAE,KAAK,KAAK,IAAI;;;;;;8DAC7C,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DAAa;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwB,KAAK,IAAI;;;;;;8DACjD,8OAAC;oDAAK,WAAU;8DAAoB,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIpD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,oBAAiB;0CAChB,cAAA,8OAAC,4IAAA,CAAA,mBAAgB;;sDACf,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,8OAAC,4IAAA,CAAA,wBAAqB;;;;;0CACtB,8OAAC,4IAAA,CAAA,mBAAgB;;kDACf,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 2807, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/app-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport \"@/components/ui/sidebar-collapsed.css\";\r\nimport \"@/components/ui/sidebar-compact.css\";\r\nimport {\r\n  Banknote,\r\n  BarChart3 as BarChart3Icon,\r\n  Building as BuildingIcon,\r\n  FileText,\r\n  GalleryVerticalEnd,\r\n  Home as HomeIcon,\r\n  Landmark,\r\n  Package,\r\n  Smartphone,\r\n  UserCheck,\r\n} from \"lucide-react\";\r\n\r\nimport { NavMain } from \"@/components/nav-main\";\r\nimport { NavUser } from \"@/components/nav-user\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n// Import usePermissions for future use\r\n// import { usePermissions } from \"@/features/auth/hooks/use-permissions\";\r\n// import { getRoutePermission } from \"@/lib/route-permissions\";\r\n// import { navigationPermissions } from \"@/lib/navigation-permissions\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  hasRouteAccess,\r\n  isNavi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>,\r\n} from \"@/lib/rbac-config\";\r\n\r\ntype NavItem = {\r\n  title: string;\r\n  url: string;\r\n  icon?: any;\r\n  isActive?: boolean;\r\n  items?: { title: string; url: string }[];\r\n};\r\n\r\n/**\r\n * Maps route permission actions to grant actions\r\n * This function is commented out for MVP, will be used in the future\r\n * when the permission system is fully implemented\r\n *\r\n * @param action The action from route permissions\r\n * @returns The corresponding grant action\r\n */\r\n// function mapActionToGrantAction(action: string): string {\r\n//   switch (action) {\r\n//     case \"view\":\r\n//       return \"read:any\";\r\n//     case \"create\":\r\n//       return \"create:any\";\r\n//     case \"update\":\r\n//       return \"update:any\";\r\n//     case \"delete\":\r\n//       return \"delete:any\";\r\n//     case \"manage\":\r\n//       return \"update:any\"; // Manage maps to update:any\r\n//     case \"transfer\":\r\n//       return \"update:any\"; // Transfer maps to update:any\r\n//     case \"report\":\r\n//       return \"read:any\"; // Report maps to read:any\r\n//     default:\r\n//       return `${action}:any`;\r\n//   }\r\n// }\r\n\r\nexport function AppSidebar() {\r\n  const { data: user } = useCurrentUser();\r\n  const pathname = usePathname();\r\n  // Permissions will be used in the future\r\n  // const { hasPermission, permissions } = usePermissions();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  // Define all navigation items\r\n  const getAllNavigationItems = (): NavItem[] => [\r\n    {\r\n      title: \"Dashboard\",\r\n      url: \"/dashboard\",\r\n      icon: HomeIcon,\r\n      isActive: pathname === \"/dashboard\" || pathname === \"/\",\r\n    },\r\n    {\r\n      title: \"Reports\",\r\n      url: \"/reports/sales-summary\",\r\n      icon: BarChart3Icon,\r\n      isActive:\r\n        pathname.startsWith(\"/reports\") ||\r\n        pathname.startsWith(\"/expense-analytics\"),\r\n      items: [\r\n        {\r\n          title: \"Sales Summary\",\r\n          url: \"/reports/sales-summary\",\r\n        },\r\n        {\r\n          title: \"Sales by Item\",\r\n          url: \"/reports/sales-by-item\",\r\n        },\r\n        {\r\n          title: \"Sales by Category\",\r\n          url: \"/reports/sales-by-category\",\r\n        },\r\n        {\r\n          title: \"Current Stock Levels\",\r\n          url: \"/reports/current-stock-levels\",\r\n        },\r\n        {\r\n          title: \"Stock History\",\r\n          url: \"/reports/stock-history\",\r\n        },\r\n        {\r\n          title: \"Banking Transactions\",\r\n          url: \"/reports/banking-transactions\",\r\n        },\r\n        {\r\n          title: \"Tax Report\",\r\n          url: \"/reports/tax-report\",\r\n        },\r\n        {\r\n          title: \"Banking Summary\",\r\n          url: \"/banking?tab=summary\",\r\n        },\r\n        {\r\n          title: \"M-Pesa Transactions\",\r\n          url: \"/reports/mpesa-transactions\",\r\n        },\r\n        {\r\n          title: \"Running Balances\",\r\n          url: \"/reports/running-balances\",\r\n        },\r\n        {\r\n          title: \"Cash Status\",\r\n          url: \"/reports/cash-status\",\r\n        },\r\n        {\r\n          title: \"Cash Float\",\r\n          url: \"/reports/cash-float\",\r\n        },\r\n        {\r\n          title: \"DSA Sales\",\r\n          url: \"/reports/dsa-sales\",\r\n        },\r\n        {\r\n          title: \"Receipts\",\r\n          url: \"/reports/receipts\",\r\n        },\r\n        {\r\n          title: \"Shifts\",\r\n          url: \"/reports/shifts\",\r\n        },\r\n        {\r\n          title: \"Phone Repairs\",\r\n          url: \"/reports/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"Stock Valuation\",\r\n          url: \"/inventory/valuation\",\r\n        },\r\n        {\r\n          title: \"Stock Aging\",\r\n          url: \"/reports/stock-aging\",\r\n        },\r\n        {\r\n          title: \"Stock Movement\",\r\n          url: \"/reports/stock-movement\",\r\n        },\r\n        {\r\n          title: \"Expense Analytics\",\r\n          url: \"/expense-analytics\",\r\n        },\r\n        {\r\n          title: \"Customers\",\r\n          url: \"/customers\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Administration\",\r\n      url: \"/users\",\r\n      icon: BuildingIcon,\r\n      isActive:\r\n        pathname.startsWith(\"/users\") ||\r\n        pathname.startsWith(\"/roles\") ||\r\n        pathname.startsWith(\"/rbac\") ||\r\n        pathname.startsWith(\"/branches\") ||\r\n        pathname.startsWith(\"/locations\") ||\r\n        pathname.startsWith(\"/employees\"),\r\n      items: [\r\n        // Tenants menu item removed\r\n        {\r\n          title: \"Users\",\r\n          url: \"/users\",\r\n        },\r\n        {\r\n          title: \"Roles\",\r\n          url: \"/roles\",\r\n        },\r\n        {\r\n          title: \"RBAC\",\r\n          url: \"/rbac\",\r\n        },\r\n        {\r\n          title: \"Branches\",\r\n          url: \"/branches\",\r\n        },\r\n        {\r\n          title: \"Locations\",\r\n          url: \"/locations\",\r\n        },\r\n        {\r\n          title: \"Employees\",\r\n          url: \"/employees\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Stock & Inventory\",\r\n      url: \"/products\",\r\n      icon: Package,\r\n      isActive:\r\n        pathname.startsWith(\"/products\") ||\r\n        pathname.startsWith(\"/categories\") ||\r\n        pathname.startsWith(\"/brands\") ||\r\n        pathname.startsWith(\"/inventory\") ||\r\n        pathname.includes(\"/inventory/stock-cards\") ||\r\n        pathname.includes(\"/inventory/purchases\"),\r\n      items: [\r\n        {\r\n          title: \"Products\",\r\n          url: \"/products\",\r\n        },\r\n        {\r\n          title: \"Categories\",\r\n          url: \"/categories\",\r\n        },\r\n        {\r\n          title: \"Brands\",\r\n          url: \"/brands\",\r\n        },\r\n        {\r\n          title: \"Stock levels\",\r\n          url: \"/inventory\",\r\n        },\r\n        // Commented out Stock Locations as requested\r\n        // {\r\n        //   title: \"Stock Locations\",\r\n        //   url: \"/inventory/locations\",\r\n        // },\r\n        {\r\n          title: \"Purchases\",\r\n          url: \"/inventory/purchases\",\r\n        },\r\n        {\r\n          title: \"Create Purchase\",\r\n          url: \"/inventory/purchases/new\",\r\n        },\r\n        {\r\n          title: \"Stock Requests\",\r\n          url: \"/inventory/stock-requests\",\r\n        },\r\n        {\r\n          title: \"Stock Transfers\",\r\n          url: \"/inventory/transfers\",\r\n        },\r\n        {\r\n          title: \"Make Stock Transfer\",\r\n          url: \"/inventory/bulk-transfer\",\r\n        },\r\n        {\r\n          title: \"Stock Transfer History\",\r\n          url: \"/inventory/bulk-transfers\",\r\n        },\r\n        // {\r\n        //   title: \"Stock Cards\",\r\n        //   url: \"/inventory/stock-cards\",\r\n        // },\r\n        {\r\n          title: \"Inventory Reports\",\r\n          url: \"/inventory/reports\",\r\n        },\r\n        {\r\n          title: \"Excel Import/Export\",\r\n          url: \"/inventory/excel\",\r\n        },\r\n        {\r\n          title: \"Price List\",\r\n          url: \"/inventory/price-list\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Float Management\",\r\n      url: \"/float\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/float\"),\r\n      items: [\r\n        {\r\n          title: \"Float Dashboard\",\r\n          url: \"/float\",\r\n        },\r\n        {\r\n          title: \"Float Movements\",\r\n          url: \"/float/movements\",\r\n        },\r\n        {\r\n          title: \"Float Reconciliations\",\r\n          url: \"/float/reconciliations\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Banking Management\",\r\n      url: \"/banking\",\r\n      icon: Landmark,\r\n      isActive: pathname.startsWith(\"/banking\"),\r\n      items: [\r\n        {\r\n          title: \"Banking Records\",\r\n          url: \"/banking\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Expenses Management\",\r\n      url: \"/expenses\",\r\n      icon: Banknote,\r\n      isActive: pathname.startsWith(\"/expenses\"),\r\n      items: [\r\n        {\r\n          title: \"Expenses\",\r\n          url: \"/expenses\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"Invoice Management\",\r\n      url: \"/invoices\",\r\n      icon: FileText,\r\n      isActive:\r\n        pathname.startsWith(\"/invoices\") ||\r\n        pathname.startsWith(\"/credit-notes\"),\r\n      items: [\r\n        {\r\n          title: \"All Invoices\",\r\n          url: \"/invoices\",\r\n        },\r\n        {\r\n          title: \"Credit Notes\",\r\n          url: \"/credit-notes\",\r\n        },\r\n      ],\r\n    },\r\n    {\r\n      title: \"DSA Management\",\r\n      url: \"/dsa\",\r\n      icon: UserCheck,\r\n      isActive: pathname.startsWith(\"/dsa\"),\r\n      items: [\r\n        {\r\n          title: \"DSA Dashboard\",\r\n          url: \"/dsa\",\r\n        },\r\n        {\r\n          title: \"DSA Agents\",\r\n          url: \"/dsa/customers\",\r\n        },\r\n        {\r\n          title: \"Stock Assignments\",\r\n          url: \"/dsa/assignments\",\r\n        },\r\n        // Commented out DSA Reconciliations as requested\r\n        // {\r\n        //   title: \"DSA Reconciliations\",\r\n        //   url: \"/dsa/reconciliations\",\r\n        // },\r\n      ],\r\n    },\r\n    // {\r\n    //   title: \"Procurement\",\r\n    //   url: \"/procurement\",\r\n    //   icon: ShoppingCart,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/procurement\") ||\r\n    //     pathname.startsWith(\"/suppliers\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"Procurement Dashboard\",\r\n    //       url: \"/procurement\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Procurement Requests\",\r\n    //       url: \"/procurement/requests\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Create Request\",\r\n    //       url: \"/procurement/requests/new\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Procurement Receipts\",\r\n    //       url: \"/procurement/receipts\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Suppliers\",\r\n    //       url: \"/suppliers\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    {\r\n      title: \"Phone Repairs\",\r\n      url: \"/phone-repairs\",\r\n      icon: Smartphone,\r\n      isActive:\r\n        pathname === \"/phone-repairs\" || pathname.startsWith(\"/phone-repairs/\")\r\n          ? true\r\n          : false,\r\n      items: [\r\n        {\r\n          title: \"All Repairs\",\r\n          url: \"/phone-repairs\",\r\n        },\r\n        {\r\n          title: \"New Repair\",\r\n          url: \"/phone-repairs/new\",\r\n        },\r\n      ],\r\n    },\r\n    // Settings section completely hidden for production - not implemented\r\n    // {\r\n    //   title: \"Settings\",\r\n    //   url: \"/settings\",\r\n    //   icon: Settings,\r\n    //   isActive:\r\n    //     pathname.startsWith(\"/settings\") || pathname.startsWith(\"/profile\"),\r\n    //   items: [\r\n    //     {\r\n    //       title: \"System Settings\",\r\n    //       url: \"/settings/system\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Company Settings\",\r\n    //       url: \"/settings/company\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Payment Methods\",\r\n    //       url: \"/settings/payment-methods\",\r\n    //     },\r\n    //     {\r\n    //       title: \"System Health\",\r\n    //       url: \"/settings/health\",\r\n    //     },\r\n    //     {\r\n    //       title: \"Profile\",\r\n    //       url: \"/profile\",\r\n    //     },\r\n    //   ],\r\n    // },\r\n    // Permissions Demo section removed\r\n  ];\r\n\r\n  // Filter navigation items based on RBAC configuration\r\n  const getFilteredNavigationItemsByRole = (): NavItem[] => {\r\n    const allItems = getAllNavigationItems();\r\n\r\n    console.log(\r\n      `[Navigation] ${userRoleName} role detected - using RBAC-based filtering`\r\n    );\r\n\r\n    // Filter navigation items based on RBAC configuration\r\n    const filteredItems = allItems.filter((item) => {\r\n      // Check if the entire navigation group should be hidden for this role\r\n      if (isNavigationItemHidden(item.title, userRoleName)) {\r\n        console.log(\r\n          `[Navigation] Hiding section \"${item.title}\" for role ${userRoleName}`\r\n        );\r\n        return false;\r\n      }\r\n\r\n      // Check if user has access to the main route of this navigation item\r\n      if (!hasRouteAccess(item.url, userRoleName)) {\r\n        console.log(\r\n          `[Navigation] No route access to \"${item.url}\" for role ${userRoleName}`\r\n        );\r\n        return false;\r\n      }\r\n\r\n      console.log(\r\n        `[Navigation] Allowing section \"${item.title}\" for role ${userRoleName}`\r\n      );\r\n      return true;\r\n    });\r\n\r\n    // Filter subitems based on RBAC configuration\r\n    const itemsWithFilteredSubitems = filteredItems.map((item) => {\r\n      // If the item has subitems, filter them based on RBAC\r\n      if (item.items && item.items.length > 0) {\r\n        console.log(\r\n          `[Navigation] Checking subitems for \"${item.title}\" section`\r\n        );\r\n\r\n        const filteredSubItems = item.items.filter((subItem) => {\r\n          // Check if user has route access for this subitem\r\n          if (!hasRouteAccess(subItem.url, userRoleName)) {\r\n            console.log(\r\n              `[Navigation] No route access to \"${subItem.url}\" for role ${userRoleName}`\r\n            );\r\n            return false;\r\n          }\r\n\r\n          // Special handling for RBAC items - only super_admin can see them\r\n          if (subItem.title === \"RBAC\" || subItem.url.includes(\"rbac\")) {\r\n            return userRoleName === ROLES.SUPER_ADMIN;\r\n          }\r\n\r\n          console.log(\r\n            `[Navigation] Allowing subitem \"${subItem.title}\" for role ${userRoleName}`\r\n          );\r\n          return true;\r\n        });\r\n\r\n        // Update the item's subitems with the filtered list\r\n        item.items = filteredSubItems;\r\n      }\r\n\r\n      return item;\r\n    });\r\n\r\n    // Only return items that have at least one subitem (if they had subitems to begin with)\r\n    const result = itemsWithFilteredSubitems.filter(\r\n      (item) => !item.items || item.items.length > 0\r\n    );\r\n\r\n    return result;\r\n  };\r\n\r\n  // Filter navigation items based on permissions (for future use)\r\n  // This function is commented out for MVP, will be implemented in the future\r\n  // when the permission system is fully implemented\r\n\r\n  // Sample data for the sidebar\r\n  const data = {\r\n    user: {\r\n      name: user?.name || \"User\",\r\n      email: user?.email || \"<EMAIL>\",\r\n      avatar: \"/placeholder-avatar.jpg\",\r\n    },\r\n    teams: [\r\n      {\r\n        name: teamData.name,\r\n        logo: GalleryVerticalEnd,\r\n        plan: teamData.plan,\r\n      },\r\n    ],\r\n    // For MVP, use role-based filtering\r\n    navMain: getFilteredNavigationItemsByRole(),\r\n    // For future: navMain: getFilteredNavigationItems(),\r\n  };\r\n\r\n  const { state, isMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\" && !isMobile;\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        \"h-full flex flex-col sidebar-compact\",\r\n        isCollapsed && \"sidebar-collapsed\"\r\n      )}\r\n    >\r\n      <div className=\"flex-1 overflow-y-auto py-2\">\r\n        <div className={cn(\"px-3\", isCollapsed && \"px-2\")}>\r\n          <NavMain items={data.navMain} />\r\n        </div>\r\n      </div>\r\n      <div className={cn(\"p-3 border-t\", isCollapsed && \"p-2\")}>\r\n        <NavUser user={data.user} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AACA;AACA,uCAAuC;AACvC,0EAA0E;AAC1E,gEAAgE;AAChE,wEAAwE;AACxE;AACA;AACA;AA3BA;;;;;;;;;;;;AAsEO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,yCAAyC;IACzC,2DAA2D;IAE3D,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,8BAA8B;IAC9B,MAAM,wBAAwB,IAAiB;YAC7C;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,mMAAA,CAAA,OAAQ;gBACd,UAAU,aAAa,gBAAgB,aAAa;YACtD;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,kNAAA,CAAA,YAAa;gBACnB,UACE,SAAS,UAAU,CAAC,eACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAY;gBAClB,UACE,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,aACpB,SAAS,UAAU,CAAC,YACpB,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL,4BAA4B;oBAC5B;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,wMAAA,CAAA,UAAO;gBACb,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC,kBACpB,SAAS,UAAU,CAAC,cACpB,SAAS,UAAU,CAAC,iBACpB,SAAS,QAAQ,CAAC,6BAClB,SAAS,QAAQ,CAAC;gBACpB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,6CAA6C;oBAC7C,IAAI;oBACJ,8BAA8B;oBAC9B,iCAAiC;oBACjC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA,IAAI;oBACJ,0BAA0B;oBAC1B,mCAAmC;oBACnC,KAAK;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,0MAAA,CAAA,WAAQ;gBACd,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,8MAAA,CAAA,WAAQ;gBACd,UACE,SAAS,UAAU,CAAC,gBACpB,SAAS,UAAU,CAAC;gBACtB,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;YACA;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,gNAAA,CAAA,YAAS;gBACf,UAAU,SAAS,UAAU,CAAC;gBAC9B,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBAMD;YACH;YACA,IAAI;YACJ,0BAA0B;YAC1B,yBAAyB;YACzB,wBAAwB;YACxB,cAAc;YACd,6CAA6C;YAC7C,yCAAyC;YACzC,aAAa;YACb,QAAQ;YACR,wCAAwC;YACxC,6BAA6B;YAC7B,SAAS;YACT,QAAQ;YACR,uCAAuC;YACvC,sCAAsC;YACtC,SAAS;YACT,QAAQ;YACR,iCAAiC;YACjC,0CAA0C;YAC1C,SAAS;YACT,QAAQ;YACR,uCAAuC;YACvC,sCAAsC;YACtC,SAAS;YACT,QAAQ;YACR,4BAA4B;YAC5B,2BAA2B;YAC3B,SAAS;YACT,OAAO;YACP,KAAK;YACL;gBACE,OAAO;gBACP,KAAK;gBACL,MAAM,8MAAA,CAAA,aAAU;gBAChB,UACE,aAAa,oBAAoB,SAAS,UAAU,CAAC,qBACjD,OACA;gBACN,OAAO;oBACL;wBACE,OAAO;wBACP,KAAK;oBACP;oBACA;wBACE,OAAO;wBACP,KAAK;oBACP;iBACD;YACH;SAgCD;IAED,sDAAsD;IACtD,MAAM,mCAAmC;QACvC,MAAM,WAAW;QAEjB,QAAQ,GAAG,CACT,CAAC,aAAa,EAAE,aAAa,2CAA2C,CAAC;QAG3E,sDAAsD;QACtD,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAC;YACrC,sEAAsE;YACtE,IAAI,CAAA,GAAA,4HAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,KAAK,EAAE,eAAe;gBACpD,QAAQ,GAAG,CACT,CAAC,6BAA6B,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,cAAc;gBAExE,OAAO;YACT;YAEA,qEAAqE;YACrE,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG,EAAE,eAAe;gBAC3C,QAAQ,GAAG,CACT,CAAC,iCAAiC,EAAE,KAAK,GAAG,CAAC,WAAW,EAAE,cAAc;gBAE1E,OAAO;YACT;YAEA,QAAQ,GAAG,CACT,CAAC,+BAA+B,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,cAAc;YAE1E,OAAO;QACT;QAEA,8CAA8C;QAC9C,MAAM,4BAA4B,cAAc,GAAG,CAAC,CAAC;YACnD,sDAAsD;YACtD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACvC,QAAQ,GAAG,CACT,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,SAAS,CAAC;gBAG9D,MAAM,mBAAmB,KAAK,KAAK,CAAC,MAAM,CAAC,CAAC;oBAC1C,kDAAkD;oBAClD,IAAI,CAAC,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,GAAG,EAAE,eAAe;wBAC9C,QAAQ,GAAG,CACT,CAAC,iCAAiC,EAAE,QAAQ,GAAG,CAAC,WAAW,EAAE,cAAc;wBAE7E,OAAO;oBACT;oBAEA,kEAAkE;oBAClE,IAAI,QAAQ,KAAK,KAAK,UAAU,QAAQ,GAAG,CAAC,QAAQ,CAAC,SAAS;wBAC5D,OAAO,iBAAiB,4HAAA,CAAA,QAAK,CAAC,WAAW;oBAC3C;oBAEA,QAAQ,GAAG,CACT,CAAC,+BAA+B,EAAE,QAAQ,KAAK,CAAC,WAAW,EAAE,cAAc;oBAE7E,OAAO;gBACT;gBAEA,oDAAoD;gBACpD,KAAK,KAAK,GAAG;YACf;YAEA,OAAO;QACT;QAEA,wFAAwF;QACxF,MAAM,SAAS,0BAA0B,MAAM,CAC7C,CAAC,OAAS,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG;QAG/C,OAAO;IACT;IAEA,gEAAgE;IAChE,4EAA4E;IAC5E,kDAAkD;IAElD,8BAA8B;IAC9B,MAAM,OAAO;QACX,MAAM;YACJ,MAAM,MAAM,QAAQ;YACpB,OAAO,MAAM,SAAS;YACtB,QAAQ;QACV;QACA,OAAO;YACL;gBACE,MAAM,SAAS,IAAI;gBACnB,MAAM,sOAAA,CAAA,qBAAkB;gBACxB,MAAM,SAAS,IAAI;YACrB;SACD;QACD,oCAAoC;QACpC,SAAS;IAEX;IAEA,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACrC,MAAM,cAAc,UAAU,eAAe,CAAC;IAE9C,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wCACA,eAAe;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,eAAe;8BACxC,cAAA,8OAAC,iIAAA,CAAA,UAAO;wBAAC,OAAO,KAAK,OAAO;;;;;;;;;;;;;;;;0BAGhC,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,eAAe;0BAChD,cAAA,8OAAC,iIAAA,CAAA,UAAO;oBAAC,MAAM,KAAK,IAAI;;;;;;;;;;;;;;;;;AAIhC", "debugId": null}}, {"offset": {"line": 3327, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/team-header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\n// import { GalleryVerticalEnd } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { useCurrentUser } from \"@/features/auth/hooks/use-auth\";\r\n\r\nexport function TeamHeader() {\r\n  const { data: user } = useCurrentUser();\r\n\r\n  // Safely access role_name with type checking\r\n  const userRoleName =\r\n    user && typeof user === \"object\" && \"role_name\" in user\r\n      ? user.role_name\r\n      : \"\";\r\n\r\n  // Get team name and plan based on user role\r\n  const getTeamData = () => {\r\n    if (user && typeof user === \"object\") {\r\n      if (userRoleName === \"super_admin\") {\r\n        return {\r\n          name: \"DukaLink\",\r\n          plan: \"Enterprise\",\r\n        };\r\n      } else if (\r\n        userRoleName === \"tenant_admin\" ||\r\n        userRoleName === \"company_admin\"\r\n      ) {\r\n        // For tenant/company admin, show tenant name and \"Company\" as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: \"Company\",\r\n        };\r\n      } else if (userRoleName === \"branch_manager\") {\r\n        // For branch manager, show tenant name and branch name as plan\r\n        return {\r\n          name: user.tenant?.name || \"Tenant\",\r\n          plan: user.branch?.name || \"Branch\",\r\n        };\r\n      }\r\n    }\r\n    return {\r\n      name: \"DukaLink\",\r\n      plan: \"Enterprise\",\r\n    };\r\n  };\r\n\r\n  const teamData = getTeamData();\r\n\r\n  return (\r\n    <div className=\"flex items-center gap-2\">\r\n      <div className=\"bg-white text-primary flex aspect-square size-6 items-center justify-center rounded-lg\">\r\n        {/* <GalleryVerticalEnd className=\"size-3\" /> */}\r\n        <Image\r\n          src={\"/images/simbatelecomlogo.png\"}\r\n          alt=\"Simba Telecom Logo\"\r\n          className=\"object-contain\"\r\n          width={16}\r\n          height={16}\r\n        />\r\n      </div>\r\n      <div className=\"grid flex-1 text-left leading-tight\">\r\n        <span className=\"truncate text-sm font-semibold text-primary-foreground\">\r\n          {teamData.name}\r\n        </span>\r\n        <span className=\"truncate text-xs text-primary-foreground/90 font-medium\">\r\n          {teamData.plan}\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA,qDAAqD;AACrD;AACA;AAJA;;;;AAMO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IAEpC,6CAA6C;IAC7C,MAAM,eACJ,QAAQ,OAAO,SAAS,YAAY,eAAe,OAC/C,KAAK,SAAS,GACd;IAEN,4CAA4C;IAC5C,MAAM,cAAc;QAClB,IAAI,QAAQ,OAAO,SAAS,UAAU;YACpC,IAAI,iBAAiB,eAAe;gBAClC,OAAO;oBACL,MAAM;oBACN,MAAM;gBACR;YACF,OAAO,IACL,iBAAiB,kBACjB,iBAAiB,iBACjB;gBACA,mEAAmE;gBACnE,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM;gBACR;YACF,OAAO,IAAI,iBAAiB,kBAAkB;gBAC5C,+DAA+D;gBAC/D,OAAO;oBACL,MAAM,KAAK,MAAM,EAAE,QAAQ;oBAC3B,MAAM,KAAK,MAAM,EAAE,QAAQ;gBAC7B;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IAEA,MAAM,WAAW;IAEjB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,WAAU;oBACV,OAAO;oBACP,QAAQ;;;;;;;;;;;0BAGZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;kCAEhB,8OAAC;wBAAK,WAAU;kCACb,SAAS,IAAI;;;;;;;;;;;;;;;;;;AAKxB", "debugId": null}}, {"offset": {"line": 3429, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/search-button.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useSearch } from \"@/components/providers/search-provider\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\n\r\nexport function SearchButton() {\r\n  const { openSearch } = useSearch();\r\n\r\n  return (\r\n    <Button\r\n      variant=\"outline\"\r\n      size=\"sm\"\r\n      onClick={openSearch}\r\n      className=\"relative h-9 w-full justify-start bg-background text-sm font-normal text-muted-foreground shadow-none sm:pr-12 md:w-40 lg:w-64\"\r\n    >\r\n      <span className=\"hidden lg:inline-flex\">Search anything...</span>\r\n      <span className=\"inline-flex lg:hidden\">Search...</span>\r\n      <kbd className=\"pointer-events-none absolute right-1.5 top-1.5 hidden h-6 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex\">\r\n        <span className=\"text-xs\">⌘</span>K\r\n      </kbd>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,YAAS,AAAD;IAE/B,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,8OAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,8OAAC;gBAAK,WAAU;0BAAwB;;;;;;0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAU;;;;;;oBAAQ;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 3494, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/top-navigation.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport {\r\n  Bell,\r\n  ChevronDown,\r\n  ChevronLeft,\r\n  LogOut,\r\n  Settings,\r\n  User,\r\n  PanelLeftIcon,\r\n  PanelRightIcon,\r\n  Home,\r\n} from \"lucide-react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useCurrentUser, useLogout } from \"@/features/auth/hooks/use-auth\";\r\nimport { TeamHeader } from \"@/components/team-header\";\r\nimport { SearchButton } from \"@/components/search-button\";\r\n\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuGroup,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useSidebar } from \"@/components/ui/sidebar\";\r\nimport Link from \"next/link\";\r\n\r\nexport function TopNavigation() {\r\n  const { data: user } = useCurrentUser();\r\n  const logout = useLogout();\r\n  const { toggleSidebar, state, isMobile, setOpenMobile } = useSidebar();\r\n  const isExpanded = state === \"expanded\";\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  // Check if we're on the stock requests page\r\n  const isStockRequestsPage = pathname.includes('/inventory/stock-requests') &&\r\n    !pathname.includes('/create') &&\r\n    !pathname.match(/\\/inventory\\/stock-requests\\/\\d+/);\r\n\r\n  const handleLogout = () => {\r\n    logout.mutate();\r\n  };\r\n\r\n  const handleBackClick = () => {\r\n    router.back();\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-30 flex h-14 shrink-0 items-center gap-2 border-b border-primary-foreground/10 bg-primary text-primary-foreground transition-[width,height] ease-linear\">\r\n      <div className=\"flex flex-1 items-center justify-between px-4 w-full\">\r\n        <div className=\"flex items-center gap-4\">\r\n          <div className=\"flex items-center gap-2\">\r\n            {isMobile && isStockRequestsPage ? (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={handleBackClick}\r\n                aria-label=\"Go back\"\r\n                title=\"Go back\"\r\n              >\r\n                <ChevronLeft className=\"h-5 w-5\" />\r\n                <span className=\"sr-only\">Go Back</span>\r\n              </Button>\r\n            ) : (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                className=\"-ml-1 h-8 w-8 text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                onClick={() => (isMobile ? setOpenMobile(true) : toggleSidebar())}\r\n                data-mobile={isMobile ? \"true\" : \"false\"}\r\n                aria-label=\"Toggle sidebar\"\r\n                title={isExpanded ? \"Collapse sidebar\" : \"Expand sidebar\"}\r\n              >\r\n                {isExpanded ? (\r\n                  <PanelLeftIcon className=\"h-5 w-5\" />\r\n                ) : (\r\n                  <PanelRightIcon className=\"h-5 w-5\" />\r\n                )}\r\n                <span className=\"sr-only\">Toggle Sidebar</span>\r\n              </Button>\r\n            )}\r\n            <TeamHeader />\r\n\r\n            <div className=\"h-6 border-r border-primary-foreground/20 mx-2\"></div>\r\n\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n              asChild\r\n            >\r\n              <Link href=\"/dashboard\">\r\n                <Home className=\"h-4 w-4 mr-1\" />\r\n                Dashboard\r\n              </Link>\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"flex items-center justify-center\">\r\n          <SearchButton />\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            className=\"relative h-8 w-8 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n            aria-label=\"Notifications\"\r\n          >\r\n            <Bell className=\"h-5 w-5\" />\r\n            <span className=\"absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-white text-[10px] font-bold text-primary\">\r\n              3\r\n            </span>\r\n          </Button>\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 gap-2 rounded-full text-primary-foreground hover:bg-primary-foreground/20 hover:text-primary-foreground\"\r\n                aria-label=\"Account\"\r\n              >\r\n                <Avatar className=\"h-7 w-7\">\r\n                  <AvatarImage\r\n                    src=\"/placeholder-avatar.jpg\"\r\n                    alt={user?.name || \"User\"}\r\n                  />\r\n                  <AvatarFallback className=\"text-xs font-semibold\">\r\n                    {user?.name ? user.name.charAt(0) : \"U\"}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n                <span className=\"hidden text-sm font-semibold md:inline-block\">\r\n                  {user?.name || \"User\"}\r\n                </span>\r\n                <ChevronDown className=\"h-4 w-4 text-primary-foreground/80\" />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\" className=\"w-56 rounded-lg\">\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-semibold leading-none\">\r\n                    {user?.name || \"User\"}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || \"<EMAIL>\"}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuGroup>\r\n                <DropdownMenuItem>\r\n                  <User className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Profile</span>\r\n                </DropdownMenuItem>\r\n                <DropdownMenuItem>\r\n                  <Settings className=\"mr-2 h-4 w-4\" />\r\n                  <span className=\"text-sm\">Settings</span>\r\n                </DropdownMenuItem>\r\n              </DropdownMenuGroup>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem onClick={handleLogout}>\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span className=\"text-sm\">Log out</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AAEA;AACA;AASA;AACA;AACA;AA/BA;;;;;;;;;;;;AAiCO,SAAS;IACd,MAAM,EAAE,MAAM,IAAI,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACnE,MAAM,aAAa,UAAU;IAC7B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,4CAA4C;IAC5C,MAAM,sBAAsB,SAAS,QAAQ,CAAC,gCAC5C,CAAC,SAAS,QAAQ,CAAC,cACnB,CAAC,SAAS,KAAK,CAAC;IAElB,MAAM,eAAe;QACnB,OAAO,MAAM;IACf;IAEA,MAAM,kBAAkB;QACtB,OAAO,IAAI;IACb;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,YAAY,oCACX,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,cAAW;gCACX,OAAM;;kDAEN,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;qDAG5B,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAO,WAAW,cAAc,QAAQ;gCACjD,eAAa,WAAW,SAAS;gCACjC,cAAW;gCACX,OAAO,aAAa,qBAAqB;;oCAExC,2BACC,8OAAC,oNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;6DAEzB,8OAAC,sNAAA,CAAA,iBAAc;wCAAC,WAAU;;;;;;kDAE5B,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;0CAG9B,8OAAC,oIAAA,CAAA,aAAU;;;;;0CAEX,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;0CAEP,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;;sDACT,8OAAC,mMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,sIAAA,CAAA,eAAY;;;;;;;;;;8BAGf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,cAAW;;8CAEX,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAA6H;;;;;;;;;;;;sCAK/I,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,cAAW;;0DAEX,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,kIAAA,CAAA,cAAW;wDACV,KAAI;wDACJ,KAAK,MAAM,QAAQ;;;;;;kEAErB,8OAAC,kIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACvB,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK;;;;;;;;;;;;0DAGxC,8OAAC;gDAAK,WAAU;0DACb,MAAM,QAAQ;;;;;;0DAEjB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG3B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,8OAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ;;;;;;kEAEjB,8OAAC;wDAAE,WAAU;kEACV,MAAM,SAAS;;;;;;;;;;;;;;;;;sDAItB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,oBAAiB;;8DAChB,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;8DAE5B,8OAAC,4IAAA,CAAA,mBAAgB;;sEACf,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;sDAG9B,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDACtB,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1C", "debugId": null}}, {"offset": {"line": 3929, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<\"nav\">) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<\"ol\">) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        \"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn(\"inline-flex items-center gap-1.5\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"a\"> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : \"a\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn(\"hover:text-foreground transition-colors\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn(\"text-foreground font-normal\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"li\">) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"[&>svg]:size-3.5\", className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"span\">) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn(\"flex size-9 items-center justify-center\", className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AAAA;AAEA;;;;;AAEA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,8OAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sNAAA,CAAA,eAAY;;;;;;;;;;AAGhC;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 4061, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/breadcrumbs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { usePathname } from \"next/navigation\";\r\nimport {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n} from \"@/components/ui/breadcrumb\";\r\nimport { NavigationLink } from \"@/components/ui/navigation-link\";\r\n\r\nexport function Breadcrumbs() {\r\n  const pathname = usePathname();\r\n\r\n  // Get the current page name from the pathname\r\n  const getPageName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length === 0) return \"Dashboard\";\r\n\r\n    // Get the last segment of the path and capitalize it\r\n    const lastSegment = path[path.length - 1];\r\n    return (\r\n      lastSegment.charAt(0).toUpperCase() +\r\n      lastSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  // Get the parent path for breadcrumb\r\n  const getParentPath = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Remove the last segment to get the parent path\r\n    return \"/\" + path.slice(0, path.length - 1).join(\"/\");\r\n  };\r\n\r\n  // Get the parent name for breadcrumb\r\n  const getParentName = () => {\r\n    const path = pathname.split(\"/\").filter(Boolean);\r\n    if (path.length <= 1) return null;\r\n\r\n    // Get the second-to-last segment and capitalize it\r\n    const parentSegment = path[path.length - 2];\r\n    return (\r\n      parentSegment.charAt(0).toUpperCase() +\r\n      parentSegment.slice(1).replace(/-/g, \" \")\r\n    );\r\n  };\r\n\r\n  const parentPath = getParentPath();\r\n  const parentName = getParentName();\r\n  const pageName = getPageName();\r\n\r\n  return (\r\n    <div className=\"mb-4\">\r\n      <Breadcrumb>\r\n        <BreadcrumbList>\r\n          {parentPath && parentName && (\r\n            <>\r\n              <BreadcrumbItem className=\"hidden md:block\">\r\n                <BreadcrumbLink asChild>\r\n                  <NavigationLink\r\n                    href={parentPath}\r\n                    className=\"text-sm font-medium text-muted-foreground hover:text-foreground\"\r\n                  >\r\n                    {parentName}\r\n                  </NavigationLink>\r\n                </BreadcrumbLink>\r\n              </BreadcrumbItem>\r\n              <BreadcrumbSeparator className=\"hidden md:block h-3 w-3\" />\r\n            </>\r\n          )}\r\n          <BreadcrumbItem>\r\n            <BreadcrumbPage className=\"text-sm font-semibold\">\r\n              {pageName}\r\n            </BreadcrumbPage>\r\n          </BreadcrumbItem>\r\n        </BreadcrumbList>\r\n      </Breadcrumb>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AAXA;;;;;AAaO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,8CAA8C;IAC9C,MAAM,cAAc;QAClB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO;QAE9B,qDAAqD;QACrD,MAAM,cAAc,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACzC,OACE,YAAY,MAAM,CAAC,GAAG,WAAW,KACjC,YAAY,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEvC;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,iDAAiD;QACjD,OAAO,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,GAAG,IAAI,CAAC;IACnD;IAEA,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,MAAM,OAAO,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QACxC,IAAI,KAAK,MAAM,IAAI,GAAG,OAAO;QAE7B,mDAAmD;QACnD,MAAM,gBAAgB,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAC3C,OACE,cAAc,MAAM,CAAC,GAAG,WAAW,KACnC,cAAc,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM;IAEzC;IAEA,MAAM,aAAa;IACnB,MAAM,aAAa;IACnB,MAAM,WAAW;IAEjB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;sBACT,cAAA,8OAAC,sIAAA,CAAA,iBAAc;;oBACZ,cAAc,4BACb;;0CACE,8OAAC,sIAAA,CAAA,iBAAc;gCAAC,WAAU;0CACxB,cAAA,8OAAC,sIAAA,CAAA,iBAAc;oCAAC,OAAO;8CACrB,cAAA,8OAAC,8IAAA,CAAA,iBAAc;wCACb,MAAM;wCACN,WAAU;kDAET;;;;;;;;;;;;;;;;0CAIP,8OAAC,sIAAA,CAAA,sBAAmB;gCAAC,WAAU;;;;;;;;kCAGnC,8OAAC,sIAAA,CAAA,iBAAc;kCACb,cAAA,8OAAC,sIAAA,CAAA,iBAAc;4BAAC,WAAU;sCACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 4177, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/layouts/main-layout.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useAuthTokens } from \"@/hooks/use-auth-tokens\";\r\nimport { ReactNode, Suspense, useEffect } from \"react\";\r\nimport { RoleGuard } from \"./role-guard\";\r\nimport {\r\n  LoadingScreen,\r\n  useLoading,\r\n} from \"@/components/providers/loading-provider\";\r\nimport { SidebarProvider, useSidebar } from \"@/components/ui/sidebar\";\r\nimport { AppSidebar } from \"@/components/app-sidebar\";\r\nimport { TopNavigation } from \"@/components/top-navigation\";\r\nimport { Breadcrumbs } from \"@/components/breadcrumbs\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport { Sheet, SheetContent } from \"@/components/ui/sheet\";\r\n\r\ninterface MainLayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport function MainLayout({ children }: MainLayoutProps) {\r\n  const { accessToken, isInitialized } = useAuthTokens();\r\n  const { isLoading, setLoading, hasShownLoading, markLoadingShown } =\r\n    useLoading();\r\n\r\n  // Use useEffect for redirects to avoid hydration issues\r\n  useEffect(() => {\r\n    // Only show loading on the very first render if:\r\n    // 1. Auth is not initialized yet\r\n    // 2. We haven't shown the loading screen before in this session\r\n    if (!isInitialized && !hasShownLoading.main) {\r\n      setLoading(\"main\", true);\r\n    } else {\r\n      setLoading(\"main\", false);\r\n    }\r\n\r\n    if (!isInitialized) {\r\n      return; // Wait until auth state is initialized\r\n    }\r\n\r\n    // After auth state is initialized, we can hide the loading screen\r\n    setLoading(\"main\", false);\r\n\r\n    // If we have an access token, mark that we've shown loading\r\n    // This prevents showing loading on subsequent navigations\r\n    if (accessToken) {\r\n      markLoadingShown(\"main\");\r\n    }\r\n\r\n    // We're disabling client-side redirects to avoid redirect loops\r\n    // The middleware will handle redirects instead\r\n    if (!accessToken) {\r\n      // We're not redirecting here anymore\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [accessToken, isInitialized, hasShownLoading.main]);\r\n\r\n  // Add a safety timeout to prevent getting stuck in loading state\r\n  useEffect(() => {\r\n    const timeoutId = setTimeout(() => {\r\n      if (isLoading.main) {\r\n        setLoading(\"main\", false);\r\n        markLoadingShown(\"main\");\r\n      }\r\n    }, 1500); // 1.5 second timeout for better UX\r\n\r\n    return () => clearTimeout(timeoutId);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [isLoading.main]);\r\n\r\n  // Don't render anything if not authenticated\r\n  if (!accessToken) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider>\r\n      <RoleGuard>\r\n        <MainLayoutContent>{children}</MainLayoutContent>\r\n      </RoleGuard>\r\n    </SidebarProvider>\r\n  );\r\n}\r\n\r\nfunction MainLayoutContent({ children }: { children: ReactNode }) {\r\n  const { state, openMobile, setOpenMobile } = useSidebar();\r\n  const isCollapsed = state === \"collapsed\";\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-screen w-full bg-gray-50\">\r\n      <TopNavigation />\r\n      <div className=\"flex h-[calc(100vh-3.5rem)] overflow-hidden\">\r\n        {/* Desktop sidebar */}\r\n        <aside\r\n          className={cn(\r\n            \"border-r bg-white transition-all duration-300 shrink-0 hidden md:block\",\r\n            isCollapsed ? \"w-16\" : \"w-64\"\r\n          )}\r\n        >\r\n          <AppSidebar />\r\n        </aside>\r\n\r\n        {/* Mobile sidebar */}\r\n        <div className=\"md:hidden\">\r\n          <Sheet open={openMobile} onOpenChange={setOpenMobile}>\r\n            <SheetContent\r\n              side=\"left\"\r\n              className=\"p-0 w-[280px] border-r bg-white\"\r\n            >\r\n              <div className=\"h-full overflow-y-auto\">\r\n                <AppSidebar />\r\n              </div>\r\n            </SheetContent>\r\n          </Sheet>\r\n        </div>\r\n\r\n        <main className=\"flex flex-col w-full overflow-y-auto overflow-x-hidden\">\r\n          <div className=\"p-6\">\r\n            <Suspense fallback={<div>Loading content...</div>}>\r\n              <Breadcrumbs />\r\n              {children}\r\n            </Suspense>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;AAoBO,SAAS,WAAW,EAAE,QAAQ,EAAmB;IACtD,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,eAAe,EAAE,gBAAgB,EAAE,GAChE,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD;IAEX,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iDAAiD;QACjD,iCAAiC;QACjC,gEAAgE;QAChE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI,EAAE;YAC3C,WAAW,QAAQ;QACrB,OAAO;YACL,WAAW,QAAQ;QACrB;QAEA,IAAI,CAAC,eAAe;YAClB,QAAQ,uCAAuC;QACjD;QAEA,kEAAkE;QAClE,WAAW,QAAQ;QAEnB,4DAA4D;QAC5D,0DAA0D;QAC1D,IAAI,aAAa;YACf,iBAAiB;QACnB;QAEA,gEAAgE;QAChE,+CAA+C;QAC/C,IAAI,CAAC,aAAa;QAChB,qCAAqC;QACvC;IACA,uDAAuD;IACzD,GAAG;QAAC;QAAa;QAAe,gBAAgB,IAAI;KAAC;IAErD,iEAAiE;IACjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,IAAI,UAAU,IAAI,EAAE;gBAClB,WAAW,QAAQ;gBACnB,iBAAiB;YACnB;QACF,GAAG,OAAO,mCAAmC;QAE7C,OAAO,IAAM,aAAa;IAC1B,uDAAuD;IACzD,GAAG;QAAC,UAAU,IAAI;KAAC;IAEnB,6CAA6C;IAC7C,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IAEA,qBACE,8OAAC,mIAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,8IAAA,CAAA,YAAS;sBACR,cAAA,8OAAC;0BAAmB;;;;;;;;;;;;;;;;AAI5B;AAEA,SAAS,kBAAkB,EAAE,QAAQ,EAA2B;IAC9D,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IACtD,MAAM,cAAc,UAAU;IAE9B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uIAAA,CAAA,gBAAa;;;;;0BACd,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA,cAAc,SAAS;kCAGzB,cAAA,8OAAC,oIAAA,CAAA,aAAU;;;;;;;;;;kCAIb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;4BAAC,MAAM;4BAAY,cAAc;sCACrC,cAAA,8OAAC,iIAAA,CAAA,eAAY;gCACX,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oIAAA,CAAA,aAAU;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnB,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gCAAC,wBAAU,8OAAC;8CAAI;;;;;;;kDACvB,8OAAC,iIAAA,CAAA,cAAW;;;;;oCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 4389, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/credit-notes/api/credit-note-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\n\r\nimport {\r\n  CreditNote,\r\n  CreditNoteFilters,\r\n  CreditNoteResponse,\r\n  CreditNotesResponse,\r\n  CreateCreditNoteRequest,\r\n  UpdateCreditNoteRequest\r\n} from '@/types/credit-note';\r\n\r\n/**\r\n * Credit Note Service\r\n */\r\nexport const CreditNoteService = {\r\n  /**\r\n   * Get all credit notes with filtering\r\n   */\r\n  getCreditNotes: async (filters?: CreditNoteFilters): Promise<{\r\n    data: CreditNote[];\r\n    pagination?: {\r\n      total: number;\r\n      page: number;\r\n      limit: number;\r\n      pages: number;\r\n    };\r\n  }> => {\r\n    try {\r\n      const response = await apiClient.get<CreditNotesResponse>('/credit-notes', {\r\n        params: filters\r\n      });\r\n\r\n      return {\r\n        data: response.data.data,\r\n        pagination: response.data.pagination\r\n      };\r\n    } catch (error) {\r\n      console.error('Error fetching credit notes:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get credit note by ID\r\n   */\r\n  getCreditNoteById: async (id: number): Promise<CreditNote> => {\r\n    try {\r\n      const response = await apiClient.get<CreditNoteResponse>(`/credit-notes/${id}`);\r\n      return response.data.data;\r\n    } catch (error) {\r\n      console.error(`Error fetching credit note with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new credit note\r\n   */\r\n  createCreditNote: async (data: CreateCreditNoteRequest): Promise<CreditNote> => {\r\n    try {\r\n      const response = await apiClient.post<CreditNoteResponse>('/credit-notes', data);\r\n      return response.data.data;\r\n    } catch (error) {\r\n      console.error('Error creating credit note:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update a credit note\r\n   */\r\n  updateCreditNote: async (id: number, data: UpdateCreditNoteRequest): Promise<CreditNote> => {\r\n    try {\r\n      const response = await apiClient.put<CreditNoteResponse>(`/credit-notes/${id}`, data);\r\n      return response.data.data;\r\n    } catch (error) {\r\n      console.error(`Error updating credit note with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Issue a credit note (send to KRA)\r\n   */\r\n  issueCreditNote: async (id: number): Promise<CreditNote> => {\r\n    try {\r\n      const response = await apiClient.post<CreditNoteResponse>(`/credit-notes/${id}/issue`);\r\n      return response.data.data;\r\n    } catch (error) {\r\n      console.error(`Error issuing credit note with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get PDF URL for credit note\r\n   */\r\n  getCreditNotePdfUrl: (id: number): string => {\r\n    // Use the environment variable for API URL\r\n    const baseUrl = process.env.NEXT_PUBLIC_API_URL || '';\r\n    return `${baseUrl}/credit-notes/${id}/pdf`;\r\n  }\r\n};\r\n\r\nexport default CreditNoteService;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAcO,MAAM,oBAAoB;IAC/B;;GAEC,GACD,gBAAgB,OAAO;QASrB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAsB,iBAAiB;gBACzE,QAAQ;YACV;YAEA,OAAO;gBACL,MAAM,SAAS,IAAI,CAAC,IAAI;gBACxB,YAAY,SAAS,IAAI,CAAC,UAAU;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,mBAAmB,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAqB,CAAC,cAAc,EAAE,IAAI;YAC9E,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC3D,MAAM;QACR;IACF;IAEA;;GAEC,GACD,kBAAkB,OAAO;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAqB,iBAAiB;YAC3E,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,kBAAkB,OAAO,IAAY;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAqB,CAAC,cAAc,EAAE,IAAI,EAAE;YAChF,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC3D,MAAM;QACR;IACF;IAEA;;GAEC,GACD,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAqB,CAAC,cAAc,EAAE,GAAG,MAAM,CAAC;YACrF,OAAO,SAAS,IAAI,CAAC,IAAI;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC1D,MAAM;QACR;IACF;IAEA;;GAEC,GACD,qBAAqB,CAAC;QACpB,2CAA2C;QAC3C,MAAM,UAAU,oEAAmC;QACnD,OAAO,GAAG,QAAQ,cAAc,EAAE,GAAG,IAAI,CAAC;IAC5C;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 4471, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/invoices/api/invoice-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport {\r\n  CreateInvoicePaymentRequest,\r\n  CreateInvoiceRequest,\r\n  Invoice,\r\n  InvoiceFilters,\r\n  PaginatedInvoiceResponse,\r\n  UpdateInvoiceRequest,\r\n} from \"../types\";\r\n\r\n/**\r\n * Invoice Service\r\n * Handles API calls for invoice operations\r\n */\r\nconst invoiceService = {\r\n  /**\r\n   * Get all invoices with optional filters, pagination, and sorting\r\n   */\r\n  getInvoices: async (\r\n    filters?: InvoiceFilters\r\n  ): Promise<PaginatedInvoiceResponse> => {\r\n    try {\r\n      console.log(\"Fetching invoices with filters:\", filters);\r\n\r\n      // Prepare request parameters\r\n      const params = {\r\n        ...filters,\r\n        // Convert date objects to ISO strings if they exist\r\n        start_date: filters?.start_date ? new Date(filters.start_date).toISOString().split('T')[0] : undefined,\r\n        end_date: filters?.end_date ? new Date(filters.end_date).toISOString().split('T')[0] : undefined,\r\n      };\r\n\r\n      // Try to fetch from the API with retry logic\r\n      let retryCount = 0;\r\n      const maxRetries = 2;\r\n      let response;\r\n\r\n      while (retryCount <= maxRetries) {\r\n        try {\r\n          response = await apiClient.get<PaginatedInvoiceResponse>(\"/invoices\", {\r\n            params,\r\n          });\r\n          break; // Success, exit the retry loop\r\n        } catch (retryError: any) {\r\n          retryCount++;\r\n\r\n          // If we've reached max retries or it's not a network error, throw\r\n          if (retryCount > maxRetries || (retryError.response && retryError.response.status !== 0)) {\r\n            throw retryError;\r\n          }\r\n\r\n          // Wait before retrying (exponential backoff)\r\n          const delay = Math.min(1000 * 2 ** retryCount, 5000);\r\n          console.warn(`Retrying invoice fetch (${retryCount}/${maxRetries}) after ${delay}ms`);\r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n        }\r\n      }\r\n\r\n      console.log(\"Invoice API response:\", response);\r\n\r\n      // If the response doesn't have the expected structure, return a default structure\r\n      if (!response || !response.data) {\r\n        console.warn(\"API returned unexpected response structure:\", response);\r\n        return {\r\n          data: [],\r\n          pagination: {\r\n            page: filters?.page || 1,\r\n            totalPages: 0,\r\n            totalItems: 0,\r\n            limit: filters?.limit || 10\r\n          }\r\n        };\r\n      }\r\n\r\n      return response;\r\n    } catch (error: any) {\r\n      console.error(\"Error fetching invoices:\", error);\r\n\r\n      // Provide more detailed error information\r\n      const errorMessage = error.response?.data?.message || error.message || 'Unknown error';\r\n      console.error(`Invoice fetch error details: ${errorMessage}`);\r\n\r\n      // Return a default structure instead of throwing\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          page: filters?.page || 1,\r\n          totalPages: 0,\r\n          totalItems: 0,\r\n          limit: filters?.limit || 10\r\n        },\r\n        error: errorMessage // Add error information to the response\r\n      };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get an invoice by ID\r\n   */\r\n  getInvoiceById: async (id: number): Promise<Invoice> => {\r\n    try {\r\n      // Try to fetch with retry logic\r\n      let retryCount = 0;\r\n      const maxRetries = 2;\r\n      let response;\r\n\r\n      while (retryCount <= maxRetries) {\r\n        try {\r\n          response = await apiClient.get<{ data: Invoice, success: boolean }>(`/invoices/${id}`);\r\n          break; // Success, exit the retry loop\r\n        } catch (retryError: any) {\r\n          retryCount++;\r\n\r\n          // If we've reached max retries or it's not a network error, throw\r\n          if (retryCount > maxRetries || (retryError.response && retryError.response.status !== 0)) {\r\n            throw retryError;\r\n          }\r\n\r\n          // Wait before retrying (exponential backoff)\r\n          const delay = Math.min(1000 * 2 ** retryCount, 5000);\r\n          console.warn(`Retrying invoice fetch (${retryCount}/${maxRetries}) after ${delay}ms`);\r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n        }\r\n      }\r\n\r\n      if (!response || !response.data) {\r\n        throw new Error(`Invoice with ID ${id} not found or response format invalid`);\r\n      }\r\n\r\n      return response.data;\r\n    } catch (error: any) {\r\n      console.error(`Error fetching invoice with ID ${id}:`, error);\r\n\r\n      // Provide more detailed error information\r\n      const errorMessage = error.response?.data?.message || error.message || 'Unknown error';\r\n      console.error(`Invoice fetch error details: ${errorMessage}`);\r\n\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new invoice\r\n   */\r\n  createInvoice: async (data: CreateInvoiceRequest): Promise<Invoice> => {\r\n    try {\r\n      const response = await apiClient.post<{ data: Invoice }>(\"/invoices\", data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error creating invoice:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update an existing invoice\r\n   */\r\n  updateInvoice: async (\r\n    id: number,\r\n    data: UpdateInvoiceRequest\r\n  ): Promise<Invoice> => {\r\n    try {\r\n      const response = await apiClient.put<{ data: Invoice }>(\r\n        `/invoices/${id}`,\r\n        data\r\n      );\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error updating invoice with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete an invoice\r\n   */\r\n  deleteInvoice: async (id: number): Promise<void> => {\r\n    try {\r\n      await apiClient.delete(`/invoices/${id}`);\r\n    } catch (error) {\r\n      console.error(`Error deleting invoice with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Add a payment to an invoice\r\n   */\r\n  addInvoicePayment: async (\r\n    invoiceId: number,\r\n    data: CreateInvoicePaymentRequest\r\n  ): Promise<any> => {\r\n    try {\r\n      const response = await apiClient.post(\r\n        `/invoices/${invoiceId}/payments`,\r\n        data\r\n      );\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Error adding payment to invoice with ID ${invoiceId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get invoice payments\r\n   */\r\n  getInvoicePayments: async (invoiceId: number): Promise<any> => {\r\n    try {\r\n      const response = await apiClient.get(`/invoices/${invoiceId}/payments`);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(\r\n        `Error fetching payments for invoice with ID ${invoiceId}:`,\r\n        error\r\n      );\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Generate invoice PDF\r\n   */\r\n  generateInvoicePdf: async (invoiceId: number): Promise<Blob> => {\r\n    try {\r\n      const response = await apiClient.get(`/invoices/${invoiceId}/pdf`, {\r\n        responseType: 'blob'\r\n      });\r\n      return response;\r\n    } catch (error) {\r\n      console.error(\r\n        `Error generating PDF for invoice with ID ${invoiceId}:`,\r\n        error\r\n      );\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n\r\nexport default invoiceService;\r\n"], "names": [], "mappings": ";;;AAAA;;AAUA;;;CAGC,GACD,MAAM,iBAAiB;IACrB;;GAEC,GACD,aAAa,OACX;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,6BAA6B;YAC7B,MAAM,SAAS;gBACb,GAAG,OAAO;gBACV,oDAAoD;gBACpD,YAAY,SAAS,aAAa,IAAI,KAAK,QAAQ,UAAU,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;gBAC7F,UAAU,SAAS,WAAW,IAAI,KAAK,QAAQ,QAAQ,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;YACzF;YAEA,6CAA6C;YAC7C,IAAI,aAAa;YACjB,MAAM,aAAa;YACnB,IAAI;YAEJ,MAAO,cAAc,WAAY;gBAC/B,IAAI;oBACF,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAA2B,aAAa;wBACpE;oBACF;oBACA,OAAO,+BAA+B;gBACxC,EAAE,OAAO,YAAiB;oBACxB;oBAEA,kEAAkE;oBAClE,IAAI,aAAa,cAAe,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,KAAK,GAAI;wBACxF,MAAM;oBACR;oBAEA,6CAA6C;oBAC7C,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,YAAY;oBAC/C,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,WAAW,CAAC,EAAE,WAAW,QAAQ,EAAE,MAAM,EAAE,CAAC;oBACpF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF;YAEA,QAAQ,GAAG,CAAC,yBAAyB;YAErC,kFAAkF;YAClF,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,EAAE;gBAC/B,QAAQ,IAAI,CAAC,+CAA+C;gBAC5D,OAAO;oBACL,MAAM,EAAE;oBACR,YAAY;wBACV,MAAM,SAAS,QAAQ;wBACvB,YAAY;wBACZ,YAAY;wBACZ,OAAO,SAAS,SAAS;oBAC3B;gBACF;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,0CAA0C;YAC1C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;YACvE,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,cAAc;YAE5D,iDAAiD;YACjD,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,MAAM,SAAS,QAAQ;oBACvB,YAAY;oBACZ,YAAY;oBACZ,OAAO,SAAS,SAAS;gBAC3B;gBACA,OAAO,aAAa,wCAAwC;YAC9D;QACF;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,gCAAgC;YAChC,IAAI,aAAa;YACjB,MAAM,aAAa;YACnB,IAAI;YAEJ,MAAO,cAAc,WAAY;gBAC/B,IAAI;oBACF,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAsC,CAAC,UAAU,EAAE,IAAI;oBACrF,OAAO,+BAA+B;gBACxC,EAAE,OAAO,YAAiB;oBACxB;oBAEA,kEAAkE;oBAClE,IAAI,aAAa,cAAe,WAAW,QAAQ,IAAI,WAAW,QAAQ,CAAC,MAAM,KAAK,GAAI;wBACxF,MAAM;oBACR;oBAEA,6CAA6C;oBAC7C,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,YAAY;oBAC/C,QAAQ,IAAI,CAAC,CAAC,wBAAwB,EAAE,WAAW,CAAC,EAAE,WAAW,QAAQ,EAAE,MAAM,EAAE,CAAC;oBACpF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACnD;YACF;YAEA,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,EAAE;gBAC/B,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,GAAG,qCAAqC,CAAC;YAC9E;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YAEvD,0CAA0C;YAC1C,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;YACvE,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,cAAc;YAE5D,MAAM;QACR;IACF;IAEA;;GAEC,GACD,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAoB,aAAa;YACtE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA;;GAEC,GACD,eAAe,OACb,IACA;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,UAAU,EAAE,IAAI,EACjB;YAEF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YACvD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YACvD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,mBAAmB,OACjB,WACA;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,UAAU,EAAE,UAAU,SAAS,CAAC,EACjC;YAEF,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wCAAwC,EAAE,UAAU,CAAC,CAAC,EAAE;YACvE,MAAM;QACR;IACF;IAEA;;GAEC,GACD,oBAAoB,OAAO;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,SAAS,CAAC;YACtE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CACX,CAAC,4CAA4C,EAAE,UAAU,CAAC,CAAC,EAC3D;YAEF,MAAM;QACR;IACF;IAEA;;GAEC,GACD,oBAAoB,OAAO;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,UAAU,IAAI,CAAC,EAAE;gBACjE,cAAc;YAChB;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CACX,CAAC,yCAAyC,EAAE,UAAU,CAAC,CAAC,EACxD;YAEF,MAAM;QACR;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 4658, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/sales/api/sales-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { Sale, SaleFilters } from \"@/types\";\r\n\r\n/**\r\n * Sales Service\r\n * Handles API calls for sales\r\n */\r\nconst salesService = {\r\n  /**\r\n   * Get all sales with optional filters\r\n   */\r\n  getSales: async (filters?: SaleFilters): Promise<Sale[]> => {\r\n    const response = await apiClient.get<Sale[]>(\"/sales\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get a sale by ID\r\n   */\r\n  getSaleById: async (id: number): Promise<Sale> => {\r\n    const response = await apiClient.get<Sale>(`/sales/${id}`);\r\n    return response;\r\n  },\r\n};\r\n\r\nexport default salesService;\r\n"], "names": [], "mappings": ";;;AAAA;;AAGA;;;CAGC,GACD,MAAM,eAAe;IACnB;;GAEC,GACD,UAAU,OAAO;QACf,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAS,UAAU;YACrD,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAO,CAAC,OAAO,EAAE,IAAI;QACzD,OAAO;IACT;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 4689, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/customers/api/customer-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport {\r\n  Customer,\r\n  CreateCustomerRequest,\r\n  UpdateCustomerRequest,\r\n  CustomerSalesResponse,\r\n  CustomerFilters,\r\n  PaginatedCustomerResponse,\r\n} from \"@/types\";\r\n\r\n/**\r\n * Customer Service\r\n * Handles API calls for customers\r\n */\r\nconst customerService = {\r\n  /**\r\n   * Get all customers with optional filters\r\n   */\r\n  getCustomers: async (\r\n    filters?: CustomerFilters\r\n  ): Promise<PaginatedCustomerResponse> => {\r\n    const response = await apiClient.get<PaginatedCustomerResponse>(\"/customers\", {\r\n      params: filters,\r\n    });\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Get a customer by ID\r\n   */\r\n  getCustomerById: async (id: number): Promise<Customer> => {\r\n    const response = await apiClient.get<Customer>(`/customers/${id}`);\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Create a new customer\r\n   */\r\n  createCustomer: async (\r\n    customer: CreateCustomerRequest\r\n  ): Promise<Customer> => {\r\n    const response = await apiClient.post<Customer>(\"/customers\", customer);\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Update a customer\r\n   */\r\n  updateCustomer: async (\r\n    id: number,\r\n    customer: UpdateCustomerRequest\r\n  ): Promise<Customer> => {\r\n    const response = await apiClient.put<Customer>(\r\n      `/customers/${id}`,\r\n      customer\r\n    );\r\n    return response;\r\n  },\r\n\r\n  /**\r\n   * Delete a customer\r\n   */\r\n  deleteCustomer: async (id: number): Promise<void> => {\r\n    await apiClient.delete(`/customers/${id}`);\r\n  },\r\n\r\n  /**\r\n   * Get sales for a customer\r\n   */\r\n  getCustomerSales: async (id: number): Promise<CustomerSalesResponse> => {\r\n    const response = await apiClient.get<CustomerSalesResponse>(\r\n      `/customers/${id}/sales`\r\n    );\r\n    return response;\r\n  },\r\n};\r\n\r\nexport default customerService;\r\n"], "names": [], "mappings": ";;;AAAA;;AAUA;;;CAGC,GACD,MAAM,kBAAkB;IACtB;;GAEC,GACD,cAAc,OACZ;QAEA,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAA4B,cAAc;YAC5E,QAAQ;QACV;QACA,OAAO;IACT;IAEA;;GAEC,GACD,iBAAiB,OAAO;QACtB,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI;QACjE,OAAO;IACT;IAEA;;GAEC,GACD,gBAAgB,OACd;QAEA,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAW,cAAc;QAC9D,OAAO;IACT;IAEA;;GAEC,GACD,gBAAgB,OACd,IACA;QAEA,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,WAAW,EAAE,IAAI,EAClB;QAEF,OAAO;IACT;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,MAAM,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;IAC3C;IAEA;;GAEC,GACD,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC;QAE1B,OAAO;IACT;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 4743, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/api/product-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse } from \"@/types/api\";\r\nimport {\r\n  Product,\r\n  CreateProductRequest,\r\n  UpdateProductRequest,\r\n  UpdateProductStatusRequest,\r\n} from \"@/types/product\";\r\nimport { toast } from \"sonner\";\r\n\r\nexport const productService = {\r\n  /**\r\n   * Get products without stock items at headquarters\r\n   * @param params Query parameters\r\n   * @returns Paginated list of products without stock items at headquarters\r\n   */\r\n  getProductsWithoutHQStock: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Product>> => {\r\n    try {\r\n      // Use the regular products endpoint with a filter for products without HQ stock\r\n      // This is a workaround until the backend endpoint is fixed\r\n      const allProducts = await productService.getProducts({\r\n        ...params,\r\n        limit: 1000, // Get a large number of products to filter client-side\r\n      });\r\n\r\n      // Get all stock items for headquarters\r\n      let stockItems: any[] = [];\r\n      try {\r\n        const stockItemsResponse = await apiClient.get<any>(\"/stock-items\", {\r\n          params: {\r\n            branch_id: 1, // Headquarters\r\n            limit: 1000,\r\n          }\r\n        });\r\n\r\n        if (stockItemsResponse && stockItemsResponse.data) {\r\n          stockItems = stockItemsResponse.data;\r\n        }\r\n      } catch (stockError) {\r\n        console.error(\"Error fetching stock items:\", stockError);\r\n        // Continue with empty stock items\r\n      }\r\n\r\n      // Create a set of product IDs that already have stock items at headquarters\r\n      const productsWithHQStock = new Set(\r\n        stockItems.map((item: any) => item.product_id)\r\n      );\r\n\r\n      // Filter out products that already have stock items at headquarters\r\n      const filteredProducts = allProducts.data.filter(\r\n        (product) => !productsWithHQStock.has(product.id)\r\n      );\r\n\r\n      // Return the filtered products with pagination\r\n      return {\r\n        data: filteredProducts,\r\n        pagination: {\r\n          total: filteredProducts.length,\r\n          page: parseInt(params?.page || \"1\", 10),\r\n          limit: parseInt(params?.limit || \"10\", 10),\r\n          totalPages: Math.ceil(filteredProducts.length / parseInt(params?.limit || \"10\", 10)),\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching products without HQ stock:\", error);\r\n      // Show a toast error message\r\n      try {\r\n        // @ts-ignore - toast is imported elsewhere\r\n        toast.error(\"Error loading products\", {\r\n          description: \"Could not load products without headquarters stock. Please try again later.\",\r\n        });\r\n      } catch (toastError) {\r\n        // Ignore toast errors\r\n      }\r\n\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getProducts: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Product>> => {\r\n    try {\r\n      // Extract branch_procurable parameter for client-side filtering\r\n      const branchProcurable = params?.branch_procurable;\r\n\r\n      // Create a new params object without branch_procurable\r\n      const apiParams = { ...params };\r\n      if (apiParams.branch_procurable !== undefined) {\r\n        delete apiParams.branch_procurable;\r\n      }\r\n\r\n      console.log(\"Fetching products with params:\", apiParams);\r\n      const response = await apiClient.get<any>(\"/products\", { params: apiParams });\r\n      console.log(\"API response for products:\", response);\r\n\r\n      // Map API response to our Product type\r\n      const mapApiProductToProduct = (apiProduct: any): Product => {\r\n        // Convert string prices to numbers for UI\r\n        const sellingPrice = apiProduct.suggested_selling_price\r\n          ? parseFloat(apiProduct.suggested_selling_price)\r\n          : 0;\r\n\r\n        // Calculate stock quantity from stock array if available\r\n        const stockQuantity =\r\n          apiProduct.stock?.reduce(\r\n            (total: number, item: any) => total + item.quantity,\r\n            0\r\n          ) || 0;\r\n\r\n        // Ensure category hierarchy is properly handled\r\n        let categoryHierarchy = apiProduct.categoryHierarchy || [];\r\n\r\n        // If categoryHierarchy is not available but ProductCategory is, create a simple hierarchy\r\n        if (\r\n          (!categoryHierarchy || categoryHierarchy.length === 0) &&\r\n          apiProduct.ProductCategory\r\n        ) {\r\n          const category = apiProduct.ProductCategory;\r\n\r\n          // If the category has a parent, include it in the hierarchy\r\n          if (category.Parent) {\r\n            categoryHierarchy = [\r\n              {\r\n                id: category.Parent.id,\r\n                name: category.Parent.name,\r\n                description: category.Parent.description,\r\n                level: category.Parent.level || 0,\r\n              },\r\n              {\r\n                id: category.id,\r\n                name: category.name,\r\n                description: category.description,\r\n                level: category.level || 1,\r\n              },\r\n            ];\r\n          } else {\r\n            // If no parent, just include the category itself\r\n            categoryHierarchy = [\r\n              {\r\n                id: category.id,\r\n                name: category.name,\r\n                description: category.description,\r\n                level: category.level || 0,\r\n              },\r\n            ];\r\n          }\r\n        }\r\n\r\n        return {\r\n          ...apiProduct,\r\n          // Map API fields to UI fields\r\n          price: sellingPrice, // Map suggested_selling_price to price for UI compatibility\r\n          stock_quantity: stockQuantity,\r\n          status: apiProduct.is_active ? \"active\" : \"inactive\", // Derive status from is_active\r\n          has_variants: apiProduct.has_serial, // Map has_serial to has_variants for UI compatibility\r\n          categoryHierarchy: categoryHierarchy, // Ensure category hierarchy is available\r\n        };\r\n      };\r\n\r\n      let mappedProducts: Product[] = [];\r\n\r\n      // If response is an array, convert to paginated format with mapped products\r\n      if (Array.isArray(response)) {\r\n        mappedProducts = response.map(mapApiProductToProduct);\r\n      }\r\n      // Check if response is already in paginated format\r\n      else if (response && response.data && Array.isArray(response.data)) {\r\n        mappedProducts = response.data.map(mapApiProductToProduct);\r\n      }\r\n\r\n      // Apply client-side filtering for branch_procurable if needed\r\n      if (branchProcurable === true) {\r\n        console.log(\"Filtering products for branch_procurable=true\");\r\n        mappedProducts = mappedProducts.filter(product => product.branch_procurable === true);\r\n      }\r\n\r\n      console.log(\"Filtered products:\", mappedProducts);\r\n\r\n      // Return the filtered products with pagination\r\n      return {\r\n        data: mappedProducts,\r\n        pagination: response.pagination || {\r\n          total: mappedProducts.length,\r\n          page: 1,\r\n          limit: mappedProducts.length,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching products:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getProductById: async (id: number): Promise<Product | null> => {\r\n    try {\r\n      const response = await apiClient.get(`/products/${id}`);\r\n      return response;\r\n    } catch (error: any) {\r\n      console.error(`Error fetching product with ID ${id}:`, error);\r\n\r\n      // If the error is a 404, return null instead of throwing\r\n      if (error.response && error.response.status === 404) {\r\n        console.warn(`Product with ID ${id} not found`);\r\n        return null;\r\n      }\r\n\r\n      // For other errors, rethrow\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  createProduct: async (product: CreateProductRequest): Promise<Product> => {\r\n    // Create a properly structured API request with only the required fields\r\n    const apiRequest: Record<string, any> = {\r\n      // Required fields\r\n      tenant_id: product.tenant_id,\r\n      name: product.name,\r\n      sku: product.sku,\r\n      has_serial: product.has_serial,\r\n      suggested_buying_price: product.suggested_buying_price,\r\n      suggested_selling_price: product.suggested_selling_price,\r\n    };\r\n\r\n    // Optional fields\r\n    if (product.category_id !== undefined)\r\n      apiRequest.category_id = product.category_id;\r\n    if (product.brand_id !== undefined) apiRequest.brand_id = product.brand_id;\r\n    if (product.brand_type_id !== undefined)\r\n      apiRequest.brand_type_id = product.brand_type_id;\r\n    if (product.description !== undefined)\r\n      apiRequest.description = product.description;\r\n    if (product.barcode !== undefined)\r\n      apiRequest.barcode = product.barcode;\r\n    if (product.default_wholesale_price !== undefined)\r\n      apiRequest.default_wholesale_price = product.default_wholesale_price;\r\n    if (product.warranty_period !== undefined)\r\n      apiRequest.warranty_period = product.warranty_period;\r\n    if (product.can_assign_to_dsa !== undefined)\r\n      apiRequest.can_assign_to_dsa = product.can_assign_to_dsa;\r\n\r\n    console.log(\"Creating product with API request:\", apiRequest);\r\n    return apiClient.post(\"/products\", apiRequest);\r\n  },\r\n\r\n  updateProduct: async (\r\n    id: number,\r\n    product: UpdateProductRequest\r\n  ): Promise<Product> => {\r\n    // Create a properly structured API request\r\n    const apiRequest: Record<string, any> = {};\r\n\r\n    // Basic information\r\n    if (product.name !== undefined) apiRequest.name = product.name;\r\n    if (product.sku !== undefined) apiRequest.sku = product.sku;\r\n    if (product.description !== undefined) apiRequest.description = product.description;\r\n    if (product.barcode !== undefined) apiRequest.barcode = product.barcode;\r\n    if (product.status !== undefined) apiRequest.is_active = product.status === 'active';\r\n    if (product.is_active !== undefined) apiRequest.is_active = product.is_active;\r\n\r\n    // Categorization\r\n    if (product.category_id !== undefined) apiRequest.category_id = product.category_id;\r\n    if (product.brand_id !== undefined) apiRequest.brand_id = product.brand_id;\r\n    if (product.brand_type_id !== undefined) apiRequest.brand_type_id = product.brand_type_id;\r\n\r\n    // Inventory tracking\r\n    if (product.has_serial !== undefined) {\r\n      apiRequest.has_serial = product.has_serial;\r\n    } else if (product.has_variants !== undefined) {\r\n      apiRequest.has_serial = product.has_variants;\r\n    }\r\n\r\n    if (product.has_variants !== undefined) apiRequest.has_variants = product.has_variants;\r\n    if (product.is_parent !== undefined) apiRequest.is_parent = product.is_parent;\r\n    if (product.parent_id !== undefined) apiRequest.parent_id = product.parent_id;\r\n\r\n    // Pricing fields\r\n    if (product.suggested_buying_price !== undefined) {\r\n      apiRequest.suggested_buying_price = product.suggested_buying_price;\r\n    } else if (product.buying_price !== undefined) {\r\n      apiRequest.suggested_buying_price = product.buying_price;\r\n    }\r\n\r\n    if (product.suggested_selling_price !== undefined) {\r\n      apiRequest.suggested_selling_price = product.suggested_selling_price;\r\n    } else if (product.selling_price !== undefined) {\r\n      apiRequest.suggested_selling_price = product.selling_price;\r\n    } else if (product.price !== undefined) {\r\n      apiRequest.suggested_selling_price = product.price;\r\n    }\r\n\r\n    if (product.default_wholesale_price !== undefined) {\r\n      apiRequest.default_wholesale_price = product.default_wholesale_price;\r\n    }\r\n\r\n    // VAT-related fields\r\n    if (product.vat_rate_id !== undefined) apiRequest.vat_rate_id = product.vat_rate_id;\r\n    if (product.is_vat_inclusive !== undefined) apiRequest.is_vat_inclusive = product.is_vat_inclusive;\r\n    if (product.is_vat_exempt !== undefined) apiRequest.is_vat_exempt = product.is_vat_exempt;\r\n    if (product.vat_exemption_reason !== undefined) apiRequest.vat_exemption_reason = product.vat_exemption_reason;\r\n\r\n    // Inventory management\r\n    if (product.reorder_level !== undefined) apiRequest.reorder_level = product.reorder_level;\r\n    if (product.reorder_quantity !== undefined) apiRequest.reorder_quantity = product.reorder_quantity;\r\n\r\n    // Other fields\r\n    if (product.warranty_period !== undefined) apiRequest.warranty_period = product.warranty_period;\r\n\r\n    // DSA-related fields\r\n    if (product.can_assign_to_dsa !== undefined) apiRequest.can_assign_to_dsa = product.can_assign_to_dsa;\r\n\r\n    console.log(\"Updating product with API request:\", apiRequest);\r\n    return apiClient.put(`/products/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteProduct: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/products/${id}`);\r\n  },\r\n\r\n  updateProductStatus: async (\r\n    id: number,\r\n    status: UpdateProductStatusRequest\r\n  ): Promise<Product> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      status: status.status, // 'active' or 'inactive'\r\n    };\r\n\r\n    return apiClient.put(`/products/${id}/status`, apiRequest);\r\n  },\r\n\r\n  uploadProductImage: async (\r\n    id: number,\r\n    file: File\r\n  ): Promise<{ image_url: string }> => {\r\n    const formData = new FormData();\r\n    formData.append(\"image\", file);\r\n\r\n    return apiClient.post(`/products/${id}/image`, formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n    });\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAQA;;;AAEO,MAAM,iBAAiB;IAC5B;;;;GAIC,GACD,2BAA2B,OACzB;QAEA,IAAI;YACF,gFAAgF;YAChF,2DAA2D;YAC3D,MAAM,cAAc,MAAM,eAAe,WAAW,CAAC;gBACnD,GAAG,MAAM;gBACT,OAAO;YACT;YAEA,uCAAuC;YACvC,IAAI,aAAoB,EAAE;YAC1B,IAAI;gBACF,MAAM,qBAAqB,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,gBAAgB;oBAClE,QAAQ;wBACN,WAAW;wBACX,OAAO;oBACT;gBACF;gBAEA,IAAI,sBAAsB,mBAAmB,IAAI,EAAE;oBACjD,aAAa,mBAAmB,IAAI;gBACtC;YACF,EAAE,OAAO,YAAY;gBACnB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,kCAAkC;YACpC;YAEA,4EAA4E;YAC5E,MAAM,sBAAsB,IAAI,IAC9B,WAAW,GAAG,CAAC,CAAC,OAAc,KAAK,UAAU;YAG/C,oEAAoE;YACpE,MAAM,mBAAmB,YAAY,IAAI,CAAC,MAAM,CAC9C,CAAC,UAAY,CAAC,oBAAoB,GAAG,CAAC,QAAQ,EAAE;YAGlD,+CAA+C;YAC/C,OAAO;gBACL,MAAM;gBACN,YAAY;oBACV,OAAO,iBAAiB,MAAM;oBAC9B,MAAM,SAAS,QAAQ,QAAQ,KAAK;oBACpC,OAAO,SAAS,QAAQ,SAAS,MAAM;oBACvC,YAAY,KAAK,IAAI,CAAC,iBAAiB,MAAM,GAAG,SAAS,QAAQ,SAAS,MAAM;gBAClF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,6BAA6B;YAC7B,IAAI;gBACF,2CAA2C;gBAC3C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,0BAA0B;oBACpC,aAAa;gBACf;YACF,EAAE,OAAO,YAAY;YACnB,sBAAsB;YACxB;YAEA,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,aAAa,OACX;QAEA,IAAI;YACF,gEAAgE;YAChE,MAAM,mBAAmB,QAAQ;YAEjC,uDAAuD;YACvD,MAAM,YAAY;gBAAE,GAAG,MAAM;YAAC;YAC9B,IAAI,UAAU,iBAAiB,KAAK,WAAW;gBAC7C,OAAO,UAAU,iBAAiB;YACpC;YAEA,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,aAAa;gBAAE,QAAQ;YAAU;YAC3E,QAAQ,GAAG,CAAC,8BAA8B;YAE1C,uCAAuC;YACvC,MAAM,yBAAyB,CAAC;gBAC9B,0CAA0C;gBAC1C,MAAM,eAAe,WAAW,uBAAuB,GACnD,WAAW,WAAW,uBAAuB,IAC7C;gBAEJ,yDAAyD;gBACzD,MAAM,gBACJ,WAAW,KAAK,EAAE,OAChB,CAAC,OAAe,OAAc,QAAQ,KAAK,QAAQ,EACnD,MACG;gBAEP,gDAAgD;gBAChD,IAAI,oBAAoB,WAAW,iBAAiB,IAAI,EAAE;gBAE1D,0FAA0F;gBAC1F,IACE,CAAC,CAAC,qBAAqB,kBAAkB,MAAM,KAAK,CAAC,KACrD,WAAW,eAAe,EAC1B;oBACA,MAAM,WAAW,WAAW,eAAe;oBAE3C,4DAA4D;oBAC5D,IAAI,SAAS,MAAM,EAAE;wBACnB,oBAAoB;4BAClB;gCACE,IAAI,SAAS,MAAM,CAAC,EAAE;gCACtB,MAAM,SAAS,MAAM,CAAC,IAAI;gCAC1B,aAAa,SAAS,MAAM,CAAC,WAAW;gCACxC,OAAO,SAAS,MAAM,CAAC,KAAK,IAAI;4BAClC;4BACA;gCACE,IAAI,SAAS,EAAE;gCACf,MAAM,SAAS,IAAI;gCACnB,aAAa,SAAS,WAAW;gCACjC,OAAO,SAAS,KAAK,IAAI;4BAC3B;yBACD;oBACH,OAAO;wBACL,iDAAiD;wBACjD,oBAAoB;4BAClB;gCACE,IAAI,SAAS,EAAE;gCACf,MAAM,SAAS,IAAI;gCACnB,aAAa,SAAS,WAAW;gCACjC,OAAO,SAAS,KAAK,IAAI;4BAC3B;yBACD;oBACH;gBACF;gBAEA,OAAO;oBACL,GAAG,UAAU;oBACb,8BAA8B;oBAC9B,OAAO;oBACP,gBAAgB;oBAChB,QAAQ,WAAW,SAAS,GAAG,WAAW;oBAC1C,cAAc,WAAW,UAAU;oBACnC,mBAAmB;gBACrB;YACF;YAEA,IAAI,iBAA4B,EAAE;YAElC,4EAA4E;YAC5E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,iBAAiB,SAAS,GAAG,CAAC;YAChC,OAEK,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAClE,iBAAiB,SAAS,IAAI,CAAC,GAAG,CAAC;YACrC;YAEA,8DAA8D;YAC9D,IAAI,qBAAqB,MAAM;gBAC7B,QAAQ,GAAG,CAAC;gBACZ,iBAAiB,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,iBAAiB,KAAK;YAClF;YAEA,QAAQ,GAAG,CAAC,sBAAsB;YAElC,+CAA+C;YAC/C,OAAO;gBACL,MAAM;gBACN,YAAY,SAAS,UAAU,IAAI;oBACjC,OAAO,eAAe,MAAM;oBAC5B,MAAM;oBACN,OAAO,eAAe,MAAM;oBAC5B,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;YACtD,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YAEvD,yDAAyD;YACzD,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;gBACnD,QAAQ,IAAI,CAAC,CAAC,gBAAgB,EAAE,GAAG,UAAU,CAAC;gBAC9C,OAAO;YACT;YAEA,4BAA4B;YAC5B,MAAM;QACR;IACF;IAEA,eAAe,OAAO;QACpB,yEAAyE;QACzE,MAAM,aAAkC;YACtC,kBAAkB;YAClB,WAAW,QAAQ,SAAS;YAC5B,MAAM,QAAQ,IAAI;YAClB,KAAK,QAAQ,GAAG;YAChB,YAAY,QAAQ,UAAU;YAC9B,wBAAwB,QAAQ,sBAAsB;YACtD,yBAAyB,QAAQ,uBAAuB;QAC1D;QAEA,kBAAkB;QAClB,IAAI,QAAQ,WAAW,KAAK,WAC1B,WAAW,WAAW,GAAG,QAAQ,WAAW;QAC9C,IAAI,QAAQ,QAAQ,KAAK,WAAW,WAAW,QAAQ,GAAG,QAAQ,QAAQ;QAC1E,IAAI,QAAQ,aAAa,KAAK,WAC5B,WAAW,aAAa,GAAG,QAAQ,aAAa;QAClD,IAAI,QAAQ,WAAW,KAAK,WAC1B,WAAW,WAAW,GAAG,QAAQ,WAAW;QAC9C,IAAI,QAAQ,OAAO,KAAK,WACtB,WAAW,OAAO,GAAG,QAAQ,OAAO;QACtC,IAAI,QAAQ,uBAAuB,KAAK,WACtC,WAAW,uBAAuB,GAAG,QAAQ,uBAAuB;QACtE,IAAI,QAAQ,eAAe,KAAK,WAC9B,WAAW,eAAe,GAAG,QAAQ,eAAe;QACtD,IAAI,QAAQ,iBAAiB,KAAK,WAChC,WAAW,iBAAiB,GAAG,QAAQ,iBAAiB;QAE1D,QAAQ,GAAG,CAAC,sCAAsC;QAClD,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,aAAa;IACrC;IAEA,eAAe,OACb,IACA;QAEA,2CAA2C;QAC3C,MAAM,aAAkC,CAAC;QAEzC,oBAAoB;QACpB,IAAI,QAAQ,IAAI,KAAK,WAAW,WAAW,IAAI,GAAG,QAAQ,IAAI;QAC9D,IAAI,QAAQ,GAAG,KAAK,WAAW,WAAW,GAAG,GAAG,QAAQ,GAAG;QAC3D,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;QACnF,IAAI,QAAQ,OAAO,KAAK,WAAW,WAAW,OAAO,GAAG,QAAQ,OAAO;QACvE,IAAI,QAAQ,MAAM,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,MAAM,KAAK;QAC5E,IAAI,QAAQ,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,SAAS;QAE7E,iBAAiB;QACjB,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;QACnF,IAAI,QAAQ,QAAQ,KAAK,WAAW,WAAW,QAAQ,GAAG,QAAQ,QAAQ;QAC1E,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,aAAa,GAAG,QAAQ,aAAa;QAEzF,qBAAqB;QACrB,IAAI,QAAQ,UAAU,KAAK,WAAW;YACpC,WAAW,UAAU,GAAG,QAAQ,UAAU;QAC5C,OAAO,IAAI,QAAQ,YAAY,KAAK,WAAW;YAC7C,WAAW,UAAU,GAAG,QAAQ,YAAY;QAC9C;QAEA,IAAI,QAAQ,YAAY,KAAK,WAAW,WAAW,YAAY,GAAG,QAAQ,YAAY;QACtF,IAAI,QAAQ,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,SAAS;QAC7E,IAAI,QAAQ,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,QAAQ,SAAS;QAE7E,iBAAiB;QACjB,IAAI,QAAQ,sBAAsB,KAAK,WAAW;YAChD,WAAW,sBAAsB,GAAG,QAAQ,sBAAsB;QACpE,OAAO,IAAI,QAAQ,YAAY,KAAK,WAAW;YAC7C,WAAW,sBAAsB,GAAG,QAAQ,YAAY;QAC1D;QAEA,IAAI,QAAQ,uBAAuB,KAAK,WAAW;YACjD,WAAW,uBAAuB,GAAG,QAAQ,uBAAuB;QACtE,OAAO,IAAI,QAAQ,aAAa,KAAK,WAAW;YAC9C,WAAW,uBAAuB,GAAG,QAAQ,aAAa;QAC5D,OAAO,IAAI,QAAQ,KAAK,KAAK,WAAW;YACtC,WAAW,uBAAuB,GAAG,QAAQ,KAAK;QACpD;QAEA,IAAI,QAAQ,uBAAuB,KAAK,WAAW;YACjD,WAAW,uBAAuB,GAAG,QAAQ,uBAAuB;QACtE;QAEA,qBAAqB;QACrB,IAAI,QAAQ,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,QAAQ,WAAW;QACnF,IAAI,QAAQ,gBAAgB,KAAK,WAAW,WAAW,gBAAgB,GAAG,QAAQ,gBAAgB;QAClG,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,aAAa,GAAG,QAAQ,aAAa;QACzF,IAAI,QAAQ,oBAAoB,KAAK,WAAW,WAAW,oBAAoB,GAAG,QAAQ,oBAAoB;QAE9G,uBAAuB;QACvB,IAAI,QAAQ,aAAa,KAAK,WAAW,WAAW,aAAa,GAAG,QAAQ,aAAa;QACzF,IAAI,QAAQ,gBAAgB,KAAK,WAAW,WAAW,gBAAgB,GAAG,QAAQ,gBAAgB;QAElG,eAAe;QACf,IAAI,QAAQ,eAAe,KAAK,WAAW,WAAW,eAAe,GAAG,QAAQ,eAAe;QAE/F,qBAAqB;QACrB,IAAI,QAAQ,iBAAiB,KAAK,WAAW,WAAW,iBAAiB,GAAG,QAAQ,iBAAiB;QAErG,QAAQ,GAAG,CAAC,sCAAsC;QAClD,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;IAC1C;IAEA,eAAe,OAAO;QACpB,OAAO,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;IAC3C;IAEA,qBAAqB,OACnB,IACA;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,QAAQ,OAAO,MAAM;QACvB;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,GAAG,OAAO,CAAC,EAAE;IACjD;IAEA,oBAAoB,OAClB,IACA;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,EAAE,UAAU;YACvD,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 5036, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/api/category-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse, User } from \"@/types/api\";\r\nimport {\r\n  Category,\r\n  CreateCategoryRequest,\r\n  UpdateCategoryRequest,\r\n  UpdateCategoryStatusRequest,\r\n} from \"@/types/product\";\r\n\r\nexport const categoryService = {\r\n  getCategories: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Category>> => {\r\n    try {\r\n      // Get the current user to determine their role\r\n      const currentUser: User = await apiClient.get(\"/auth/me\");\r\n      console.log(\"Current user for categories:\", currentUser);\r\n\r\n      // Determine which endpoint to use based on user role\r\n      const isAdmin =\r\n        currentUser?.role_name &&\r\n        [\"super_admin\", \"company_admin\", \"tenant_admin\"].includes(\r\n          currentUser.role_name.toLowerCase()\r\n        );\r\n\r\n      // For non-admin users, add tenant_id filter if not already present\r\n      const queryParams = { ...params };\r\n      if (!isAdmin && currentUser?.tenant_id && !queryParams.tenant_id) {\r\n        queryParams.tenant_id = currentUser.tenant_id;\r\n      }\r\n\r\n      // console.log(\"Fetching categories with params:\", queryParams);\r\n      const response = await apiClient.get<any>(\"/product-categories\", {\r\n        params: queryParams,\r\n      });\r\n\r\n      // Map API response to our Category type\r\n      const mapApiCategoryToCategory = (apiCategory: any): Category => {\r\n        return {\r\n          ...apiCategory,\r\n          // Add missing fields that our UI expects but API doesn't provide\r\n          status: apiCategory.deleted_at ? \"inactive\" : \"active\", // Derive status from deleted_at\r\n          // Map Parent to parent for UI consistency\r\n          parent: apiCategory.Parent ? {\r\n            id: apiCategory.Parent.id,\r\n            name: apiCategory.Parent.name,\r\n            description: apiCategory.Parent.description,\r\n            tenant_id: apiCategory.Parent.tenant_id,\r\n            created_at: apiCategory.Parent.created_at,\r\n            updated_at: apiCategory.Parent.updated_at,\r\n            deleted_at: apiCategory.Parent.deleted_at,\r\n            status: apiCategory.Parent.deleted_at ? \"inactive\" : \"active\",\r\n          } : null,\r\n        };\r\n      };\r\n\r\n      // If response is an array, convert to paginated format with mapped categories\r\n      if (Array.isArray(response)) {\r\n        const mappedCategories = response.map(mapApiCategoryToCategory);\r\n        return {\r\n          data: mappedCategories,\r\n          pagination: {\r\n            total: mappedCategories.length,\r\n            page: 1,\r\n            limit: mappedCategories.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        const mappedCategories = response.data.map(mapApiCategoryToCategory);\r\n        return {\r\n          data: mappedCategories,\r\n          pagination: response.pagination || {\r\n            total: mappedCategories.length,\r\n            page: 1,\r\n            limit: mappedCategories.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Default fallback\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error: any) {\r\n      console.error(\"Error in getCategories:\", error?.message || error);\r\n\r\n      // Log more detailed error information\r\n      if (error.code === 'ECONNABORTED') {\r\n        console.error(\"Request timeout in getCategories\");\r\n      } else if (error.response) {\r\n        console.error(\"Response error in getCategories:\", error.response.status, error.response.data);\r\n      } else if (error.request) {\r\n        console.error(\"Request error in getCategories (no response)\");\r\n      }\r\n\r\n      // Rethrow network errors to allow proper handling by the UI\r\n      if (error.code === 'ECONNABORTED' || (error.request && !error.response)) {\r\n        throw new Error(\"Network error. Please check your connection.\");\r\n      }\r\n\r\n      // Return empty data on other errors\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  getCategoryById: async (id: number): Promise<Category> => {\r\n    return apiClient.get(`/product-categories/${id}`);\r\n  },\r\n\r\n  createCategory: async (\r\n    category: CreateCategoryRequest\r\n  ): Promise<Category> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      description: category.description,\r\n      parent_id: category.parent_id,\r\n      // Include other fields that might be used by the UI\r\n      ...category,\r\n    };\r\n\r\n    return apiClient.post(\"/product-categories\", apiRequest);\r\n  },\r\n\r\n  updateCategory: async (\r\n    id: number,\r\n    category: UpdateCategoryRequest\r\n  ): Promise<Category> => {\r\n    // Ensure we're sending the correct fields to the API\r\n    const apiRequest = {\r\n      name: category.name,\r\n      description: category.description,\r\n      parent_id: category.parent_id,\r\n      // Include other fields that might be used by the UI\r\n      ...category,\r\n    };\r\n\r\n    return apiClient.put(`/product-categories/${id}`, apiRequest);\r\n  },\r\n\r\n  deleteCategory: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/product-categories/${id}`);\r\n  },\r\n\r\n  updateCategoryStatus: async (\r\n    id: number,\r\n    status: UpdateCategoryStatusRequest\r\n  ): Promise<Category> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      status: status.status, // 'active' or 'inactive'\r\n    };\r\n\r\n    return apiClient.put(`/product-categories/${id}/status`, apiRequest);\r\n  },\r\n\r\n  uploadCategoryImage: async (\r\n    id: number,\r\n    file: File\r\n  ): Promise<{ image_url: string }> => {\r\n    const formData = new FormData();\r\n    formData.append(\"image\", file);\r\n\r\n    return apiClient.post(`/product-categories/${id}/image`, formData, {\r\n      headers: {\r\n        \"Content-Type\": \"multipart/form-data\",\r\n      },\r\n    });\r\n  },\r\n\r\n  getCategoryTree: async (\r\n    params?: Record<string, any>\r\n  ): Promise<Category[]> => {\r\n    return apiClient.get(\"/product-categories/tree\", { params });\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AASO,MAAM,kBAAkB;IAC7B,eAAe,OACb;QAEA,IAAI;YACF,+CAA+C;YAC/C,MAAM,cAAoB,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;YAC9C,QAAQ,GAAG,CAAC,gCAAgC;YAE5C,qDAAqD;YACrD,MAAM,UACJ,aAAa,aACb;gBAAC;gBAAe;gBAAiB;aAAe,CAAC,QAAQ,CACvD,YAAY,SAAS,CAAC,WAAW;YAGrC,mEAAmE;YACnE,MAAM,cAAc;gBAAE,GAAG,MAAM;YAAC;YAChC,IAAI,CAAC,WAAW,aAAa,aAAa,CAAC,YAAY,SAAS,EAAE;gBAChE,YAAY,SAAS,GAAG,YAAY,SAAS;YAC/C;YAEA,gEAAgE;YAChE,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,uBAAuB;gBAC/D,QAAQ;YACV;YAEA,wCAAwC;YACxC,MAAM,2BAA2B,CAAC;gBAChC,OAAO;oBACL,GAAG,WAAW;oBACd,iEAAiE;oBACjE,QAAQ,YAAY,UAAU,GAAG,aAAa;oBAC9C,0CAA0C;oBAC1C,QAAQ,YAAY,MAAM,GAAG;wBAC3B,IAAI,YAAY,MAAM,CAAC,EAAE;wBACzB,MAAM,YAAY,MAAM,CAAC,IAAI;wBAC7B,aAAa,YAAY,MAAM,CAAC,WAAW;wBAC3C,WAAW,YAAY,MAAM,CAAC,SAAS;wBACvC,YAAY,YAAY,MAAM,CAAC,UAAU;wBACzC,YAAY,YAAY,MAAM,CAAC,UAAU;wBACzC,YAAY,YAAY,MAAM,CAAC,UAAU;wBACzC,QAAQ,YAAY,MAAM,CAAC,UAAU,GAAG,aAAa;oBACvD,IAAI;gBACN;YACF;YAEA,8EAA8E;YAC9E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,MAAM,mBAAmB,SAAS,GAAG,CAAC;gBACtC,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,iBAAiB,MAAM;wBAC9B,MAAM;wBACN,OAAO,iBAAiB,MAAM;wBAC9B,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,MAAM,mBAAmB,SAAS,IAAI,CAAC,GAAG,CAAC;gBAC3C,OAAO;oBACL,MAAM;oBACN,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,iBAAiB,MAAM;wBAC9B,MAAM;wBACN,OAAO,iBAAiB,MAAM;wBAC9B,YAAY;oBACd;gBACF;YACF;YAEA,mBAAmB;YACnB,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B,OAAO,WAAW;YAE3D,sCAAsC;YACtC,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,QAAQ,KAAK,CAAC;YAChB,OAAO,IAAI,MAAM,QAAQ,EAAE;gBACzB,QAAQ,KAAK,CAAC,oCAAoC,MAAM,QAAQ,CAAC,MAAM,EAAE,MAAM,QAAQ,CAAC,IAAI;YAC9F,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,QAAQ,KAAK,CAAC;YAChB;YAEA,4DAA4D;YAC5D,IAAI,MAAM,IAAI,KAAK,kBAAmB,MAAM,OAAO,IAAI,CAAC,MAAM,QAAQ,EAAG;gBACvE,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA,iBAAiB,OAAO;QACtB,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;IAClD;IAEA,gBAAgB,OACd;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,aAAa,SAAS,WAAW;YACjC,WAAW,SAAS,SAAS;YAC7B,oDAAoD;YACpD,GAAG,QAAQ;QACb;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;IAC/C;IAEA,gBAAgB,OACd,IACA;QAEA,qDAAqD;QACrD,MAAM,aAAa;YACjB,MAAM,SAAS,IAAI;YACnB,aAAa,SAAS,WAAW;YACjC,WAAW,SAAS,SAAS;YAC7B,oDAAoD;YACpD,GAAG,QAAQ;QACb;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;IACpD;IAEA,gBAAgB,OAAO;QACrB,OAAO,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;IACrD;IAEA,sBAAsB,OACpB,IACA;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,QAAQ,OAAO,MAAM;QACvB;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,GAAG,OAAO,CAAC,EAAE;IAC3D;IAEA,qBAAqB,OACnB,IACA;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,oBAAoB,EAAE,GAAG,MAAM,CAAC,EAAE,UAAU;YACjE,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF;IAEA,iBAAiB,OACf;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,4BAA4B;YAAE;QAAO;IAC5D;AACF", "debugId": null}}, {"offset": {"line": 5200, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/api/product-stock-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse, User } from \"@/types/api\";\r\nimport { BranchInventory } from \"@/types/inventory\";\r\nimport { Product } from \"@/types/product\";\r\n\r\nexport const productStockService = {\r\n  /**\r\n   * Get location-wise stock for a specific product\r\n   */\r\n  getProductLocationStock: async (productId: number): Promise<any[]> => {\r\n    try {\r\n      // Get stock items for this product across all branches\r\n      const response = await apiClient.get('/stock-items', {\r\n        params: {\r\n          product_id: productId,\r\n          limit: 100, // Get a large number of results\r\n        },\r\n      });\r\n\r\n      // If response is an array, return it directly\r\n      if (Array.isArray(response)) {\r\n        return response;\r\n      }\r\n\r\n      // If response is paginated, return the data array\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        return response.data;\r\n      }\r\n\r\n      // Default fallback\r\n      return [];\r\n    } catch (error) {\r\n      console.error(`Error fetching location stock for product ${productId}:`, error);\r\n      return [];\r\n    }\r\n  },\r\n  /**\r\n   * Get products with pricing information from stock items\r\n   * This uses the stock-items endpoint which includes pricing information\r\n   */\r\n  getProductsFromStock: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Product>> => {\r\n    try {\r\n      console.log(\"Fetching products from stock items with params:\", params);\r\n\r\n      // Get the current user to determine their role\r\n      const currentUser: User = await apiClient.get(\"/auth/me\");\r\n      console.log(\"Current user:\", currentUser);\r\n\r\n      // Determine which endpoint to use based on user role\r\n      const userRole = currentUser?.role_name?.toLowerCase() || \"\";\r\n      const isSuperAdmin = userRole === \"super_admin\";\r\n      const isAdmin = [\"super_admin\", \"company_admin\", \"tenant_admin\"].includes(\r\n        userRole\r\n      );\r\n\r\n      let response;\r\n\r\n      // For super_admin, try to get all products directly\r\n      if (isSuperAdmin) {\r\n        try {\r\n          console.log(\"Super admin user, using products endpoint\");\r\n          response = await apiClient.get<\r\n            Product[] | PaginatedResponse<Product>\r\n          >(\"/products\", {\r\n            params,\r\n          });\r\n\r\n          // If we got a valid response with products, return it directly\r\n          if (\r\n            response &&\r\n            (Array.isArray(response) ||\r\n              (response.data && Array.isArray(response.data)))\r\n          ) {\r\n            console.log(\"Successfully fetched products for super_admin\");\r\n\r\n            // If it's an array, convert to paginated format\r\n            if (Array.isArray(response)) {\r\n              return {\r\n                data: response,\r\n                pagination: {\r\n                  total: response.length,\r\n                  page: 1,\r\n                  limit: response.length,\r\n                  totalPages: 1,\r\n                },\r\n              };\r\n            }\r\n\r\n            // If it's already paginated, return as is\r\n            return response as PaginatedResponse<Product>;\r\n          }\r\n        } catch (error) {\r\n          console.log(\r\n            \"Error fetching from products endpoint, falling back to HQ stock items\",\r\n            error\r\n          );\r\n        }\r\n      }\r\n\r\n      // For all users, use stock-items endpoint\r\n      if (isAdmin) {\r\n        // Admin users should see HQ stock items (now using stock-items with branch_id=1)\r\n        console.log(\"Admin user, using stock-items endpoint with HQ branch\");\r\n        const adminParams = { ...params, branch_id: 1 }; // Assuming branch_id 1 is Headquarters\r\n        response = await apiClient.get<\r\n          BranchInventory[] | PaginatedResponse<BranchInventory>\r\n        >(\"/stock-items\", {\r\n          params: adminParams,\r\n        });\r\n      } else {\r\n        // Non-admin users should see their branch's stock items\r\n        const queryParams = { ...params };\r\n\r\n        if (!queryParams.branch_id && currentUser?.branch_id) {\r\n          console.log(\"Using user's branch_id:\", currentUser.branch_id);\r\n          queryParams.branch_id = currentUser.branch_id;\r\n        }\r\n\r\n        console.log(\"Final query params:\", queryParams);\r\n        response = await apiClient.get<\r\n          BranchInventory[] | PaginatedResponse<BranchInventory>\r\n        >(\"/stock-items\", {\r\n          params: queryParams,\r\n        });\r\n      }\r\n      console.log(\"Raw stock items API response:\", response);\r\n\r\n      // If response is an array, process it\r\n      if (Array.isArray(response)) {\r\n        // Group stock items by product to consolidate quantities and get latest pricing\r\n        const productMap = new Map<number, Product>();\r\n\r\n        response.forEach((stockItem) => {\r\n          if (!stockItem.Product) return;\r\n\r\n          const productId = stockItem.Product.id;\r\n          const existingProduct = productMap.get(productId);\r\n\r\n          // Convert string prices to numbers\r\n          const sellingPrice = parseFloat(stockItem.default_selling_price || \"0\");\r\n          const buyingPrice = parseFloat(stockItem.default_buying_price || \"0\");\r\n\r\n          if (existingProduct) {\r\n            // Update existing product with additional stock quantity and more details\r\n            productMap.set(productId, {\r\n              ...existingProduct,\r\n              stock_quantity:\r\n                (existingProduct.stock_quantity || 0) + stockItem.quantity,\r\n              // Keep the highest price if multiple entries exist\r\n              price: Math.max(existingProduct.price || 0, sellingPrice),\r\n              selling_price: Math.max(existingProduct.price || 0, sellingPrice),\r\n              buying_price: Math.max(\r\n                parseFloat(existingProduct.buying_price as string) || 0,\r\n                buyingPrice\r\n              ),\r\n              // Update category information if not already set\r\n              category_id: existingProduct.category_id || stockItem.Product.category_id || null,\r\n              ProductCategory: existingProduct.ProductCategory || stockItem.Product.ProductCategory || null,\r\n              // Update description if not already set\r\n              description: existingProduct.description || stockItem.Product.description || \"\",\r\n              // Update barcode if not already set\r\n              barcode: existingProduct.barcode || stockItem.Product.barcode || \"\",\r\n              // Update reorder level if not already set\r\n              reorder_level: existingProduct.reorder_level || stockItem.Product.reorder_level || 0,\r\n              // Update brand information if not already set\r\n              brand_id: existingProduct.brand_id || stockItem.Product.brand_id || null,\r\n              Brand: existingProduct.Brand || stockItem.Product.Brand || null,\r\n              // Update category hierarchy if not already set\r\n              categoryHierarchy: existingProduct.categoryHierarchy || stockItem.Product.categoryHierarchy || [],\r\n            });\r\n          } else {\r\n            // Create new product entry with more complete information\r\n            productMap.set(productId, {\r\n              id: productId,\r\n              tenant_id: stockItem.Branch?.tenant_id || stockItem.Branch?.id || 0,\r\n              name: stockItem.Product.name,\r\n              sku: stockItem.Product.sku || null,\r\n              has_serial: stockItem.Product.has_serial || false,\r\n              created_at: stockItem.created_at,\r\n              updated_at: stockItem.updated_at,\r\n              deleted_at: null,\r\n              // Add pricing information\r\n              price: sellingPrice,\r\n              selling_price: sellingPrice,\r\n              buying_price: buyingPrice,\r\n              // Add stock information\r\n              stock_quantity: stockItem.quantity,\r\n              // Set status based on availability\r\n              status: stockItem.quantity > 0 ? \"active\" : \"inactive\",\r\n              // Add category if available\r\n              category_id: stockItem.Product.category_id || null,\r\n              ProductCategory: stockItem.Product.ProductCategory || null,\r\n              // Add image if available\r\n              image_url: stockItem.Product.image_url,\r\n              // Add description if available\r\n              description: stockItem.Product.description || \"\",\r\n              // Add barcode if available\r\n              barcode: stockItem.Product.barcode || \"\",\r\n              // Add reorder level if available\r\n              reorder_level: stockItem.Product.reorder_level || 0,\r\n              // Add other product details\r\n              has_variants: stockItem.Product.has_serial || false,\r\n              is_taxable: stockItem.Product.is_taxable || false,\r\n              tax_rate: stockItem.Product.tax_rate || 0,\r\n              variants: stockItem.Product.variants || [],\r\n              // Add brand information if available\r\n              brand_id: stockItem.Product.brand_id || null,\r\n              Brand: stockItem.Product.Brand || null,\r\n              // Add category hierarchy if available\r\n              categoryHierarchy: stockItem.Product.categoryHierarchy || [],\r\n            });\r\n          }\r\n        });\r\n\r\n        // Convert map to array\r\n        const products = Array.from(productMap.values());\r\n        console.log(`Processed ${products.length} products from stock items`);\r\n\r\n        // Return in paginated format\r\n        return {\r\n          data: products,\r\n          pagination: {\r\n            total: products.length,\r\n            page: 1,\r\n            limit: products.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // If response is already in paginated format (unlikely for stock-items endpoint)\r\n      if (\r\n        response &&\r\n        typeof response === \"object\" &&\r\n        \"data\" in response &&\r\n        Array.isArray(response.data)\r\n      ) {\r\n        // Process the data similar to above\r\n        // This is a fallback case that's unlikely to be hit\r\n        return response as unknown as PaginatedResponse<Product>;\r\n      }\r\n\r\n      // Default fallback\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error fetching products from stock items:\", error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a single product with pricing information from stock items\r\n   */\r\n  getProductFromStockById: async (id: number): Promise<Product | null> => {\r\n    try {\r\n      // Get the current user to determine their role\r\n      const currentUser: User = await apiClient.get(\"/auth/me\");\r\n      console.log(\"Current user for product detail:\", currentUser);\r\n\r\n      // Determine which endpoint to use based on user role\r\n      const userRole = currentUser?.role_name?.toLowerCase() || \"\";\r\n      const isSuperAdmin = userRole === \"super_admin\";\r\n      const isAdmin = [\"super_admin\", \"company_admin\", \"tenant_admin\"].includes(\r\n        userRole\r\n      );\r\n\r\n      let response;\r\n\r\n      // For super_admin, try to get the product directly first\r\n      if (isSuperAdmin) {\r\n        try {\r\n          console.log(\r\n            \"Super admin user, using products endpoint for product detail\"\r\n          );\r\n          const product = await apiClient.get<Product>(`/products/${id}`);\r\n          if (product) {\r\n            console.log(\"Successfully fetched product for super_admin\");\r\n            return product;\r\n          }\r\n        } catch (error) {\r\n          console.log(\r\n            \"Error fetching from products endpoint, falling back to HQ stock items\",\r\n            error\r\n          );\r\n        }\r\n      }\r\n\r\n      // For all users, use stock-items endpoint\r\n      if (isAdmin) {\r\n        // Admin users should see HQ stock items (now using stock-items with branch_id=1)\r\n        console.log(\r\n          \"Admin user, using stock-items endpoint for product detail\"\r\n        );\r\n        try {\r\n          // Get the product from stock-items with HQ branch\r\n          response = await apiClient.get<BranchInventory[]>(`/stock-items`, {\r\n            params: {\r\n              product_id: id,\r\n              branch_id: 1, // Assuming branch_id 1 is Headquarters\r\n            },\r\n          });\r\n        } catch {\r\n          console.log(\r\n            \"Error fetching from stock-items, falling back to products endpoint\"\r\n          );\r\n          // If that fails, fall back to regular product endpoint\r\n          const product = await apiClient.get<Product>(`/products/${id}`);\r\n          return product;\r\n        }\r\n      } else {\r\n        // Non-admin users should see their branch's stock items\r\n        const queryParams: Record<string, any> = { product_id: id };\r\n\r\n        if (currentUser?.branch_id) {\r\n          console.log(\r\n            \"Using user's branch_id for product detail:\",\r\n            currentUser.branch_id\r\n          );\r\n          queryParams.branch_id = currentUser.branch_id;\r\n        }\r\n\r\n        console.log(\"Final query params for product detail:\", queryParams);\r\n        response = await apiClient.get<\r\n          BranchInventory[] | PaginatedResponse<BranchInventory>\r\n        >(\"/stock-items\", {\r\n          params: queryParams,\r\n        });\r\n      }\r\n\r\n      if (!Array.isArray(response) || response.length === 0) {\r\n        // If no stock items found, fall back to regular product endpoint\r\n        const product = await apiClient.get<Product>(`/products/${id}`);\r\n        return product;\r\n      }\r\n\r\n      // Get the first stock item with this product\r\n      const stockItem = response.find((item) => item.Product?.id === id);\r\n\r\n      if (!stockItem || !stockItem.Product) {\r\n        return null;\r\n      }\r\n\r\n      // Calculate total quantity across all stock items for this product\r\n      const totalQuantity = response\r\n        .filter((item) => item.Product?.id === id)\r\n        .reduce((sum, item) => sum + item.quantity, 0);\r\n\r\n      // Convert string prices to numbers\r\n      const sellingPrice = parseFloat(stockItem.default_selling_price || \"0\");\r\n      const buyingPrice = parseFloat(stockItem.default_buying_price || \"0\");\r\n\r\n      // Create product with comprehensive information\r\n      return {\r\n        id: stockItem.Product.id,\r\n        tenant_id: stockItem.Branch?.tenant_id || stockItem.Branch?.id || 0,\r\n        name: stockItem.Product.name,\r\n        sku: stockItem.Product.sku || null,\r\n        has_serial: stockItem.Product.has_serial || false,\r\n        created_at: stockItem.created_at,\r\n        updated_at: stockItem.updated_at,\r\n        deleted_at: null,\r\n        // Add pricing information\r\n        price: sellingPrice,\r\n        selling_price: sellingPrice,\r\n        buying_price: buyingPrice,\r\n        // Add stock information\r\n        stock_quantity: totalQuantity,\r\n        // Set status based on availability\r\n        status: totalQuantity > 0 ? \"active\" : \"inactive\",\r\n        // Add category information\r\n        category_id: stockItem.Product.category_id || null,\r\n        ProductCategory: stockItem.Product.ProductCategory || null,\r\n        // Add image if available\r\n        image_url: stockItem.Product.image_url,\r\n        // Add description if available\r\n        description: stockItem.Product.description || \"\",\r\n        // Add barcode if available\r\n        barcode: stockItem.Product.barcode || \"\",\r\n        // Add reorder level if available\r\n        reorder_level: stockItem.Product.reorder_level || 0,\r\n        // Add other product details\r\n        has_variants: stockItem.Product.has_serial || false,\r\n        is_taxable: stockItem.Product.is_taxable || false,\r\n        tax_rate: stockItem.Product.tax_rate || 0,\r\n        variants: stockItem.Product.variants || [],\r\n        // Add brand information if available\r\n        brand_id: stockItem.Product.brand_id || null,\r\n        Brand: stockItem.Product.Brand || null,\r\n        // Add category hierarchy if available\r\n        categoryHierarchy: stockItem.Product.categoryHierarchy || [],\r\n      };\r\n    } catch (error) {\r\n      console.error(`Error fetching product ${id} from stock items:`, error);\r\n      return null;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAKO,MAAM,sBAAsB;IACjC;;GAEC,GACD,yBAAyB,OAAO;QAC9B,IAAI;YACF,uDAAuD;YACvD,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,gBAAgB;gBACnD,QAAQ;oBACN,YAAY;oBACZ,OAAO;gBACT;YACF;YAEA,8CAA8C;YAC9C,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,OAAO;YACT;YAEA,kDAAkD;YAClD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,OAAO,SAAS,IAAI;YACtB;YAEA,mBAAmB;YACnB,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,UAAU,CAAC,CAAC,EAAE;YACzE,OAAO,EAAE;QACX;IACF;IACA;;;GAGC,GACD,sBAAsB,OACpB;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,mDAAmD;YAE/D,+CAA+C;YAC/C,MAAM,cAAoB,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;YAC9C,QAAQ,GAAG,CAAC,iBAAiB;YAE7B,qDAAqD;YACrD,MAAM,WAAW,aAAa,WAAW,iBAAiB;YAC1D,MAAM,eAAe,aAAa;YAClC,MAAM,UAAU;gBAAC;gBAAe;gBAAiB;aAAe,CAAC,QAAQ,CACvE;YAGF,IAAI;YAEJ,oDAAoD;YACpD,IAAI,cAAc;gBAChB,IAAI;oBACF,QAAQ,GAAG,CAAC;oBACZ,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAE5B,aAAa;wBACb;oBACF;oBAEA,+DAA+D;oBAC/D,IACE,YACA,CAAC,MAAM,OAAO,CAAC,aACZ,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAE,GACjD;wBACA,QAAQ,GAAG,CAAC;wBAEZ,gDAAgD;wBAChD,IAAI,MAAM,OAAO,CAAC,WAAW;4BAC3B,OAAO;gCACL,MAAM;gCACN,YAAY;oCACV,OAAO,SAAS,MAAM;oCACtB,MAAM;oCACN,OAAO,SAAS,MAAM;oCACtB,YAAY;gCACd;4BACF;wBACF;wBAEA,0CAA0C;wBAC1C,OAAO;oBACT;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CACT,yEACA;gBAEJ;YACF;YAEA,0CAA0C;YAC1C,IAAI,SAAS;gBACX,iFAAiF;gBACjF,QAAQ,GAAG,CAAC;gBACZ,MAAM,cAAc;oBAAE,GAAG,MAAM;oBAAE,WAAW;gBAAE,GAAG,uCAAuC;gBACxF,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAE5B,gBAAgB;oBAChB,QAAQ;gBACV;YACF,OAAO;gBACL,wDAAwD;gBACxD,MAAM,cAAc;oBAAE,GAAG,MAAM;gBAAC;gBAEhC,IAAI,CAAC,YAAY,SAAS,IAAI,aAAa,WAAW;oBACpD,QAAQ,GAAG,CAAC,2BAA2B,YAAY,SAAS;oBAC5D,YAAY,SAAS,GAAG,YAAY,SAAS;gBAC/C;gBAEA,QAAQ,GAAG,CAAC,uBAAuB;gBACnC,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAE5B,gBAAgB;oBAChB,QAAQ;gBACV;YACF;YACA,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,sCAAsC;YACtC,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,gFAAgF;gBAChF,MAAM,aAAa,IAAI;gBAEvB,SAAS,OAAO,CAAC,CAAC;oBAChB,IAAI,CAAC,UAAU,OAAO,EAAE;oBAExB,MAAM,YAAY,UAAU,OAAO,CAAC,EAAE;oBACtC,MAAM,kBAAkB,WAAW,GAAG,CAAC;oBAEvC,mCAAmC;oBACnC,MAAM,eAAe,WAAW,UAAU,qBAAqB,IAAI;oBACnE,MAAM,cAAc,WAAW,UAAU,oBAAoB,IAAI;oBAEjE,IAAI,iBAAiB;wBACnB,0EAA0E;wBAC1E,WAAW,GAAG,CAAC,WAAW;4BACxB,GAAG,eAAe;4BAClB,gBACE,CAAC,gBAAgB,cAAc,IAAI,CAAC,IAAI,UAAU,QAAQ;4BAC5D,mDAAmD;4BACnD,OAAO,KAAK,GAAG,CAAC,gBAAgB,KAAK,IAAI,GAAG;4BAC5C,eAAe,KAAK,GAAG,CAAC,gBAAgB,KAAK,IAAI,GAAG;4BACpD,cAAc,KAAK,GAAG,CACpB,WAAW,gBAAgB,YAAY,KAAe,GACtD;4BAEF,iDAAiD;4BACjD,aAAa,gBAAgB,WAAW,IAAI,UAAU,OAAO,CAAC,WAAW,IAAI;4BAC7E,iBAAiB,gBAAgB,eAAe,IAAI,UAAU,OAAO,CAAC,eAAe,IAAI;4BACzF,wCAAwC;4BACxC,aAAa,gBAAgB,WAAW,IAAI,UAAU,OAAO,CAAC,WAAW,IAAI;4BAC7E,oCAAoC;4BACpC,SAAS,gBAAgB,OAAO,IAAI,UAAU,OAAO,CAAC,OAAO,IAAI;4BACjE,0CAA0C;4BAC1C,eAAe,gBAAgB,aAAa,IAAI,UAAU,OAAO,CAAC,aAAa,IAAI;4BACnF,8CAA8C;4BAC9C,UAAU,gBAAgB,QAAQ,IAAI,UAAU,OAAO,CAAC,QAAQ,IAAI;4BACpE,OAAO,gBAAgB,KAAK,IAAI,UAAU,OAAO,CAAC,KAAK,IAAI;4BAC3D,+CAA+C;4BAC/C,mBAAmB,gBAAgB,iBAAiB,IAAI,UAAU,OAAO,CAAC,iBAAiB,IAAI,EAAE;wBACnG;oBACF,OAAO;wBACL,0DAA0D;wBAC1D,WAAW,GAAG,CAAC,WAAW;4BACxB,IAAI;4BACJ,WAAW,UAAU,MAAM,EAAE,aAAa,UAAU,MAAM,EAAE,MAAM;4BAClE,MAAM,UAAU,OAAO,CAAC,IAAI;4BAC5B,KAAK,UAAU,OAAO,CAAC,GAAG,IAAI;4BAC9B,YAAY,UAAU,OAAO,CAAC,UAAU,IAAI;4BAC5C,YAAY,UAAU,UAAU;4BAChC,YAAY,UAAU,UAAU;4BAChC,YAAY;4BACZ,0BAA0B;4BAC1B,OAAO;4BACP,eAAe;4BACf,cAAc;4BACd,wBAAwB;4BACxB,gBAAgB,UAAU,QAAQ;4BAClC,mCAAmC;4BACnC,QAAQ,UAAU,QAAQ,GAAG,IAAI,WAAW;4BAC5C,4BAA4B;4BAC5B,aAAa,UAAU,OAAO,CAAC,WAAW,IAAI;4BAC9C,iBAAiB,UAAU,OAAO,CAAC,eAAe,IAAI;4BACtD,yBAAyB;4BACzB,WAAW,UAAU,OAAO,CAAC,SAAS;4BACtC,+BAA+B;4BAC/B,aAAa,UAAU,OAAO,CAAC,WAAW,IAAI;4BAC9C,2BAA2B;4BAC3B,SAAS,UAAU,OAAO,CAAC,OAAO,IAAI;4BACtC,iCAAiC;4BACjC,eAAe,UAAU,OAAO,CAAC,aAAa,IAAI;4BAClD,4BAA4B;4BAC5B,cAAc,UAAU,OAAO,CAAC,UAAU,IAAI;4BAC9C,YAAY,UAAU,OAAO,CAAC,UAAU,IAAI;4BAC5C,UAAU,UAAU,OAAO,CAAC,QAAQ,IAAI;4BACxC,UAAU,UAAU,OAAO,CAAC,QAAQ,IAAI,EAAE;4BAC1C,qCAAqC;4BACrC,UAAU,UAAU,OAAO,CAAC,QAAQ,IAAI;4BACxC,OAAO,UAAU,OAAO,CAAC,KAAK,IAAI;4BAClC,sCAAsC;4BACtC,mBAAmB,UAAU,OAAO,CAAC,iBAAiB,IAAI,EAAE;wBAC9D;oBACF;gBACF;gBAEA,uBAAuB;gBACvB,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,MAAM;gBAC7C,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,SAAS,MAAM,CAAC,0BAA0B,CAAC;gBAEpE,6BAA6B;gBAC7B,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,SAAS,MAAM;wBACtB,MAAM;wBACN,OAAO,SAAS,MAAM;wBACtB,YAAY;oBACd;gBACF;YACF;YAEA,iFAAiF;YACjF,IACE,YACA,OAAO,aAAa,YACpB,UAAU,YACV,MAAM,OAAO,CAAC,SAAS,IAAI,GAC3B;gBACA,oCAAoC;gBACpC,oDAAoD;gBACpD,OAAO;YACT;YAEA,mBAAmB;YACnB,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA;;GAEC,GACD,yBAAyB,OAAO;QAC9B,IAAI;YACF,+CAA+C;YAC/C,MAAM,cAAoB,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;YAC9C,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,qDAAqD;YACrD,MAAM,WAAW,aAAa,WAAW,iBAAiB;YAC1D,MAAM,eAAe,aAAa;YAClC,MAAM,UAAU;gBAAC;gBAAe;gBAAiB;aAAe,CAAC,QAAQ,CACvE;YAGF,IAAI;YAEJ,yDAAyD;YACzD,IAAI,cAAc;gBAChB,IAAI;oBACF,QAAQ,GAAG,CACT;oBAEF,MAAM,UAAU,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAU,CAAC,UAAU,EAAE,IAAI;oBAC9D,IAAI,SAAS;wBACX,QAAQ,GAAG,CAAC;wBACZ,OAAO;oBACT;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,GAAG,CACT,yEACA;gBAEJ;YACF;YAEA,0CAA0C;YAC1C,IAAI,SAAS;gBACX,iFAAiF;gBACjF,QAAQ,GAAG,CACT;gBAEF,IAAI;oBACF,kDAAkD;oBAClD,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAoB,CAAC,YAAY,CAAC,EAAE;wBAChE,QAAQ;4BACN,YAAY;4BACZ,WAAW;wBACb;oBACF;gBACF,EAAE,OAAM;oBACN,QAAQ,GAAG,CACT;oBAEF,uDAAuD;oBACvD,MAAM,UAAU,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAU,CAAC,UAAU,EAAE,IAAI;oBAC9D,OAAO;gBACT;YACF,OAAO;gBACL,wDAAwD;gBACxD,MAAM,cAAmC;oBAAE,YAAY;gBAAG;gBAE1D,IAAI,aAAa,WAAW;oBAC1B,QAAQ,GAAG,CACT,8CACA,YAAY,SAAS;oBAEvB,YAAY,SAAS,GAAG,YAAY,SAAS;gBAC/C;gBAEA,QAAQ,GAAG,CAAC,0CAA0C;gBACtD,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAE5B,gBAAgB;oBAChB,QAAQ;gBACV;YACF;YAEA,IAAI,CAAC,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,KAAK,GAAG;gBACrD,iEAAiE;gBACjE,MAAM,UAAU,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAU,CAAC,UAAU,EAAE,IAAI;gBAC9D,OAAO;YACT;YAEA,6CAA6C;YAC7C,MAAM,YAAY,SAAS,IAAI,CAAC,CAAC,OAAS,KAAK,OAAO,EAAE,OAAO;YAE/D,IAAI,CAAC,aAAa,CAAC,UAAU,OAAO,EAAE;gBACpC,OAAO;YACT;YAEA,mEAAmE;YACnE,MAAM,gBAAgB,SACnB,MAAM,CAAC,CAAC,OAAS,KAAK,OAAO,EAAE,OAAO,IACtC,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE;YAE9C,mCAAmC;YACnC,MAAM,eAAe,WAAW,UAAU,qBAAqB,IAAI;YACnE,MAAM,cAAc,WAAW,UAAU,oBAAoB,IAAI;YAEjE,gDAAgD;YAChD,OAAO;gBACL,IAAI,UAAU,OAAO,CAAC,EAAE;gBACxB,WAAW,UAAU,MAAM,EAAE,aAAa,UAAU,MAAM,EAAE,MAAM;gBAClE,MAAM,UAAU,OAAO,CAAC,IAAI;gBAC5B,KAAK,UAAU,OAAO,CAAC,GAAG,IAAI;gBAC9B,YAAY,UAAU,OAAO,CAAC,UAAU,IAAI;gBAC5C,YAAY,UAAU,UAAU;gBAChC,YAAY,UAAU,UAAU;gBAChC,YAAY;gBACZ,0BAA0B;gBAC1B,OAAO;gBACP,eAAe;gBACf,cAAc;gBACd,wBAAwB;gBACxB,gBAAgB;gBAChB,mCAAmC;gBACnC,QAAQ,gBAAgB,IAAI,WAAW;gBACvC,2BAA2B;gBAC3B,aAAa,UAAU,OAAO,CAAC,WAAW,IAAI;gBAC9C,iBAAiB,UAAU,OAAO,CAAC,eAAe,IAAI;gBACtD,yBAAyB;gBACzB,WAAW,UAAU,OAAO,CAAC,SAAS;gBACtC,+BAA+B;gBAC/B,aAAa,UAAU,OAAO,CAAC,WAAW,IAAI;gBAC9C,2BAA2B;gBAC3B,SAAS,UAAU,OAAO,CAAC,OAAO,IAAI;gBACtC,iCAAiC;gBACjC,eAAe,UAAU,OAAO,CAAC,aAAa,IAAI;gBAClD,4BAA4B;gBAC5B,cAAc,UAAU,OAAO,CAAC,UAAU,IAAI;gBAC9C,YAAY,UAAU,OAAO,CAAC,UAAU,IAAI;gBAC5C,UAAU,UAAU,OAAO,CAAC,QAAQ,IAAI;gBACxC,UAAU,UAAU,OAAO,CAAC,QAAQ,IAAI,EAAE;gBAC1C,qCAAqC;gBACrC,UAAU,UAAU,OAAO,CAAC,QAAQ,IAAI;gBACxC,OAAO,UAAU,OAAO,CAAC,KAAK,IAAI;gBAClC,sCAAsC;gBACtC,mBAAmB,UAAU,OAAO,CAAC,iBAAiB,IAAI,EAAE;YAC9D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,kBAAkB,CAAC,EAAE;YAChE,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 5555, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/api/brand-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse } from \"@/types/api\";\r\nimport { Brand, CreateBrandRequest, UpdateBrandRequest } from \"@/types/brand\";\r\n\r\nexport const brandService = {\r\n  /**\r\n   * Get all brands with optional filtering\r\n   * @param params Query parameters\r\n   * @returns Paginated list of brands\r\n   */\r\n  getBrands: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<Brand>> => {\r\n    try {\r\n      console.log(\"Fetching brands with params:\", params);\r\n      const response = await apiClient.get<any>(\"/brands\", { params });\r\n\r\n      // Map API response to our Brand type\r\n      const mapApiBrandToBrand = (apiBrand: any): Brand => ({\r\n        ...apiBrand,\r\n        // Add missing fields that our UI expects but API doesn't provide\r\n        status: apiBrand.deleted_at ? \"inactive\" : \"active\", // Derive status from deleted_at\r\n      });\r\n\r\n      // If response is an array, convert to paginated format with mapped brands\r\n      if (Array.isArray(response)) {\r\n        const mappedBrands = response.map(mapApiBrandToBrand);\r\n        return {\r\n          data: mappedBrands,\r\n          pagination: {\r\n            total: mappedBrands.length,\r\n            page: 1,\r\n            limit: mappedBrands.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        const mappedBrands = response.data.map(mapApiBrandToBrand);\r\n        return {\r\n          data: mappedBrands,\r\n          pagination: response.pagination || {\r\n            total: mappedBrands.length,\r\n            page: 1,\r\n            limit: mappedBrands.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Default fallback\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error: any) {\r\n      console.error(\"Error in getBrands:\", error?.message || error);\r\n\r\n      // Log more detailed error information\r\n      if (error.code === 'ECONNABORTED') {\r\n        console.error(\"Request timeout in getBrands\");\r\n      } else if (error.response) {\r\n        console.error(\"Response error in getBrands:\", error.response.status, error.response.data);\r\n      } else if (error.request) {\r\n        console.error(\"Request error in getBrands (no response)\");\r\n      }\r\n\r\n      // Rethrow network errors to allow proper handling by the UI\r\n      if (error.code === 'ECONNABORTED' || (error.request && !error.response)) {\r\n        throw new Error(\"Network error. Please check your connection.\");\r\n      }\r\n\r\n      // Return empty data on other errors\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get a brand by ID\r\n   * @param id Brand ID\r\n   * @returns Brand object\r\n   */\r\n  getBrandById: async (id: number): Promise<Brand> => {\r\n    return apiClient.get(`/brands/${id}`);\r\n  },\r\n\r\n  /**\r\n   * Create a new brand\r\n   * @param brand Brand data\r\n   * @returns Created brand\r\n   */\r\n  createBrand: async (brand: CreateBrandRequest): Promise<Brand> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      name: brand.name,\r\n      description: brand.description,\r\n    };\r\n\r\n    return apiClient.post(\"/brands\", apiRequest);\r\n  },\r\n\r\n  /**\r\n   * Update an existing brand\r\n   * @param id Brand ID\r\n   * @param brand Brand data to update\r\n   * @returns Updated brand\r\n   */\r\n  updateBrand: async (\r\n    id: number,\r\n    brand: UpdateBrandRequest\r\n  ): Promise<Brand> => {\r\n    // Ensure we're sending the correct fields to the API\r\n    const apiRequest = {\r\n      name: brand.name,\r\n      description: brand.description,\r\n    };\r\n\r\n    return apiClient.put(`/brands/${id}`, apiRequest);\r\n  },\r\n\r\n  /**\r\n   * Delete a brand\r\n   * @param id Brand ID\r\n   */\r\n  deleteBrand: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/brands/${id}`);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAIO,MAAM,eAAe;IAC1B;;;;GAIC,GACD,WAAW,OACT;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,WAAW;gBAAE;YAAO;YAE9D,qCAAqC;YACrC,MAAM,qBAAqB,CAAC,WAAyB,CAAC;oBACpD,GAAG,QAAQ;oBACX,iEAAiE;oBACjE,QAAQ,SAAS,UAAU,GAAG,aAAa;gBAC7C,CAAC;YAED,0EAA0E;YAC1E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,MAAM,eAAe,SAAS,GAAG,CAAC;gBAClC,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,aAAa,MAAM;wBAC1B,MAAM;wBACN,OAAO,aAAa,MAAM;wBAC1B,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,MAAM,eAAe,SAAS,IAAI,CAAC,GAAG,CAAC;gBACvC,OAAO;oBACL,MAAM;oBACN,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,aAAa,MAAM;wBAC1B,MAAM;wBACN,OAAO,aAAa,MAAM;wBAC1B,YAAY;oBACd;gBACF;YACF;YAEA,mBAAmB;YACnB,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uBAAuB,OAAO,WAAW;YAEvD,sCAAsC;YACtC,IAAI,MAAM,IAAI,KAAK,gBAAgB;gBACjC,QAAQ,KAAK,CAAC;YAChB,OAAO,IAAI,MAAM,QAAQ,EAAE;gBACzB,QAAQ,KAAK,CAAC,gCAAgC,MAAM,QAAQ,CAAC,MAAM,EAAE,MAAM,QAAQ,CAAC,IAAI;YAC1F,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,QAAQ,KAAK,CAAC;YAChB;YAEA,4DAA4D;YAC5D,IAAI,MAAM,IAAI,KAAK,kBAAmB,MAAM,OAAO,IAAI,CAAC,MAAM,QAAQ,EAAG;gBACvE,MAAM,IAAI,MAAM;YAClB;YAEA,oCAAoC;YACpC,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA;;;;GAIC,GACD,cAAc,OAAO;QACnB,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI;IACtC;IAEA;;;;GAIC,GACD,aAAa,OAAO;QAClB,sDAAsD;QACtD,MAAM,aAAa;YACjB,MAAM,MAAM,IAAI;YAChB,aAAa,MAAM,WAAW;QAChC;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,WAAW;IACnC;IAEA;;;;;GAKC,GACD,aAAa,OACX,IACA;QAEA,qDAAqD;QACrD,MAAM,aAAa;YACjB,MAAM,MAAM,IAAI;YAChB,aAAa,MAAM,WAAW;QAChC;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI,EAAE;IACxC;IAEA;;;GAGC,GACD,aAAa,OAAO;QAClB,OAAO,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,IAAI;IACzC;AACF", "debugId": null}}, {"offset": {"line": 5684, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/api/brand-type-service.ts"], "sourcesContent": ["import apiClient from \"@/lib/api-client\";\r\nimport { PaginatedResponse } from \"@/types/api\";\r\nimport {\r\n  BrandType,\r\n  CreateBrandTypeRequest,\r\n  UpdateBrandTypeRequest,\r\n} from \"@/types/brand\";\r\n\r\nexport const brandTypeService = {\r\n  /**\r\n   * Get all brand types with optional filtering\r\n   * @param params Query parameters\r\n   * @returns Paginated list of brand types\r\n   */\r\n  getBrandTypes: async (\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<BrandType>> => {\r\n    try {\r\n      console.log(\"Fetching brand types with params:\", params);\r\n      const response = await apiClient.get<any>(\"/brand-types\", { params });\r\n\r\n      // Map API response to our BrandType type\r\n      const mapApiBrandTypeToBrandType = (apiBrandType: any): BrandType => ({\r\n        ...apiBrandType,\r\n        // Add missing fields that our UI expects but API doesn't provide\r\n        status: apiBrandType.deleted_at ? \"inactive\" : \"active\", // Derive status from deleted_at\r\n      });\r\n\r\n      // If response is an array, convert to paginated format with mapped brand types\r\n      if (Array.isArray(response)) {\r\n        const mappedBrandTypes = response.map(mapApiBrandTypeToBrandType);\r\n        return {\r\n          data: mappedBrandTypes,\r\n          pagination: {\r\n            total: mappedBrandTypes.length,\r\n            page: 1,\r\n            limit: mappedBrandTypes.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Check if response is already in paginated format\r\n      if (response && response.data && Array.isArray(response.data)) {\r\n        const mappedBrandTypes = response.data.map(mapApiBrandTypeToBrandType);\r\n        return {\r\n          data: mappedBrandTypes,\r\n          pagination: response.pagination || {\r\n            total: mappedBrandTypes.length,\r\n            page: 1,\r\n            limit: mappedBrandTypes.length,\r\n            totalPages: 1,\r\n          },\r\n        };\r\n      }\r\n\r\n      // Default fallback\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    } catch (error: any) {\r\n      console.error(\"Error in getBrandTypes:\", error?.message || error);\r\n      // Return empty data on error\r\n      return {\r\n        data: [],\r\n        pagination: {\r\n          total: 0,\r\n          page: 1,\r\n          limit: 0,\r\n          totalPages: 1,\r\n        },\r\n      };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get brand types filtered by brand ID\r\n   * @param brandId Brand ID to filter by\r\n   * @param params Additional query parameters\r\n   * @returns Paginated list of brand types for the specified brand\r\n   */\r\n  getBrandTypesByBrandId: async (\r\n    brandId: number,\r\n    params?: Record<string, any>\r\n  ): Promise<PaginatedResponse<BrandType>> => {\r\n    const queryParams = {\r\n      ...params,\r\n      brand_id: brandId,\r\n    };\r\n    return brandTypeService.getBrandTypes(queryParams);\r\n  },\r\n\r\n  /**\r\n   * Get a brand type by ID\r\n   * @param id Brand type ID\r\n   * @returns Brand type object\r\n   */\r\n  getBrandTypeById: async (id: number): Promise<BrandType> => {\r\n    return apiClient.get(`/brand-types/${id}`);\r\n  },\r\n\r\n  /**\r\n   * Create a new brand type\r\n   * @param brandType Brand type data\r\n   * @returns Created brand type\r\n   */\r\n  createBrandType: async (\r\n    brandType: CreateBrandTypeRequest\r\n  ): Promise<BrandType> => {\r\n    // Ensure we're sending the required fields to the API\r\n    const apiRequest = {\r\n      name: brandType.name,\r\n      description: brandType.description,\r\n      brand_id: brandType.brand_id,\r\n    };\r\n\r\n    return apiClient.post(\"/brand-types\", apiRequest);\r\n  },\r\n\r\n  /**\r\n   * Update an existing brand type\r\n   * @param id Brand type ID\r\n   * @param brandType Brand type data to update\r\n   * @returns Updated brand type\r\n   */\r\n  updateBrandType: async (\r\n    id: number,\r\n    brandType: UpdateBrandTypeRequest\r\n  ): Promise<BrandType> => {\r\n    // Ensure we're sending the correct fields to the API\r\n    const apiRequest = {\r\n      name: brandType.name,\r\n      description: brandType.description,\r\n      brand_id: brandType.brand_id,\r\n    };\r\n\r\n    return apiClient.put(`/brand-types/${id}`, apiRequest);\r\n  },\r\n\r\n  /**\r\n   * Delete a brand type\r\n   * @param id Brand type ID\r\n   */\r\n  deleteBrandType: async (id: number): Promise<void> => {\r\n    return apiClient.delete(`/brand-types/${id}`);\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAQO,MAAM,mBAAmB;IAC9B;;;;GAIC,GACD,eAAe,OACb;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,qCAAqC;YACjD,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,gBAAgB;gBAAE;YAAO;YAEnE,yCAAyC;YACzC,MAAM,6BAA6B,CAAC,eAAiC,CAAC;oBACpE,GAAG,YAAY;oBACf,iEAAiE;oBACjE,QAAQ,aAAa,UAAU,GAAG,aAAa;gBACjD,CAAC;YAED,+EAA+E;YAC/E,IAAI,MAAM,OAAO,CAAC,WAAW;gBAC3B,MAAM,mBAAmB,SAAS,GAAG,CAAC;gBACtC,OAAO;oBACL,MAAM;oBACN,YAAY;wBACV,OAAO,iBAAiB,MAAM;wBAC9B,MAAM;wBACN,OAAO,iBAAiB,MAAM;wBAC9B,YAAY;oBACd;gBACF;YACF;YAEA,mDAAmD;YACnD,IAAI,YAAY,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBAC7D,MAAM,mBAAmB,SAAS,IAAI,CAAC,GAAG,CAAC;gBAC3C,OAAO;oBACL,MAAM;oBACN,YAAY,SAAS,UAAU,IAAI;wBACjC,OAAO,iBAAiB,MAAM;wBAC9B,MAAM;wBACN,OAAO,iBAAiB,MAAM;wBAC9B,YAAY;oBACd;gBACF;YACF;YAEA,mBAAmB;YACnB,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,2BAA2B,OAAO,WAAW;YAC3D,6BAA6B;YAC7B,OAAO;gBACL,MAAM,EAAE;gBACR,YAAY;oBACV,OAAO;oBACP,MAAM;oBACN,OAAO;oBACP,YAAY;gBACd;YACF;QACF;IACF;IAEA;;;;;GAKC,GACD,wBAAwB,OACtB,SACA;QAEA,MAAM,cAAc;YAClB,GAAG,MAAM;YACT,UAAU;QACZ;QACA,OAAO,iBAAiB,aAAa,CAAC;IACxC;IAEA;;;;GAIC,GACD,kBAAkB,OAAO;QACvB,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI;IAC3C;IAEA;;;;GAIC,GACD,iBAAiB,OACf;QAEA,sDAAsD;QACtD,MAAM,aAAa;YACjB,MAAM,UAAU,IAAI;YACpB,aAAa,UAAU,WAAW;YAClC,UAAU,UAAU,QAAQ;QAC9B;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBAAgB;IACxC;IAEA;;;;;GAKC,GACD,iBAAiB,OACf,IACA;QAEA,qDAAqD;QACrD,MAAM,aAAa;YACjB,MAAM,UAAU,IAAI;YACpB,aAAa,UAAU,WAAW;YAClC,UAAU,UAAU,QAAQ;QAC9B;QAEA,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI,EAAE;IAC7C;IAEA;;;GAGC,GACD,iBAAiB,OAAO;QACtB,OAAO,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI;IAC9C;AACF", "debugId": null}}, {"offset": {"line": 5815, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/products/api/index.ts"], "sourcesContent": ["export * from './product-service';\r\nexport * from './category-service';\r\nexport * from './product-stock-service';\r\nexport * from './brand-service';\r\nexport * from './brand-type-service';\r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 5845, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/inventory/api/supplier-service.ts"], "sourcesContent": ["import apiClient from '@/lib/api-client';\r\nimport { Supplier, SupplierFormData } from '../types/supplier';\r\n\r\nexport const supplierService = {\r\n  /**\r\n   * Get all suppliers with optional filtering\r\n   */\r\n  getSuppliers: async (params?: {\r\n    search?: string;\r\n    page?: number;\r\n    limit?: number;\r\n  }): Promise<{ data: Supplier[]; pagination: any }> => {\r\n    try {\r\n      console.log('Fetching suppliers with params:', params);\r\n      const response = await apiClient.get<any>('/suppliers', { params });\r\n      console.log('Suppliers API response:', response);\r\n      return response;\r\n    } catch (error) {\r\n      console.error('Error fetching suppliers:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Get supplier by ID\r\n   */\r\n  getSupplierById: async (id: number): Promise<Supplier> => {\r\n    try {\r\n      const response = await apiClient.get<Supplier>(`/suppliers/${id}`);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Error fetching supplier with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Create a new supplier\r\n   */\r\n  createSupplier: async (data: SupplierFormData): Promise<Supplier> => {\r\n    try {\r\n      // Validate required fields before sending to API\r\n      if (!data.name || !data.krapin) {\r\n        throw new Error('Name and KRA PIN are required fields');\r\n      }\r\n\r\n      // Trim whitespace from string fields\r\n      const cleanedData = Object.entries(data).reduce((acc, [key, value]) => {\r\n        acc[key] = typeof value === 'string' ? value.trim() : value;\r\n        return acc;\r\n      }, {} as Record<string, any>);\r\n\r\n      console.log('Creating supplier with data:', cleanedData);\r\n      const response = await apiClient.post<Supplier>('/suppliers', cleanedData);\r\n      console.log('Supplier creation response:', response);\r\n      return response;\r\n    } catch (error: any) {\r\n      console.error('Error creating supplier:', error);\r\n      // Enhance error message with more details if available\r\n      if (error.response?.data?.message) {\r\n        error.message = error.response.data.message;\r\n      } else if (!error.message) {\r\n        error.message = 'Failed to create supplier. Please check your input and try again.';\r\n      }\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Update an existing supplier\r\n   */\r\n  updateSupplier: async (id: number, data: SupplierFormData): Promise<Supplier> => {\r\n    try {\r\n      const response = await apiClient.put<Supplier>(`/suppliers/${id}`, data);\r\n      return response;\r\n    } catch (error) {\r\n      console.error(`Error updating supplier with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Delete a supplier\r\n   */\r\n  deleteSupplier: async (id: number): Promise<void> => {\r\n    try {\r\n      await apiClient.delete(`/suppliers/${id}`);\r\n    } catch (error) {\r\n      console.error(`Error deleting supplier with ID ${id}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,kBAAkB;IAC7B;;GAEC,GACD,cAAc,OAAO;QAKnB,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAC/C,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAM,cAAc;gBAAE;YAAO;YACjE,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA;;GAEC,GACD,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI;YACjE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,iDAAiD;YACjD,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,MAAM,EAAE;gBAC9B,MAAM,IAAI,MAAM;YAClB;YAEA,qCAAqC;YACrC,MAAM,cAAc,OAAO,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,MAAM;gBAChE,GAAG,CAAC,IAAI,GAAG,OAAO,UAAU,WAAW,MAAM,IAAI,KAAK;gBACtD,OAAO;YACT,GAAG,CAAC;YAEJ,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAW,cAAc;YAC9D,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,uDAAuD;YACvD,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACjC,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,OAAO,IAAI,CAAC,MAAM,OAAO,EAAE;gBACzB,MAAM,OAAO,GAAG;YAClB;YACA,MAAM;QACR;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO,IAAY;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAW,CAAC,WAAW,EAAE,IAAI,EAAE;YACnE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 5933, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn(\"px-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 6030, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\r\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = \"default\",\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: \"sm\" | \"default\"\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = \"popper\",\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\r\n          position === \"popper\" &&\r\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n          className\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            \"p-1\",\r\n            position === \"popper\" &&\r\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        \"flex cursor-default items-center justify-center py-1\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 6255, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn(\"w-full caption-bottom text-sm\", className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn(\"[&_tr]:border-b\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<\"caption\">) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 6373, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = \"center\",\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot=\"popover-content\"\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,8OAAC,mKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,8OAAC,mKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE", "debugId": null}}, {"offset": {"line": 6442, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\r\nimport { DayPicker } from \"react-day-picker\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: React.ComponentProps<typeof DayPicker>) {\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\"p-3\", className)}\r\n      classNames={{\r\n        months: \"flex flex-col sm:flex-row gap-2\",\r\n        month: \"flex flex-col gap-4\",\r\n        caption: \"flex justify-center pt-1 relative items-center w-full\",\r\n        caption_label: \"text-sm font-medium\",\r\n        nav: \"flex items-center gap-1\",\r\n        nav_button: cn(\r\n          buttonVariants({ variant: \"outline\" }),\r\n          \"size-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n        ),\r\n        nav_button_previous: \"absolute left-1\",\r\n        nav_button_next: \"absolute right-1\",\r\n        table: \"w-full border-collapse space-x-1\",\r\n        head_row: \"flex\",\r\n        head_cell:\r\n          \"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]\",\r\n        row: \"flex w-full mt-2\",\r\n        cell: cn(\r\n          \"relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md\",\r\n          props.mode === \"range\"\r\n            ? \"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md\"\r\n            : \"[&:has([aria-selected])]:rounded-md\"\r\n        ),\r\n        day: cn(\r\n          buttonVariants({ variant: \"ghost\" }),\r\n          \"size-8 p-0 font-normal aria-selected:opacity-100\"\r\n        ),\r\n        day_range_start:\r\n          \"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_range_end:\r\n          \"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground\",\r\n        day_selected:\r\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\r\n        day_today: \"bg-accent text-accent-foreground\",\r\n        day_outside:\r\n          \"day-outside text-muted-foreground aria-selected:text-muted-foreground\",\r\n        day_disabled: \"text-muted-foreground opacity-50\",\r\n        day_range_middle:\r\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\r\n        day_hidden: \"invisible\",\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        IconLeft: ({ className, ...props }) => (\r\n          <ChevronLeft className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n        IconRight: ({ className, ...props }) => (\r\n          <ChevronRight className={cn(\"size-4\", className)} {...props} />\r\n        ),\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Calendar }\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AASA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACoC;IACvC,qBACE,8OAAC,8JAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YAC<PERSON>,MAAM,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACL,mKACA,MAAM,IAAI,KAAK,UACX,yKACA;YAEN,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,iBACE;YACF,eACE;YACF,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBAChC,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAE5D,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,iBACjC,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;QAE/D;QACC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 6522, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\r\n  return (\r\n    <textarea\r\n      data-slot=\"textarea\"\r\n      className={cn(\r\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 6547, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 6575, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<\"div\">) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn(\"grid gap-2\", className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  )\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn(\"data-[error=true]:text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<\"p\">) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn(\"text-destructive text-sm\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;AAhBA;;;;;;;AAkBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 6727, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/features/credit-notes/components/credit-note-form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useCallback, useMemo } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useQuery, useMutation, useQueryClient } from \"@tanstack/react-query\";\r\nimport { useForm, useFieldArray } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport * as z from \"zod\";\r\nimport { toast } from \"sonner\";\r\nimport { format } from \"date-fns\";\r\nimport { Plus, Trash2, ArrowLeft, Save, CalendarIcon } from \"lucide-react\";\r\n\r\nimport {\r\n  CreateCreditNoteRequest,\r\n  CreditNoteReason,\r\n  CreditNoteReferenceType\r\n} from '@/types/credit-note';\r\nimport { CreditNoteService } from '../api/credit-note-service';\r\nimport invoiceService from '@/features/invoices/api/invoice-service';\r\nimport salesService from '@/features/sales/api/sales-service';\r\nimport customerService from '@/features/customers/api/customer-service';\r\nimport { productService } from '@/features/products/api';\r\nimport { supplierService } from '@/features/inventory/api/supplier-service';\r\nimport { formatCurrency, cn } from '@/lib/utils';\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@/components/ui/select\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger,\r\n} from \"@/components/ui/popover\";\r\nimport { Calendar } from \"@/components/ui/calendar\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\n\r\n// Define the form schema\r\nconst creditNoteFormSchema = z.object({\r\n  reference_type: z.enum([\"invoice\", \"sale\"]),\r\n  reference_id: z.number().optional(),\r\n  reference_number: z.string().optional(),\r\n  credit_note_date: z.date(),\r\n  customer_id: z.number().optional().nullable(),\r\n  supplier_id: z.number().optional().nullable(),\r\n  reason: z.enum([\"return\", \"price_adjustment\", \"quantity_adjustment\", \"discount\", \"cancellation\", \"other\"]),\r\n  reason_details: z.string().optional(),\r\n  notes: z.string().optional(),\r\n  items: z.array(\r\n    z.object({\r\n      description: z.string().min(1, \"Description is required\"),\r\n      quantity: z.number().min(0.01, \"Quantity must be greater than 0\"),\r\n      unit_price: z.number().min(0, \"Unit price must be a valid number\"),\r\n      total_price: z.number().min(0, \"Total price must be a valid number\"),\r\n      product_id: z.number().optional().nullable(),\r\n      vat_rate: z.union([z.number(), z.string().transform(val => parseFloat(val))]).optional().nullable(),\r\n      vat_amount: z.number().optional().nullable(),\r\n      is_vat_exempt: z.boolean().optional().default(false),\r\n      reference_item_id: z.number().optional(),\r\n      reference_item_type: z.string().optional(),\r\n    })\r\n  ).min(1, \"At least one item is required\"),\r\n  subtotal: z.number().min(0),\r\n  vat_amount: z.number().min(0),\r\n  total_amount: z.number().min(0),\r\n});\r\n\r\ntype CreditNoteFormValues = z.infer<typeof creditNoteFormSchema>;\r\n\r\ninterface CreditNoteFormProps {\r\n  creditNoteId?: number;\r\n  isEdit?: boolean;\r\n}\r\n\r\n/**\r\n * Credit Note Form Component\r\n */\r\nexport const CreditNoteForm: React.FC<CreditNoteFormProps> = ({\r\n  creditNoteId,\r\n  isEdit = false,\r\n}) => {\r\n  const router = useRouter();\r\n  const queryClient = useQueryClient();\r\n  const [referenceType, setReferenceType] =\r\n    useState<CreditNoteReferenceType>(\"invoice\");\r\n  const [referenceId, setReferenceId] = useState<number | null>(null);\r\n  const [isLoadingReference, setIsLoadingReference] = useState(false);\r\n\r\n\r\n  // Form setup\r\n  const form = useForm<CreditNoteFormValues>({\r\n    resolver: zodResolver(creditNoteFormSchema),\r\n    defaultValues: {\r\n      reference_type: \"invoice\",\r\n      credit_note_date: new Date(),\r\n      items: [],\r\n      subtotal: 0,\r\n      vat_amount: 0,\r\n      total_amount: 0,\r\n      reason: \"return\",\r\n    },\r\n  });\r\n\r\n\r\n  // Field array for items\r\n  const { fields, append, remove } = useFieldArray({\r\n    control: form.control,\r\n    name: \"items\",\r\n  });\r\n\r\n\r\n  // Watch form values for calculations\r\n  const items = form.watch('items');\r\n  const watchReferenceType = form.watch('reference_type');\r\n\r\n  // Queries for data\r\n  const {\r\n    data: creditNote,\r\n    isLoading: isLoadingCreditNote,\r\n    isError: isErrorCreditNote,\r\n  } = useQuery({\r\n    queryKey: [\"creditNote\", creditNoteId],\r\n    queryFn: () => CreditNoteService.getCreditNoteById(creditNoteId as number),\r\n    enabled: isEdit && !!creditNoteId,\r\n  });\r\n\r\n\r\n  const { data: invoices } = useQuery({\r\n    queryKey: [\"invoices\", { status: \"sent\", kra_integration_status: \"completed\" }],\r\n    queryFn: () => invoiceService.getInvoices({\r\n      status: \"sent\",\r\n      kra_integration_status: \"completed\",\r\n      limit: 100\r\n    }),\r\n    enabled: watchReferenceType === \"invoice\",\r\n  });\r\n\r\n\r\n  const { data: sales } = useQuery({\r\n    queryKey: [\"sales\", { status: \"completed\", kra_integration_status: \"completed\" }],\r\n    queryFn: () => salesService.getSales({\r\n      status: \"completed\",\r\n      kra_integration_status: \"completed\",\r\n      limit: 100\r\n    }),\r\n    enabled: watchReferenceType === \"sale\",\r\n  });\r\n\r\n\r\n  const { data: customers } = useQuery({\r\n    queryKey: [\"customers\"],\r\n    queryFn: () => customerService.getCustomers({ limit: 100 }),\r\n  });\r\n\r\n\r\n  const { data: suppliers } = useQuery({\r\n    queryKey: [\"suppliers\"],\r\n    queryFn: () => supplierService.getSuppliers({ limit: 100 }),\r\n  });\r\n\r\n\r\n  const { data: products } = useQuery({\r\n    queryKey: [\"products\"],\r\n    queryFn: () => productService.getProducts({ limit: 100 }),\r\n  });\r\n\r\n\r\n  // Mutations for create/update\r\n  const createMutation = useMutation({\r\n    mutationFn: (data: CreateCreditNoteRequest) =>\r\n      CreditNoteService.createCreditNote(data),\r\n    onSuccess: () => {\r\n      toast.success(\"Credit note created successfully\");\r\n      queryClient.invalidateQueries({ queryKey: [\"creditNotes\"] });\r\n      router.push(\"/credit-notes\");\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to create credit note\"\r\n      );\r\n    },\r\n  });\r\n\r\n\r\n  const updateMutation = useMutation({\r\n    mutationFn: ({ id, data }: { id: number, data: any }) =>\r\n      CreditNoteService.updateCreditNote(id, data),\r\n    onSuccess: () => {\r\n      toast.success(\"Credit note updated successfully\");\r\n      queryClient.invalidateQueries({ queryKey: [\"creditNotes\"] });\r\n      queryClient.invalidateQueries({ queryKey: [\"creditNote\", creditNoteId] });\r\n      router.push(`/credit-notes/${creditNoteId}`);\r\n    },\r\n    onError: (error: any) => {\r\n      toast.error(\r\n        error.response?.data?.message || \"Failed to update credit note\"\r\n      );\r\n    },\r\n  });\r\n\r\n\r\n  // Calculate totals based on items - memoized to prevent infinite re-renders\r\n  const calculateTotals = useCallback((itemsToCalculate?: any[]) => {\r\n    const currentItems = itemsToCalculate || form.getValues('items');\r\n\r\n    if (!currentItems || currentItems.length === 0) {\r\n      form.setValue(\"subtotal\", 0, { shouldValidate: false });\r\n      form.setValue(\"vat_amount\", 0, { shouldValidate: false });\r\n      form.setValue(\"total_amount\", 0, { shouldValidate: false });\r\n      return;\r\n    }\r\n\r\n    const subtotal = currentItems.reduce((sum, item) => sum + (item.total_price || 0), 0);\r\n    const vatAmount = currentItems.reduce((sum, item) => sum + (item.vat_amount || 0), 0);\r\n    const totalAmount = subtotal + vatAmount;\r\n\r\n    form.setValue('subtotal', subtotal, { shouldValidate: false });\r\n    form.setValue('vat_amount', vatAmount, { shouldValidate: false });\r\n    form.setValue('total_amount', totalAmount, { shouldValidate: false });\r\n  }, [form]);\r\n\r\n  // Load reference data (invoice or sale) - optimized to prevent performance issues\r\n  const loadReferenceData = useCallback(async (\r\n    type: CreditNoteReferenceType,\r\n    id: number\r\n  ) => {\r\n    setIsLoadingReference(true);\r\n    try {\r\n      if (type === \"invoice\") {\r\n        const invoice = await invoiceService.getInvoiceById(id);\r\n\r\n        // Batch form updates to prevent multiple re-renders\r\n        form.setValue('reference_id', invoice.id);\r\n        form.setValue('reference_number', invoice.invoice_number);\r\n        form.setValue('customer_id', invoice.customer_id);\r\n        form.setValue('supplier_id', invoice.supplier_id);\r\n\r\n        // Clear existing items efficiently\r\n        form.setValue('items', []);\r\n\r\n        // Prepare new items array\r\n        const newItems = invoice.items?.map((item) => ({\r\n          description: item.description,\r\n          quantity: item.quantity,\r\n          unit_price: item.unit_price,\r\n          total_price: item.quantity * item.unit_price,\r\n          product_id: item.product_id,\r\n          vat_rate: item.vat_rate,\r\n          vat_amount: item.vat_amount,\r\n          is_vat_exempt: item.is_vat_exempt,\r\n          reference_item_id: item.id,\r\n          reference_item_type: \"invoice_item\",\r\n        })) || [];\r\n\r\n        // Set all items at once\r\n        form.setValue('items', newItems);\r\n\r\n        // Calculate totals with the new items\r\n        calculateTotals(newItems);\r\n      } else if (type === \"sale\") {\r\n        const sale = await salesService.getSaleById(id);\r\n\r\n        // Batch form updates to prevent multiple re-renders\r\n        form.setValue('reference_id', sale.id);\r\n        form.setValue('reference_number', sale.receipt_number);\r\n        form.setValue('customer_id', sale.customer_id);\r\n\r\n        // Clear existing items efficiently\r\n        form.setValue('items', []);\r\n\r\n        // Prepare new items array\r\n        const newItems = sale.items?.map((item) => ({\r\n          description: item.product?.name || \"Unknown Product\",\r\n          quantity: item.quantity,\r\n          unit_price: item.unit_price,\r\n          total_price: item.total_price,\r\n          product_id: item.product_id,\r\n          vat_rate: item.vat_rate,\r\n          vat_amount: item.vat_amount,\r\n          is_vat_exempt: false, // Assuming sales don't have VAT exempt flag\r\n          reference_item_id: item.id,\r\n          reference_item_type: \"sale_item\",\r\n        })) || [];\r\n\r\n        // Set all items at once\r\n        form.setValue('items', newItems);\r\n\r\n        // Calculate totals with the new items\r\n        calculateTotals(newItems);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error loading reference data:\", error);\r\n      toast.error(\"Failed to load reference data\");\r\n    } finally {\r\n      setIsLoadingReference(false);\r\n    }\r\n  }, [form, calculateTotals]);\r\n\r\n\r\n  // Add a new empty item\r\n  const handleAddItem = () => {\r\n    append({\r\n      description: \"\",\r\n      quantity: 1,\r\n      unit_price: 0,\r\n      total_price: 0,\r\n      vat_rate: 0,\r\n      vat_amount: 0,\r\n      is_vat_exempt: false,\r\n    });\r\n  };\r\n\r\n\r\n  // Handle form submission\r\n  const onSubmit = (data: CreditNoteFormValues) => {\r\n    console.log('🎯 Credit note form submitted with data:', data);\r\n    console.log('🎯 Form validation state:', form.formState);\r\n    console.log('🎯 Form errors:', form.formState.errors);\r\n\r\n    // Format the date and ensure all numbers are properly converted\r\n    const formattedData = {\r\n      ...data,\r\n      credit_note_date: format(data.credit_note_date, 'yyyy-MM-dd'),\r\n      items: data.items.map(item => ({\r\n        ...item,\r\n        // Fix VAT rate handling - don't convert 0 to null\r\n        vat_rate: item.vat_rate !== undefined && item.vat_rate !== null\r\n          ? (typeof item.vat_rate === 'string' ? parseFloat(item.vat_rate) : item.vat_rate)\r\n          : null,\r\n        quantity: Number(item.quantity),\r\n        unit_price: Number(item.unit_price),\r\n        total_price: Number(item.total_price),\r\n        vat_amount: item.vat_amount !== undefined && item.vat_amount !== null ? Number(item.vat_amount) : 0,\r\n        is_vat_exempt: Boolean(item.is_vat_exempt)\r\n      }))\r\n    };\r\n\r\n    console.log('🎯 Formatted data for API:', formattedData);\r\n\r\n    if (isEdit && creditNoteId) {\r\n      // For edit, we only update certain fields\r\n      const updateData = {\r\n        credit_note_date: formattedData.credit_note_date,\r\n        reason: formattedData.reason,\r\n        reason_details: formattedData.reason_details,\r\n        notes: formattedData.notes,\r\n      };\r\n\r\n\r\n      updateMutation.mutate({ id: creditNoteId, data: updateData });\r\n    } else {\r\n      // For create, we send the full data\r\n      createMutation.mutate(formattedData as CreateCreditNoteRequest);\r\n    }\r\n  };\r\n\r\n\r\n  // Initialize form with credit note data for edit mode\r\n  useEffect(() => {\r\n    if (isEdit && creditNote) {\r\n      form.reset({\r\n        reference_type: creditNote.reference_type as CreditNoteReferenceType,\r\n        reference_id: creditNote.reference_id,\r\n        reference_number: creditNote.reference_number,\r\n        credit_note_date: new Date(creditNote.credit_note_date),\r\n        customer_id: creditNote.customer_id,\r\n        supplier_id: creditNote.supplier_id,\r\n        reason: creditNote.reason as CreditNoteReason,\r\n        reason_details: creditNote.reason_details,\r\n        subtotal: creditNote.subtotal,\r\n        vat_amount: creditNote.vat_amount,\r\n        total_amount: creditNote.total_amount,\r\n        notes: creditNote.notes,\r\n        items:\r\n          creditNote.items?.map((item) => ({\r\n            description: item.description,\r\n            quantity: item.quantity,\r\n            unit_price: item.unit_price,\r\n            total_price: item.total_price,\r\n            product_id: item.product_id,\r\n            vat_rate: item.vat_rate,\r\n            vat_amount: item.vat_amount,\r\n            is_vat_exempt: item.is_vat_exempt,\r\n            reference_item_id: item.reference_item_id,\r\n            reference_item_type: item.reference_item_type,\r\n          })) || [],\r\n      });\r\n\r\n\r\n      setReferenceType(creditNote.reference_type as CreditNoteReferenceType);\r\n      setReferenceId(creditNote.reference_id);\r\n    }\r\n  }, [isEdit, creditNote, form]);\r\n\r\n\r\n  // Recalculate totals when items change - optimized to prevent infinite loops\r\n  useEffect(() => {\r\n    calculateTotals();\r\n  }, [items, calculateTotals]);\r\n\r\n  // Show loading state\r\n  if (isEdit && isLoadingCreditNote) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-[400px]\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n\r\n  // Show error state\r\n  if (isEdit && isErrorCreditNote) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center h-[400px] space-y-4\">\r\n        <h3 className=\"text-xl font-semibold\">Error Loading Credit Note</h3>\r\n        <p className=\"text-gray-600 mb-4\">Unable to load credit note data.</p>\r\n        <Button\r\n          onClick={() =>\r\n            queryClient.invalidateQueries({\r\n              queryKey: [\"creditNote\", creditNoteId],\r\n            })\r\n          }\r\n        >\r\n          Try Again\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <Button\r\n          variant=\"outline\"\r\n          onClick={() =>\r\n            router.push(\r\n              isEdit ? `/credit-notes/${creditNoteId}` : \"/credit-notes\"\r\n            )\r\n          }\r\n        >\r\n          <ArrowLeft className=\"mr-2 h-4 w-4\" />\r\n          {isEdit ? \"Back to Credit Note\" : \"Back to Credit Notes\"}\r\n        </Button>\r\n\r\n        {/* Debug Test Button */}\r\n        <Button\r\n          variant=\"secondary\"\r\n          onClick={() => {\r\n            console.log('🧪 TEST BUTTON CLICKED - React is working!');\r\n            console.log('🧪 Form values:', form.getValues());\r\n            console.log('🧪 Form errors:', form.formState.errors);\r\n            alert('Test button clicked! Check console for details.');\r\n          }}\r\n        >\r\n          🧪 Test Button\r\n        </Button>\r\n      </div>\r\n\r\n\r\n      <Form {...form}>\r\n        <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-6\">\r\n          <Card>\r\n            <CardHeader>\r\n              <CardTitle>\r\n                {isEdit ? \"Edit Credit Note\" : \"Create Credit Note\"}\r\n              </CardTitle>\r\n            </CardHeader>\r\n            <CardContent className=\"space-y-6\">\r\n              {/* Reference Type and Reference Selection */}\r\n              {!isEdit && (\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"reference_type\"\r\n                    render={({ field }) => (\r\n                      <FormItem>\r\n                        <FormLabel>Reference Type</FormLabel>\r\n                        <Select\r\n                          onValueChange={(value) => {\r\n                            field.onChange(value);\r\n                            setReferenceType(value as CreditNoteReferenceType);\r\n                            setReferenceId(null);\r\n                            form.setValue('reference_id', undefined);\r\n                            form.setValue('reference_number', '');\r\n                            form.setValue('customer_id', undefined);\r\n                            form.setValue('supplier_id', undefined);\r\n\r\n                            // Clear items\r\n                            while (fields.length > 0) {\r\n                              remove(0);\r\n                            }\r\n                          }}\r\n                          defaultValue={field.value}\r\n                        >\r\n                          <FormControl>\r\n                            <SelectTrigger>\r\n                              <SelectValue placeholder=\"Select reference type\" />\r\n                            </SelectTrigger>\r\n                          </FormControl>\r\n                          <SelectContent>\r\n                            <SelectItem value=\"invoice\">Invoice</SelectItem>\r\n                            <SelectItem value=\"sale\">Sale</SelectItem>\r\n                          </SelectContent>\r\n                        </Select>\r\n                        <FormMessage />\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  {referenceType === 'invoice' && (\r\n                    <FormField\r\n                      control={form.control}\r\n                      name=\"reference_id\"\r\n                      render={({ field }) => (\r\n                        <FormItem>\r\n                          <FormLabel>Select Invoice</FormLabel>\r\n                          <Select\r\n                            onValueChange={(value) => {\r\n                              const numValue = parseInt(value, 10);\r\n                              field.onChange(numValue);\r\n                              setReferenceId(numValue);\r\n                              if (numValue) {\r\n                                loadReferenceData(\"invoice\", numValue);\r\n                              }\r\n                            }}\r\n                            defaultValue={field.value?.toString()}\r\n                            disabled={isLoadingReference}\r\n                          >\r\n                            <FormControl>\r\n                              <SelectTrigger>\r\n                                <SelectValue placeholder=\"Select an invoice\" />\r\n                              </SelectTrigger>\r\n                            </FormControl>\r\n                            <SelectContent>\r\n                              {invoices?.data?.map((invoice) => (\r\n                                <SelectItem\r\n                                  key={invoice.id}\r\n                                  value={invoice.id.toString()}\r\n                                >\r\n                                  {invoice.invoice_number} -{\" \"}\r\n                                  {invoice.customer?.name ||\r\n                                    invoice.supplier?.name ||\r\n                                    \"Unknown\"}{\" \"}\r\n                                  ({formatCurrency(invoice.total_amount)})\r\n                                </SelectItem>\r\n                              ))}\r\n                            </SelectContent>\r\n                          </Select>\r\n                          <FormMessage />\r\n                        </FormItem>\r\n                      )}\r\n                    />\r\n                  )}\r\n\r\n                  {referenceType === 'sale' && (\r\n                    <FormField\r\n                      control={form.control}\r\n                      name=\"reference_id\"\r\n                      render={({ field }) => (\r\n                        <FormItem>\r\n                          <FormLabel>Select Sale</FormLabel>\r\n                          <Select\r\n                            onValueChange={(value) => {\r\n                              const numValue = parseInt(value, 10);\r\n                              field.onChange(numValue);\r\n                              setReferenceId(numValue);\r\n                              if (numValue) {\r\n                                loadReferenceData(\"sale\", numValue);\r\n                              }\r\n                            }}\r\n                            defaultValue={field.value?.toString()}\r\n                            disabled={isLoadingReference}\r\n                          >\r\n                            <FormControl>\r\n                              <SelectTrigger>\r\n                                <SelectValue placeholder=\"Select a sale\" />\r\n                              </SelectTrigger>\r\n                            </FormControl>\r\n                            <SelectContent>\r\n                              {sales?.data?.map((sale) => (\r\n                                <SelectItem\r\n                                  key={sale.id}\r\n                                  value={sale.id.toString()}\r\n                                >\r\n                                  {sale.receipt_number} -{\" \"}\r\n                                  {sale.customer?.name || \"Walk-in Customer\"} (\r\n                                  {formatCurrency(sale.total_amount)})\r\n                                </SelectItem>\r\n                              ))}\r\n                            </SelectContent>\r\n                          </Select>\r\n                          <FormMessage />\r\n                        </FormItem>\r\n                      )}\r\n                    />\r\n                  )}\r\n                </div>\r\n              )}\r\n\r\n\r\n              {/* Credit Note Details */}\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"credit_note_date\"\r\n                  render={({ field }) => (\r\n                    <FormItem className=\"flex flex-col\">\r\n                      <FormLabel>Credit Note Date</FormLabel>\r\n                      <Popover>\r\n                        <PopoverTrigger asChild>\r\n                          <FormControl>\r\n                            <Button\r\n                              variant={\"outline\"}\r\n                              className={cn(\r\n                                \"w-full pl-3 text-left font-normal\",\r\n                                !field.value && \"text-muted-foreground\"\r\n                              )}\r\n                            >\r\n                              {field.value ? (\r\n                                format(field.value, \"PPP\")\r\n                              ) : (\r\n                                <span>Pick a date</span>\r\n                              )}\r\n                              <CalendarIcon className=\"ml-auto h-4 w-4 opacity-50\" />\r\n                            </Button>\r\n                          </FormControl>\r\n                        </PopoverTrigger>\r\n                        <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                          <Calendar\r\n                            mode=\"single\"\r\n                            selected={field.value}\r\n                            onSelect={field.onChange}\r\n                            disabled={(date) =>\r\n                              date > new Date() || date < new Date(\"1900-01-01\")\r\n                            }\r\n                            initialFocus\r\n                          />\r\n                        </PopoverContent>\r\n                      </Popover>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"reason\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <FormLabel>Reason</FormLabel>\r\n                      <Select\r\n                        onValueChange={field.onChange}\r\n                        defaultValue={field.value}\r\n                      >\r\n                        <FormControl>\r\n                          <SelectTrigger>\r\n                            <SelectValue placeholder=\"Select reason\" />\r\n                          </SelectTrigger>\r\n                        </FormControl>\r\n                        <SelectContent>\r\n                          <SelectItem value=\"return\">Return</SelectItem>\r\n                          <SelectItem value=\"price_adjustment\">Price Adjustment</SelectItem>\r\n                          <SelectItem value=\"quantity_adjustment\">Quantity Adjustment</SelectItem>\r\n                          <SelectItem value=\"discount\">Discount</SelectItem>\r\n                          <SelectItem value=\"cancellation\">Cancellation</SelectItem>\r\n                          <SelectItem value=\"other\">Other</SelectItem>\r\n                        </SelectContent>\r\n                      </Select>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n              </div>\r\n\r\n\r\n              <FormField\r\n                control={form.control}\r\n                name=\"reason_details\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel>Reason Details</FormLabel>\r\n                    <FormControl>\r\n                      <Textarea\r\n                        placeholder=\"Provide additional details about the reason for this credit note\"\r\n                        className=\"resize-none\"\r\n                        {...field}\r\n                      />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n\r\n\r\n              {/* Items Table */}\r\n              <div>\r\n                <div className=\"flex justify-between items-center mb-4\">\r\n                  <h3 className=\"text-lg font-medium\">Items</h3>\r\n                  {!isEdit && (\r\n                    <Button\r\n                      type=\"button\"\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={handleAddItem}\r\n                    >\r\n                      <Plus className=\"mr-2 h-4 w-4\" /> Add Item\r\n                    </Button>\r\n                  )}\r\n                </div>\r\n\r\n\r\n                <div className=\"border rounded-md\">\r\n                  <Table>\r\n                    <TableHeader>\r\n                      <TableRow>\r\n                        <TableHead>Description</TableHead>\r\n                        <TableHead className=\"w-[100px]\">Quantity</TableHead>\r\n                        <TableHead className=\"w-[120px]\">Unit Price</TableHead>\r\n                        <TableHead className=\"w-[100px]\">VAT Rate (%)</TableHead>\r\n                        <TableHead className=\"w-[120px]\">VAT Amount</TableHead>\r\n                        <TableHead className=\"w-[120px]\">Total</TableHead>\r\n                        {!isEdit && (\r\n                          <TableHead className=\"w-[80px]\"></TableHead>\r\n                        )}\r\n                      </TableRow>\r\n                    </TableHeader>\r\n                    <TableBody>\r\n                      {fields.length === 0 ? (\r\n                        <TableRow>\r\n                          <TableCell colSpan={isEdit ? 6 : 7} className=\"text-center py-4 text-muted-foreground\">\r\n                            {isEdit ? \"No items found\" : \"Add items to this credit note\"}\r\n                          </TableCell>\r\n                        </TableRow>\r\n                      ) : (\r\n                        fields.map((field, index) => (\r\n                          <TableRow key={field.id}>\r\n                            <TableCell>\r\n                              <Input\r\n                                {...form.register(`items.${index}.description` as const)}\r\n                                disabled={isEdit}\r\n                              />\r\n                            </TableCell>\r\n                            <TableCell>\r\n                              <Input\r\n                                type=\"number\"\r\n                                {...form.register(`items.${index}.quantity` as const, {\r\n                                  valueAsNumber: true,\r\n                                  onChange: () => calculateTotals()\r\n                                })}\r\n                                disabled={isEdit}\r\n                              />\r\n                            </TableCell>\r\n                            <TableCell>\r\n                              <Input\r\n                                type=\"number\"\r\n                                step=\"0.01\"\r\n                                {...form.register(`items.${index}.unit_price` as const, {\r\n                                  valueAsNumber: true,\r\n                                  onChange: () => calculateTotals()\r\n                                })}\r\n                                disabled={isEdit}\r\n                              />\r\n                            </TableCell>\r\n                            <TableCell>\r\n                              <Input\r\n                                type=\"number\"\r\n                                step=\"0.01\"\r\n                                placeholder=\"0\"\r\n                                {...form.register(`items.${index}.vat_rate` as const, {\r\n                                  valueAsNumber: true,\r\n                                  onChange: () => calculateTotals()\r\n                                })}\r\n                                disabled={isEdit}\r\n                              />\r\n                            </TableCell>\r\n                            <TableCell>\r\n                              <Input\r\n                                type=\"number\"\r\n                                step=\"0.01\"\r\n                                {...form.register(\r\n                                  `items.${index}.vat_amount` as const,\r\n                                  {\r\n                                    valueAsNumber: true,\r\n                                    onChange: () => calculateTotals(),\r\n                                  }\r\n                                )}\r\n                                disabled={isEdit}\r\n                              />\r\n                            </TableCell>\r\n                            <TableCell>\r\n                              <Input\r\n                                type=\"number\"\r\n                                step=\"0.01\"\r\n                                {...form.register(\r\n                                  `items.${index}.total_price` as const,\r\n                                  {\r\n                                    valueAsNumber: true,\r\n                                  }\r\n                                )}\r\n                                disabled={true}\r\n                              />\r\n                            </TableCell>\r\n                            {!isEdit && (\r\n                              <TableCell>\r\n                                <Button\r\n                                  type=\"button\"\r\n                                  variant=\"ghost\"\r\n                                  size=\"icon\"\r\n                                  onClick={() => {\r\n                                    remove(index);\r\n                                    calculateTotals();\r\n                                  }}\r\n                                >\r\n                                  <Trash2 className=\"h-4 w-4\" />\r\n                                </Button>\r\n                              </TableCell>\r\n                            )}\r\n                          </TableRow>\r\n                        ))\r\n                      )}\r\n                    </TableBody>\r\n                  </Table>\r\n                </div>\r\n              </div>\r\n\r\n\r\n              {/* Totals */}\r\n              <div className=\"flex flex-col gap-2 items-end\">\r\n                <div className=\"flex justify-between w-full max-w-xs\">\r\n                  <span className=\"text-muted-foreground\">Subtotal:</span>\r\n                  <span>{formatCurrency(form.watch(\"subtotal\"))}</span>\r\n                </div>\r\n                <div className=\"flex justify-between w-full max-w-xs\">\r\n                  <span className=\"text-muted-foreground\">VAT:</span>\r\n                  <span>{formatCurrency(form.watch(\"vat_amount\"))}</span>\r\n                </div>\r\n                <div className=\"flex justify-between w-full max-w-xs font-medium text-lg\">\r\n                  <span>Total:</span>\r\n                  <span>{formatCurrency(form.watch(\"total_amount\"))}</span>\r\n                </div>\r\n              </div>\r\n\r\n\r\n              {/* Notes */}\r\n              <FormField\r\n                control={form.control}\r\n                name=\"notes\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel>Notes</FormLabel>\r\n                    <FormControl>\r\n                      <Textarea\r\n                        placeholder=\"Add any additional notes or information\"\r\n                        className=\"resize-none\"\r\n                        {...field}\r\n                      />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n            </CardContent>\r\n          </Card>\r\n\r\n          {/* Submit Button */}\r\n          <div className=\"flex justify-end\">\r\n            <Button\r\n              type=\"submit\"\r\n              disabled={createMutation.isPending || updateMutation.isPending || isLoadingReference}\r\n              onClick={(e) => {\r\n                console.log('🔥 Button clicked!', e);\r\n                console.log('🔥 Form state:', form.formState);\r\n                console.log('🔥 Form values:', form.getValues());\r\n                console.log('🔥 Form errors:', form.formState.errors);\r\n              }}\r\n            >\r\n              <Save className=\"mr-2 h-4 w-4\" />\r\n              {isEdit ? 'Update Credit Note' : 'Create Credit Note'}\r\n            </Button>\r\n          </div>\r\n        </form>\r\n      </Form>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AAOA;AAQA;AAKA;AACA;AACA;AAlDA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA,yBAAyB;AACzB,MAAM,uBAAuB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IACpC,gBAAgB,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAW;KAAO;IAC1C,cAAc,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IACjC,kBAAkB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IACrC,kBAAkB,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD;IACvB,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC3C,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;IAC3C,QAAQ,CAAA,GAAA,oIAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAU;QAAoB;QAAuB;QAAY;QAAgB;KAAQ;IACzG,gBAAgB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IACnC,OAAO,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAC1B,OAAO,CAAA,GAAA,oIAAA,CAAA,QAAO,AAAD,EACX,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;QACP,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAC/B,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,MAAM;QAC/B,YAAY,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAC9B,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;QAC/B,YAAY,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;QAC1C,UAAU,CAAA,GAAA,oIAAA,CAAA,QAAO,AAAD,EAAE;YAAC,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD;YAAK,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,SAAS,CAAC,CAAA,MAAO,WAAW;SAAM,EAAE,QAAQ,GAAG,QAAQ;QACjG,YAAY,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;QAC1C,eAAe,CAAA,GAAA,oIAAA,CAAA,UAAS,AAAD,IAAI,QAAQ,GAAG,OAAO,CAAC;QAC9C,mBAAmB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;QACtC,qBAAqB,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ;IAC1C,IACA,GAAG,CAAC,GAAG;IACT,UAAU,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC;IACzB,YAAY,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC;IAC3B,cAAc,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC;AAC/B;AAYO,MAAM,iBAAgD,CAAC,EAC5D,YAAY,EACZ,SAAS,KAAK,EACf;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,CAAC,eAAe,iBAAiB,GACrC,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IACpC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAG7D,aAAa;IACb,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAwB;QACzC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,gBAAgB;YAChB,kBAAkB,IAAI;YACtB,OAAO,EAAE;YACT,UAAU;YACV,YAAY;YACZ,cAAc;YACd,QAAQ;QACV;IACF;IAGA,wBAAwB;IACxB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE;QAC/C,SAAS,KAAK,OAAO;QACrB,MAAM;IACR;IAGA,qCAAqC;IACrC,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,MAAM,qBAAqB,KAAK,KAAK,CAAC;IAEtC,mBAAmB;IACnB,MAAM,EACJ,MAAM,UAAU,EAChB,WAAW,mBAAmB,EAC9B,SAAS,iBAAiB,EAC3B,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACX,UAAU;YAAC;YAAc;SAAa;QACtC,SAAS,IAAM,sKAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC;QACnD,SAAS,UAAU,CAAC,CAAC;IACvB;IAGA,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAClC,UAAU;YAAC;YAAY;gBAAE,QAAQ;gBAAQ,wBAAwB;YAAY;SAAE;QAC/E,SAAS,IAAM,wJAAA,CAAA,UAAc,CAAC,WAAW,CAAC;gBACxC,QAAQ;gBACR,wBAAwB;gBACxB,OAAO;YACT;QACA,SAAS,uBAAuB;IAClC;IAGA,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAC/B,UAAU;YAAC;YAAS;gBAAE,QAAQ;gBAAa,wBAAwB;YAAY;SAAE;QACjF,SAAS,IAAM,mJAAA,CAAA,UAAY,CAAC,QAAQ,CAAC;gBACnC,QAAQ;gBACR,wBAAwB;gBACxB,OAAO;YACT;QACA,SAAS,uBAAuB;IAClC;IAGA,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,UAAU;YAAC;SAAY;QACvB,SAAS,IAAM,0JAAA,CAAA,UAAe,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAI;IAC3D;IAGA,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,UAAU;YAAC;SAAY;QACvB,SAAS,IAAM,0JAAA,CAAA,kBAAe,CAAC,YAAY,CAAC;gBAAE,OAAO;YAAI;IAC3D;IAGA,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD,EAAE;QAClC,UAAU;YAAC;SAAW;QACtB,SAAS,IAAM,wJAAA,CAAA,iBAAc,CAAC,WAAW,CAAC;gBAAE,OAAO;YAAI;IACzD;IAGA,8BAA8B;IAC9B,MAAM,iBAAiB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjC,YAAY,CAAC,OACX,sKAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC;QACrC,WAAW;YACT,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;YAC1D,OAAO,IAAI,CAAC;QACd;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;QAErC;IACF;IAGA,MAAM,iBAAiB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACjC,YAAY,CAAC,EAAE,EAAE,EAAE,IAAI,EAA6B,GAClD,sKAAA,CAAA,oBAAiB,CAAC,gBAAgB,CAAC,IAAI;QACzC,WAAW;YACT,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAc;YAAC;YAC1D,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;oBAAc;iBAAa;YAAC;YACvE,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,cAAc;QAC7C;QACA,SAAS,CAAC;YACR,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,WAAW;QAErC;IACF;IAGA,4EAA4E;IAC5E,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACnC,MAAM,eAAe,oBAAoB,KAAK,SAAS,CAAC;QAExD,IAAI,CAAC,gBAAgB,aAAa,MAAM,KAAK,GAAG;YAC9C,KAAK,QAAQ,CAAC,YAAY,GAAG;gBAAE,gBAAgB;YAAM;YACrD,KAAK,QAAQ,CAAC,cAAc,GAAG;gBAAE,gBAAgB;YAAM;YACvD,KAAK,QAAQ,CAAC,gBAAgB,GAAG;gBAAE,gBAAgB;YAAM;YACzD;QACF;QAEA,MAAM,WAAW,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,WAAW,IAAI,CAAC,GAAG;QACnF,MAAM,YAAY,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,UAAU,IAAI,CAAC,GAAG;QACnF,MAAM,cAAc,WAAW;QAE/B,KAAK,QAAQ,CAAC,YAAY,UAAU;YAAE,gBAAgB;QAAM;QAC5D,KAAK,QAAQ,CAAC,cAAc,WAAW;YAAE,gBAAgB;QAAM;QAC/D,KAAK,QAAQ,CAAC,gBAAgB,aAAa;YAAE,gBAAgB;QAAM;IACrE,GAAG;QAAC;KAAK;IAET,kFAAkF;IAClF,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACpC,MACA;QAEA,sBAAsB;QACtB,IAAI;YACF,IAAI,SAAS,WAAW;gBACtB,MAAM,UAAU,MAAM,wJAAA,CAAA,UAAc,CAAC,cAAc,CAAC;gBAEpD,oDAAoD;gBACpD,KAAK,QAAQ,CAAC,gBAAgB,QAAQ,EAAE;gBACxC,KAAK,QAAQ,CAAC,oBAAoB,QAAQ,cAAc;gBACxD,KAAK,QAAQ,CAAC,eAAe,QAAQ,WAAW;gBAChD,KAAK,QAAQ,CAAC,eAAe,QAAQ,WAAW;gBAEhD,mCAAmC;gBACnC,KAAK,QAAQ,CAAC,SAAS,EAAE;gBAEzB,0BAA0B;gBAC1B,MAAM,WAAW,QAAQ,KAAK,EAAE,IAAI,CAAC,OAAS,CAAC;wBAC7C,aAAa,KAAK,WAAW;wBAC7B,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,UAAU;wBAC3B,aAAa,KAAK,QAAQ,GAAG,KAAK,UAAU;wBAC5C,YAAY,KAAK,UAAU;wBAC3B,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,UAAU;wBAC3B,eAAe,KAAK,aAAa;wBACjC,mBAAmB,KAAK,EAAE;wBAC1B,qBAAqB;oBACvB,CAAC,MAAM,EAAE;gBAET,wBAAwB;gBACxB,KAAK,QAAQ,CAAC,SAAS;gBAEvB,sCAAsC;gBACtC,gBAAgB;YAClB,OAAO,IAAI,SAAS,QAAQ;gBAC1B,MAAM,OAAO,MAAM,mJAAA,CAAA,UAAY,CAAC,WAAW,CAAC;gBAE5C,oDAAoD;gBACpD,KAAK,QAAQ,CAAC,gBAAgB,KAAK,EAAE;gBACrC,KAAK,QAAQ,CAAC,oBAAoB,KAAK,cAAc;gBACrD,KAAK,QAAQ,CAAC,eAAe,KAAK,WAAW;gBAE7C,mCAAmC;gBACnC,KAAK,QAAQ,CAAC,SAAS,EAAE;gBAEzB,0BAA0B;gBAC1B,MAAM,WAAW,KAAK,KAAK,EAAE,IAAI,CAAC,OAAS,CAAC;wBAC1C,aAAa,KAAK,OAAO,EAAE,QAAQ;wBACnC,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,UAAU;wBAC3B,aAAa,KAAK,WAAW;wBAC7B,YAAY,KAAK,UAAU;wBAC3B,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,UAAU;wBAC3B,eAAe;wBACf,mBAAmB,KAAK,EAAE;wBAC1B,qBAAqB;oBACvB,CAAC,MAAM,EAAE;gBAET,wBAAwB;gBACxB,KAAK,QAAQ,CAAC,SAAS;gBAEvB,sCAAsC;gBACtC,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,sBAAsB;QACxB;IACF,GAAG;QAAC;QAAM;KAAgB;IAG1B,uBAAuB;IACvB,MAAM,gBAAgB;QACpB,OAAO;YACL,aAAa;YACb,UAAU;YACV,YAAY;YACZ,aAAa;YACb,UAAU;YACV,YAAY;YACZ,eAAe;QACjB;IACF;IAGA,yBAAyB;IACzB,MAAM,WAAW,CAAC;QAChB,QAAQ,GAAG,CAAC,4CAA4C;QACxD,QAAQ,GAAG,CAAC,6BAA6B,KAAK,SAAS;QACvD,QAAQ,GAAG,CAAC,mBAAmB,KAAK,SAAS,CAAC,MAAM;QAEpD,gEAAgE;QAChE,MAAM,gBAAgB;YACpB,GAAG,IAAI;YACP,kBAAkB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,gBAAgB,EAAE;YAChD,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBAC7B,GAAG,IAAI;oBACP,kDAAkD;oBAClD,UAAU,KAAK,QAAQ,KAAK,aAAa,KAAK,QAAQ,KAAK,OACtD,OAAO,KAAK,QAAQ,KAAK,WAAW,WAAW,KAAK,QAAQ,IAAI,KAAK,QAAQ,GAC9E;oBACJ,UAAU,OAAO,KAAK,QAAQ;oBAC9B,YAAY,OAAO,KAAK,UAAU;oBAClC,aAAa,OAAO,KAAK,WAAW;oBACpC,YAAY,KAAK,UAAU,KAAK,aAAa,KAAK,UAAU,KAAK,OAAO,OAAO,KAAK,UAAU,IAAI;oBAClG,eAAe,QAAQ,KAAK,aAAa;gBAC3C,CAAC;QACH;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,IAAI,UAAU,cAAc;YAC1B,0CAA0C;YAC1C,MAAM,aAAa;gBACjB,kBAAkB,cAAc,gBAAgB;gBAChD,QAAQ,cAAc,MAAM;gBAC5B,gBAAgB,cAAc,cAAc;gBAC5C,OAAO,cAAc,KAAK;YAC5B;YAGA,eAAe,MAAM,CAAC;gBAAE,IAAI;gBAAc,MAAM;YAAW;QAC7D,OAAO;YACL,oCAAoC;YACpC,eAAe,MAAM,CAAC;QACxB;IACF;IAGA,sDAAsD;IACtD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,YAAY;YACxB,KAAK,KAAK,CAAC;gBACT,gBAAgB,WAAW,cAAc;gBACzC,cAAc,WAAW,YAAY;gBACrC,kBAAkB,WAAW,gBAAgB;gBAC7C,kBAAkB,IAAI,KAAK,WAAW,gBAAgB;gBACtD,aAAa,WAAW,WAAW;gBACnC,aAAa,WAAW,WAAW;gBACnC,QAAQ,WAAW,MAAM;gBACzB,gBAAgB,WAAW,cAAc;gBACzC,UAAU,WAAW,QAAQ;gBAC7B,YAAY,WAAW,UAAU;gBACjC,cAAc,WAAW,YAAY;gBACrC,OAAO,WAAW,KAAK;gBACvB,OACE,WAAW,KAAK,EAAE,IAAI,CAAC,OAAS,CAAC;wBAC/B,aAAa,KAAK,WAAW;wBAC7B,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,UAAU;wBAC3B,aAAa,KAAK,WAAW;wBAC7B,YAAY,KAAK,UAAU;wBAC3B,UAAU,KAAK,QAAQ;wBACvB,YAAY,KAAK,UAAU;wBAC3B,eAAe,KAAK,aAAa;wBACjC,mBAAmB,KAAK,iBAAiB;wBACzC,qBAAqB,KAAK,mBAAmB;oBAC/C,CAAC,MAAM,EAAE;YACb;YAGA,iBAAiB,WAAW,cAAc;YAC1C,eAAe,WAAW,YAAY;QACxC;IACF,GAAG;QAAC;QAAQ;QAAY;KAAK;IAG7B,6EAA6E;IAC7E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAO;KAAgB;IAE3B,qBAAqB;IACrB,IAAI,UAAU,qBAAqB;QACjC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAGA,mBAAmB;IACnB,IAAI,UAAU,mBAAmB;QAC/B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAwB;;;;;;8BACtC,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAS,IACP,YAAY,iBAAiB,CAAC;4BAC5B,UAAU;gCAAC;gCAAc;6BAAa;wBACxC;8BAEH;;;;;;;;;;;;IAKP;IAGA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS,IACP,OAAO,IAAI,CACT,SAAS,CAAC,cAAc,EAAE,cAAc,GAAG;;0CAI/C,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BACpB,SAAS,wBAAwB;;;;;;;kCAIpC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,SAAS;4BACP,QAAQ,GAAG,CAAC;4BACZ,QAAQ,GAAG,CAAC,mBAAmB,KAAK,SAAS;4BAC7C,QAAQ,GAAG,CAAC,mBAAmB,KAAK,SAAS,CAAC,MAAM;4BACpD,MAAM;wBACR;kCACD;;;;;;;;;;;;0BAMH,8OAAC,gIAAA,CAAA,OAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBAAK,UAAU,KAAK,YAAY,CAAC;oBAAW,WAAU;;sCACrD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDACP,SAAS,qBAAqB;;;;;;;;;;;8CAGnC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;wCAEpB,CAAC,wBACA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8EACP,8OAAC,gIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,kIAAA,CAAA,SAAM;oEACL,eAAe,CAAC;wEACd,MAAM,QAAQ,CAAC;wEACf,iBAAiB;wEACjB,eAAe;wEACf,KAAK,QAAQ,CAAC,gBAAgB;wEAC9B,KAAK,QAAQ,CAAC,oBAAoB;wEAClC,KAAK,QAAQ,CAAC,eAAe;wEAC7B,KAAK,QAAQ,CAAC,eAAe;wEAE7B,cAAc;wEACd,MAAO,OAAO,MAAM,GAAG,EAAG;4EACxB,OAAO;wEACT;oEACF;oEACA,cAAc,MAAM,KAAK;;sFAEzB,8OAAC,gIAAA,CAAA,cAAW;sFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;;;;;;sFAG7B,8OAAC,kIAAA,CAAA,gBAAa;;8FACZ,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAU;;;;;;8FAC5B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAO;;;;;;;;;;;;;;;;;;8EAG7B,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;gDAKjB,kBAAkB,2BACjB,8OAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8EACP,8OAAC,gIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,kIAAA,CAAA,SAAM;oEACL,eAAe,CAAC;wEACd,MAAM,WAAW,SAAS,OAAO;wEACjC,MAAM,QAAQ,CAAC;wEACf,eAAe;wEACf,IAAI,UAAU;4EACZ,kBAAkB,WAAW;wEAC/B;oEACF;oEACA,cAAc,MAAM,KAAK,EAAE;oEAC3B,UAAU;;sFAEV,8OAAC,gIAAA,CAAA,cAAW;sFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;;;;;;sFAG7B,8OAAC,kIAAA,CAAA,gBAAa;sFACX,UAAU,MAAM,IAAI,CAAC,wBACpB,8OAAC,kIAAA,CAAA,aAAU;oFAET,OAAO,QAAQ,EAAE,CAAC,QAAQ;;wFAEzB,QAAQ,cAAc;wFAAC;wFAAG;wFAC1B,QAAQ,QAAQ,EAAE,QACjB,QAAQ,QAAQ,EAAE,QAClB;wFAAW;wFAAI;wFACf,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;wFAAE;;mFAPlC,QAAQ,EAAE;;;;;;;;;;;;;;;;8EAYvB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;gDAMnB,kBAAkB,wBACjB,8OAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8EACP,8OAAC,gIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,kIAAA,CAAA,SAAM;oEACL,eAAe,CAAC;wEACd,MAAM,WAAW,SAAS,OAAO;wEACjC,MAAM,QAAQ,CAAC;wEACf,eAAe;wEACf,IAAI,UAAU;4EACZ,kBAAkB,QAAQ;wEAC5B;oEACF;oEACA,cAAc,MAAM,KAAK,EAAE;oEAC3B,UAAU;;sFAEV,8OAAC,gIAAA,CAAA,cAAW;sFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;;;;;;sFAG7B,8OAAC,kIAAA,CAAA,gBAAa;sFACX,OAAO,MAAM,IAAI,CAAC,qBACjB,8OAAC,kIAAA,CAAA,aAAU;oFAET,OAAO,KAAK,EAAE,CAAC,QAAQ;;wFAEtB,KAAK,cAAc;wFAAC;wFAAG;wFACvB,KAAK,QAAQ,EAAE,QAAQ;wFAAmB;wFAC1C,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,YAAY;wFAAE;;mFAL9B,KAAK,EAAE;;;;;;;;;;;;;;;;8EAUpB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;sDAUxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;4DAAC,WAAU;;8EAClB,8OAAC,gIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,mIAAA,CAAA,UAAO;;sFACN,8OAAC,mIAAA,CAAA,iBAAc;4EAAC,OAAO;sFACrB,cAAA,8OAAC,gIAAA,CAAA,cAAW;0FACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;oFACL,SAAS;oFACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,CAAC,MAAM,KAAK,IAAI;;wFAGjB,MAAM,KAAK,GACV,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,KAAK,EAAE,uBAEpB,8OAAC;sGAAK;;;;;;sGAER,8OAAC,8MAAA,CAAA,eAAY;4FAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sFAI9B,8OAAC,mIAAA,CAAA,iBAAc;4EAAC,WAAU;4EAAa,OAAM;sFAC3C,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gFACP,MAAK;gFACL,UAAU,MAAM,KAAK;gFACrB,UAAU,MAAM,QAAQ;gFACxB,UAAU,CAAC,OACT,OAAO,IAAI,UAAU,OAAO,IAAI,KAAK;gFAEvC,YAAY;;;;;;;;;;;;;;;;;8EAIlB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8DAMlB,8OAAC,gIAAA,CAAA,YAAS;oDACR,SAAS,KAAK,OAAO;oDACrB,MAAK;oDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;8EACP,8OAAC,gIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,8OAAC,kIAAA,CAAA,SAAM;oEACL,eAAe,MAAM,QAAQ;oEAC7B,cAAc,MAAM,KAAK;;sFAEzB,8OAAC,gIAAA,CAAA,cAAW;sFACV,cAAA,8OAAC,kIAAA,CAAA,gBAAa;0FACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;;;;;;sFAG7B,8OAAC,kIAAA,CAAA,gBAAa;;8FACZ,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAS;;;;;;8FAC3B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAmB;;;;;;8FACrC,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAsB;;;;;;8FACxC,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAW;;;;;;8FAC7B,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAe;;;;;;8FACjC,8OAAC,kIAAA,CAAA,aAAU;oFAAC,OAAM;8FAAQ;;;;;;;;;;;;;;;;;;8EAG9B,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;sDAOpB,8OAAC,gIAAA,CAAA,YAAS;4CACR,SAAS,KAAK,OAAO;4CACrB,MAAK;4CACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sEACP,8OAAC,gIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,gIAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gEACP,aAAY;gEACZ,WAAU;gEACT,GAAG,KAAK;;;;;;;;;;;sEAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;sDAOlB,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAsB;;;;;;wDACnC,CAAC,wBACA,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,SAAS;;8EAET,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;8DAMvC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;0EACJ,8OAAC,iIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;sFACP,8OAAC,iIAAA,CAAA,YAAS;sFAAC;;;;;;sFACX,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAAY;;;;;;sFACjC,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAAY;;;;;;sFACjC,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAAY;;;;;;sFACjC,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAAY;;;;;;sFACjC,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;sFAAY;;;;;;wEAChC,CAAC,wBACA,8OAAC,iIAAA,CAAA,YAAS;4EAAC,WAAU;;;;;;;;;;;;;;;;;0EAI3B,8OAAC,iIAAA,CAAA,YAAS;0EACP,OAAO,MAAM,KAAK,kBACjB,8OAAC,iIAAA,CAAA,WAAQ;8EACP,cAAA,8OAAC,iIAAA,CAAA,YAAS;wEAAC,SAAS,SAAS,IAAI;wEAAG,WAAU;kFAC3C,SAAS,mBAAmB;;;;;;;;;;2EAIjC,OAAO,GAAG,CAAC,CAAC,OAAO,sBACjB,8OAAC,iIAAA,CAAA,WAAQ;;0FACP,8OAAC,iIAAA,CAAA,YAAS;0FACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;oFACH,GAAG,KAAK,QAAQ,CAAC,CAAC,MAAM,EAAE,MAAM,YAAY,CAAC,CAAU;oFACxD,UAAU;;;;;;;;;;;0FAGd,8OAAC,iIAAA,CAAA,YAAS;0FACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;oFACJ,MAAK;oFACJ,GAAG,KAAK,QAAQ,CAAC,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC,EAAW;wFACpD,eAAe;wFACf,UAAU,IAAM;oFAClB,EAAE;oFACF,UAAU;;;;;;;;;;;0FAGd,8OAAC,iIAAA,CAAA,YAAS;0FACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;oFACJ,MAAK;oFACL,MAAK;oFACJ,GAAG,KAAK,QAAQ,CAAC,CAAC,MAAM,EAAE,MAAM,WAAW,CAAC,EAAW;wFACtD,eAAe;wFACf,UAAU,IAAM;oFAClB,EAAE;oFACF,UAAU;;;;;;;;;;;0FAGd,8OAAC,iIAAA,CAAA,YAAS;0FACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;oFACJ,MAAK;oFACL,MAAK;oFACL,aAAY;oFACX,GAAG,KAAK,QAAQ,CAAC,CAAC,MAAM,EAAE,MAAM,SAAS,CAAC,EAAW;wFACpD,eAAe;wFACf,UAAU,IAAM;oFAClB,EAAE;oFACF,UAAU;;;;;;;;;;;0FAGd,8OAAC,iIAAA,CAAA,YAAS;0FACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;oFACJ,MAAK;oFACL,MAAK;oFACJ,GAAG,KAAK,QAAQ,CACf,CAAC,MAAM,EAAE,MAAM,WAAW,CAAC,EAC3B;wFACE,eAAe;wFACf,UAAU,IAAM;oFAClB,EACD;oFACD,UAAU;;;;;;;;;;;0FAGd,8OAAC,iIAAA,CAAA,YAAS;0FACR,cAAA,8OAAC,iIAAA,CAAA,QAAK;oFACJ,MAAK;oFACL,MAAK;oFACJ,GAAG,KAAK,QAAQ,CACf,CAAC,MAAM,EAAE,MAAM,YAAY,CAAC,EAC5B;wFACE,eAAe;oFACjB,EACD;oFACD,UAAU;;;;;;;;;;;4EAGb,CAAC,wBACA,8OAAC,iIAAA,CAAA,YAAS;0FACR,cAAA,8OAAC,kIAAA,CAAA,SAAM;oFACL,MAAK;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS;wFACP,OAAO;wFACP;oFACF;8FAEA,cAAA,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;;;;;;;;;;;;uEA9EX,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sDA4FnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;sEAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK,CAAC;;;;;;;;;;;;8DAEnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;sEACxC,8OAAC;sEAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK,CAAC;;;;;;;;;;;;8DAEnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAK;;;;;;sEACN,8OAAC;sEAAM,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK,CAAC;;;;;;;;;;;;;;;;;;sDAMrC,8OAAC,gIAAA,CAAA,YAAS;4CACR,SAAS,KAAK,OAAO;4CACrB,MAAK;4CACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;sEACP,8OAAC,gIAAA,CAAA,YAAS;sEAAC;;;;;;sEACX,8OAAC,gIAAA,CAAA,cAAW;sEACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;gEACP,aAAY;gEACZ,WAAU;gEACT,GAAG,KAAK;;;;;;;;;;;sEAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU,eAAe,SAAS,IAAI,eAAe,SAAS,IAAI;gCAClE,SAAS,CAAC;oCACR,QAAQ,GAAG,CAAC,sBAAsB;oCAClC,QAAQ,GAAG,CAAC,kBAAkB,KAAK,SAAS;oCAC5C,QAAQ,GAAG,CAAC,mBAAmB,KAAK,SAAS;oCAC7C,QAAQ,GAAG,CAAC,mBAAmB,KAAK,SAAS,CAAC,MAAM;gCACtD;;kDAEA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,SAAS,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 8288, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/projects/dukalink/dukalink-web/src/app/credit-notes/new/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { MainLayout } from \"@/components/layouts/main-layout\";\r\nimport { CreditNoteForm } from '@/features/credit-notes/components/credit-note-form';\r\n\r\nexport default function CreateCreditNotePage() {\r\n  return (\r\n    <MainLayout>\r\n      <CreditNoteForm />\r\n    </MainLayout>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMe,SAAS;IACtB,qBACE,8OAAC,+IAAA,CAAA,aAAU;kBACT,cAAA,8OAAC,2KAAA,CAAA,iBAAc;;;;;;;;;;AAGrB", "debugId": null}}]}