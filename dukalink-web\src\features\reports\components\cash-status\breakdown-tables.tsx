import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { DataPagination } from '@/components/ui/data-pagination';
import { formatCurrency } from '@/lib/utils';
import { CashStatusReportResponse } from '@/services/report-service';

interface BreakdownTablesProps {
  data: CashStatusReportResponse['data'] | null;
  view: 'region' | 'branch' | 'details';
  isLoading: boolean;
  pagination?: {
    page: number;
    limit: number;
    onPageChange: (page: number) => void;
    onPageSizeChange: (limit: number) => void;
  };
}

export function BreakdownTables({ data, view, isLoading, pagination }: BreakdownTablesProps) {
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card className="animate-pulse">
          <CardHeader>
            <CardTitle>
              <div className="h-6 w-48 bg-muted rounded"></div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 bg-muted rounded"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No data available. Please select a date range and refresh.</p>
      </div>
    );
  }

  if (view === 'region' && data.byRegion) {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Cash Status by Region</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Region</TableHead>
                  <TableHead className="text-right">Opening Cash</TableHead>
                  <TableHead className="text-right">Cash Inflows</TableHead>
                  <TableHead className="text-right">Cash Outflows</TableHead>
                  <TableHead className="text-right">Net Change</TableHead>
                  <TableHead className="text-right">Closing Cash</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.byRegion.map((region) => (
                  <TableRow key={region.region_id}>
                    <TableCell className="font-medium">{region.region_name}</TableCell>
                    <TableCell className="text-right">{formatCurrency(region.summary.totalOpeningCash)}</TableCell>
                    <TableCell className="text-right text-green-600">{formatCurrency(region.summary.totalInflows)}</TableCell>
                    <TableCell className="text-right text-red-600">{formatCurrency(region.summary.totalOutflows)}</TableCell>
                    <TableCell className={`text-right ${region.summary.netCashChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(region.summary.netCashChange)}
                    </TableCell>
                    <TableCell className="text-right">{formatCurrency(region.summary.totalClosingCash)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Pagination for regions */}
        {pagination && data.regionPagination && (
          <DataPagination
            currentPage={data.regionPagination.page}
            totalPages={data.regionPagination.pages}
            pageSize={data.regionPagination.limit}
            totalItems={data.regionPagination.total}
            onPageChange={pagination.onPageChange}
            onPageSizeChange={pagination.onPageSizeChange}
            isLoading={isLoading}
          />
        )}
      </div>
    );
  }

  if (view === 'branch' && data.byBranch) {
    return (
      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Cash Status by Branch</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Branch</TableHead>
                  <TableHead>Region</TableHead>
                  <TableHead className="text-right">Opening Cash</TableHead>
                  <TableHead className="text-right">Cash Inflows</TableHead>
                  <TableHead className="text-right">Cash Outflows</TableHead>
                  <TableHead className="text-right">Net Change</TableHead>
                  <TableHead className="text-right">Closing Cash</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.byBranch.map((branch) => (
                  <TableRow key={branch.branch_id}>
                    <TableCell className="font-medium">{branch.branch_name}</TableCell>
                    <TableCell>{branch.region_name}</TableCell>
                    <TableCell className="text-right">{formatCurrency(branch.summary.totalOpeningCash)}</TableCell>
                    <TableCell className="text-right text-green-600">{formatCurrency(branch.summary.totalInflows)}</TableCell>
                    <TableCell className="text-right text-red-600">{formatCurrency(branch.summary.totalOutflows)}</TableCell>
                    <TableCell className={`text-right ${branch.summary.netCashChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(branch.summary.netCashChange)}
                    </TableCell>
                    <TableCell className="text-right">{formatCurrency(branch.summary.totalClosingCash)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Pagination for branches */}
        {pagination && data.branchPagination && (
          <DataPagination
            currentPage={data.branchPagination.page}
            totalPages={data.branchPagination.pages}
            pageSize={data.branchPagination.limit}
            totalItems={data.branchPagination.total}
            onPageChange={pagination.onPageChange}
            onPageSizeChange={pagination.onPageSizeChange}
            isLoading={isLoading}
          />
        )}
      </div>
    );
  }

  if (view === 'details') {
    return (
      <div className="space-y-6">
        {/* Cash Inflows Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Cash Inflows Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Source</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead className="text-right">Transactions</TableHead>
                  <TableHead className="text-right">% of Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium">Cash Sales</TableCell>
                  <TableCell className="text-right">{formatCurrency(data.cashInflows.cashSales?.total || 0)}</TableCell>
                  <TableCell className="text-right">
                    {data.cashInflows.cashSales?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0}
                  </TableCell>
                  <TableCell className="text-right">
                    {data.cashInflows.total ?
                      `${(((data.cashInflows.cashSales?.total || 0) / data.cashInflows.total) * 100).toFixed(1)}%` :
                      '0%'}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">MPESA Deposits</TableCell>
                  <TableCell className="text-right">{formatCurrency(data.cashInflows.mpesaDeposits?.total || 0)}</TableCell>
                  <TableCell className="text-right">
                    {data.cashInflows.mpesaDeposits?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0}
                  </TableCell>
                  <TableCell className="text-right">
                    {data.cashInflows.total ?
                      `${(((data.cashInflows.mpesaDeposits?.total || 0) / data.cashInflows.total) * 100).toFixed(1)}%` :
                      '0%'}
                  </TableCell>
                </TableRow>

                <TableRow className="font-bold">
                  <TableCell>Total Inflows</TableCell>
                  <TableCell className="text-right">{formatCurrency(data.cashInflows.total || 0)}</TableCell>
                  <TableCell className="text-right">
                    {(data.cashInflows.cashSales?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0) +
                     (data.cashInflows.mpesaDeposits?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0)}
                  </TableCell>
                  <TableCell className="text-right">100%</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Cash Outflows Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Cash Outflows Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Source</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead className="text-right">Transactions</TableHead>
                  <TableHead className="text-right">% of Total</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium">Expenses</TableCell>
                  <TableCell className="text-right">{formatCurrency(data.cashOutflows.expenses?.total || 0)}</TableCell>
                  <TableCell className="text-right">
                    {data.cashOutflows.expenses?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0}
                  </TableCell>
                  <TableCell className="text-right">
                    {data.cashOutflows.total ?
                      `${(((data.cashOutflows.expenses?.total || 0) / data.cashOutflows.total) * 100).toFixed(1)}%` :
                      '0%'}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Banking Deposits</TableCell>
                  <TableCell className="text-right">{formatCurrency(data.cashOutflows.bankingDeposits?.total || 0)}</TableCell>
                  <TableCell className="text-right">
                    {data.cashOutflows.bankingDeposits?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0}
                  </TableCell>
                  <TableCell className="text-right">
                    {data.cashOutflows.total ?
                      `${(((data.cashOutflows.bankingDeposits?.total || 0) / data.cashOutflows.total) * 100).toFixed(1)}%` :
                      '0%'}
                  </TableCell>
                </TableRow>

                <TableRow className="font-bold">
                  <TableCell>Total Outflows</TableCell>
                  <TableCell className="text-right">{formatCurrency(data.cashOutflows.total || 0)}</TableCell>
                  <TableCell className="text-right">
                    {(data.cashOutflows.expenses?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0) +
                     (data.cashOutflows.bankingDeposits?.data?.reduce((sum, item) => sum + (item.transaction_count || 0), 0) || 0)}
                  </TableCell>
                  <TableCell className="text-right">100%</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="text-center py-8">
      <p className="text-muted-foreground">Please select a view to see detailed breakdown.</p>
    </div>
  );
}
