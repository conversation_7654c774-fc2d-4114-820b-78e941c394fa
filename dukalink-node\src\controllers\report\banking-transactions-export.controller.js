const ExcelJS = require('exceljs');
const { BankingTransaction, Branch, User, Bank } = require('../../models');
const { Op } = require('sequelize');
const logger = require('../../utils/logger');

class BankingTransactionsExportController {
  /**
   * Get banking transactions data with filtering
   */
  async getBankingTransactionsData(filters = {}) {
    try {
      const {
        start_date,
        end_date,
        branch_id,
        region_id,
        banking_method,
        transaction_type,
        status,
        bank_id
      } = filters;

      logger.info('Exporting banking transactions data with filters:', filters);

      // Build where clause for transactions
      const whereClause = {};

      // Default to last 30 days if no date range specified
      if (start_date && end_date) {
        whereClause.transaction_date = {
          [Op.between]: [new Date(start_date), new Date(end_date)]
        };
      } else if (start_date) {
        whereClause.transaction_date = {
          [Op.gte]: new Date(start_date)
        };
      } else if (end_date) {
        whereClause.transaction_date = {
          [Op.lte]: new Date(end_date)
        };
      } else {
        // Default to last 30 days
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        whereClause.transaction_date = {
          [Op.gte]: thirtyDaysAgo
        };
        logger.info('No date range specified, defaulting to last 30 days');
      }

      if (branch_id) {
        whereClause.branch_id = branch_id;
      }

      if (banking_method) {
        whereClause.banking_method = banking_method;
      }

      if (transaction_type) {
        whereClause.transaction_type = transaction_type;
      }

      if (status) {
        whereClause.status = status;
      }

      if (bank_id) {
        whereClause.bank_id = bank_id;
      }

      // Fetch banking transactions data with basic includes
      const transactions = await BankingTransaction.findAll({
        where: whereClause,
        include: [
          {
            model: Branch,
            attributes: ['id', 'name', 'location'],
            required: false
          },
          {
            model: User,
            attributes: ['id', 'name', 'email'],
            required: false
          },
          {
            model: Bank,
            attributes: ['id', 'name', 'code'],
            required: false
          }
        ],
        order: [['transaction_date', 'DESC']],
        limit: 3000, // Limit for performance
        raw: false
      });

      logger.info(`Found ${transactions.length} banking transactions`);

      // Process summary statistics
      const summary = this.generateSummaryStats(transactions, filters);

      return {
        transactions: transactions.map(transaction => ({
          id: transaction.id,
          transaction_date: transaction.transaction_date,
          reference_number: transaction.reference_number,
          banking_method: transaction.banking_method,
          transaction_type: transaction.transaction_type,
          amount: parseFloat(transaction.amount) || 0,
          status: transaction.status,
          description: transaction.description,
          branch_name: transaction.Branch?.name || 'Unknown',
          branch_location: transaction.Branch?.location || '',
          user_name: transaction.User?.name || 'Unknown',
          bank_name: transaction.Bank?.name || 'Unknown',
          bank_code: transaction.Bank?.code || '',
          has_receipt: !!transaction.receipt_path,
          created_at: transaction.created_at,
          updated_at: transaction.updated_at
        })),
        summary
      };
    } catch (error) {
      logger.error('Error getting banking transactions data:', error);
      throw error;
    }
  }

  /**
   * Generate summary statistics
   */
  generateSummaryStats(transactions, filters) {
    const stats = {
      total_transactions: transactions.length,
      total_amount: 0,
      total_deposits: 0,
      total_withdrawals: 0,
      total_transfers: 0,
      by_method: {},
      by_type: {},
      by_status: {},
      by_branch: {},
      filters_applied: filters
    };

    transactions.forEach(transaction => {
      const amount = parseFloat(transaction.amount) || 0;
      stats.total_amount += amount;

      // By type
      const type = transaction.transaction_type || 'unknown';
      if (type === 'deposit') {
        stats.total_deposits += amount;
      } else if (type === 'withdrawal') {
        stats.total_withdrawals += amount;
      } else if (type === 'transfer') {
        stats.total_transfers += amount;
      }

      // By method
      const method = transaction.banking_method || 'unknown';
      if (!stats.by_method[method]) {
        stats.by_method[method] = { count: 0, total: 0 };
      }
      stats.by_method[method].count += 1;
      stats.by_method[method].total += amount;

      // By type breakdown
      if (!stats.by_type[type]) {
        stats.by_type[type] = { count: 0, total: 0 };
      }
      stats.by_type[type].count += 1;
      stats.by_type[type].total += amount;

      // By status
      const status = transaction.status || 'unknown';
      if (!stats.by_status[status]) {
        stats.by_status[status] = { count: 0, total: 0 };
      }
      stats.by_status[status].count += 1;
      stats.by_status[status].total += amount;

      // By branch
      const branchName = transaction.Branch?.name || 'Unknown';
      if (!stats.by_branch[branchName]) {
        stats.by_branch[branchName] = { count: 0, total: 0 };
      }
      stats.by_branch[branchName].count += 1;
      stats.by_branch[branchName].total += amount;
    });

    return stats;
  }

  /**
   * Create Excel workbook with banking transactions data
   */
  async createExcelWorkbook(data, options = {}) {
    const {
      include_summary = true,
      include_details = true,
      include_method_breakdown = true,
      include_type_breakdown = true,
      include_status_breakdown = true,
      include_charts = false,
      filters = {}
    } = options;

    logger.info(`Creating Excel workbook for ${data.transactions.length} banking transactions`);

    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'DukaLink POS System';
    workbook.created = new Date();

    // Summary Sheet
    if (include_summary) {
      const summarySheet = workbook.addWorksheet('Transaction Summary');

      // Title
      summarySheet.addRow(['Banking Transactions Export']);
      summarySheet.addRow(['Generated on:', new Date().toLocaleString()]);
      summarySheet.addRow([]);

      // Key metrics
      summarySheet.addRow(['Total Transactions:', data.summary.total_transactions]);
      summarySheet.addRow(['Total Amount:', data.summary.total_amount.toFixed(2)]);
      summarySheet.addRow(['Total Deposits:', data.summary.total_deposits.toFixed(2)]);
      summarySheet.addRow(['Total Withdrawals:', data.summary.total_withdrawals.toFixed(2)]);
      summarySheet.addRow(['Total Transfers:', data.summary.total_transfers.toFixed(2)]);
      summarySheet.addRow([]);

      // Applied filters
      summarySheet.addRow(['Applied Filters:']);
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          summarySheet.addRow([`${key}:`, value]);
        }
      });

      // Style the summary sheet
      summarySheet.getCell('A1').font = { bold: true, size: 16 };
      summarySheet.getColumn('A').width = 25;
      summarySheet.getColumn('B').width = 20;
    }

    // Transaction Details Sheet
    if (include_details) {
      const detailsSheet = workbook.addWorksheet('Transaction Details');

      // Headers
      const headers = [
        'Transaction Date',
        'Reference Number',
        'Banking Method',
        'Transaction Type',
        'Amount',
        'Status',
        'Description',
        'Branch',
        'User',
        'Bank',
        'Has Receipt'
      ];

      detailsSheet.addRow(headers);

      // Data rows
      data.transactions.forEach(transaction => {
        detailsSheet.addRow([
          transaction.transaction_date ? new Date(transaction.transaction_date).toLocaleDateString() : '',
          transaction.reference_number,
          transaction.banking_method,
          transaction.transaction_type,
          transaction.amount.toFixed(2),
          transaction.status,
          transaction.description,
          transaction.branch_name,
          transaction.user_name,
          transaction.bank_name,
          transaction.has_receipt ? 'Yes' : 'No'
        ]);
      });

      // Style the headers
      const headerRow = detailsSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Auto-fit columns
      detailsSheet.columns.forEach(column => {
        column.width = 15;
      });
    }

    // Method Breakdown Sheet
    if (include_method_breakdown) {
      const methodSheet = workbook.addWorksheet('Method Breakdown');

      methodSheet.addRow(['Banking Method', 'Transaction Count', 'Total Amount']);

      Object.entries(data.summary.by_method).forEach(([method, stats]) => {
        methodSheet.addRow([method, stats.count, stats.total.toFixed(2)]);
      });

      // Style headers
      const headerRow = methodSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      methodSheet.columns.forEach(column => {
        column.width = 20;
      });
    }

    // Type Breakdown Sheet
    if (include_type_breakdown) {
      const typeSheet = workbook.addWorksheet('Type Breakdown');

      typeSheet.addRow(['Transaction Type', 'Transaction Count', 'Total Amount']);

      Object.entries(data.summary.by_type).forEach(([type, stats]) => {
        typeSheet.addRow([type, stats.count, stats.total.toFixed(2)]);
      });

      // Style headers
      const headerRow = typeSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      typeSheet.columns.forEach(column => {
        column.width = 20;
      });
    }

    // Status Breakdown Sheet
    if (include_status_breakdown) {
      const statusSheet = workbook.addWorksheet('Status Breakdown');

      statusSheet.addRow(['Status', 'Transaction Count', 'Total Amount']);

      Object.entries(data.summary.by_status).forEach(([status, stats]) => {
        statusSheet.addRow([status, stats.count, stats.total.toFixed(2)]);
      });

      // Style headers
      const headerRow = statusSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      statusSheet.columns.forEach(column => {
        column.width = 20;
      });
    }

    return workbook;
  }
}

const bankingTransactionsExportController = new BankingTransactionsExportController();

/**
 * Export all banking transactions data
 */
const exportAllBankingTransactions = async (req, res, next) => {
  const timeout = setTimeout(() => {
    if (!res.headersSent) {
      res.status(408).json({
        error: 'Export timeout',
        message: 'The export is taking too long. Try filtering your data or use lightweight export.'
      });
    }
  }, 60000); // 1 minute timeout

  try {
    const filters = req.query;
    logger.info('Starting comprehensive banking transactions export with filters:', filters);

    // Get transactions data
    const data = await bankingTransactionsExportController.getBankingTransactionsData(filters);

    if (data.transactions.length === 0) {
      clearTimeout(timeout);
      return res.status(404).json({
        error: 'No banking transactions found for the specified criteria',
        message: 'Try adjusting your date range or removing filters to see more data.'
      });
    }

    logger.info(`Creating Excel workbook for ${data.transactions.length} banking transactions`);

    // Create Excel workbook
    const workbook = await bankingTransactionsExportController.createExcelWorkbook(data, {
      include_summary: true,
      include_details: true,
      include_method_breakdown: true,
      include_type_breakdown: true,
      include_status_breakdown: true,
      include_charts: true,
      filters
    });

    // Clear timeout since we're about to send response
    clearTimeout(timeout);

    // Set response headers
    const filename = `banking-transactions-comprehensive-${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info(`Banking transactions export completed successfully: ${data.transactions.length} transactions exported as ${filename}`);
  } catch (error) {
    clearTimeout(timeout);
    logger.error('Error in exportAllBankingTransactions:', error);

    // Send error response if headers haven't been sent
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to export banking transactions',
        message: error.message || 'An unexpected error occurred during export'
      });
    }
  }
};

/**
 * Export custom banking transactions data
 */
const exportCustomBankingTransactions = async (req, res, next) => {
  const timeout = setTimeout(() => {
    if (!res.headersSent) {
      res.status(408).json({
        error: 'Export timeout',
        message: 'The export is taking too long. Try using lightweight format or filtering the data.'
      });
    }
  }, 45000); // 45 seconds timeout for custom exports

  try {
    const {
      include_summary = 'true',
      include_details = 'true',
      include_method_breakdown = 'true',
      include_type_breakdown = 'true',
      include_status_breakdown = 'true',
      include_charts = 'true',
      format_type = 'detailed',
      ...filters
    } = req.query;

    logger.info('Starting custom banking transactions export with options:', {
      include_summary,
      include_details,
      include_method_breakdown,
      include_type_breakdown,
      include_status_breakdown,
      include_charts,
      format_type,
      filters
    });

    // Get transactions data
    const data = await bankingTransactionsExportController.getBankingTransactionsData(filters);

    if (data.transactions.length === 0) {
      clearTimeout(timeout);
      return res.status(404).json({
        error: 'No banking transactions found for the specified criteria',
        message: 'Try adjusting your date range or removing filters to see more data.'
      });
    }

    // Create Excel workbook with custom options
    const workbook = await bankingTransactionsExportController.createExcelWorkbook(data, {
      include_summary: include_summary === 'true',
      include_details: include_details === 'true',
      include_method_breakdown: include_method_breakdown === 'true',
      include_type_breakdown: include_type_breakdown === 'true',
      include_status_breakdown: include_status_breakdown === 'true',
      include_charts: include_charts === 'true',
      filters
    });

    // Clear timeout since we're about to send response
    clearTimeout(timeout);

    // Set response headers
    const formatSuffix = format_type === 'summary' ? 'summary' : 'custom';
    const filename = `banking-transactions-${formatSuffix}-${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info(`Custom banking transactions export completed: ${data.transactions.length} transactions exported as ${filename}`);
  } catch (error) {
    clearTimeout(timeout);
    logger.error('Error in exportCustomBankingTransactions:', error);

    // Send error response if headers haven't been sent
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to export custom banking transactions',
        message: error.message || 'An unexpected error occurred during export'
      });
    }
  }
};

/**
 * Export lightweight banking transactions data (fastest option)
 */
const exportLightweightBankingTransactions = async (req, res, next) => {
  try {
    const filters = req.query;
    logger.info('Starting lightweight banking transactions export with filters:', filters);

    // Get transactions data
    const data = await bankingTransactionsExportController.getBankingTransactionsData(filters);

    if (data.transactions.length === 0) {
      return res.status(404).json({
        error: 'No banking transactions found for the specified criteria',
        message: 'Try adjusting your date range or removing filters to see more data.'
      });
    }

    logger.info(`Creating lightweight Excel workbook for ${data.transactions.length} banking transactions`);

    // Create simple workbook with just transaction data
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'DukaLink POS System';
    workbook.created = new Date();

    // Simple transactions sheet only
    const transactionsSheet = workbook.addWorksheet('Banking Transactions');

    // Headers
    const headers = [
      'Date',
      'Reference',
      'Method',
      'Type',
      'Amount',
      'Status',
      'Branch',
      'Bank'
    ];

    transactionsSheet.addRow(headers);

    // Data rows
    data.transactions.forEach(transaction => {
      transactionsSheet.addRow([
        transaction.transaction_date ? new Date(transaction.transaction_date).toLocaleDateString() : '',
        transaction.reference_number,
        transaction.banking_method,
        transaction.transaction_type,
        transaction.amount.toFixed(2),
        transaction.status,
        transaction.branch_name,
        transaction.bank_name
      ]);
    });

    // Style the headers
    const headerRow = transactionsSheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Auto-fit columns
    transactionsSheet.columns.forEach(column => {
      column.width = 15;
    });

    // Set response headers
    const filename = `banking-transactions-${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info(`Lightweight banking transactions export completed: ${data.transactions.length} transactions exported as ${filename}`);
  } catch (error) {
    logger.error('Error in exportLightweightBankingTransactions:', error);

    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to export banking transactions data',
        message: error.message || 'An unexpected error occurred during export'
      });
    }
  }
};

module.exports = {
  bankingTransactionsExportController,
  exportAllBankingTransactions,
  exportCustomBankingTransactions,
  exportLightweightBankingTransactions
};
