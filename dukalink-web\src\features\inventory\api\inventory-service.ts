import apiClient from "@/lib/api-client";
import { PaginatedResponse } from "@/types/api";
import {
  BranchInventory,
  InventoryTransaction,
  CreateInventoryTransactionRequest,
  InventoryAdjustment,
  CreateInventoryAdjustmentRequest,
  UpdateInventoryAdjustmentStatusRequest,
  InventoryReportSummary,
  InventoryReportFilters,
} from "@/types/inventory";

export const inventoryService = {
  getBranchInventory: async (
    branchId: number,
    params?: Record<string, any>
  ): Promise<PaginatedResponse<BranchInventory>> => {
    try {
      // Special case: branchId = -1 means "All Branches"
      if (branchId === -1) {
        console.log("Viewing all branches inventory");
        try {
          const result = await inventoryService.getAllBranchesInventory(params);
          console.log("All branches inventory result:", result);
          return result;
        } catch (error) {
          console.error("Error fetching all branches inventory:", error);
          throw error;
        }
      }

      // If no branch ID is provided, try to get the user's branch ID
      if (!branchId) {
        try {
          // Get the current user to determine their branch
          const currentUser = await apiClient.get("/auth/me");
          console.log("Current user for inventory:", currentUser);

          // Type assertion to access branch_id
          const userWithBranch = currentUser as any;
          if (userWithBranch?.branch_id) {
            console.log("Using user's branch_id:", userWithBranch.branch_id);
            branchId = userWithBranch.branch_id;
          } else {
            console.log("No branch ID available, returning empty data");
            return {
              data: [],
              pagination: {
                total: 0,
                page: 1,
                limit: 0,
                totalPages: 1,
              },
            };
          }
        } catch (error) {
          console.error("Error getting user data:", error);
          return {
            data: [],
            pagination: {
              total: 0,
              page: 1,
              limit: 0,
              totalPages: 1,
            },
          };
        }
      }

      // Use stock-items endpoint with branch_id for branch-specific inventory
      const queryParams = {
        branch_id: branchId,
        page: params?.page || 1,
        limit: params?.limit || 100,
        // Handle search parameters - the backend expects 'search' parameter for product name search
        ...(params?.search && { search: params.search }),
        ...(params?.product_name && { search: params.product_name }),
      };

      // Log search parameters for debugging
      if (queryParams.search) {
        console.log(`Searching inventory by product name:`, queryParams.search);
      }

      console.log(`Calling API: /stock-items with params:`, queryParams);
      // Use the correct endpoint based on API structure
      try {
        const response = await apiClient.get(`/stock-items`, {
          params: queryParams,
        });
        console.log("Raw API response:", response);

        // Check if response is undefined or null
        if (!response) {
          console.log("API response is undefined or null");
          return {
            data: [],
            pagination: {
              total: 0,
              page: 1,
              limit: 0,
              totalPages: 1,
            },
          };
        }

        // The API returns a paginated response with data and pagination properties
        if (response && typeof response === "object" && "data" in response) {
          console.log("Response is a paginated object with data property");

          // Extract the data array and pagination info
          const { data, pagination } = response as {
            data: BranchInventory[];
            pagination: {
              total: number;
              page: number;
              limit: number;
              pages: number;
            };
          };

          // Use the pagination data directly from the API response
          const total = pagination.total || 0;
          const page = pagination.page || 1;
          const limit = pagination.limit || 100;
          const pages = pagination.pages || Math.ceil(total / limit) || 1;

          console.log("API pagination response:", {
            total,
            page,
            limit,
            pages,
            shouldShowPagination: pages > 1
          });

          return {
            data: data,
            pagination: {
              total,
              page,
              limit,
              pages,
              totalPages: pages, // For backward compatibility
            },
          };
        }
        // Handle case where response is an array (unlikely with current API)
        else if (Array.isArray(response)) {
          console.log("Response is an array, converting to paginated format");
          const limit = params?.limit ? parseInt(params.limit as string, 10) : 10;
          const page = params?.page ? parseInt(params.page as string, 10) : 1;
          const total = response.length;

          // Always calculate pages based on total and limit
          const calculatedPages = Math.ceil(total / limit) || 1;

          // Make sure we have at least 1 page
          const pages = calculatedPages;

          console.log("Pages calculation (fallback):", {
            calculatedPages,
            finalPages: pages,
            total,
            limit,
            shouldShowPagination: pages > 1
          });

          return {
            data: response,
            pagination: {
              total: total,
              page: page,
              limit: limit,
              pages: pages,
            },
          };
        }
        // If the response format is unexpected
        else {
          console.log("Unexpected response format:", response);
          return {
            data: [],
            pagination: {
              total: 0,
              page: 1,
              limit: 10,
              pages: 1,
            },
          };
        }

        // This code is unreachable due to the if-else above, but TypeScript doesn't know that
        // We'll keep it for safety
      } catch (error) {
        console.error("Error in API call to stock-items:", error);
        throw error;
      }
    } catch (error) {
      console.error("Error in getBranchInventory:", error);
      // Return empty data on error
      return {
        data: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 10,
          pages: 1,
        },
      };
    }
  },

  getProductInventory: async (
    productId: number,
    params?: Record<string, any>
  ): Promise<PaginatedResponse<BranchInventory>> => {
    return apiClient.get(`/products/${productId}/stock-items`, { params });
  },

  getInventoryTransactions: async (
    params?: Record<string, any>
  ): Promise<PaginatedResponse<InventoryTransaction>> => {
    return apiClient.get("/stock-movements", { params });
  },

  getInventoryTransactionById: async (
    id: number
  ): Promise<InventoryTransaction> => {
    return apiClient.get(`/stock-movements/${id}`);
  },

  createInventoryTransaction: async (
    transaction: CreateInventoryTransactionRequest
  ): Promise<InventoryTransaction> => {
    return apiClient.post("/stock-movements", transaction);
  },

  createStockItem: async (data: {
    product_id: number;
    branch_id: number;
    quantity: number;
    quantity_reserved?: number;
    serial_numbers?: string[]; // Array of serial numbers for serialized products
    buying_price?: number | string;
    default_buying_price?: number | string;
    default_selling_price?: number | string;
    default_wholesale_price?: number | string;
    buying_price_including_vat?: number | string;
    buying_price_excluding_vat?: number | string;
    buying_vat_amount?: number | string;
    buying_vat_rate?: number | string;
    batch_number?: string;
    expiry_date?: string;
    manufacturing_date?: string;
    reorder_level?: number;
    reorder_quantity?: number;
    valuation_method?: 'FIFO' | 'LIFO' | 'WEIGHTED_AVERAGE';
    tenant_id?: number;
  }): Promise<any> => {
    // Ensure we're sending the required fields to the API
    const apiRequest = {
      // Required fields
      product_id: data.product_id,
      branch_id: data.branch_id,
      quantity: data.quantity,

      // Serial numbers for serialized products
      serial_numbers: data.serial_numbers,

      // Optional fields with backward compatibility
      quantity_reserved: data.quantity_reserved,
      buying_price: data.buying_price,
      default_buying_price: data.default_buying_price || data.buying_price,
      default_selling_price: data.default_selling_price,
      default_wholesale_price: data.default_wholesale_price,

      // VAT-related fields
      buying_price_including_vat: data.buying_price_including_vat,
      buying_price_excluding_vat: data.buying_price_excluding_vat,
      buying_vat_amount: data.buying_vat_amount,
      buying_vat_rate: data.buying_vat_rate,

      // Batch/lot tracking
      batch_number: data.batch_number,
      expiry_date: data.expiry_date,
      manufacturing_date: data.manufacturing_date,

      // Inventory management
      reorder_level: data.reorder_level,
      reorder_quantity: data.reorder_quantity,
      valuation_method: data.valuation_method,

      // Tenant information
      tenant_id: data.tenant_id,
    };

    console.log('Creating stock item with data:', apiRequest);

    // For serialized products, we need to use a different endpoint
    if (data.serial_numbers && data.serial_numbers.length > 0) {
      console.log('Creating inventory items with serial numbers');
      return apiClient.post("/inventory-items", {
        stock_item_id: null, // This will be created by the backend
        product_id: data.product_id,
        quantity: data.quantity,
        serial_numbers: data.serial_numbers,
        batch_number: data.batch_number,
        expiry_date: data.expiry_date
      });
    }

    // For non-serialized products, use the regular endpoint
    return apiClient.post("/stock-items", apiRequest);
  },

  updateStockItem: async (
    id: number,
    data: {
      quantity?: number;
      buying_price?: number | string;
      selling_price?: number | string;
      status?: string;
      is_in_transit?: boolean;
    }
  ): Promise<any> => {
    // Ensure we're sending the required fields to the API
    const apiRequest = {
      quantity: data.quantity,
      buying_price: data.buying_price,
      selling_price: data.selling_price,
      status: data.status,
      is_in_transit: data.is_in_transit,
    };

    return apiClient.put(`/stock-items/${id}`, apiRequest);
  },

  updateStockItemStatus: async (
    id: number,
    status: string,
    isInTransit: boolean = false
  ): Promise<any> => {
    // Update just the status of a stock item
    const apiRequest = {
      status: status,
      is_in_transit: isInTransit,
    };

    return apiClient.put(`/stock-items/${id}/status`, apiRequest);
  },

  getInventoryAdjustments: async (
    params?: Record<string, any>
  ): Promise<PaginatedResponse<InventoryAdjustment>> => {
    return apiClient.get("/stock-adjustments", { params });
  },

  getInventoryAdjustmentById: async (
    id: number
  ): Promise<InventoryAdjustment> => {
    return apiClient.get(`/stock-adjustments/${id}`);
  },

  getStockAdjustmentTypes: async () => {
    return apiClient.get("/stock-adjustments/types");
  },

  getStockItemsByBranch: async (branch_id: number) => {
    return apiClient.get("/stock-items", {
      params: { branch_id }
    });
  },

  createInventoryAdjustment: async (
    adjustment: CreateInventoryAdjustmentRequest
  ): Promise<InventoryAdjustment> => {
    // Ensure we're sending the required fields to the API
    const apiRequest = {
      reference_number: `ADJ-${Date.now()}`,
      location_id: adjustment.branch_id, // Map branch_id to location_id
      adjustment_type_id: adjustment.adjustment_type_id, // Use the actual adjustment_type_id
      status: "pending", // Default status for new adjustments
      notes: adjustment.notes || "",
      requested_by: 1, // Default to user ID 1, should be replaced with actual user ID from user context
      items: adjustment.items.map((item) => ({
        stock_item_id: item.stock_item_id, // Use the actual stock_item_id
        quantity: item.quantity,
        unit_cost: 0, // Default unit cost, should be replaced with actual unit cost if available
        reason: item.reason, // Use the item-specific reason
      })),
    };

    return apiClient.post("/stock-adjustments", apiRequest);
  },

  updateInventoryAdjustmentStatus: async (
    id: number,
    status: UpdateInventoryAdjustmentStatusRequest
  ): Promise<InventoryAdjustment> => {
    // Ensure we're sending the required fields to the API
    const apiRequest = {
      status: status.status, // 'approved', 'rejected', 'pending'
      notes: status.notes || "",
      processed_by: status.processed_by || 1, // Default to user ID 1, should be replaced with actual user ID from user context
    };

    return apiClient.put(`/stock-adjustments/${id}/process`, apiRequest);
  },

  getAllBranchesInventory: async (
    params?: Record<string, any>
  ): Promise<PaginatedResponse<BranchInventory>> => {
    try {
      console.log("Fetching all branches first");
      // First, get all branches
      const branchesResponse = await apiClient.get<any[]>("/branches");
      const branches = Array.isArray(branchesResponse) ? branchesResponse : [];

      console.log(`Found ${branches.length} branches`);

      if (branches.length === 0) {
        return {
          data: [],
          pagination: {
            total: 0,
            page: 1,
            limit: 0,
            totalPages: 1,
          },
        };
      }

      // Fetch all inventory items at once without branch filter
      console.log("Fetching all inventory items");

      // Log search parameters for debugging
      if (params?.search || params?.product_name || params?.['product.name']) {
        console.log(`Searching all branches inventory by product name:`, params?.product_name || params?.['product.name'] || params?.search);
        console.log(`Note: This searches for stock items where the associated product name matches the search term`);
      }

      try {
        // Pass search and pagination parameters to the API
        const queryParams = {
          page: params?.page || 1,
          limit: params?.limit || 100,
          // Handle search parameters - the backend expects 'search' parameter for product name search
          ...(params?.search && { search: params.search }),
          ...(params?.product_name && { search: params.product_name }),
        };

        console.log("Fetching all branches inventory with params:", queryParams);

        const allInventoryResponse = await apiClient.get(`/stock-items`, {
          params: queryParams
        });

        // Check if the response has the expected structure
        if (
          !allInventoryResponse ||
          typeof allInventoryResponse !== "object" ||
          !("data" in allInventoryResponse)
        ) {
          console.log("Unexpected response format:", allInventoryResponse);
          return {
            data: [],
            pagination: {
              total: 0,
              page: 1,
              limit: 0,
              totalPages: 1,
            },
          };
        }

        // Extract the data array and pagination from the response
        const inventoryItems = (allInventoryResponse as any).data || [];
        const apiPagination = (allInventoryResponse as any).pagination || {};

        console.log(`Received ${inventoryItems.length} inventory items`);
        console.log("API pagination:", apiPagination);

        // Add branch information to each inventory item
        const inventoryWithBranchInfo = inventoryItems.map((item: any) => {
          // Find the branch for this item
          const branch = branches.find((b) => b.id === item.branch_id);
          return {
            ...item,
            Branch: branch || {
              id: item.branch_id,
              name: "Unknown Branch",
              location: "Unknown",
            },
          };
        });

        // Use the pagination data from the API response
        const total = apiPagination.total || inventoryWithBranchInfo.length;
        const page = apiPagination.page || 1;
        const limit = apiPagination.limit || 100;
        const pages = apiPagination.pages || Math.ceil(total / limit) || 1;
        console.log(
          `Returning ${inventoryWithBranchInfo.length} items for page ${page} of ${pages}`
        );

        return {
          data: inventoryWithBranchInfo,
          pagination: {
            total,
            page,
            limit,
            pages,
            totalPages: pages, // For backward compatibility
          },
        };
      } catch (apiError) {
        console.error("Error fetching inventory items:", apiError);
        return {
          data: [],
          pagination: {
            total: 0,
            page: 1,
            limit: 0,
            pages: 1,
          },
        };
      }
    } catch (error) {
      console.error("Error fetching inventory for all branches:", error);
      return {
        data: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 0,
          pages: 1,
        },
      };
    }
  },

  // Helper function to process HQ inventory response
  processHQInventoryResponse: (
    response: any
  ): PaginatedResponse<BranchInventory> => {
    console.log("Processing HQ inventory response");

    // Process array response
    if (Array.isArray(response)) {
      console.log("Response is an array with", response.length, "items");
      const mappedItems = response.map((item: any) => {
        // Make sure Product is properly handled
        const product = item.Product || {};

        return {
          id: item.id,
          branch_id: 0, // Use 0 to indicate HQ
          product_id: item.product_id,
          quantity: item.quantity,
          buying_price: item.buying_price,
          selling_price: item.selling_price,
          // Add default price fields that the InventoryTable component looks for
          default_selling_price: item.default_selling_price || item.selling_price,
          default_buying_price: item.default_buying_price || item.buying_price,
          default_wholesale_price: item.default_wholesale_price || item.wholesale_price,
          created_at: item.created_at,
          updated_at: item.updated_at,
          deleted_at: item.deleted_at,
          Branch: {
            id: 0,
            name: "Headquarters",
            location: "HQ",
          },
          Product: product,
          // Add reorder_level for consistency with branch inventory
          reorder_level: product.reorder_level || 0,
        };
      });

      console.log(
        `Mapped ${mappedItems.length} HQ stock items to branch inventory format`
      );

      return {
        data: mappedItems,
        pagination: {
          total: mappedItems.length,
          page: 1,
          limit: mappedItems.length,
          pages: 1,
        },
      };
    }

    // Handle paginated response
    if (response && typeof response === "object" && "data" in response) {
      console.log("Response is a paginated object with data property");
      const { data, pagination } = response as any;

      const mappedItems = data.map((item: any) => {
        // Make sure Product is properly handled
        const product = item.Product || {};

        return {
          id: item.id,
          branch_id: 0, // Use 0 to indicate HQ
          product_id: item.product_id,
          quantity: item.quantity,
          buying_price: item.buying_price,
          selling_price: item.selling_price,
          // Add default price fields that the InventoryTable component looks for
          default_selling_price: item.default_selling_price || item.selling_price,
          default_buying_price: item.default_buying_price || item.buying_price,
          default_wholesale_price: item.default_wholesale_price || item.wholesale_price,
          created_at: item.created_at,
          updated_at: item.updated_at,
          deleted_at: item.deleted_at,
          Branch: {
            id: 0,
            name: "Headquarters",
            location: "HQ",
          },
          Product: product,
          // Add reorder_level for consistency with branch inventory
          reorder_level: product.reorder_level || 0,
        };
      });

      console.log(
        `Mapped ${mappedItems.length} HQ stock items from paginated response`
      );

      return {
        data: mappedItems,
        pagination: {
          total: pagination.total,
          page: pagination.page,
          limit: pagination.limit,
          pages: pagination.pages || 1,
        },
      };
    }

    // Default fallback
    console.log("No HQ stock items found or unexpected response format");
    return {
      data: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 0,
        pages: 1,
      },
    };
  },

  getHQInventory: async (
    params?: Record<string, any>
  ): Promise<PaginatedResponse<BranchInventory>> => {
    try {
      console.log("Fetching HQ inventory with params:", params);

      // Use stock-items endpoint with branch_id=1 for headquarters stock
      const queryParams = {
        ...params,
        branch_id: 1, // Use branch_id=1 for headquarters stock
      };

      // Log search parameters for debugging
      if (params?.search || params?.product_name || params?.['product.name']) {
        console.log(`Searching HQ inventory by product name:`, params?.product_name || params?.['product.name'] || params?.search);
        console.log(`Note: This searches for stock items where the associated product name matches the search term`);
      }

      console.log("Calling API: stock-items with params:", queryParams);

      const response = await apiClient.get("/stock-items", {
        params: queryParams,
      });
      console.log("Stock items API call successful");

      // Process the response
      return inventoryService.processHQInventoryResponse(response);
    } catch (error) {
      console.error("Error fetching HQ inventory:", error);
      return {
        data: [],
        pagination: {
          total: 0,
          page: 1,
          limit: 0,
          pages: 1,
        },
      };
    }
  },

  getInventoryReportSummary: async (
    filters?: InventoryReportFilters
  ): Promise<InventoryReportSummary> => {
    try {
      console.log(
        "Calling API: /reports/inventory/summary with filters:",
        filters
      );
      const response = await apiClient.get<any>("/reports/inventory/summary", {
        params: filters,
      });
      console.log(
        "Raw inventory report API response:",
        JSON.stringify(response, null, 2)
      );

      // Return the response directly
      return response as InventoryReportSummary;
    } catch (error) {
      console.error("Error in getInventoryReportSummary:", error);
      // Return default data structure on error
      return {
        total_products: 0,
        total_value: 0,
        low_stock_count: 0,
        out_of_stock_count: 0,
        top_products: [],
        by_category: [],
        by_branch: [],
      };
    }
  },

  getStockValuationReport: async (filters?: {
    branch_id?: number;
    category_id?: number;
    valuation_method?: 'FIFO' | 'LIFO' | 'WEIGHTED_AVERAGE';
    include_zero_stock?: boolean;
  }) => {
    try {
      console.log(
        "Calling API: /reports/inventory/valuation with filters:",
        filters
      );
      const response = await apiClient.get<any>("/reports/inventory/valuation", {
        params: filters,
      });
      console.log(
        "Raw stock valuation report API response:",
        JSON.stringify(response, null, 2)
      );

      // Return the response directly
      return response;
    } catch (error) {
      console.error("Error in getStockValuationReport:", error);
      // Return default data structure on error
      return {
        summary: {
          total_items: 0,
          total_quantity: 0,
          total_value: 0,
          valuation_methods: {
            FIFO: { count: 0, value: 0 },
            LIFO: { count: 0, value: 0 },
            WEIGHTED_AVERAGE: { count: 0, value: 0 }
          }
        },
        items: [],
        by_category: [],
        by_branch: [],
        by_valuation_method: []
      };
    }
  },

  // Get all branches
  getBranches: async () => {
    try {
      console.log("Fetching all branches");
      const response = await apiClient.get("/branches");

      // Check if the response is an array (direct branches list)
      if (Array.isArray(response)) {
        console.log(`Received ${response.length} branches directly`);
        return {
          data: response,
          total: response.length
        };
      }

      // Check if the response has a data property (paginated response)
      if (response && typeof response === "object" && "data" in response) {
        console.log(`Received ${(response as any).data.length} branches in paginated format`);
        const { data, pagination } = response as any;
        return {
          data,
          total: pagination?.total || data.length
        };
      }

      // Default fallback
      console.log("Unexpected branches response format, returning empty array");
      return {
        data: [],
        total: 0
      };
    } catch (error) {
      console.error("Error fetching branches:", error);
      return {
        data: [],
        total: 0
      };
    }
  },
};
