const {
  StockMovement,
  StockMovementItem,
  Product,
  Branch,
  Tenant,
  StockItem,
  User,
  InventoryTransaction
} = require('../models');
const AppError = require('../utils/error');
const sequelize = require('../../config/database');
const Sequelize = require('sequelize');
const { Op } = Sequelize; // Import Sequelize operators directly

/**
 * Get all stock movements
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllStockMovements = async (req, res, next) => {
  try {
    const {
      tenant_id,
      from_branch_id,
      to_branch_id,
      status,
      start_date,
      end_date,
      from_hq,
      skip_role_filter,
      search,
      page = 1,
      limit = 100
    } = req.query;

    const whereClause = {};

    if (tenant_id) {
      whereClause.tenant_id = tenant_id;
    }

    // Check if we should skip role-based filtering (for mobile app)
    if (skip_role_filter === 'true') {
      console.log('Skipping role-based filtering due to skip_role_filter parameter');

      // Apply direct branch filter if provided
      if (to_branch_id) {
        whereClause.to_branch_id = to_branch_id;
        console.log(`Filtering by to_branch_id: ${to_branch_id}`);
      }

      if (from_branch_id) {
        whereClause.from_branch_id = from_branch_id;
        console.log(`Filtering by from_branch_id: ${from_branch_id}`);
      }
    }
    // Role-based access control
    // Company admins and super_admins can see all stock movements
    // Branch managers and other users can only see stock movements from or to their branch
    else if (!req.user) {
      // If user is not authenticated, return empty result
      console.log('User not authenticated, returning empty result');
      return res.json([]);
    }
    else if (req.user.role_name !== 'super_admin' &&
        req.user.role_name !== 'company_admin' &&
        req.user.role_name !== 'tenant_admin' &&
        req.user.role_name !== 'hq_admin') {
      // For branch managers, branch admins, and all other users, show stock movements related to their branch
      try {
        // Make sure Op is defined before using it
        if (Op && typeof Op === 'object' && Op.or) {
          whereClause[Op.or] = [
            { from_branch_id: req.user.branch_id },
            { to_branch_id: req.user.branch_id }
          ];
          console.log(`Filtering stock movements for ${req.user.role_name}. Branch ID: ${req.user.branch_id}`);
        } else {
          // Fallback if Op.or is not available
          console.log('Op.or is not available, falling back to direct branch filter');
          // Use a simple filter that includes both from and to branch
          if (req.user.role_name === 'branch_manager' || req.user.role_name === 'branch_admin' || req.user.branch_id) {
            // For branch managers and all users with a branch_id, we want to show both incoming and outgoing transfers
            // Since we can't use Op.or, we'll default to showing transfers to their branch
            // The frontend can make a separate request for outgoing transfers if needed
            whereClause.to_branch_id = req.user.branch_id;
            console.log(`Filtering stock movements for ${req.user.role_name} to branch ${req.user.branch_id}`);
          } else {
            // For other roles without a branch_id, just show transfers to their branch
            whereClause.to_branch_id = req.user.branch_id;
          }
        }
      } catch (err) {
        // Fallback if Op.or is not available
        console.log('Error using Op.or, falling back to direct branch filter:', err.message);
        whereClause.to_branch_id = req.user.branch_id;
        console.log(`Filtering stock movements for ${req.user.role_name} to branch ${req.user.branch_id}`);
      }
    } else {
      console.log(`Showing all stock movements for ${req.user.role_name}`);

      // Apply optional filters for company admins
      if (from_branch_id) {
        whereClause.from_branch_id = from_branch_id;
      } else if (from_hq === 'true') {
        // Get HQ branch
        const hqBranch = await Branch.findOne({
          where: { level: 0 }
        });

        if (hqBranch) {
          whereClause.from_branch_id = hqBranch.id;
        }
      }

      if (to_branch_id) {
        whereClause.to_branch_id = to_branch_id;
      }
    }

    if (status) {
      whereClause.status = status;
    }

    // Add date filters with safeguards for Op
    if (start_date && end_date) {
      if (Op && typeof Op === 'object' && Op.between) {
        whereClause.initiated_at = {
          [Op.between]: [new Date(start_date), new Date(end_date)]
        };
      } else {
        // Fallback to direct comparison if Op.between is not available
        console.log('Op.between is not available, using direct date comparison');
        whereClause.initiated_at = {
          $gte: new Date(start_date),
          $lte: new Date(end_date)
        };
      }
    } else if (start_date) {
      if (Op && typeof Op === 'object' && Op.gte) {
        whereClause.initiated_at = {
          [Op.gte]: new Date(start_date)
        };
      } else {
        console.log('Op.gte is not available, using direct date comparison');
        whereClause.initiated_at = {
          $gte: new Date(start_date)
        };
      }
    } else if (end_date) {
      if (Op && typeof Op === 'object' && Op.lte) {
        whereClause.initiated_at = {
          [Op.lte]: new Date(end_date)
        };
      } else {
        console.log('Op.lte is not available, using direct date comparison');
        whereClause.initiated_at = {
          $lte: new Date(end_date)
        };
      }
    }

    // Add search functionality for reference number and notes
    if (search) {
      const searchTerm = search.trim();
      if (Op && typeof Op === 'object' && Op.or && Op.like) {
        // If we already have an OR condition from role-based filtering, we need to combine them
        if (whereClause[Op.or]) {
          // Combine existing OR condition with search OR condition using AND
          const existingOr = whereClause[Op.or];
          delete whereClause[Op.or];

          whereClause[Op.and] = [
            { [Op.or]: existingOr },
            {
              [Op.or]: [
                { reference_number: { [Op.like]: `%${searchTerm}%` } },
                { notes: { [Op.like]: `%${searchTerm}%` } }
              ]
            }
          ];
        } else {
          // No existing OR condition, just add search OR condition
          whereClause[Op.or] = [
            { reference_number: { [Op.like]: `%${searchTerm}%` } },
            { notes: { [Op.like]: `%${searchTerm}%` } }
          ];
        }
        console.log(`Searching stock movements by reference number or notes: "${searchTerm}"`);
      } else {
        // Fallback if Op is not available
        console.log('Op.or or Op.like is not available, using direct search on reference_number');
        whereClause.reference_number = { $like: `%${searchTerm}%` };
      }
    }

    // Calculate pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Get total count for pagination
    const totalCount = await StockMovement.count({ where: whereClause });
    const totalPages = Math.ceil(totalCount / parseInt(limit));

    console.log(`Stock movements query - Page: ${page}, Limit: ${limit}, Total: ${totalCount}, Pages: ${totalPages}`);
    console.log('Final where clause:', JSON.stringify(whereClause, null, 2));

    const stockMovements = await StockMovement.findAll({
      where: whereClause,
      include: [
        {
          model: Branch,
          as: 'FromBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: Branch,
          as: 'ToBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: Tenant,
          attributes: ['id', 'name']
        },
        {
          model: StockMovementItem,
          include: [
            {
              model: Product,
              attributes: ['id', 'name', 'sku', 'has_serial']
            }
          ]
        },
        {
          model: User,
          as: 'RequestedBy',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'CreatedBy',
          attributes: ['id', 'name', 'email']
        },
        {
          model: User,
          as: 'LastUpdatedBy',
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [['initiated_at', 'DESC']],
      limit: parseInt(limit),
      offset: offset
    });

    // Return paginated response
    res.json({
      data: stockMovements,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        totalPages: totalPages
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get stock movement by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getStockMovementById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const stockMovement = await StockMovement.findByPk(id, {
      include: [
        {
          model: Branch,
          as: 'FromBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: Branch,
          as: 'ToBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: Tenant,
          attributes: ['id', 'name']
        },
        {
          model: StockMovementItem,
          include: [
            {
              model: Product,
              attributes: ['id', 'name', 'sku', 'has_serial']
            }
          ]
        },
        {
          model: User,
          as: 'CreatedBy',
          attributes: ['id', 'name', 'email'],
          foreignKey: 'created_by'
        },
        {
          model: User,
          as: 'LastUpdatedBy',
          attributes: ['id', 'name', 'email'],
          foreignKey: 'last_updated_by'
        }
      ]
    });

    if (!stockMovement) {
      throw new AppError(404, 'Stock movement not found');
    }

    // Check if skip_role_filter parameter is provided
    const skip_role_filter = req.query.skip_role_filter === 'true';

    // Log user information for debugging
    console.log('User info for stock movement access:', {
      id: req.user?.id,
      role: req.user?.role_name,
      branch_id: req.user?.branch_id,
      movement_id: id,
      from_branch: stockMovement.from_branch_id,
      to_branch: stockMovement.to_branch_id,
      skip_role_filter
    });

    if (skip_role_filter) {
      console.log(`Skipping role-based filtering due to skip_role_filter parameter for movement ID: ${id}`);
      // Allow access when skip_role_filter is true, regardless of user role
    }
    // Role-based access control
    // Company admins and super_admins can see all stock movements
    // Branch managers and other users can only see stock movements from or to their branch
    else if (!req.user) {
      // If user is not authenticated, return 401 Unauthorized
      console.log('User not authenticated, returning 401');
      throw new AppError(401, 'Authentication required');
    }
    else if (req.user.role_name !== 'super_admin' &&
        req.user.role_name !== 'company_admin' &&
        req.user.role_name !== 'tenant_admin' &&
        req.user.role_name !== 'hq_admin') {
      // For branch managers, branch admins, and all other users, only allow access to stock movements related to their branch
      if (stockMovement.from_branch_id !== req.user.branch_id && stockMovement.to_branch_id !== req.user.branch_id) {
        console.log(`Access denied: ${req.user.role_name} (${req.user.branch_id}) trying to access stock movement for branches ${stockMovement.from_branch_id} to ${stockMovement.to_branch_id}`);
        throw new AppError(403, 'You do not have permission to view this stock movement');
      } else {
        console.log(`Access granted: ${req.user.role_name} (${req.user.branch_id}) accessing stock movement for branches ${stockMovement.from_branch_id} to ${stockMovement.to_branch_id}`);
      }
    } else {
      console.log(`${req.user.role_name} accessing stock movement ID: ${id}`);
    }

    res.json(stockMovement);
  } catch (error) {
    next(error);
  }
};

/**
 * Create a new stock movement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createStockMovement = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    let {
      tenant_id,
      from_branch_id,
      to_branch_id,
      notes,
      items
    } = req.body;

    // Set default tenant_id to 1 if not provided
    if (!tenant_id) {
      console.log('No tenant_id provided in request, defaulting to 1');
      tenant_id = 1;
    }

    // Validate required fields
    if (!to_branch_id || !items || !items.length) {
      throw new AppError(400, 'Destination branch ID and items are required');
    }

    // Check if tenant exists
    const tenant = await Tenant.findByPk(tenant_id);
    if (!tenant) {
      throw new AppError(404, 'Tenant not found');
    }

    // Check if destination branch exists
    const toBranch = await Branch.findByPk(to_branch_id);
    if (!toBranch) {
      throw new AppError(404, 'Destination branch not found');
    }

    // Check if source branch exists if provided
    if (from_branch_id) {
      const fromBranch = await Branch.findByPk(from_branch_id);
      if (!fromBranch) {
        throw new AppError(404, 'Source branch not found');
      }

      // Check if source and destination branches are different
      if (from_branch_id === to_branch_id) {
        throw new AppError(400, 'Source and destination branches must be different');
      }
    }

    // Validate items
    for (const item of items) {
      // Handle both quantity_sent and requested_quantity formats
      const quantity = item.quantity_sent || item.requested_quantity;

      // Update the item to have quantity_sent if it doesn't already
      if (!item.quantity_sent && item.requested_quantity) {
        item.quantity_sent = item.requested_quantity;
      }

      if (!item.product_id || !quantity || quantity <= 0) {
        throw new AppError(400, 'Each item must have a product ID and a positive quantity');
      }

      // Check if product exists
      const product = await Product.findByPk(item.product_id);
      if (!product) {
        throw new AppError(404, `Product with ID ${item.product_id} not found`);
      }

      // If from_branch_id is provided, check if there's enough stock
      if (from_branch_id) {
        const stockItem = await StockItem.findOne({
          where: {
            branch_id: from_branch_id,
            product_id: item.product_id
          }
        });

        // Use the quantity_sent field which we've ensured is set correctly above
        if (!stockItem || stockItem.quantity < item.quantity_sent) {
          throw new AppError(400, `Insufficient stock for product ${product.name} in source branch`);
        }
      }
    }

    // Import the helper function for generating reference numbers
    const { generateReferenceNumber } = require('../utils/helpers');

    // Generate a reference number
    const reference_number = generateReferenceNumber('REQ');

    // Get the current user ID or use a default if not available
    const userId = req.user?.id;

    // If user ID is not available, find a default user
    if (!userId) {
      console.warn('No user ID available in request, attempting to find a default user');

      // Try to find a valid user (e.g., an admin user)
      const User = require('../models').User;
      const validUser = await User.findOne({
        where: {
          role_id: [1, 2, 11], // Admin role IDs (super_admin, tenant_admin, company_admin)
          deleted_at: null
        }
      });

      if (!validUser) {
        throw new AppError(400, 'No valid user found for creating stock movement');
      }

      console.log(`Using default user ID ${validUser.id} for stock movement creation`);
      req.user = { id: validUser.id };
    }

    // Create stock movement
    const stockMovement = await StockMovement.create({
      tenant_id,
      reference_number,
      from_branch_id,
      to_branch_id,
      status: 'REQUESTED',
      initiated_at: new Date(),
      requested_by: req.user.id,
      request_date: new Date(),
      notes,
      created_by: req.user.id,
      last_updated_by: req.user.id
    }, { transaction });

    // Create stock movement items
    const stockMovementItems = [];
    for (const item of items) {
      // We've already ensured item.quantity_sent is set correctly in the validation step
      // Log the item data for debugging
      console.log('Creating stock movement item with data:', {
        product_id: item.product_id,
        quantity_sent: item.quantity_sent,
        requested_quantity: item.requested_quantity
      });

      const stockMovementItem = await StockMovementItem.create({
        stock_movement_id: stockMovement.id,
        product_id: item.product_id,
        requested_quantity: item.requested_quantity || item.quantity_sent, // Use either field, ensuring one is set
        quantity_sent: item.quantity_sent,
        quantity_received: null,
        notes: item.notes,
        created_by: req.user.id,
        last_updated_by: req.user.id
      }, { transaction });

      stockMovementItems.push(stockMovementItem);

      // If from_branch_id is provided, check if there's enough stock but don't decrement yet
      if (from_branch_id) {
        // Get the source stock item
        const sourceStockItem = await StockItem.findOne({
          where: {
            branch_id: from_branch_id,
            product_id: item.product_id
          },
          transaction
        });

        if (!sourceStockItem) {
          throw new AppError(404, `Stock item for product ID ${item.product_id} not found in source branch`);
        }

        // Check if there's enough stock
        if (sourceStockItem.quantity < item.quantity_sent) {
          throw new AppError(400, `Insufficient stock for product ID ${item.product_id} in source branch. Available: ${sourceStockItem.quantity}, Requested: ${item.quantity_sent}`);
        }

        // Note: Stock will be decremented when the movement status is changed to IN_TRANSIT
      }
      // If this is a movement from HQ (from_branch_id is null), check HQ stock
      else {
        // Get HQ branch
        const hqBranch = await Branch.findOne({
          where: { level: 0 },
          transaction
        });

        if (!hqBranch) {
          throw new AppError(404, 'HQ branch not found');
        }

        // Check if HQ has enough stock
        const hqStockItem = await StockItem.findOne({
          where: {
            branch_id: hqBranch.id,
            product_id: item.product_id
          },
          transaction
        });

        if (!hqStockItem) {
          throw new AppError(404, `HQ stock item for product ID ${item.product_id} not found`);
        }

        if (hqStockItem.quantity < item.quantity_sent) {
          throw new AppError(400, `Insufficient HQ stock for product ID ${item.product_id}. Available: ${hqStockItem.quantity}, Requested: ${item.quantity_sent}`);
        }

        // Note: Stock will be decremented when the movement status is changed to IN_TRANSIT

        // Update from_branch_id to HQ branch ID
        if (!stockMovement.from_branch_id) {
          await stockMovement.update({ from_branch_id: hqBranch.id }, { transaction });
        }
      }
    }

    await transaction.commit();

    // Fetch the complete stock movement with associations
    const completeStockMovement = await StockMovement.findByPk(stockMovement.id, {
      include: [
        {
          model: Branch,
          as: 'FromBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: Branch,
          as: 'ToBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: StockMovementItem,
          include: [
            {
              model: Product,
              attributes: ['id', 'name', 'sku', 'has_serial']
            }
          ]
        }
      ]
    });

    res.status(201).json(completeStockMovement);
  } catch (error) {
    await transaction.rollback();
    next(error);
  }
};

/**
 * Update stock movement status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateStockMovementStatus = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { status, notes } = req.body;

    // Validate status
    const validStatuses = ['pending', 'in_transit', 'received', 'cancelled'];
    if (!status || !validStatuses.includes(status)) {
      throw new AppError(400, 'Valid status is required (pending, in_transit, received, cancelled)');
    }

    // Find stock movement
    const stockMovement = await StockMovement.findByPk(id, {
      include: [
        {
          model: StockMovementItem
        }
      ]
    });

    if (!stockMovement) {
      throw new AppError(404, 'Stock movement not found');
    }

    // Check if status transition is valid
    const currentStatus = stockMovement.status;

    if (currentStatus === 'cancelled') {
      throw new AppError(400, 'Cannot update a cancelled stock movement');
    }

    if (currentStatus === 'received' && status !== 'received') {
      throw new AppError(400, 'Cannot change status of a received stock movement');
    }

    // Handle status transition
    if (status === 'received' && currentStatus !== 'received') {
      // Mark as received and update destination branch stock
      stockMovement.status = 'received';
      stockMovement.completed_at = new Date();

      // Update destination branch stock
      for (const item of stockMovement.StockMovementItems) {
        // Find or create stock item in destination branch
        let stockItem = await StockItem.findOne({
          where: {
            branch_id: stockMovement.to_branch_id,
            product_id: item.product_id
          }
        });

        if (stockItem) {
          // Update existing stock item
          await stockItem.increment('quantity', {
            by: item.quantity_sent,
            transaction
          });
        } else {
          // Create new stock item
          stockItem = await StockItem.create({
            branch_id: stockMovement.to_branch_id,
            product_id: item.product_id,
            quantity: item.quantity_sent,
            created_by: req.user.id,
            last_updated_by: req.user.id
          }, { transaction });
        }

        // Note: Inventory transaction is created by database trigger
        // No need to manually create inventory transaction here

        // Update received quantity
        await item.update({
          quantity_received: item.quantity_sent,
          last_updated_by: req.user.id
        }, { transaction });
      }
    } else if (status === 'CANCELLED' && currentStatus !== 'CANCELLED') {
      // Cancel stock movement and restore source stock if applicable
      stockMovement.status = 'CANCELLED';

      // If there was a source branch, restore its stock
      if (stockMovement.from_branch_id) {
        for (const item of stockMovement.StockMovementItems) {
          await StockItem.increment(
            { quantity: item.quantity_sent },
            {
              where: {
                branch_id: stockMovement.from_branch_id,
                product_id: item.product_id
              },
              transaction
            }
          );
        }
      }
      // If this was a movement from HQ, restore HQ stock
      else {
        // Get HQ branch
        const hqBranch = await Branch.findOne({
          where: { level: 0 },
          transaction
        });

        if (!hqBranch) {
          throw new AppError(404, 'HQ branch not found');
        }

        for (const item of stockMovement.StockMovementItems) {
          // Find HQ stock item
          const hqStockItem = await StockItem.findOne({
            where: {
              branch_id: hqBranch.id,
              product_id: item.product_id
            },
            transaction
          });

          if (hqStockItem) {
            // Restore HQ stock
            await hqStockItem.increment('quantity', {
              by: item.quantity_sent,
              transaction
            });
          } else {
            // If HQ stock item doesn't exist, create it
            await StockItem.create({
              branch_id: hqBranch.id,
              product_id: item.product_id,
              quantity: item.quantity_sent,
              tenant_id: stockMovement.tenant_id,
              created_by: req.user.id,
              last_updated_by: req.user.id
            }, { transaction });
          }
        }
      }
    } else if (status === 'IN_TRANSIT' && currentStatus === 'REQUESTED') {
      // Update status to IN_TRANSIT
      stockMovement.status = 'IN_TRANSIT';
      stockMovement.shipped_at = new Date();

      // Now that the stock is in transit, decrement the source branch stock
      if (stockMovement.from_branch_id) {
        console.log(`Decrementing stock from branch ${stockMovement.from_branch_id} for stock movement ${stockMovement.id}`);

        for (const item of stockMovement.StockMovementItems) {
          // Get the source stock item
          const sourceStockItem = await StockItem.findOne({
            where: {
              branch_id: stockMovement.from_branch_id,
              product_id: item.product_id
            },
            transaction
          });

          if (!sourceStockItem) {
            throw new AppError(404, `Stock item for product ID ${item.product_id} not found in source branch`);
          }

          // Check if there's still enough stock (in case it was reduced by other operations)
          if (sourceStockItem.quantity < item.quantity_sent) {
            throw new AppError(400, `Insufficient stock for product ID ${item.product_id} in source branch. Available: ${sourceStockItem.quantity}, Requested: ${item.quantity_sent}`);
          }

          // Decrement source stock
          await sourceStockItem.decrement('quantity', {
            by: item.quantity_sent,
            transaction
          });

          console.log(`Decremented ${item.quantity_sent} units of product ${item.product_id} from branch ${stockMovement.from_branch_id}`);
        }
      }
    } else {
      // Simple status update - make sure status is uppercase to match ENUM values
      stockMovement.status = status.toUpperCase();
    }

    // Update notes if provided
    if (notes) {
      stockMovement.notes = notes;
    }

    stockMovement.last_updated_by = req.user.id;
    await stockMovement.save({ transaction });

    await transaction.commit();

    // Fetch the updated stock movement with associations
    const updatedStockMovement = await StockMovement.findByPk(id, {
      include: [
        {
          model: Branch,
          as: 'FromBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: Branch,
          as: 'ToBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: StockMovementItem,
          include: [
            {
              model: Product,
              attributes: ['id', 'name', 'sku', 'has_serial']
            }
          ]
        }
      ]
    });

    res.json(updatedStockMovement);
  } catch (error) {
    await transaction.rollback();
    next(error);
  }
};

/**
 * Update stock movement items received quantities
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateStockMovementItems = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { items } = req.body;

    if (!items || !Array.isArray(items) || !items.length) {
      throw new AppError(400, 'Items array is required');
    }

    // Find stock movement
    const stockMovement = await StockMovement.findByPk(id, {
      include: [
        {
          model: StockMovementItem
        }
      ]
    });

    if (!stockMovement) {
      throw new AppError(404, 'Stock movement not found');
    }

    // Check if stock movement is in a valid state for updating items
    if (stockMovement.status === 'cancelled') {
      throw new AppError(400, 'Cannot update items of a cancelled stock movement');
    }

    if (stockMovement.status === 'received') {
      throw new AppError(400, 'Cannot update items of a received stock movement');
    }

    // Validate and update items
    for (const item of items) {
      if (!item.id || !item.quantity_received) {
        throw new AppError(400, 'Each item must have an ID and received quantity');
      }

      // Find the stock movement item
      const stockMovementItem = stockMovement.StockMovementItems.find(
        smi => smi.id === item.id
      );

      if (!stockMovementItem) {
        throw new AppError(404, `Stock movement item with ID ${item.id} not found`);
      }

      // Update the item
      await stockMovementItem.update({
        quantity_received: item.quantity_received,
        notes: item.notes || stockMovementItem.notes,
        last_updated_by: req.user.id
      }, { transaction });
    }

    // Update the stock movement
    await stockMovement.update({
      last_updated_by: req.user.id
    }, { transaction });

    await transaction.commit();

    // Fetch the updated stock movement with associations
    const updatedStockMovement = await StockMovement.findByPk(id, {
      include: [
        {
          model: Branch,
          as: 'FromBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: Branch,
          as: 'ToBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: StockMovementItem,
          include: [
            {
              model: Product,
              attributes: ['id', 'name', 'sku', 'has_serial']
            }
          ]
        }
      ]
    });

    res.json(updatedStockMovement);
  } catch (error) {
    await transaction.rollback();
    next(error);
  }
};

/**
 * Receive stock movement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const receiveStockMovement = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { items, notes } = req.body;
    const skip_role_filter = req.query.skip_role_filter === 'true';

    console.log(`Receiving stock movement ID: ${id}`, {
      user_id: req.user?.id,
      user_role: req.user?.role_name,
      user_branch: req.user?.branch_id,
      items_count: items?.length || 0,
      skip_role_filter
    });

    // Log the request body for debugging
    console.log('Request body for receiving stock movement:', {
      id,
      items: items?.map(item => ({
        id: item.id,
        received_quantity: item.received_quantity
      })),
      notes
    });

    // Find stock movement
    const stockMovement = await StockMovement.findByPk(id, {
      include: [
        {
          model: StockMovementItem,
          include: [
            {
              model: Product
            }
          ]
        }
      ]
    });

    if (!stockMovement) {
      console.error(`Stock movement with ID ${id} not found`);
      throw new AppError(404, 'Stock movement not found');
    }

    console.log('Found stock movement:', {
      id: stockMovement.id,
      status: stockMovement.status,
      from_branch_id: stockMovement.from_branch_id,
      to_branch_id: stockMovement.to_branch_id,
      items_count: stockMovement.StockMovementItems?.length || 0
    });

    // Check if stock movement is in a valid state for receiving
    if (stockMovement.status === 'CANCELLED') {
      console.error(`Cannot receive cancelled stock movement with ID ${id}`);
      throw new AppError(400, 'Cannot receive a cancelled stock movement');
    }

    if (stockMovement.status === 'FULLY_RECEIVED' || stockMovement.status === 'PARTIALLY_RECEIVED') {
      console.error(`Stock movement with ID ${id} has already been received`);
      throw new AppError(400, 'Stock movement has already been received');
    }

    // Check if user has permission to receive this stock movement
    if (!skip_role_filter && req.user) {
      // Allow branch_manager to receive stock transfers for their branch
      if (req.user.role_name === 'branch_manager' || req.user.role_name === 'branch_admin') {
        // Branch managers can only receive transfers for their own branch
        if (stockMovement.to_branch_id !== req.user.branch_id) {
          console.error(`Branch manager ${req.user.id} (${req.user.role_name}) with branch ${req.user.branch_id} attempted to receive stock movement for branch ${stockMovement.to_branch_id}`);
          throw new AppError(403, 'You can only receive stock transfers for your own branch');
        }
        // If it's for their branch, they're allowed to receive it
        console.log(`Branch manager ${req.user.id} (${req.user.role_name}) receiving stock movement for their branch ${req.user.branch_id}`);
      }
      // For other roles, check if they have admin permissions or are receiving for their branch
      else if (req.user.role_name !== 'super_admin' &&
          req.user.role_name !== 'company_admin' &&
          req.user.role_name !== 'tenant_admin' &&
          req.user.role_name !== 'hq_admin' &&
          stockMovement.to_branch_id !== req.user.branch_id) {
        console.error(`User ${req.user.id} (${req.user.role_name}) with branch ${req.user.branch_id} attempted to receive stock movement for branch ${stockMovement.to_branch_id}`);
        throw new AppError(403, 'You do not have permission to receive this stock movement');
      }
    }

    // Process received items
    if (items && Array.isArray(items)) {
      for (const item of items) {
        if (!item.id || item.quantity_received === undefined) {
          throw new AppError(400, 'Each item must have an ID and received quantity');
        }

        // Find the stock movement item
        const stockMovementItem = stockMovement.StockMovementItems.find(
          smi => smi.id === item.id
        );

        if (!stockMovementItem) {
          throw new AppError(404, `Stock movement item with ID ${item.id} not found`);
        }

        // Update the item
        await stockMovementItem.update({
          quantity_received: item.quantity_received,
          notes: item.notes || stockMovementItem.notes,
          last_updated_by: req.user.id
        }, { transaction });

        // Find or create stock item in destination branch
        let stockItem = await StockItem.findOne({
          where: {
            branch_id: stockMovement.to_branch_id,
            product_id: stockMovementItem.product_id
          }
        });

        // Find source stock item to get pricing information
        let sourceStockItem = null;
        if (stockMovement.from_branch_id) {
          sourceStockItem = await StockItem.findOne({
            where: {
              branch_id: stockMovement.from_branch_id,
              product_id: stockMovementItem.product_id
            }
          });
        } else {
          // If from HQ, get HQ branch
          const hqBranch = await Branch.findOne({
            where: { level: 0 },
            transaction
          });

          if (hqBranch) {
            sourceStockItem = await StockItem.findOne({
              where: {
                branch_id: hqBranch.id,
                product_id: stockMovementItem.product_id
              }
            });
          }
        }

        // Get pricing information from source stock item
        const pricingInfo = sourceStockItem ? {
          default_selling_price: sourceStockItem.default_selling_price,
          default_buying_price: sourceStockItem.default_buying_price,
          default_wholesale_price: sourceStockItem.default_wholesale_price,
          buying_price_including_vat: sourceStockItem.buying_price_including_vat,
          buying_price_excluding_vat: sourceStockItem.buying_price_excluding_vat,
          buying_vat_amount: sourceStockItem.buying_vat_amount,
          buying_vat_rate: sourceStockItem.buying_vat_rate
        } : {};

        console.log(`Pricing info for product ${stockMovementItem.product_id}:`, pricingInfo);

        if (stockItem) {
          // Update existing stock item
          await stockItem.increment('quantity', {
            by: item.quantity_received,
            transaction
          });

          // Update pricing information if available
          if (Object.keys(pricingInfo).length > 0) {
            await stockItem.update(pricingInfo, { transaction });
          }
        } else {
          // Create new stock item with pricing information
          await StockItem.create({
            branch_id: stockMovement.to_branch_id,
            product_id: stockMovementItem.product_id,
            quantity: item.quantity_received,
            ...pricingInfo,
            created_by: req.user.id,
            last_updated_by: req.user.id
          }, { transaction });
        }
      }
    } else {
      // If no items provided, assume all items received in full
      for (const stockMovementItem of stockMovement.StockMovementItems) {
        // Update the item
        await stockMovementItem.update({
          quantity_received: stockMovementItem.quantity_sent,
          last_updated_by: req.user.id
        }, { transaction });

        // Find or create stock item in destination branch
        let stockItem = await StockItem.findOne({
          where: {
            branch_id: stockMovement.to_branch_id,
            product_id: stockMovementItem.product_id
          }
        });

        // Find source stock item to get pricing information
        let sourceStockItem = null;
        if (stockMovement.from_branch_id) {
          sourceStockItem = await StockItem.findOne({
            where: {
              branch_id: stockMovement.from_branch_id,
              product_id: stockMovementItem.product_id
            }
          });
        } else {
          // If from HQ, get HQ branch
          const hqBranch = await Branch.findOne({
            where: { level: 0 },
            transaction
          });

          if (hqBranch) {
            sourceStockItem = await StockItem.findOne({
              where: {
                branch_id: hqBranch.id,
                product_id: stockMovementItem.product_id
              }
            });
          }
        }

        // Get pricing information from source stock item
        const pricingInfo = sourceStockItem ? {
          default_selling_price: sourceStockItem.default_selling_price,
          default_buying_price: sourceStockItem.default_buying_price,
          default_wholesale_price: sourceStockItem.default_wholesale_price,
          buying_price_including_vat: sourceStockItem.buying_price_including_vat,
          buying_price_excluding_vat: sourceStockItem.buying_price_excluding_vat,
          buying_vat_amount: sourceStockItem.buying_vat_amount,
          buying_vat_rate: sourceStockItem.buying_vat_rate
        } : {};

        console.log(`Pricing info for product ${stockMovementItem.product_id} (auto-receive):`, pricingInfo);

        if (stockItem) {
          // Update existing stock item
          await stockItem.increment('quantity', {
            by: stockMovementItem.quantity_sent,
            transaction
          });

          // Update pricing information if available
          if (Object.keys(pricingInfo).length > 0) {
            await stockItem.update(pricingInfo, { transaction });
          }
        } else {
          // Create new stock item with pricing information
          await StockItem.create({
            branch_id: stockMovement.to_branch_id,
            product_id: stockMovementItem.product_id,
            quantity: stockMovementItem.quantity_sent,
            ...pricingInfo,
            created_by: req.user.id,
            last_updated_by: req.user.id
          }, { transaction });
        }
      }
    }

    // If this was a branch-to-branch transfer (not from HQ), update the source branch's stock
    // to reflect any discrepancies between sent and received quantities
    if (stockMovement.from_branch_id) {
      for (const stockMovementItem of stockMovement.StockMovementItems) {
        const receivedQty = items && Array.isArray(items)
          ? (items.find(i => i.id === stockMovementItem.id)?.quantity_received || stockMovementItem.quantity_sent)
          : stockMovementItem.quantity_sent;

        // If received quantity is less than sent quantity, adjust the source branch stock
        if (receivedQty < stockMovementItem.quantity_sent) {
          const difference = stockMovementItem.quantity_sent - receivedQty;

          // Add the difference back to the source branch
          await StockItem.increment(
            { quantity: difference },
            {
              where: {
                branch_id: stockMovement.from_branch_id,
                product_id: stockMovementItem.product_id
              },
              transaction
            }
          );
        }
      }
    }

    // Check if all items are fully received
    const isPartiallyReceived = stockMovement.StockMovementItems.some(item => {
      // If no items were provided in the request, we're using the default behavior (all items fully received)
      if (!items || !Array.isArray(items)) {
        return false;
      }

      const receivedItem = items.find(ri => ri.id === item.id);

      // If this item wasn't in the received items list or the quantity is less than dispatched
      if (!receivedItem) {
        return true;
      }

      const dispatchedQty = item.dispatched_quantity || item.quantity_sent || 0;
      return receivedItem.quantity_received < dispatchedQty;
    });

    // Log the status determination
    console.log('Stock movement receipt status determination:', {
      id: stockMovement.id,
      isPartiallyReceived,
      status: isPartiallyReceived ? 'PARTIALLY_RECEIVED' : 'FULLY_RECEIVED',
      items_count: items?.length || 0,
      stockMovementItems_count: stockMovement.StockMovementItems?.length || 0
    });

    // Update stock movement with appropriate status
    await stockMovement.update({
      status: isPartiallyReceived ? 'PARTIALLY_RECEIVED' : 'FULLY_RECEIVED',
      completed_at: new Date(),
      notes: notes || stockMovement.notes,
      last_updated_by: req.user.id
    }, { transaction });

    await transaction.commit();

    // Fetch the updated stock movement with associations
    const updatedStockMovement = await StockMovement.findByPk(id, {
      include: [
        {
          model: Branch,
          as: 'FromBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: Branch,
          as: 'ToBranch',
          attributes: ['id', 'name', 'location']
        },
        {
          model: StockMovementItem,
          include: [
            {
              model: Product,
              attributes: ['id', 'name', 'sku', 'has_serial']
            }
          ]
        }
      ]
    });

    res.json(updatedStockMovement);
  } catch (error) {
    await transaction.rollback();
    next(error);
  }
};

/**
 * Delete a stock movement
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteStockMovement = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    // Find stock movement
    const stockMovement = await StockMovement.findByPk(id, {
      include: [
        {
          model: StockMovementItem
        }
      ]
    });

    if (!stockMovement) {
      throw new AppError(404, 'Stock movement not found');
    }

    // Check if stock movement is in a valid state for deletion
    if (stockMovement.status === 'FULLY_RECEIVED' || stockMovement.status === 'PARTIALLY_RECEIVED') {
      throw new AppError(400, 'Cannot delete a received stock movement');
    }

    // If stock movement is not cancelled, restore source stock
    if (stockMovement.status !== 'CANCELLED') {
      // If it has a source branch, restore branch stock
      if (stockMovement.from_branch_id) {
        for (const item of stockMovement.StockMovementItems) {
          await StockItem.increment(
            { quantity: item.quantity_sent },
            {
              where: {
                branch_id: stockMovement.from_branch_id,
                product_id: item.product_id
              },
              transaction
            }
          );
        }
      }
      // If it's from HQ, restore HQ stock
      else {
        // Get HQ branch
        const hqBranch = await Branch.findOne({
          where: { level: 0 },
          transaction
        });

        if (!hqBranch) {
          throw new AppError(404, 'HQ branch not found');
        }

        for (const item of stockMovement.StockMovementItems) {
          // Find HQ stock item
          const hqStockItem = await StockItem.findOne({
            where: {
              branch_id: hqBranch.id,
              tenant_id: stockMovement.tenant_id,
              product_id: item.product_id
            },
            transaction
          });

          if (hqStockItem) {
            // Restore HQ stock
            await hqStockItem.increment('quantity', {
              by: item.quantity_sent,
              transaction
            });
          } else {
            // If HQ stock item doesn't exist, create it
            await StockItem.create({
              branch_id: hqBranch.id,
              tenant_id: stockMovement.tenant_id,
              product_id: item.product_id,
              quantity: item.quantity_sent,
              created_by: req.user.id,
              last_updated_by: req.user.id
            }, { transaction });
          }
        }
      }
    }

    // Delete stock movement items
    await StockMovementItem.destroy({
      where: { stock_movement_id: id },
      transaction
    });

    // Delete stock movement
    await stockMovement.destroy({ transaction });

    await transaction.commit();

    res.json({ message: 'Stock movement deleted successfully' });
  } catch (error) {
    await transaction.rollback();
    next(error);
  }
};

/**
 * Get HQ stock movement statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getHqStockMovementStats = async (req, res, next) => {
  try {
    const { tenant_id, start_date, end_date } = req.query;

    // Get HQ branch
    const hqBranch = await Branch.findOne({
      where: { level: 0 }
    });

    if (!hqBranch) {
      throw new AppError(404, 'HQ branch not found');
    }

    const whereClause = {
      from_branch_id: hqBranch.id // Only HQ movements
    };

    if (tenant_id) {
      whereClause.tenant_id = tenant_id;
    }

    // Add date filters if provided
    if (start_date && end_date) {
      whereClause.initiated_at = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      whereClause.initiated_at = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      whereClause.initiated_at = {
        [Op.lte]: new Date(end_date)
      };
    }

    // Get total count of HQ movements
    const totalCount = await StockMovement.count({
      where: whereClause
    });

    // Get count by status
    const pendingCount = await StockMovement.count({
      where: { ...whereClause, status: 'REQUESTED' }
    });

    const inTransitCount = await StockMovement.count({
      where: { ...whereClause, status: 'IN_TRANSIT' }
    });

    const receivedCount = await StockMovement.count({
      where: {
        ...whereClause,
        status: {
          [Op.or]: ['FULLY_RECEIVED', 'PARTIALLY_RECEIVED']
        }
      }
    });

    const cancelledCount = await StockMovement.count({
      where: { ...whereClause, status: 'CANCELLED' }
    });

    // Get total quantity sent from HQ
    const totalQuantitySent = await StockMovementItem.sum('dispatched_quantity', {
      include: [{
        model: StockMovement,
        where: whereClause
      }]
    }) || 0;

    // Get total quantity received at branches
    const totalQuantityReceived = await StockMovementItem.sum('received_quantity', {
      include: [{
        model: StockMovement,
        where: {
          ...whereClause,
          status: {
            [Op.or]: ['FULLY_RECEIVED', 'PARTIALLY_RECEIVED']
          }
        }
      }]
    }) || 0;

    // Get top branches receiving from HQ
    const topBranches = await StockMovement.findAll({
      attributes: [
        'to_branch_id',
        [sequelize.fn('COUNT', sequelize.col('StockMovement.id')), 'movement_count']
      ],
      where: whereClause,
      include: [{
        model: Branch,
        as: 'ToBranch',
        attributes: ['id', 'name', 'location']
      }],
      group: ['to_branch_id'],
      order: [[sequelize.fn('COUNT', sequelize.col('StockMovement.id')), 'DESC']],
      limit: 5
    });

    // Get top products moved from HQ
    const topProducts = await StockMovementItem.findAll({
      attributes: [
        'product_id',
        [sequelize.fn('SUM', sequelize.col('dispatched_quantity')), 'total_quantity']
      ],
      include: [{
        model: StockMovement,
        where: whereClause,
        attributes: []
      }, {
        model: Product,
        attributes: ['id', 'name', 'sku']
      }],
      group: ['product_id'],
      order: [[sequelize.fn('SUM', sequelize.col('dispatched_quantity')), 'DESC']],
      limit: 5
    });

    res.json({
      total_movements: totalCount,
      status_breakdown: {
        pending: pendingCount,
        in_transit: inTransitCount,
        received: receivedCount,
        cancelled: cancelledCount
      },
      total_quantity_sent: totalQuantitySent,
      total_quantity_received: totalQuantityReceived,
      discrepancy: totalQuantitySent - totalQuantityReceived,
      top_branches: topBranches,
      top_products: topProducts
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllStockMovements,
  getStockMovementById,
  createStockMovement,
  updateStockMovementStatus,
  updateStockMovementItems,
  receiveStockMovement,
  deleteStockMovement,
  getHqStockMovementStats
};
