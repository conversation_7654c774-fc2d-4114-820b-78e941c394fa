"use client";

import { <PERSON><PERSON><PERSON><PERSON>gle, Clock, Filter, Zap } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";

interface ExportPerformanceNoticeProps {
  totalRecords: number;
  hasDateFilter: boolean;
  className?: string;
}

export function ExportPerformanceNotice({ 
  totalRecords, 
  hasDateFilter, 
  className = "" 
}: ExportPerformanceNoticeProps) {
  // Don't show notice for small datasets
  if (totalRecords <= 1000) {
    return null;
  }

  const getPerformanceLevel = () => {
    if (totalRecords <= 3000) return "good";
    if (totalRecords <= 5000) return "moderate";
    return "slow";
  };

  const performanceLevel = getPerformanceLevel();

  const getRecommendations = () => {
    const recommendations = [];
    
    if (!hasDateFilter) {
      recommendations.push("Add date filters to reduce data size");
    }
    
    if (totalRecords > 5000) {
      recommendations.push("Use Summary Export for faster processing");
      recommendations.push("Export data in smaller date ranges");
    }
    
    if (totalRecords > 3000) {
      recommendations.push("Consider Custom Export with fewer sheets");
    }

    return recommendations;
  };

  const recommendations = getRecommendations();

  if (recommendations.length === 0) {
    return null;
  }

  return (
    <Alert className={`${className} ${
      performanceLevel === "slow" ? "border-red-200 bg-red-50" :
      performanceLevel === "moderate" ? "border-yellow-200 bg-yellow-50" :
      "border-blue-200 bg-blue-50"
    }`}>
      <div className="flex items-start gap-3">
        {performanceLevel === "slow" ? (
          <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
        ) : performanceLevel === "moderate" ? (
          <Clock className="h-5 w-5 text-yellow-600 mt-0.5" />
        ) : (
          <Zap className="h-5 w-5 text-blue-600 mt-0.5" />
        )}
        
        <div className="flex-1">
          <AlertTitle className={`${
            performanceLevel === "slow" ? "text-red-900" :
            performanceLevel === "moderate" ? "text-yellow-900" :
            "text-blue-900"
          }`}>
            Export Performance Notice
          </AlertTitle>
          
          <AlertDescription className={`mt-1 ${
            performanceLevel === "slow" ? "text-red-800" :
            performanceLevel === "moderate" ? "text-yellow-800" :
            "text-blue-800"
          }`}>
            <div className="flex items-center gap-2 mb-2">
              <span>Dataset size: {totalRecords.toLocaleString()} records</span>
              <Badge variant={
                performanceLevel === "slow" ? "destructive" :
                performanceLevel === "moderate" ? "secondary" :
                "default"
              }>
                {performanceLevel === "slow" ? "Large" :
                 performanceLevel === "moderate" ? "Medium" :
                 "Manageable"}
              </Badge>
            </div>
            
            {performanceLevel === "slow" && (
              <p className="text-sm mb-2">
                <strong>Performance Impact:</strong> Exports may take 1-3 minutes and are limited to 3,000 most recent records.
              </p>
            )}
            
            {recommendations.length > 0 && (
              <div>
                <p className="text-sm font-medium mb-1">Recommendations for faster exports:</p>
                <ul className="text-sm space-y-1">
                  {recommendations.map((rec, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <div className="w-1 h-1 rounded-full bg-current" />
                      {rec}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </AlertDescription>
        </div>
      </div>
    </Alert>
  );
}
