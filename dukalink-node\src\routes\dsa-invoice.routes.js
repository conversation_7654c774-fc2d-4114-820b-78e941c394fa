const express = require('express');
const router = express.Router();
const dsaInvoiceController = require('../controllers/dsa-invoice.controller');
const enhancedDsaPaymentController = require("../controllers/enhanced-dsa-payment.controller");
const { authenticate, rbac } = require('../middleware/auth.middleware');

/**
 * @swagger
 * /dsa-invoices:
 *   get:
 *     summary: Get all DSA invoices
 *     tags: [DSA Invoices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         description: Filter by branch ID
 *       - in: query
 *         name: branch_only
 *         schema:
 *           type: boolean
 *         description: If true, only return invoices for the specified branch
 *     responses:
 *       200:
 *         description: List of DSA invoices
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authenticate, rbac.checkPermission('dsa_invoices', 'read'), dsaInvoiceController.getDsaInvoices);

// Enhanced DSA Invoice Routes (must come before the generic /:assignment_identifier route)
router.get('/enhanced', authenticate, rbac.checkPermission('dsa_invoices', 'read'), enhancedDsaPaymentController.getEnhancedInvoicesList);
router.get('/enhanced/:assignment_identifier', authenticate, rbac.checkPermission('dsa_invoices', 'read'), enhancedDsaPaymentController.getEnhancedInvoice);

/**
 * @swagger
 * /dsa-invoices/{assignment_identifier}:
 *   get:
 *     summary: Get a single DSA invoice by assignment identifier
 *     tags: [DSA Invoices]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: assignment_identifier
 *         required: true
 *         schema:
 *           type: string
 *         description: Assignment identifier
 *     responses:
 *       200:
 *         description: DSA invoice details
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Invoice not found
 *       500:
 *         description: Server error
 */
router.get('/:assignment_identifier', authenticate, rbac.checkPermission('dsa_invoices', 'read'), dsaInvoiceController.getDsaInvoiceById);

module.exports = router;

