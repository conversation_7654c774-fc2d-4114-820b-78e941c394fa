import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { toast } from 'sonner';
import { StockMovement, StockMovementItem, ReceiveStockMovementDto } from '../types';
import { formatDate } from '@/lib/utils';

const receiveItemSchema = z.object({
  id: z.number(),
  received_quantity: z.coerce.number().min(0, 'Received quantity must be a positive number'),
  batch_number: z.string().optional(),
  expiry_date: z.string().optional(),
});

const receiveSchema = z.object({
  items: z.array(receiveItemSchema),
});

type FormValues = z.infer<typeof receiveSchema>;

interface StockMovementReceiveFormProps {
  movement: StockMovement;
  items: StockMovementItem[];
  products: { id: number; name: string }[];
  onSubmit: (data: ReceiveStockMovementDto) => Promise<void>;
  isSubmitting: boolean;
}

export function StockMovementReceiveForm({
  movement,
  items,
  products,
  onSubmit,
  isSubmitting,
}: StockMovementReceiveFormProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(receiveSchema),
    defaultValues: {
      items: items.map((item) => ({
        id: item.id,
        received_quantity: item.dispatched_quantity || 0,
        batch_number: item.batch_number || '',
        expiry_date: item.expiry_date ? new Date(item.expiry_date).toISOString().split('T')[0] : '',
      })),
    },
  });

  const handleSubmit = async (data: FormValues) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Failed to receive stock movement');
    }
  };

  const getProductName = (productId: number) => {
    const product = products.find((p) => p.id === productId);
    return product ? product.name : 'Unknown Product';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Receive Stock Movement</CardTitle>
        <CardDescription>
          Reference: {movement.reference_number || 'N/A'} |
          Dispatched on: {formatDate(movement.dispatch_date || '')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="space-y-4">
              {items
                .filter((item) => (item.dispatched_quantity || 0) > 0)
                .map((item, index) => (
                  <div key={item.id} className="border p-4 rounded-md space-y-4">
                    <div className="flex justify-between">
                      <h3 className="font-medium">{getProductName(item.product_id)}</h3>
                      <span>Dispatched: {item.dispatched_quantity}</span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`items.${index}.received_quantity`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Received Quantity</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max={item.dispatched_quantity}
                                step="1"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Can be less than or equal to the dispatched quantity ({item.dispatched_quantity})
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`items.${index}.batch_number`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Order/Lot Number</FormLabel>
                            <FormControl>
                              <Input placeholder="ORDER-001" {...field} value={field.value || ''} />
                            </FormControl>
                            <FormDescription>
                              {item.batch_number ? `Dispatched with order: ${item.batch_number}` : 'Optional order or lot number'}
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`items.${index}.expiry_date`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Expiry Date</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} value={field.value || ''} />
                          </FormControl>
                          <FormDescription>
                            {item.expiry_date
                              ? `Dispatched with expiry: ${formatDate(item.expiry_date)}`
                              : 'Optional expiry date'}
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                ))}
            </div>

            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? 'Receiving...' : 'Receive Stock Movement'}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
