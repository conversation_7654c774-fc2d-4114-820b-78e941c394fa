const { DsaStockAssignment, DsaPayment, DsaStockReconciliation, Customer, Product } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');
const AppError = require('../utils/error');
const DsaPricingService = require('./DsaPricingService');

/**
 * Enhanced DSA Balance Service
 * Centralized service with consistent pricing and balance calculations
 */
class EnhancedDsaBalanceService {
  /**
   * Recalculate and update balance for a DSA assignment
   * This is the single source of truth for balance calculations
   * @param {string} assignmentIdentifier - Assignment identifier
   * @param {Object} transaction - Database transaction (optional)
   * @returns {Object} Updated balance information
   */
  static async recalculateBalance(assignmentIdentifier, transaction = null) {
    try {
      logger.info(`Recalculating balance for assignment: ${assignmentIdentifier}`);

      // Get all assignments for this identifier with product data
      const assignments = await DsaStockAssignment.findAll({
        where: { assignment_identifier: assignmentIdentifier },
        include: [
          {
            model: Product,
            attributes: ['id', 'name', 'default_wholesale_price', 'suggested_selling_price']
          }
        ],
        transaction
      });

      if (assignments.length === 0) {
        throw new AppError(404, 'No assignments found for the given identifier');
      }

      // Calculate total value using centralized pricing service
      const invoiceCalculation = DsaPricingService.calculateInvoiceTotal(assignments);

      // Get all payments for this assignment
      const payments = await DsaPayment.findAll({
        where: { assignment_identifier: assignmentIdentifier },
        transaction
      });

      const totalPaid = payments.reduce((sum, payment) =>
        sum + parseFloat(payment.cash_amount || 0) + parseFloat(payment.paybill_amount || 0), 0
      );

      // Calculate balance
      const balance = Math.max(0, invoiceCalculation.total_remaining_value - totalPaid);

      // Check if all items are returned
      const allItemsReturned = assignments.every(assignment => {
        const quantityAssigned = assignment.quantity_assigned || 0;
        const quantityReturned = assignment.quantity_returned || 0;
        return quantityReturned >= quantityAssigned;
      });

      // Determine payment status
      const paymentStatus = DsaPricingService.determinePaymentStatus(balance, totalPaid, allItemsReturned);

      // Update all assignment records with consistent values
      await DsaStockAssignment.update({
        total_amount: invoiceCalculation.total_remaining_value,
        amount_paid: totalPaid,
        balance: balance,
        payment_status: paymentStatus,
        last_payment_date: payments.length > 0 ? payments[0].payment_date : null
      }, {
        where: { assignment_identifier: assignmentIdentifier },
        transaction
      });

      const result = {
        assignment_identifier: assignmentIdentifier,
        total_assigned_value: invoiceCalculation.total_assigned_value,
        total_returned_value: invoiceCalculation.total_returned_value,
        total_remaining_value: invoiceCalculation.total_remaining_value,
        total_paid: totalPaid,
        balance: balance,
        payment_status: paymentStatus,
        all_items_returned: allItemsReturned,
        item_breakdown: invoiceCalculation.item_breakdown,
        payment_count: payments.length,
        last_updated: new Date()
      };

      logger.info(`Balance recalculated for ${assignmentIdentifier}: Balance=${balance}, Status=${paymentStatus}`);
      return result;

    } catch (error) {
      logger.error(`Error recalculating balance for ${assignmentIdentifier}:`, error);
      throw error;
    }
  }

  /**
   * Process a payment and update balances
   * @param {string} assignmentIdentifier - Assignment identifier
   * @param {number} paymentAmount - Total payment amount
   * @param {Object} paymentDetails - Payment breakdown (cash, mpesa)
   * @param {Object} transaction - Database transaction
   * @returns {Object} Updated balance information
   */
  static async processPayment(assignmentIdentifier, paymentAmount, paymentDetails, transaction = null) {
    try {
      logger.info(`Processing payment for assignment ${assignmentIdentifier}: Amount=${paymentAmount}`);

      // Get current balance before payment
      const currentBalance = await this.getCurrentBalance(assignmentIdentifier, transaction);

      // Validate payment amount
      const validation = DsaPricingService.validatePaymentAmount(
        paymentAmount,
        currentBalance.balance,
        { allowZeroPayment: currentBalance.all_items_returned }
      );

      if (!validation.isValid) {
        throw new AppError(400, `Payment validation failed: ${validation.errors.join(', ')}`);
      }

      // Log warnings if any
      if (validation.warnings.length > 0) {
        logger.warn(`Payment warnings for ${assignmentIdentifier}: ${validation.warnings.join(', ')}`);
      }

      // Create payment record (this should be done by the calling function)
      // We just recalculate the balance after payment is created
      const updatedBalance = await this.recalculateBalance(assignmentIdentifier, transaction);

      logger.info(`Payment processed successfully for ${assignmentIdentifier}`);
      return {
        ...updatedBalance,
        payment_validation: validation
      };

    } catch (error) {
      logger.error(`Error processing payment for ${assignmentIdentifier}:`, error);
      throw error;
    }
  }

  /**
   * Get current balance without updating records
   * @param {string} assignmentIdentifier - Assignment identifier
   * @param {Object} transaction - Database transaction (optional)
   * @returns {Object} Current balance information
   */
  static async getCurrentBalance(assignmentIdentifier, transaction = null) {
    try {
      // Get assignments with product data
      const assignments = await DsaStockAssignment.findAll({
        where: { assignment_identifier: assignmentIdentifier },
        include: [
          {
            model: Product,
            attributes: ['id', 'name', 'default_wholesale_price', 'suggested_selling_price']
          }
        ],
        transaction
      });

      if (assignments.length === 0) {
        throw new AppError(404, 'Assignment not found');
      }

      // Calculate current value using pricing service
      const invoiceCalculation = DsaPricingService.calculateInvoiceTotal(assignments);

      // Get payments
      const payments = await DsaPayment.findAll({
        where: { assignment_identifier: assignmentIdentifier },
        transaction
      });

      const totalPaid = payments.reduce((sum, payment) =>
        sum + parseFloat(payment.cash_amount || 0) + parseFloat(payment.paybill_amount || 0), 0
      );

      const balance = Math.max(0, invoiceCalculation.total_remaining_value - totalPaid);

      // Check if all items are returned
      const allItemsReturned = assignments.every(assignment => {
        const quantityAssigned = assignment.quantity_assigned || 0;
        const quantityReturned = assignment.quantity_returned || 0;
        return quantityReturned >= quantityAssigned;
      });

      const paymentStatus = DsaPricingService.determinePaymentStatus(balance, totalPaid, allItemsReturned);

      return {
        assignment_identifier: assignmentIdentifier,
        total_remaining_value: invoiceCalculation.total_remaining_value,
        total_paid: totalPaid,
        balance: balance,
        payment_status: paymentStatus,
        all_items_returned: allItemsReturned,
        assignments: assignments,
        payments: payments
      };

    } catch (error) {
      logger.error(`Error getting current balance for ${assignmentIdentifier}:`, error);
      throw error;
    }
  }

  /**
   * Process a return and update balances
   * @param {string} assignmentIdentifier - Assignment identifier
   * @param {number} assignmentId - Specific assignment ID for the return
   * @param {number} returnQuantity - Quantity being returned
   * @param {Object} transaction - Database transaction
   * @returns {Object} Updated balance information
   */
  static async processReturn(assignmentIdentifier, assignmentId, returnQuantity, transaction = null) {
    try {
      logger.info(`Processing return for assignment ${assignmentId}: Quantity=${returnQuantity}`);

      // Get the specific assignment
      const assignment = await DsaStockAssignment.findOne({
        where: {
          id: assignmentId,
          assignment_identifier: assignmentIdentifier
        },
        include: [{ model: Product }],
        transaction
      });

      if (!assignment) {
        throw new AppError(404, 'Assignment not found');
      }

      // Calculate return impact
      const returnImpact = DsaPricingService.calculateReturnImpact(assignment, returnQuantity);

      // Validate return quantity
      const maxReturnQuantity = assignment.quantity_assigned - (assignment.quantity_returned || 0);
      if (returnQuantity > maxReturnQuantity) {
        throw new AppError(400, `Cannot return ${returnQuantity} items. Maximum returnable: ${maxReturnQuantity}`);
      }

      // Update the assignment with new return quantity
      await DsaStockAssignment.update({
        quantity_returned: returnImpact.new_quantity_returned
      }, {
        where: { id: assignmentId },
        transaction
      });

      // Recalculate balance for the entire assignment identifier
      const updatedBalance = await this.recalculateBalance(assignmentIdentifier, transaction);

      logger.info(`Return processed successfully for assignment ${assignmentId}`);
      return {
        ...updatedBalance,
        return_impact: returnImpact
      };

    } catch (error) {
      logger.error(`Error processing return for assignment ${assignmentId}:`, error);
      throw error;
    }
  }

  /**
   * Get balance summary for multiple assignments
   * @param {Array} assignmentIdentifiers - Array of assignment identifiers
   * @param {Object} transaction - Database transaction (optional)
   * @returns {Object} Summary of balances
   */
  static async getBalanceSummary(assignmentIdentifiers, transaction = null) {
    try {
      const summaries = await Promise.all(
        assignmentIdentifiers.map(identifier => this.getCurrentBalance(identifier, transaction))
      );

      const totalSummary = summaries.reduce((acc, summary) => {
        acc.total_remaining_value += summary.total_remaining_value;
        acc.total_paid += summary.total_paid;
        acc.total_balance += summary.balance;

        if (summary.payment_status === 'UNPAID') acc.unpaid_count++;
        else if (summary.payment_status === 'PARTIALLY_PAID') acc.partially_paid_count++;
        else if (summary.payment_status === 'FULLY_PAID') acc.fully_paid_count++;

        return acc;
      }, {
        total_remaining_value: 0,
        total_paid: 0,
        total_balance: 0,
        unpaid_count: 0,
        partially_paid_count: 0,
        fully_paid_count: 0
      });

      return {
        assignment_count: assignmentIdentifiers.length,
        individual_summaries: summaries,
        total_summary: totalSummary
      };

    } catch (error) {
      logger.error('Error getting balance summary:', error);
      throw error;
    }
  }
}

module.exports = EnhancedDsaBalanceService;
