import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import mpesaTransactionsExportService, {
  MpesaTransactionsExportParams,
  CustomMpesaTransactionsExportParams,
} from "../api/mpesa-transactions-export-service";

/**
 * Hook for comprehensive MPESA transactions export
 * Exports all data with multiple sheets
 */
export const useMpesaTransactionsExportAll = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: MpesaTransactionsExportParams) => {
      // Validate parameters
      const validation = mpesaTransactionsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await mpesaTransactionsExportService.exportAllMpesaTransactions(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = mpesaTransactionsExportService.generateFilename(params, 'comprehensive');
        mpesaTransactionsExportService.downloadBlob(blob, filename);
        
        toast.success("MPESA transactions exported successfully!", {
          description: `Comprehensive report with all data sheets downloaded as ${filename}`,
          duration: 5000,
        });

        // Invalidate related queries to refresh any cached data
        queryClient.invalidateQueries({ queryKey: ["mpesa-transactions"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Export error:', error);
      
      let errorMessage = "Failed to export MPESA transactions";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export MPESA reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try filtering the data or use lightweight export.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for custom MPESA transactions export
 * Allows format and sheet selection
 */
export const useMpesaTransactionsExportCustom = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: CustomMpesaTransactionsExportParams) => {
      // Validate parameters
      const validation = mpesaTransactionsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await mpesaTransactionsExportService.exportCustomMpesaTransactions(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const formatType = params.format_type || 'detailed';
        const filename = mpesaTransactionsExportService.generateFilename(params, formatType);
        mpesaTransactionsExportService.downloadBlob(blob, filename);
        
        toast.success("Custom MPESA transactions export completed!", {
          description: `${formatType.charAt(0).toUpperCase() + formatType.slice(1)} report downloaded as ${filename}`,
          duration: 5000,
        });

        queryClient.invalidateQueries({ queryKey: ["mpesa-transactions"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Custom export error:', error);
      
      let errorMessage = "Failed to export custom MPESA transactions";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export MPESA reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try using lightweight format or filtering the data.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Custom Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for lightweight MPESA transactions export
 * Fastest export with essential data only
 */
export const useMpesaTransactionsExportLightweight = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: MpesaTransactionsExportParams) => {
      const validation = mpesaTransactionsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await mpesaTransactionsExportService.exportLightweightMpesaTransactions(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = mpesaTransactionsExportService.generateFilename(params, 'lightweight');
        mpesaTransactionsExportService.downloadBlob(blob, filename);
        
        toast.success("Quick export completed!", {
          description: `MPESA transaction summary report downloaded as ${filename}`,
          duration: 4000,
        });

        queryClient.invalidateQueries({ queryKey: ["mpesa-transactions"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Lightweight export error:', error);
      
      let errorMessage = "Failed to export MPESA transactions data";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export MPESA reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. The system may be busy, please try again.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Quick Export Failed", {
        description: errorMessage,
        duration: 6000,
      });
    },
  });
};

/**
 * Hook to get export recommendations based on data size
 */
export const useMpesaTransactionsExportRecommendation = (estimatedRecords: number) => {
  return mpesaTransactionsExportService.getExportRecommendation(estimatedRecords);
};

/**
 * Hook to get export format options for UI
 */
export const useMpesaTransactionsExportFormatOptions = () => {
  return mpesaTransactionsExportService.getExportFormatOptions();
};

/**
 * Hook to get column options for custom export
 */
export const useMpesaTransactionsExportColumnOptions = () => {
  return mpesaTransactionsExportService.getColumnOptions();
};

/**
 * Combined hook that provides all export functionality
 * Convenient single hook for components that need multiple export options
 */
export const useMpesaTransactionsExportSuite = () => {
  const exportAll = useMpesaTransactionsExportAll();
  const exportCustom = useMpesaTransactionsExportCustom();
  const exportLightweight = useMpesaTransactionsExportLightweight();

  const isAnyExporting = exportAll.isPending || exportCustom.isPending || exportLightweight.isPending;

  return {
    // Individual export methods
    exportAll: exportAll.mutateAsync,
    exportCustom: exportCustom.mutateAsync,
    exportLightweight: exportLightweight.mutateAsync,
    
    // Loading states
    isExportingAll: exportAll.isPending,
    isExportingCustom: exportCustom.isPending,
    isExportingLightweight: exportLightweight.isPending,
    isAnyExporting,
    
    // Error states
    exportAllError: exportAll.error,
    exportCustomError: exportCustom.error,
    exportLightweightError: exportLightweight.error,
    
    // Utility functions
    getRecommendation: mpesaTransactionsExportService.getExportRecommendation,
    getFormatOptions: mpesaTransactionsExportService.getExportFormatOptions,
    getColumnOptions: mpesaTransactionsExportService.getColumnOptions,
    validateParams: mpesaTransactionsExportService.validateExportParams,
    generateFilename: mpesaTransactionsExportService.generateFilename,
    
    // Reset functions
    resetAll: () => {
      exportAll.reset();
      exportCustom.reset();
      exportLightweight.reset();
    },
  };
};
