'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Make reference_number nullable for CBA bank transactions
    await queryInterface.changeColumn('banking_transactions', 'reference_number', {
      type: Sequelize.STRING(100),
      allowNull: true,
      comment: 'TID/CBSREF for bank/agent, transaction_id for MPESA - Optional for CBA bank'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Revert reference_number to not nullable
    // First, update any null values to empty string to avoid constraint violation
    await queryInterface.sequelize.query(`
      UPDATE banking_transactions 
      SET reference_number = '' 
      WHERE reference_number IS NULL
    `);
    
    await queryInterface.changeColumn('banking_transactions', 'reference_number', {
      type: Sequelize.STRING(100),
      allowNull: false,
      comment: 'TID/CBSREF for bank/agent, transaction_id for MPESA'
    });
  }
};
