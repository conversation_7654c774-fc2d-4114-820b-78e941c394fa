const mysql = require('mysql2/promise');
require('dotenv').config();

async function deleteNullReceiptSales() {
  let connection;

  try {
    console.log('🗑️  Preparing to delete sales with null receipt numbers...');
    console.log('📅 Date:', new Date().toISOString().split('T')[0]);
    console.log('⚠️  WARNING: This will permanently delete sales records!');

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT || 3306,
    });

    console.log('✅ Connected to database successfully');

    // Get today's date in MySQL format
    const today = new Date().toISOString().split('T')[0];

    // First, let's analyze what we're about to delete
    console.log('🔍 Analyzing sales to be deleted...');

    const analysisQuery = `
      SELECT
        s.id as sale_id,
        s.customer_id,
        s.total_amount,
        s.payment_method_id,
        s.payment_reference,
        s.created_at,
        s.is_dsa,
        s.sale_type,
        c.name as customer_name,
        pm.name as payment_method_name,
        COUNT(si.id) as remaining_items
      FROM sales s
      LEFT JOIN customers c ON s.customer_id = c.id
      LEFT JOIN payment_methods pm ON s.payment_method_id = pm.id
      LEFT JOIN sale_items si ON s.id = si.sale_id
      WHERE DATE(s.created_at) = ?
        AND s.receipt_number IS NULL
        AND s.is_dsa = 1
      GROUP BY s.id, s.customer_id, s.total_amount, s.payment_method_id, s.payment_reference, s.created_at, s.is_dsa, s.sale_type, c.name, pm.name
      ORDER BY s.customer_id, s.created_at;
    `;

    const [salesToDelete] = await connection.execute(analysisQuery, [today]);

    if (salesToDelete.length === 0) {
      console.log('✅ No sales with null receipts found for today!');
      return;
    }

    console.log(`📊 Found ${salesToDelete.length} sales with null receipts:`);
    console.log('');

    let totalAmountToDelete = 0;
    let salesWithItems = 0;

    salesToDelete.forEach((sale, index) => {
      console.log(`${index + 1}. Sale ID ${sale.sale_id} - ${sale.customer_name}`);
      console.log(`   Amount: ${sale.total_amount} KES`);
      console.log(`   Payment: ${sale.payment_method_name} (ID: ${sale.payment_method_id})`);
      console.log(`   Reference: ${sale.payment_reference || 'None'}`);
      console.log(`   Sale Type: ${sale.sale_type || 'NULL'}`);
      console.log(`   Remaining Items: ${sale.remaining_items}`);
      console.log(`   Created: ${sale.created_at}`);

      totalAmountToDelete += parseFloat(sale.total_amount || 0);
      if (sale.remaining_items > 0) {
        salesWithItems++;
      }
    });

    console.log('');
    console.log(`📈 SUMMARY:`);
    console.log(`   Sales to delete: ${salesToDelete.length}`);
    console.log(`   Total amount of sales to delete: ${totalAmountToDelete} KES`);
    console.log(`   Sales with remaining items: ${salesWithItems}`);
    console.log('');

    // Safety check for sales with remaining items
    if (salesWithItems > 0) {
      console.log('⚠️  WARNING: Some sales still have items!');
      console.log('   This could indicate that the sale items cleanup was not complete.');
      console.log('   Proceeding will delete sales that still have items.');
      console.log('');
    }

    // Safety check - require manual confirmation
    console.log('⚠️  SAFETY CHECK:');
    console.log('   This operation will permanently delete sales records.');
    console.log('   This should complete the cleanup of duplicate DSA sales.');
    console.log('   Make sure you have a database backup before proceeding.');
    console.log('');
    console.log('🔄 To proceed, you need to modify this script to set CONFIRM_DELETE = true');

    const CONFIRM_DELETE = true; // Set to true to actually perform the deletion

    if (!CONFIRM_DELETE) {
      console.log('❌ Deletion not confirmed. Exiting safely.');
      console.log('   To proceed, set CONFIRM_DELETE = true in the script.');
      return;
    }

    // If we reach here, deletion is confirmed
    console.log('🚀 Proceeding with deletion...');

    // Start transaction for safety
    await connection.beginTransaction();

    try {
      // Get the sale IDs to delete
      const saleIdsQuery = `
        SELECT id
        FROM sales
        WHERE DATE(created_at) = ?
          AND receipt_number IS NULL
          AND is_dsa = 1
      `;

      const [saleIds] = await connection.execute(saleIdsQuery, [today]);

      if (saleIds.length === 0) {
        console.log('❌ No sale IDs found for deletion');
        await connection.rollback();
        return;
      }

      const saleIdList = saleIds.map(row => row.id);
      console.log(`🎯 Deleting sales with IDs: ${saleIdList.join(', ')}`);

      // First, delete any remaining sale items (safety measure)
      const deleteItemsQuery = `
        DELETE FROM sale_items
        WHERE sale_id IN (${saleIdList.map(() => '?').join(',')})
      `;

      const [deleteItemsResult] = await connection.execute(deleteItemsQuery, saleIdList);

      if (deleteItemsResult.affectedRows > 0) {
        console.log(`⚠️  Deleted ${deleteItemsResult.affectedRows} remaining sale items`);
      }

      // Now delete the sales records
      const deleteSalesQuery = `
        DELETE FROM sales
        WHERE id IN (${saleIdList.map(() => '?').join(',')})
      `;

      const [deleteSalesResult] = await connection.execute(deleteSalesQuery, saleIdList);

      console.log(`✅ Deleted ${deleteSalesResult.affectedRows} sales records`);

      // Verify deletion
      const verifyQuery = `
        SELECT COUNT(*) as remaining_sales
        FROM sales
        WHERE DATE(created_at) = ?
          AND receipt_number IS NULL
          AND is_dsa = 1
      `;

      const [verifyResult] = await connection.execute(verifyQuery, [today]);
      const remainingSales = verifyResult[0].remaining_sales;

      if (remainingSales === 0) {
        console.log('✅ Verification passed: No null receipt sales remain');
        await connection.commit();
        console.log('🎉 Transaction committed successfully!');
      } else {
        console.log(`❌ Verification failed: ${remainingSales} sales still remain`);
        await connection.rollback();
        console.log('🔄 Transaction rolled back for safety');
      }

    } catch (deleteError) {
      console.error('❌ Error during deletion:', deleteError.message);
      await connection.rollback();
      console.log('🔄 Transaction rolled back due to error');
      throw deleteError;
    }

    // Final summary
    console.log('');
    console.log('📊 CLEANUP SUMMARY:');
    console.log(`   ✅ Deleted ${salesToDelete.length} sales with null receipts`);
    console.log(`   ✅ Removed ${totalAmountToDelete} KES worth of duplicate sales`);
    console.log('');
    console.log('🎯 Duplicate DSA sales cleanup completed!');
    console.log('   1. ✅ Duplicate sale items removed');
    console.log('   2. ✅ Duplicate sales records deleted');
    console.log('   3. ✅ Only legitimate receipt sales remain');
    console.log('   4. 🚀 Deploy DSA fix to prevent future duplicates');

  } catch (error) {
    console.error('❌ Error deleting null receipt sales:', error.message);
    console.error('Full error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the cleanup
deleteNullReceiptSales();
