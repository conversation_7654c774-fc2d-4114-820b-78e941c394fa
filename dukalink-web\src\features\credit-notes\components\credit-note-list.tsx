"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { DateRange } from "react-day-picker";
import {
  Plus,
  Eye,
  Pencil,
  FileText,
  Search,
  AlertCircle
} from "lucide-react";
import { useRouter } from 'next/navigation';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';

import { CreditNote, CreditNoteStatus } from '@/types/credit-note';
import { CreditNoteService } from '../api/credit-note-service';
import { formatCurrency } from '@/lib/utils';
import { EmptyState } from '@/components/empty-state';
import {
  Pagination,
  PaginationContent,
  Pa<PERSON>ationItem,
  PaginationLink,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Pa<PERSON>ationPrevious,
} from "@/components/ui/pagination";
import { DateRangePicker } from "@/components/date-range-picker";

/**
 * Status badge component
 */
const CreditNoteStatusBadge = ({ status }: { status: CreditNoteStatus }) => {
  const statusConfig = {
    draft: {
      variant: "outline" as const,
      className: "border-blue-500 text-blue-500",
      label: 'Draft'
    },
    issued: {
      variant: "outline" as const,
      className: "border-green-500 text-green-500",
      label: 'Issued'
    },
    applied: {
      variant: "outline" as const,
      className: "border-purple-500 text-purple-500",
      label: 'Applied'
    },
    cancelled: {
      variant: "outline" as const,
      className: "border-red-500 text-red-500",
      label: 'Cancelled'
    }
  };

  const config = statusConfig[status];

  return (
    <Badge
      variant={config.variant}
      className={config.className}
    >
      {config.label}
    </Badge>
  );
};

/**
 * Credit Note List Component
 */
export const CreditNoteList: React.FC = () => {
  const router = useRouter();
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: undefined,
    to: undefined
  });

  // Query for credit notes
  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ['creditNotes', page, limit, search, dateRange],
    queryFn: () => CreditNoteService.getCreditNotes({
      page: page + 1, // API uses 1-based pagination
      limit,
      search,
      start_date: dateRange.from ? format(dateRange.from, 'yyyy-MM-dd') : undefined,
      end_date: dateRange.to ? format(dateRange.to, 'yyyy-MM-dd') : undefined
    })
  });

  // Debug logging
  console.log('Credit notes query data:', data);
  console.log('Credit notes isLoading:', isLoading);
  console.log('Credit notes isError:', isError);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleLimitChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setLimit(Number(e.target.value));
    setPage(0);
  };

  // Handle search
  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setPage(0);
  };

  // Handle search input change
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  // Handle date range change
  const handleDateRangeChange = (range: DateRange | undefined) => {
    if (range) {
      setDateRange({
        from: range.from,
        to: range.to
      });
    } else {
      setDateRange({
        from: undefined,
        to: undefined
      });
    }
    setPage(0);
  };

  // Navigate to create credit note page
  const handleCreateCreditNote = () => {
    router.push('/credit-notes/new');
  };

  // Navigate to credit note details page
  const handleViewCreditNote = (id: number) => {
    router.push(`/credit-notes/${id}`);
  };

  // Navigate to edit credit note page
  const handleEditCreditNote = (id: number) => {
    router.push(`/credit-notes/${id}/edit`);
  };

  // Open credit note PDF
  const handlePrintCreditNote = (id: number) => {
    window.open(CreditNoteService.getCreditNotePdfUrl(id), '_blank');
  };

  // Render header and actions
  const renderHeader = () => (
    <>
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h1 className="text-2xl font-bold">Credit Notes</h1>
        <Button onClick={handleCreateCreditNote}>
          <Plus className="mr-2 h-4 w-4" /> Create Credit Note
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative w-full sm:w-auto sm:flex-1">
          <form onSubmit={handleSearch} className="flex w-full">
            <Input
              placeholder="Search credit notes..."
              value={search}
              onChange={handleSearchInputChange}
              className="w-full"
            />
            <Button type="submit" variant="ghost" className="absolute right-0 top-0 h-full">
              <Search className="h-4 w-4" />
            </Button>
          </form>
        </div>
        <DateRangePicker
          date={{ from: dateRange.from, to: dateRange.to }}
          onDateChange={handleDateRangeChange}
        />
      </div>
    </>
  );

  // Show loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        {renderHeader()}
        <div className="flex items-center justify-center h-[400px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  // Show error state
  if (isError) {
    return (
      <div className="space-y-6">
        {renderHeader()}
        <div className="flex flex-col items-center justify-center h-[400px] space-y-4">
          <div className="text-red-500">
            <AlertCircle className="h-12 w-12" />
          </div>
          <h3 className="text-xl font-semibold">Error Loading Credit Notes</h3>
          <p className="text-gray-600 mb-4">Unable to load credit notes.</p>
          <Button onClick={() => refetch()}>Try Again</Button>
        </div>
      </div>
    );
  }

  // Show empty state if no credit notes or data is not loaded yet
  if (!data || !data.data || data.data.length === 0) {
    return (
      <div className="space-y-6">
        {renderHeader()}
        <EmptyState
          icon={<FileText className="h-12 w-12 text-muted-foreground" />}
          title="No Credit Notes Found"
          description="Create your first credit note to get started."
          actions={
            <Button onClick={handleCreateCreditNote}>
              Create Credit Note
            </Button>
          }
        />
      </div>
    );
  }

  // Calculate pagination
  const totalPages = Math.ceil((data.pagination?.total || 0) / limit);

  return (
    <div className="space-y-6">
      {renderHeader()}

      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Credit Note #</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead>Customer/Supplier</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.data.map((creditNote: CreditNote) => (
                  <TableRow key={creditNote.id}>
                    <TableCell>{creditNote.credit_note_number}</TableCell>
                    <TableCell>{format(new Date(creditNote.credit_note_date), 'dd/MM/yyyy')}</TableCell>
                    <TableCell>
                      {creditNote.reference_type.toUpperCase()} #{creditNote.reference_number}
                    </TableCell>
                    <TableCell>
                      {creditNote.customer?.name || creditNote.supplier?.name || 'N/A'}
                    </TableCell>
                    <TableCell>{formatCurrency(creditNote.total_amount)}</TableCell>
                    <TableCell>
                      <CreditNoteStatusBadge status={creditNote.status} />
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleViewCreditNote(creditNote.id)}
                          title="View"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        {creditNote.status === 'draft' && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditCreditNote(creditNote.id)}
                            title="Edit"
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handlePrintCreditNote(creditNote.id)}
                          title="Print"
                        >
                          <FileText className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {totalPages > 1 && (
            <div className="flex items-center justify-end p-4">
              <div className="flex items-center gap-2 mr-4">
                <span className="text-sm text-muted-foreground">
                  Rows per page:
                </span>
                <select
                  value={limit}
                  onChange={handleLimitChange}
                  className="h-8 w-16 rounded-md border border-input bg-background px-2 text-sm"
                >
                  <option value={5}>5</option>
                  <option value={10}>10</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                </select>
              </div>

              <Pagination>
                <PaginationContent>
                  {page > 0 && (
                    <PaginationItem>
                      <PaginationPrevious onClick={() => handlePageChange(page - 1)} />
                    </PaginationItem>
                  )}

                  {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                    let pageNumber: number;

                    if (totalPages <= 5) {
                      pageNumber = i;
                    } else if (page < 3) {
                      pageNumber = i;
                    } else if (page > totalPages - 3) {
                      pageNumber = totalPages - 5 + i;
                    } else {
                      pageNumber = page - 2 + i;
                    }

                    if (pageNumber >= 0 && pageNumber < totalPages) {
                      return (
                        <PaginationItem key={pageNumber}>
                          <PaginationLink
                            isActive={page === pageNumber}
                            onClick={() => handlePageChange(pageNumber)}
                          >
                            {pageNumber + 1}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    }

                    return null;
                  })}

                  {page < totalPages - 1 && (
                    <PaginationItem>
                      <PaginationNext onClick={() => handlePageChange(page + 1)} />
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CreditNoteList;




