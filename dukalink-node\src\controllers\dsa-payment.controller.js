const { DsaPayment, DsaStockAssignment, DsaStockReconciliation, Customer, Branch, User } = require('../models');
const sequelize = require('../../config/database');
const AppError = require('../utils/error');
const logger = require('../utils/logger');

/**
const DsaPricingService = require("../services/DsaPricingService");
const EnhancedDsaBalanceService = require("../services/EnhancedDsaBalanceService");
 * Create a new DSA payment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createPayment = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      assignment_identifier,
      customer_id,
      branch_id,
      cash_amount,
      paybill_amount,
      cash_received, // Alternative field name used by mobile app
      paybill_amount: mobilePaibillAmount, // Alternative field name used by mobile app
      notes,
      is_partial_payment, // Flag to indicate if this is a partial payment
      allow_zero_amount = false // Flag to allow zero amount payments in special cases
    } = req.body;

    // Log the request body for debugging
    logger.debug(`DSA payment request: ${JSON.stringify({
      assignment_identifier,
      customer_id,
      branch_id,
      cash_amount,
      paybill_amount,
      cash_received,
      mobilePaibillAmount,
      is_partial_payment,
      allow_zero_amount
    })}`);

    // Validate request
    if (!assignment_identifier || !customer_id || !branch_id) {
      throw new AppError(400, 'Missing required fields');
    }

    // Handle different field names from mobile app
    const cashAmountValue = cash_amount !== undefined ? cash_amount : (cash_received !== undefined ? cash_received : 0);
    const paybillAmountValue = paybill_amount !== undefined ? paybill_amount : (mobilePaibillAmount !== undefined ? mobilePaibillAmount : 0);

    // Parse values to ensure they're numbers
    const parsedCashAmount = parseFloat(cashAmountValue) || 0;
    const parsedPaybillAmount = parseFloat(paybillAmountValue) || 0;

    // Calculate total amount
    const totalAmount = parsedCashAmount + parsedPaybillAmount;

    // Only validate amount if allow_zero_amount is false
    if (totalAmount <= 0 && !allow_zero_amount) {
      logger.error(`Payment amount validation failed: cash_amount=${parsedCashAmount}, paybill_amount=${parsedPaybillAmount}, total=${totalAmount}`);
      throw new AppError(400, 'Payment amount must be greater than zero');
    }

    // Check if DSA agent exists
    const dsaAgent = await Customer.findOne({
      where: { id: customer_id, is_dsa: true },
      transaction
    });

    if (!dsaAgent) {
      throw new AppError(404, 'DSA agent not found');
    }

    // Check if branch exists
    const branch = await Branch.findByPk(branch_id, { transaction });
    if (!branch) {
      throw new AppError(404, 'Branch not found');
    }

    // Get all assignments for this identifier
    const assignments = await DsaStockAssignment.findAll({
      where: {
        assignment_identifier,
        customer_id,
        branch_id
      },
      transaction
    });

    if (assignments.length === 0) {
      throw new AppError(404, 'No assignments found for this identifier');
    }

    // Calculate total amount due and amount already paid
    let totalAmountDue = 0;
    let totalAmountPaid = 0;
    let totalAssignedQuantity = 0;
    let totalReturnedQuantity = 0;
    let totalRemainingValue = 0;
    let totalAssignmentBalance = 0;

    // Log assignments for debugging
    logger.debug(`Processing ${assignments.length} assignments for payment calculation`);

    for (const assignment of assignments) {
      // Get basic assignment data
      const quantityAssigned = parseInt(assignment.quantity_assigned || 0);
      const quantityReturned = parseInt(assignment.quantity_returned || 0);
      const unitPrice = parseFloat(assignment.default_wholesale_price || 0);
      const amountPaid = parseFloat(assignment.amount_paid || 0);
      const assignmentBalance = parseFloat(assignment.balance || 0);

      // Calculate remaining quantity (not returned)
      const remainingQuantity = Math.max(0, quantityAssigned - quantityReturned);

      // Calculate value of remaining items
      const remainingValue = remainingQuantity * unitPrice;

      // Add to totals
      totalAmountDue += parseFloat(assignment.total_amount || 0);
      totalAmountPaid += amountPaid;
      totalAssignedQuantity += quantityAssigned;
      totalReturnedQuantity += quantityReturned;
      totalRemainingValue += remainingValue;
      totalAssignmentBalance += assignmentBalance;

      // Log individual assignment data for debugging
      logger.debug(`Assignment: id=${assignment.id}, assigned=${quantityAssigned}, returned=${quantityReturned}, remaining=${remainingQuantity}, unitPrice=${unitPrice}, remainingValue=${remainingValue}, balance=${assignmentBalance}`);
    }

    // Use the balance from the assignment table as the primary source of truth
    // If the balance is zero or negative, allow the payment to proceed
    const remainingBalance = Math.max(0, totalAssignmentBalance);
    const allItemsReturned = totalReturnedQuantity >= totalAssignedQuantity;

    // Log detailed calculation for debugging
    logger.debug(`Payment calculation: totalRemainingValue=${totalRemainingValue}, totalAmountPaid=${totalAmountPaid}, assignmentBalance=${totalAssignmentBalance}, remainingBalance=${remainingBalance}`);
    logger.debug(`Items: assigned=${totalAssignedQuantity}, returned=${totalReturnedQuantity}, remaining=${totalAssignedQuantity - totalReturnedQuantity}`);

    // Ensure payment amount doesn't exceed remaining balance
    // Only validate if there's a positive balance and not all items have been returned
    if (totalAmount > remainingBalance && remainingBalance > 0 && !allItemsReturned) {
      logger.error(`Payment validation failed: amount=${totalAmount}, balance=${remainingBalance}, allItemsReturned=${allItemsReturned}`);
      throw new AppError(400, `Payment amount (${totalAmount}) exceeds remaining balance (${remainingBalance})`);
    }

    // Get the active POS session for the branch (if any)
    const { PosSession } = require('../models');
    let activePosSession = null;
    try {
      activePosSession = await PosSession.findOne({
        where: {
          branch_id: branch_id,
          status: 'open',
          deleted_at: null
        },
        order: [['start_time', 'DESC']]
      });
    } catch (sessionError) {
      logger.warn(`Could not find active POS session for branch ${branch_id}: ${sessionError.message}`);
    }

    // Create payment record
    let payment;
    try {
      payment = await DsaPayment.create({
        assignment_identifier,
        customer_id,
        branch_id,
        pos_session_id: activePosSession?.id || null,
        cash_amount: parsedCashAmount,
        paybill_amount: parsedPaybillAmount,
        total_amount: totalAmount,
        payment_date: new Date(),
        notes,
        created_by: req.user?.id
      }, { transaction });
    } catch (createError) {
      // If there's an error with total_amount column, try without it
      if (createError.message && createError.message.includes("total_amount")) {
        logger.warn(`Error with total_amount column, trying without it: ${createError.message}`);

        payment = await DsaPayment.create({
          assignment_identifier,
          customer_id,
          branch_id,
          pos_session_id: activePosSession?.id || null,
          cash_amount: parsedCashAmount,
          paybill_amount: parsedPaybillAmount,
          payment_date: new Date(),
          notes,
          created_by: req.user?.id
        }, { transaction });
      } else {
        // If it's a different error, rethrow it
        throw createError;
      }
    }

    // Update assignment records
    const newTotalPaid = totalAmountPaid + totalAmount;

    // Calculate new balance based on the assignment balance
    // Subtract the payment amount from the current balance
    const newBalance = Math.max(0, totalAssignmentBalance - totalAmount);

    // Determine payment status based on balance and item status
    const paymentStatus = (newBalance <= 0 || allItemsReturned) ? 'FULLY_PAID' : 'PARTIALLY_PAID';

    // Log the balance calculation
    logger.debug(`Balance calculation: currentBalance=${totalAssignmentBalance}, payment=${totalAmount}, newBalance=${newBalance}, status=${paymentStatus}`);

    // Update all assignments with the same identifier
    await Promise.all(assignments.map(assignment => {
      const updateData = {
        amount_paid: newTotalPaid,
        balance: newBalance,
        payment_status: paymentStatus,
        last_payment_date: new Date(),
        last_updated_by: req.user?.id
      };

      // If fully paid, mark as reconciled
      if (paymentStatus === 'FULLY_PAID') {
        updateData.reconciled = true;
        updateData.reconciled_at = new Date();
      }

      return assignment.update(updateData, { transaction });
    }));

    // Find and update related reconciliation records
    const reconciliations = await DsaStockReconciliation.findAll({
      where: {
        customer_id
      },
      order: [['created_at', 'DESC']],
      limit: 5 // Get the most recent reconciliations
    }, { transaction });

    if (reconciliations && reconciliations.length > 0) {
      // Update the most recent reconciliation
      const latestReconciliation = reconciliations[0];

      // Calculate new values for the reconciliation
      const reconTotalPaid = parseFloat(latestReconciliation.amount_paid || 0) + totalAmount;
      const reconCurrentBalance = parseFloat(latestReconciliation.balance || 0);
      const reconBalance = Math.max(0, reconCurrentBalance - totalAmount);
      const reconPaymentStatus = reconBalance <= 0 || allItemsReturned ? 'FULLY_PAID' : 'PARTIALLY_PAID';

      // Log reconciliation balance calculation
      logger.debug(`Reconciliation balance calculation: currentBalance=${reconCurrentBalance}, payment=${totalAmount}, newBalance=${reconBalance}, status=${reconPaymentStatus}`);

      // Update the reconciliation record
      await latestReconciliation.update({
        amount_paid: reconTotalPaid,
        balance: reconBalance,
        payment_status: reconPaymentStatus,
        last_payment_date: new Date(),
        last_updated_by: req.user?.id
      }, { transaction });

      logger.debug(`Updated reconciliation #${latestReconciliation.id} with new payment: total_paid=${reconTotalPaid}, balance=${reconBalance}, status=${reconPaymentStatus}`);
    }

    await transaction.commit();

    // Prepare reconciliation data for response
    let reconciliationData = null;
    if (reconciliations && reconciliations.length > 0) {
      const latestReconciliation = reconciliations[0];
      reconciliationData = {
        id: latestReconciliation.id,
        total_amount: parseFloat(latestReconciliation.total_amount || 0),
        amount_paid: parseFloat(latestReconciliation.amount_paid || 0),
        balance: parseFloat(latestReconciliation.balance || 0),
        payment_status: latestReconciliation.payment_status,
        last_payment_date: latestReconciliation.last_payment_date
      };
    }

    res.status(201).json({
      success: true,
      message: 'Payment recorded successfully',
      data: {
        payment,
        totalAmountDue,
        totalAmountPaid: newTotalPaid,
        totalRemainingValue,
        remainingBalance: newBalance,
        paymentStatus,
        items: {
          totalAssigned: totalAssignedQuantity,
          totalReturned: totalReturnedQuantity,
          totalRemaining: totalAssignedQuantity - totalReturnedQuantity,
          allItemsReturned
        },
        reconciliation: reconciliationData
      }
    });
  } catch (error) {
    await transaction.rollback();
    logger.error(`Error creating DSA payment: ${error.message}`);
    next(error);
  }
};

/**
 * Get payments for a DSA assignment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getPaymentsByAssignment = async (req, res, next) => {
  try {
    const { assignment_identifier } = req.params;

    if (!assignment_identifier) {
      throw new AppError(400, 'Assignment identifier is required');
    }

    const payments = await DsaPayment.findAll({
      where: { assignment_identifier },
      include: [
        { model: Customer, as: 'DsaCustomer' },
        { model: Branch },
        { model: User, as: 'Creator' }
      ],
      order: [['payment_date', 'DESC']]
    });

    res.status(200).json({
      success: true,
      data: payments
    });
  } catch (error) {
    logger.error(`Error fetching DSA payments: ${error.message}`);
    next(error);
  }
};

/**
 * Get DSA cash collections for a specific branch and date range
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getDsaCashCollections = async (req, res, next) => {
  try {
    const { branch_id } = req.params;
    const { start_date, end_date, shift_id } = req.query;

    if (!branch_id) {
      throw new AppError(400, 'Branch ID is required');
    }

    // Build where clause for filtering
    const whereClause = {
      [sequelize.Op.or]: [
        { cash_amount: { [sequelize.Op.gt]: 0 } },
        { paybill_amount: { [sequelize.Op.gt]: 0 } }
      ]
    };

    // Handle shift-based filtering if shift_id is provided
    if (shift_id) {
      const { PosSession } = require('../models');

      // Get the shift details
      const shift = await PosSession.findByPk(shift_id);
      if (!shift) {
        throw new AppError(404, 'Shift not found');
      }

      // Verify the shift belongs to the requested branch
      if (shift.branch_id !== parseInt(branch_id)) {
        throw new AppError(400, 'Shift does not belong to the specified branch');
      }

      // Filter by session ID for exact session records
      whereClause.pos_session_id = shift_id;

      logger.info(`Filtering DSA collections for session ${shift_id}`);
    }
    // Add date filtering if provided and no shift_id
    else if (start_date && end_date) {
      // For date-based filtering, also filter by branch
      whereClause.branch_id = parseInt(branch_id);

      const startDate = new Date(start_date);
      const endDate = new Date(end_date);

      // Set end date to end of day
      endDate.setHours(23, 59, 59, 999);

      whereClause.payment_date = {
        [sequelize.Op.between]: [startDate, endDate]
      };
    } else if (start_date) {
      // If only start_date provided, get payments from that date onwards
      whereClause.branch_id = parseInt(branch_id);

      const startDate = new Date(start_date);
      whereClause.payment_date = {
        [sequelize.Op.gte]: startDate
      };
    } else {
      // If no date provided, get payments for today
      whereClause.branch_id = parseInt(branch_id);

      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      whereClause.payment_date = {
        [sequelize.Op.between]: [today, tomorrow]
      };
    }

    logger.info(`Fetching DSA collections for branch ${branch_id} with where clause:`, whereClause);

    // Get DSA payments (both cash and M-PESA)
    const dsaPayments = await DsaPayment.findAll({
      where: whereClause,
      include: [
        {
          model: Customer,
          as: 'DsaCustomer',
          attributes: ['id', 'name', 'phone', 'email']
        },
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'name', 'email']
        }
      ],
      attributes: [
        'id',
        'assignment_identifier',
        'cash_amount',
        'paybill_amount',
        'total_amount',
        'payment_date',
        'notes',
        'created_at'
      ],
      order: [['payment_date', 'DESC']]
    });

    // Calculate total cash collected
    const totalCashCollected = dsaPayments.reduce((sum, payment) => {
      return sum + parseFloat(payment.cash_amount || 0);
    }, 0);

    // Calculate total M-PESA amount
    const totalMpesaAmount = dsaPayments.reduce((sum, payment) => {
      return sum + parseFloat(payment.paybill_amount || 0);
    }, 0);

    // Calculate total amount
    const totalAmount = totalCashCollected + totalMpesaAmount;

    // Group payments by DSA agent for better organization
    const paymentsByDsa = {};
    dsaPayments.forEach(payment => {
      const dsaId = payment.DsaCustomer?.id;
      const dsaName = payment.DsaCustomer?.name || 'Unknown DSA';

      if (!paymentsByDsa[dsaId]) {
        paymentsByDsa[dsaId] = {
          dsa_id: dsaId,
          dsa_name: dsaName,
          dsa_phone: payment.DsaCustomer?.phone,
          total_cash_collected: 0,
          total_mpesa_amount: 0,
          total_amount: 0,
          payment_count: 0,
          payments: []
        };
      }

      paymentsByDsa[dsaId].total_cash_collected += parseFloat(payment.cash_amount || 0);
      paymentsByDsa[dsaId].total_mpesa_amount += parseFloat(payment.paybill_amount || 0);
      paymentsByDsa[dsaId].total_amount += parseFloat(payment.total_amount || 0);
      paymentsByDsa[dsaId].payment_count += 1;
      paymentsByDsa[dsaId].payments.push({
        id: payment.id,
        assignment_identifier: payment.assignment_identifier,
        cash_amount: parseFloat(payment.cash_amount || 0),
        paybill_amount: parseFloat(payment.paybill_amount || 0),
        total_amount: parseFloat(payment.total_amount || 0),
        payment_date: payment.payment_date,
        notes: payment.notes,
        created_by: payment.Creator ? {
          id: payment.Creator.id,
          name: payment.Creator.name
        } : null
      });
    });

    // Convert to array for easier consumption
    const dsaCollections = Object.values(paymentsByDsa);

    logger.info(`Found ${dsaPayments.length} DSA payments: ${totalCashCollected} cash + ${totalMpesaAmount} M-PESA = ${totalAmount} total for branch ${branch_id}`);

    res.status(200).json({
      success: true,
      data: {
        branch_id: parseInt(branch_id),
        date_range: {
          start_date: start_date || (shift_id ? 'shift_period' : 'today'),
          end_date: end_date || (shift_id ? 'shift_period' : 'today')
        },
        shift_id: shift_id || null,
        summary: {
          total_cash_collected: totalCashCollected,
          total_mpesa_amount: totalMpesaAmount,
          total_amount: totalAmount,
          total_payments: dsaPayments.length,
          dsa_count: dsaCollections.length
        },
        collections_by_dsa: dsaCollections,
        all_payments: dsaPayments.map(payment => ({
          id: payment.id,
          assignment_identifier: payment.assignment_identifier,
          dsa_name: payment.DsaCustomer?.name || 'Unknown DSA',
          cash_amount: parseFloat(payment.cash_amount || 0),
          paybill_amount: parseFloat(payment.paybill_amount || 0),
          total_amount: parseFloat(payment.total_amount || 0),
          payment_date: payment.payment_date,
          notes: payment.notes,
          created_by: payment.Creator ? {
            id: payment.Creator.id,
            name: payment.Creator.name
          } : null
        }))
      }
    });
  } catch (error) {
    logger.error(`Error fetching DSA cash collections: ${error.message}`);
    next(error);
  }
};
