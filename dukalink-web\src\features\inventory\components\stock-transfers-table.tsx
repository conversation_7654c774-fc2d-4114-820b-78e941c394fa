"use client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { StockTransfer } from "@/types/inventory";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Download } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { CustomPagination } from "@/components/ui/custom-pagination";
import { TransfersExportDialog } from "./transfers-export-dialog";
import { ManualSearchInput } from "@/components/ui/search-input";

interface StockTransfersTableProps {
  transfers: StockTransfer[];
  isLoading: boolean;
  onSearch?: (query: string) => void;
  pagination?: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    total?: number;
    limit?: number;
    onItemsPerPageChange?: (value: string) => void;
  };
  highlightReference?: string;
  // Props for enhanced export functionality
  currentFilters?: {
    status?: string;
    source_branch_id?: number;
    destination_branch_id?: number;
    search?: string;
    date_from?: string;
    date_to?: string;
  };
  // Search loading state
  isSearching?: boolean;
}

export function StockTransfersTable({
  transfers,
  isLoading,
  onSearch,
  pagination,
  highlightReference,
  currentFilters,
  isSearching = false,
}: StockTransfersTableProps) {
  const getStatusBadge = (status: string) => {
    // Normalize status to uppercase for case-insensitive comparison
    const normalizedStatus = status.toUpperCase();

    // Pending statuses
    if (["PENDING", "REQUESTED", "PARTIALLY_APPROVED", "FULLY_APPROVED"].includes(normalizedStatus)) {
      return <Badge variant="outline">Pending</Badge>;
    }

    // In Transit statuses
    if (["IN_TRANSIT", "DISPATCHED"].includes(normalizedStatus)) {
      return <Badge variant="secondary">In Transit</Badge>;
    }

    // Completed statuses
    if (["COMPLETED", "APPROVED", "FULLY_RECEIVED", "PARTIALLY_RECEIVED"].includes(normalizedStatus)) {
      return <Badge variant="success">Completed</Badge>;
    }

    // Cancelled statuses
    if (["CANCELLED", "REJECTED"].includes(normalizedStatus)) {
      return <Badge variant="destructive">Cancelled</Badge>;
    }

    // Default case for unknown statuses
    return <Badge>{status}</Badge>;
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <ManualSearchInput
          placeholder="Search transfers by reference, notes..."
          onSearch={onSearch}
          className="w-full max-w-sm"
          isLoading={isSearching || isLoading}
        />

        <div className="flex gap-2">
          <TransfersExportDialog
            filters={currentFilters || {}}
            totalRecords={pagination?.total || transfers.length}
            trigger={
              <Button
                variant="outline"
                disabled={isLoading || transfers.length === 0}
              >
                <Download className="mr-2 h-4 w-4" /> Export Transfers
              </Button>
            }
          />
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Reference</TableHead>
              <TableHead>Source</TableHead>
              <TableHead>Destination</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Created By</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell>
                    <Skeleton className="h-4 w-24" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-32" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-32" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-20" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-24" />
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-4 w-24" />
                  </TableCell>
                  <TableCell className="text-right">
                    <Skeleton className="h-9 w-16 ml-auto" />
                  </TableCell>
                </TableRow>
              ))
            ) : transfers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="h-24 text-center">
                  No stock transfers found.
                </TableCell>
              </TableRow>
            ) : (
              transfers.map(
                (transfer) =>
                  transfer && (
                    <TableRow
                      key={transfer.id}
                      className={highlightReference && transfer.reference_number === highlightReference
                        ? "bg-green-50"
                        : undefined
                      }>
                      <TableCell className="font-medium">
                        {transfer.reference_number ||
                          `SM-${transfer.id}` ||
                          "N/A"}
                      </TableCell>
                      <TableCell>
                        {transfer.FromBranch?.name ||
                         (transfer.FromBranch ? transfer.FromBranch.name : null) ||
                         (transfer.source_branch ? transfer.source_branch.name : null) ||
                         "HQ"}
                      </TableCell>
                      <TableCell>
                        {transfer.ToBranch?.name ||
                         (transfer.ToBranch ? transfer.ToBranch.name : null) ||
                         (transfer.destination_branch ? transfer.destination_branch.name : null) ||
                         "Unknown"}
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(transfer.status || "pending")}
                      </TableCell>
                      <TableCell>
                        {transfer.CreatedBy?.name ||
                         (transfer.CreatedBy ? transfer.CreatedBy.name : null) ||
                         (transfer.user ? transfer.user.name : null) ||
                         "Unknown"}
                      </TableCell>
                      <TableCell>
                        {transfer.created_at
                          ? new Date(transfer.created_at).toLocaleDateString()
                          : (transfer.initiated_at
                             ? new Date(transfer.initiated_at).toLocaleDateString()
                             : "N/A")}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/inventory/transfers/${transfer.id}`}>
                            <ArrowRight className="h-4 w-4" />
                          </Link>
                        </Button>
                      </TableCell>
                    </TableRow>
                  )
              )
            )}
          </TableBody>
        </Table>
      </div>

      {pagination && (
        <CustomPagination
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          onPageChange={pagination.onPageChange}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit || 100}
          onItemsPerPageChange={pagination.onItemsPerPageChange}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
