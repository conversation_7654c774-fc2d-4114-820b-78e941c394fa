const ExcelJS = require('exceljs');
const { StockMovement, Branch, User, Product } = require('../../models');
const { Op } = require('sequelize');
const logger = require('../../utils/logger');

class StockMovementsExportController {
  /**
   * Export all stock movements with comprehensive data
   */
  async getAllExportData(filters = {}) {
    try {
      const {
        status,
        source_branch_id,
        destination_branch_id,
        created_by,
        date_from,
        date_to,
        search,
        sort_by = 'created_at',
        sort_direction = 'desc'
      } = filters;

      // Build where clause for stock movements
      const whereClause = {};

      logger.info('Exporting all stock movements without role-based filtering');

      if (status) {
        whereClause.status = status;
      }

      if (source_branch_id) {
        whereClause.from_branch_id = source_branch_id;
      }

      if (destination_branch_id) {
        whereClause.to_branch_id = destination_branch_id;
      }

      if (created_by) {
        whereClause.requested_by = created_by;
      }

      if (date_from && date_to) {
        whereClause.created_at = {
          [Op.between]: [new Date(date_from), new Date(date_to)]
        };
      } else if (date_from) {
        whereClause.created_at = {
          [Op.gte]: new Date(date_from)
        };
      } else if (date_to) {
        whereClause.created_at = {
          [Op.lte]: new Date(date_to)
        };
      }

      // Add search functionality
      const includeClause = [
        {
          model: Branch,
          as: 'FromBranch',
          attributes: ['id', 'name', 'location'],
          required: false
        },
        {
          model: Branch,
          as: 'ToBranch',
          attributes: ['id', 'name', 'location'],
          required: false
        },
        {
          model: User,
          as: 'RequestedBy',
          attributes: ['id', 'name', 'email'],
          required: false
        }
      ];

      if (search) {
        whereClause[Op.or] = [
          { reference_number: { [Op.like]: `%${search}%` } },
          { notes: { [Op.like]: `%${search}%` } }
        ];
      }

      // Fetch stock movements
      const movements = await StockMovement.findAll({
        where: whereClause,
        include: includeClause,
        order: [[sort_by, sort_direction.toUpperCase()]],
        raw: false
      });

      logger.info(`Found ${movements.length} stock movements for export`);
      logger.info(`Export query filters:`, JSON.stringify(filters, null, 2));
      logger.info(`Export where clause:`, JSON.stringify(whereClause, null, 2));

      // Transform movements to export format
      const transformedMovements = movements.map((movement) => ({
        id: movement.id,
        reference_number: movement.reference_number || `SM-${movement.id}`,
        source_branch: movement.FromBranch?.name || 'HQ',
        destination_branch: movement.ToBranch?.name || 'Unknown',
        status: movement.status || 'pending',
        created_by: movement.RequestedBy?.name || 'Unknown',
        created_at: movement.created_at,
        updated_at: movement.updated_at,
        notes: movement.notes || '',
        // Additional fields for comprehensive export
        source_branch_location: movement.FromBranch?.location || '',
        destination_branch_location: movement.ToBranch?.location || '',
        created_by_email: movement.RequestedBy?.email || '',
        items_count: movement.items_count || 0,
        total_quantity: movement.total_quantity || 0,
      }));

      return transformedMovements;
    } catch (error) {
      logger.error('Error getting stock movements export data:', error);
      throw error;
    }
  }

  /**
   * Generate summary statistics
   */
  generateSummaryStats(movements, filters) {
    const stats = {
      total_movements: movements.length,
      by_status: {},
      by_source_branch: {},
      by_destination_branch: {},
      date_range: {
        earliest: null,
        latest: null
      },
      filters_applied: filters
    };

    // Count by status
    movements.forEach(movement => {
      const status = movement.status || 'unknown';
      stats.by_status[status] = (stats.by_status[status] || 0) + 1;
    });

    // Count by source branch
    movements.forEach(movement => {
      const branch = movement.source_branch || 'unknown';
      stats.by_source_branch[branch] = (stats.by_source_branch[branch] || 0) + 1;
    });

    // Count by destination branch
    movements.forEach(movement => {
      const branch = movement.destination_branch || 'unknown';
      stats.by_destination_branch[branch] = (stats.by_destination_branch[branch] || 0) + 1;
    });

    // Date range
    if (movements.length > 0) {
      const dates = movements.map(m => new Date(m.created_at)).sort();
      stats.date_range.earliest = dates[0];
      stats.date_range.latest = dates[dates.length - 1];
    }

    return stats;
  }

  /**
   * Create Excel workbook with multiple sheets
   */
  async createExcelWorkbook(movements, options = {}) {
    const {
      include_summary = true,
      include_details = true,
      include_status_breakdown = true,
      include_branch_breakdown = true,
      filters = {}
    } = options;

    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'DukaLink POS System';
    workbook.created = new Date();

    // Generate summary statistics
    const stats = this.generateSummaryStats(movements, filters);

    // Summary Sheet
    if (include_summary) {
      const summarySheet = workbook.addWorksheet('Summary');

      // Title
      summarySheet.addRow(['Stock Movements Export Summary']);
      summarySheet.addRow(['Generated on:', new Date().toLocaleString()]);
      summarySheet.addRow([]);

      // Key metrics
      summarySheet.addRow(['Total Movements:', stats.total_movements]);
      summarySheet.addRow(['Date Range:',
        stats.date_range.earliest ? stats.date_range.earliest.toLocaleDateString() : 'N/A',
        'to',
        stats.date_range.latest ? stats.date_range.latest.toLocaleDateString() : 'N/A'
      ]);
      summarySheet.addRow([]);

      // Applied filters
      summarySheet.addRow(['Applied Filters:']);
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          summarySheet.addRow([`${key}:`, value]);
        }
      });

      // Style the summary sheet
      summarySheet.getCell('A1').font = { bold: true, size: 16 };
      summarySheet.getColumn('A').width = 20;
      summarySheet.getColumn('B').width = 20;
    }

    // Movements Details Sheet
    if (include_details) {
      const detailsSheet = workbook.addWorksheet('Movements');

      // Headers
      const headers = [
        'Reference Number',
        'Source Branch',
        'Destination Branch',
        'Status',
        'Created By',
        'Date Created',
        'Notes',
        'Items Count',
        'Total Quantity'
      ];

      detailsSheet.addRow(headers);

      // Data rows
      movements.forEach(movement => {
        detailsSheet.addRow([
          movement.reference_number,
          movement.source_branch,
          movement.destination_branch,
          movement.status,
          movement.created_by,
          movement.created_at ? new Date(movement.created_at).toLocaleDateString() : '',
          movement.notes,
          movement.items_count,
          movement.total_quantity
        ]);
      });

      // Style the headers
      const headerRow = detailsSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Auto-fit columns
      detailsSheet.columns.forEach(column => {
        column.width = 15;
      });
    }

    // Status Breakdown Sheet
    if (include_status_breakdown && Object.keys(stats.by_status).length > 0) {
      const statusSheet = workbook.addWorksheet('Status Breakdown');

      statusSheet.addRow(['Status', 'Count', 'Percentage']);

      Object.entries(stats.by_status).forEach(([status, count]) => {
        const percentage = ((count / stats.total_movements) * 100).toFixed(1);
        statusSheet.addRow([status, count, `${percentage}%`]);
      });

      // Style headers
      const headerRow = statusSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      statusSheet.columns.forEach(column => {
        column.width = 15;
      });
    }

    // Branch Breakdown Sheet
    if (include_branch_breakdown && Object.keys(stats.by_source_branch).length > 0) {
      const branchSheet = workbook.addWorksheet('Branch Breakdown');

      branchSheet.addRow(['Source Branch Analysis']);
      branchSheet.addRow(['Branch', 'Outgoing Movements']);

      Object.entries(stats.by_source_branch).forEach(([branch, count]) => {
        branchSheet.addRow([branch, count]);
      });

      branchSheet.addRow([]);
      branchSheet.addRow(['Destination Branch Analysis']);
      branchSheet.addRow(['Branch', 'Incoming Movements']);

      Object.entries(stats.by_destination_branch).forEach(([branch, count]) => {
        branchSheet.addRow([branch, count]);
      });

      // Style
      branchSheet.getCell('A1').font = { bold: true };
      branchSheet.getCell('A6').font = { bold: true };
      branchSheet.columns.forEach(column => {
        column.width = 20;
      });
    }

    return workbook;
  }
}

// Route handlers
const exportAllStockMovements = async (req, res, next) => {
  try {
    logger.info('Starting comprehensive stock movements export', {
      filters: req.query,
      user: req.user?.id
    });

    const controller = new StockMovementsExportController();
    const movements = await controller.getAllExportData(req.query);

    const workbook = await controller.createExcelWorkbook(movements, {
      include_summary: true,
      include_details: true,
      include_status_breakdown: true,
      include_branch_breakdown: true,
      filters: req.query
    });

    // Set response headers for Excel download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=stock-movements-export.xlsx');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info('Stock movements export completed successfully', {
      movementsCount: movements.length,
      user: req.user?.id
    });

  } catch (error) {
    logger.error('Error exporting stock movements:', error);
    next(error);
  }
};

const exportCustomStockMovements = async (req, res, next) => {
  try {
    logger.info('Starting custom stock movements export', {
      filters: req.query,
      user: req.user?.id
    });

    const {
      include_summary = true,
      include_details = true,
      include_status_breakdown = true,
      include_branch_breakdown = true,
      format_type = 'detailed'
    } = req.query;

    const controller = new StockMovementsExportController();
    const movements = await controller.getAllExportData(req.query);

    const workbook = await controller.createExcelWorkbook(movements, {
      include_summary: include_summary === 'true',
      include_details: include_details === 'true',
      include_status_breakdown: include_status_breakdown === 'true',
      include_branch_breakdown: include_branch_breakdown === 'true',
      filters: req.query
    });

    // Set response headers for Excel download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=stock-movements-custom-export.xlsx');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info('Custom stock movements export completed successfully', {
      movementsCount: movements.length,
      user: req.user?.id
    });

  } catch (error) {
    logger.error('Error exporting custom stock movements:', error);
    next(error);
  }
};

module.exports = {
  StockMovementsExportController,
  exportAllStockMovements,
  exportCustomStockMovements
};
