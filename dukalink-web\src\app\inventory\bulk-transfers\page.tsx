"use client";

import { MainLayout } from "@/components/layouts/main-layout";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { RefreshCw } from "lucide-react";
import { useAuthTokens } from "@/hooks/use-auth-tokens";
import apiClient from "@/lib/api-client";
import { StockMovementItemsTable } from "@/features/inventory/components/stock-movement-items-table";
import { useStockMovementItemsSearch } from "@/features/inventory/hooks/use-stock-movement-items-search";

export default function BulkTransfersReportPage() {
  const router = useRouter();
  const { accessToken, isInitialized } = useAuthTokens();
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(100);
  const [queryParams, setQueryParams] = useState<Record<string, any>>({});
  const [isChangingPage, setIsChangingPage] = useState(false);

  // Initialize API client with auth token
  useEffect(() => {
    if (accessToken) {
      apiClient.setAuthToken(accessToken);
    } else if (isInitialized && !accessToken) {
      // If auth is initialized but no token, redirect to login
      router.push('/login');
    }
  }, [accessToken, isInitialized, router]);

  const { data: itemsData, isLoading, refetch } = useStockMovementItemsSearch(
    {
      ...queryParams,
      page: currentPage,
      limit: itemsPerPage,
    },
    {
      // Only enable the query if the user is authenticated
      enabled: !!accessToken && isInitialized
    }
  );

  // Ensure we have a valid array of items
  const items = itemsData?.data || [];
  const totalPages = itemsData?.pagination?.totalPages || 1;
  const totalRecords = itemsData?.pagination?.total || items.length;

  const handleSearch = (query: string) => {
    setQueryParams((prev) => ({
      ...prev,
      search: query || undefined,
    }));
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setIsChangingPage(true);
    setCurrentPage(page);
    // Reset loading state after a brief delay to show loading indicator
    setTimeout(() => setIsChangingPage(false), 100);
  };

  const handleItemsPerPageChange = (value: string) => {
    setIsChangingPage(true);
    setItemsPerPage(parseInt(value));
    setCurrentPage(1); // Reset to first page when changing items per page
    // Reset loading state after a brief delay to show loading indicator
    setTimeout(() => setIsChangingPage(false), 100);
  };

  return (
    <MainLayout>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">All Stock Transfers</h1>
            <p className="text-muted-foreground">
              View all stock transfer items across all branches with their reference numbers
              {totalRecords > 0 && (
                <span className="ml-2">
                  • {totalRecords.toLocaleString()} total items
                </span>
              )}
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => {
              router.push("/inventory/bulk-transfer");
            }}
          >
            Make Stock Transfer
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Stock Movement Items</CardTitle>
          </CardHeader>
          <CardContent>
            <StockMovementItemsTable
              items={items}
              isLoading={isLoading || isChangingPage}
              onSearch={handleSearch}
              pagination={{
                currentPage,
                totalPages,
                onPageChange: handlePageChange,
                total: totalRecords,
                limit: itemsPerPage,
                onItemsPerPageChange: handleItemsPerPageChange,
              }}
              currentFilters={{
                search: queryParams.search,
              }}
            />
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
