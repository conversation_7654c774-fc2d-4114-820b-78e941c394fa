import apiClient from "@/lib/api-client";
import { StockLevelsParams } from "@/types/stock-levels";

/**
 * Enhanced Stock Levels Export Service
 * Provides methods for comprehensive Excel export functionality
 */

export interface StockLevelsExportParams extends Omit<StockLevelsParams, 'format' | 'page' | 'limit'> {
  include_summary?: boolean;
  include_alerts?: boolean;
  include_categories?: boolean;
  include_branches?: boolean;
}

export interface CustomExportParams extends StockLevelsExportParams {
  columns?: string; // comma-separated list or "all"
  format_type?: 'detailed' | 'summary';
}

export interface ExportProgress {
  status: 'preparing' | 'processing' | 'downloading' | 'completed' | 'error';
  message: string;
  progress?: number;
}

const stockLevelsExportService = {
  /**
   * Export all stock levels with comprehensive data
   * Includes all sheets: Summary, Products, Categories, Branches, Alerts
   */
  exportAllStockLevels: async (params?: StockLevelsExportParams): Promise<Blob> => {
    try {
      console.log("Starting comprehensive stock levels export...", params);

      const response: any = await apiClient.get("/reports/stock-levels-export/all", {
        params: {
          ...params,
          include_summary: params?.include_summary ?? true,
          include_alerts: params?.include_alerts ?? true,
          include_categories: params?.include_categories ?? true,
          include_branches: params?.include_branches ?? true,
        },
        responseType: "blob",
        timeout: 120000, // 2 minutes timeout for large exports
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting all stock levels:", error);

      // Provide more specific error messages
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export stock reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. The dataset might be too large. Try filtering the data.");
      } else {
        throw new Error(error.message || "Failed to export stock levels");
      }
    }
  },

  /**
   * Export stock levels with custom options
   * Allows selection of format type and specific columns
   */
  exportCustomStockLevels: async (params?: CustomExportParams): Promise<Blob> => {
    try {
      console.log("Starting custom stock levels export...", params);

      const response: any = await apiClient.get("/reports/stock-levels-export/custom", {
        params: {
          ...params,
          columns: params?.columns || "all",
          format_type: params?.format_type || "detailed",
        },
        responseType: "blob",
        timeout: 90000, // 1.5 minutes timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting custom stock levels:", error);

      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export stock reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. Try using summary format or filtering the data.");
      } else {
        throw new Error(error.message || "Failed to export custom stock levels");
      }
    }
  },

  /**
   * Export summary format (lightweight)
   * Quick export with essential data only
   */
  exportSummaryStockLevels: async (params?: StockLevelsExportParams): Promise<Blob> => {
    return stockLevelsExportService.exportCustomStockLevels({
      ...params,
      format_type: 'summary',
      columns: 'name,sku,category,quantity,status,branch',
    });
  },

  /**
   * Download blob as file with proper filename
   * Utility method for handling file downloads
   */
  downloadBlob: (blob: Blob, filename?: string): void => {
    try {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Generate filename if not provided
      if (!filename) {
        const timestamp = new Date().toISOString().split('T')[0];
        filename = `stock-levels-export-${timestamp}.xlsx`;
      }

      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log(`File downloaded: ${filename}`);
    } catch (error) {
      console.error('Error downloading file:', error);
      throw new Error('Failed to download the export file');
    }
  },

  /**
   * Generate descriptive filename based on filters
   */
  generateFilename: (params?: StockLevelsExportParams, formatType: string = 'comprehensive'): string => {
    const timestamp = new Date().toISOString().split('T')[0];
    const parts = ['stock-levels'];

    if (formatType !== 'comprehensive') {
      parts.push(formatType);
    }

    if (params?.branch_id) {
      parts.push(`branch-${params.branch_id}`);
    }

    if (params?.region_id) {
      parts.push(`region-${params.region_id}`);
    }

    if (params?.category_id) {
      parts.push(`category-${params.category_id}`);
    }

    if (params?.search) {
      parts.push('filtered');
    }

    if (params?.include_zero_stock === false) {
      parts.push('in-stock-only');
    }

    parts.push(timestamp);

    return `${parts.join('-')}.xlsx`;
  },

  /**
   * Estimate export size and provide recommendations
   */
  getExportRecommendation: (estimatedRecords: number): {
    recommended: 'all' | 'custom' | 'summary';
    message: string;
    estimatedTime: string;
  } => {
    if (estimatedRecords <= 1000) {
      return {
        recommended: 'all',
        message: 'Small dataset - comprehensive export recommended',
        estimatedTime: '< 30 seconds'
      };
    } else if (estimatedRecords <= 5000) {
      return {
        recommended: 'custom',
        message: 'Medium dataset - custom export with selected sheets recommended',
        estimatedTime: '30-60 seconds'
      };
    } else {
      return {
        recommended: 'summary',
        message: 'Large dataset - summary export recommended for faster processing',
        estimatedTime: '1-2 minutes'
      };
    }
  },

  /**
   * Validate export parameters
   */
  validateExportParams: (params?: StockLevelsExportParams): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (params?.branch_id && (params.branch_id < 1 || !Number.isInteger(params.branch_id))) {
      errors.push('Branch ID must be a positive integer');
    }

    if (params?.region_id && (params.region_id < 1 || !Number.isInteger(params.region_id))) {
      errors.push('Region ID must be a positive integer');
    }

    if (params?.category_id && (params.category_id < 1 || !Number.isInteger(params.category_id))) {
      errors.push('Category ID must be a positive integer');
    }

    if (params?.search && params.search.length < 2) {
      errors.push('Search term must be at least 2 characters long');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Get export format options for UI
   */
  getExportFormatOptions: () => [
    {
      value: 'all',
      label: 'Comprehensive Export',
      description: 'All data with summary, products, categories, branches, and alerts',
      icon: 'FileSpreadsheet',
      estimatedTime: 'Slower but complete'
    },
    {
      value: 'custom',
      label: 'Custom Export',
      description: 'Select specific sheets and columns to include',
      icon: 'Settings',
      estimatedTime: 'Customizable speed'
    },
    {
      value: 'summary',
      label: 'Summary Export',
      description: 'Essential data only for quick analysis',
      icon: 'Zap',
      estimatedTime: 'Fast and lightweight'
    }
  ],

  /**
   * Get available column options for custom export
   */
  getColumnOptions: () => [
    { value: 'id', label: 'Product ID', category: 'basic' },
    { value: 'name', label: 'Product Name', category: 'basic' },
    { value: 'sku', label: 'SKU', category: 'basic' },
    { value: 'category', label: 'Category', category: 'basic' },
    { value: 'brand', label: 'Brand', category: 'basic' },
    { value: 'quantity', label: 'Current Quantity', category: 'stock' },
    { value: 'status', label: 'Stock Status', category: 'stock' },
    { value: 'buying_price', label: 'Buying Price', category: 'pricing' },
    { value: 'selling_price', label: 'Selling Price', category: 'pricing' },
    { value: 'margin', label: 'Margin %', category: 'pricing' },
    { value: 'total_value', label: 'Total Value', category: 'pricing' },
    { value: 'branch', label: 'Branch', category: 'location' },
    { value: 'region', label: 'Region', category: 'location' },
    { value: 'min_stock', label: 'Min Stock Level', category: 'stock' },
    { value: 'last_restocked', label: 'Last Restocked', category: 'stock' },
  ]
};

export default stockLevelsExportService;
