/**
 * KRA Integration Service
 * Handles integration with Kenya Revenue Authority (KRA) for fiscal receipts
 */
const axios = require("axios");
const fs = require("fs");
const https = require("https");
const crypto = require("crypto");
const QRCode = require("qrcode");
const logger = require("../utils/logger");
const timsLogger = require("../utils/tims-logger");

/**
 * Generate a random verification code
 * @returns {string} - Random verification code
 */
function generateVerificationCode() {
  return crypto.randomBytes(8).toString("hex").toUpperCase();
}

/**
 * KRA Integration Service
 */
class KraIntegrationService {
  constructor() {
    // Load configuration from environment variables
    this.prodApiEndpoint = process.env.KRA_API_ENDPOINT;
    this.devApiEndpoint = process.env.KRA_DEV_API_ENDPOINT;
    this.useDevApi = process.env.KRA_USE_DEV_API;
    this.apiKey = process.env.KRA_API_KEY;
    this.certificatePath = process.env.KRA_CERTIFICATE_PATH;
    this.enabled = true; // Force enabled for testing

    // Use sample data mode - controlled by environment variable
    this.useSampleData = process.env.KRA_USE_SAMPLE_DATA;

    // Determine which API endpoint to use
    this.apiEndpoint = this.useDevApi
      ? this.devApiEndpoint
      : this.prodApiEndpoint;

    // Initialize the service
    this.init();

    // Log the current mode
    if (this.useSampleData) {
      logger.warn('KRA Integration Service initialized with SAMPLE DATA mode');
    } else {
      logger.info('KRA Integration Service initialized with PRODUCTION mode - sample data disabled');
    }
  }

  /**
   * Initialize the KRA integration service
   */
  init() {
    // Check if required configuration is available
    if (!this.apiKey && !this.useSampleData) {
      logger.warn("KRA Integration is missing required configuration");
    }

    // Log initialization mode
    if (this.useDevApi) {
      logger.info(
        `KRA Integration Service initialized in DEVELOPMENT mode using endpoint: ${this.devApiEndpoint}`
      );
    } else {
      logger.info(
        `KRA Integration Service initialized in PRODUCTION mode using endpoint: ${this.prodApiEndpoint}`
      );
    }
  }

  /**
   * Register a sale with KRA
   * @param {Object} saleData - The sale data to register
   * @returns {Promise<Object>} - KRA verification data
   */
  async registerSale(saleData) {
    logger.info(`Registering sale ID ${saleData.id} with KRA`);

    try {
      // Always use the new KRA integration endpoint
      logger.info(`Using KRA process-sale endpoint for sale ID ${saleData.id}`);
      return await this.useNewKraEndpoint(saleData);
    } catch (error) {
      logger.error(`KRA API error for sale ID ${saleData.id}: ${error.message}`);
      logger.error(error.stack);

      // No offline mode - fail the sale if KRA integration fails
      logger.error(`KRA integration failed for sale ID ${saleData.id}. Sale will be rejected.`);
      throw error; // Re-throw the original KRA API error
    }
  }

  /**
   * Format sale data according to KRA requirements
   * @param {Object} saleData - The sale data to format
   * @returns {Object} - Formatted sale data
   */
  formatSaleForKRA(saleData) {
    // Extract relevant data
    const { id, total_amount, created_at, Branch, User, SaleItems } = saleData;

    // Format items
    const items = SaleItems.map((item) => ({
      name: item.Product.name,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.total_price,
      tax_rate: 16, // Assume 16% VAT
    }));

    // Calculate tax amount (16% VAT)
    const taxAmount = total_amount * 0.16;
    const subtotal = total_amount - taxAmount;

    // Format data according to KRA requirements
    return {
      invoice_number: String(id),
      invoice_date: created_at,
      seller_info: {
        name: Branch?.name || "Unknown Branch",
        pin_number: "P051234567X", // Placeholder PIN number
        address: Branch?.location || "Unknown Location",
        contact_number: Branch?.phone || "Unknown Phone",
      },
      buyer_info: {
        name: "Walk-in Customer",
        pin_number: "",
        address: "",
        contact_number: "",
      },
      items,
      totals: {
        subtotal,
        tax_amount: taxAmount,
        total_amount,
      },
      payment_info: {
        method: "Cash",
        amount: total_amount,
      },
      user_info: {
        name: User?.name || "Unknown User",
        id: User?.id || 0,
      },
    };
  }





  /**
   * Register an invoice with KRA
   * @param {Object} invoice - The invoice to register
   * @returns {Promise<Object>} KRA response data
   */
  async registerInvoice(invoice) {
    logger.info(
      `Registering invoice ${invoice.invoice_number || invoice.id} with KRA`
    );

    try {
      // Adapt the invoice to a sale-like structure for the KRA endpoint
      const adaptedData = {
        id: invoice.id,
        Branch: invoice.Branch,
        Customer: invoice.Customer,
        SaleItems:
          invoice.InvoiceItems?.map((item) => ({
            Product: item.Product,
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.total_price,
            vat_rate: item.vat_rate || 16,
          })) || [],
      };

      // Use the same KRA endpoint as for sales
      logger.info(`Using KRA process-sale endpoint for invoice ${invoice.id}`);
      return await this.useNewKraEndpoint(adaptedData);
    } catch (error) {
      logger.error(`KRA API error for invoice ID ${invoice.id}: ${error.message}`);
      logger.error(error.stack);

      // No offline mode - fail the invoice if KRA integration fails
      logger.error(`KRA integration failed for invoice ID ${invoice.id}. Invoice will be rejected.`);
      throw error; // Re-throw the original KRA API error
    }
  }

  /**
   * Generate a QR code for a URL
   * @param {string} url - The URL to encode in the QR code
   * @returns {Promise<string>} - Data URL of the QR code
   */
  async generateQRCodeForUrl(url) {
    try {
      // Check if URL is valid
      if (!url || url.trim() === "") {
        logger.warn("Cannot generate QR code for empty URL");
        return null;
      }

      // Clean the URL (remove trailing spaces)
      const cleanUrl = url.trim();

      // Log the URL being used for QR code generation
      logger.info(`Generating QR code for URL: ${cleanUrl}`);

      // Generate the QR code with high error correction level
      const qrCodeImage = await QRCode.toDataURL(cleanUrl, {
        errorCorrectionLevel: "H",
        margin: 1,
        width: 300,
      });

      // Log success
      logger.info(
        `Successfully generated QR code, data URL length: ${qrCodeImage ? qrCodeImage.length : 0} characters`
      );

      return qrCodeImage;
    } catch (error) {
      logger.error(`Error generating QR code for URL: ${error.message}`);

      // Try with a simplified URL if the original fails
      try {
        if (url && url.includes("?")) {
          const simplifiedUrl = url.split("?")[0];
          logger.info(
            `Retrying QR code generation with simplified URL: ${simplifiedUrl}`
          );

          return await QRCode.toDataURL(simplifiedUrl, {
            errorCorrectionLevel: "H",
            margin: 1,
            width: 300,
          });
        }
      } catch (retryError) {
        logger.error(
          `Error generating QR code with simplified URL: ${retryError.message}`
        );
      }

      // If all else fails, generate a QR code with a simple text
      try {
        const fallbackText = `DukaLink Receipt: ${new Date().toISOString()}`;
        logger.info(`Using fallback text for QR code: ${fallbackText}`);

        return await QRCode.toDataURL(fallbackText, {
          errorCorrectionLevel: "H",
          margin: 1,
          width: 300,
        });
      } catch (fallbackError) {
        logger.error(
          `Error generating fallback QR code: ${fallbackError.message}`
        );
        return null;
      }
    }
  }

  /**
   * Generate sample KRA data for testing
   * @param {Object} invoice - The invoice (not used but kept for API compatibility)
   * @returns {Promise<Object>} Sample KRA data
   */
  async generateSampleKraDataForInvoice(_invoice) {
    // Generate data in the new format
    const invoiceNum = `00801107900000${Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0")}`;
    const cuNumber = `KRAMW00820220701${Math.floor(Math.random() * 10000)
      .toString()
      .padStart(4, "0")}`;
    const qrCode = `https://tims-test.kra.go.ke/KRA-Portal/invoiceChk.htm?actionCode=loadPage&invoiceNo=${invoiceNum}`;
    const dateTime = new Date().toISOString();

    // Generate QR code
    const qrCodeImage = await this.generateQRCodeForUrl(qrCode);

    return {
      success: true,
      verificationCode: cuNumber,
      fiscalReceiptNumber: invoiceNum,
      verificationUrl: qrCode,
      qrCodeUrl: qrCode,
      qrCodeImage,
      timestamp: new Date(dateTime),
      offline: true,
      fullResponseData: JSON.stringify({
        invoiceNum,
        qrCode,
        cuNumber,
        dateTime,
      }),
    };
  }

  /**
   * Use the new KRA integration endpoint
   * @param {Object} saleData - The sale data to register
   * @returns {Promise<Object>} - KRA verification data
   */
  async useNewKraEndpoint(saleData) {
    try {
      logger.info(`Using new KRA endpoint for sale ID ${saleData.id}`);

      // Check if we have the raw sale data from the mobile app
      const rawItems = saleData.raw_items || null;
      const rawCustomerInfo = saleData.raw_customer_info || null;

      let formattedItems, customerInfo;

      if (rawItems && Array.isArray(rawItems) && rawItems.length > 0) {
        // Use the raw items directly from the mobile app
        logger.info(
          `Using raw items from mobile app for sale ID ${saleData.id}`
        );
        formattedItems = rawItems.map((item) => {
          // Get the raw VAT rate value
          const rawVatRate = item.VATGrRate || item.vat_rate || 16;

          // Log the raw VAT rate for debugging
          logger.info(
            `Raw VAT rate value for item ${item.NamePLU || item.name || "Unknown"}: ${rawVatRate} (type: ${typeof rawVatRate})`
          );

          // Parse the VAT rate to a number
          const vatRate = parseFloat(rawVatRate);

          // Simplified VAT logic: Only check is_vat_exempt boolean field
          // If is_vat_exempt === true, VAT rate = 0%, Tax class = "E"
          // If is_vat_exempt !== true, VAT rate = 16%, Tax class = "A"
          const isExemptByProperty = item.is_vat_exempt === true || item.isVatExempt === true;
          const isExemptByRate =
            vatRate === 0 ||
            rawVatRate === "0" ||
            rawVatRate === "0.0" ||
            rawVatRate === "0.00";
          const isExempt = isExemptByProperty || isExemptByRate;

          // Log detailed exemption status
          logger.info(`Raw item ${item.NamePLU || item.name || "Unknown"}:`);
          logger.info(
            `  - is_vat_exempt property: ${isExemptByProperty ? "Yes" : "No"}`
          );
          logger.info(
            `  - VAT rate is zero: ${isExemptByRate ? "Yes" : "No"} (rate: ${vatRate}, raw: ${rawVatRate})`
          );
          logger.info(
            `  - Final exemption status: ${isExempt ? "Exempt" : "Taxable"}`
          );

          // Set VAT class based on exemption status
          const providedVatClass = item.OptionVATClass;
          const calculatedVatClass = isExempt ? "E" : "A";
          const vatClass = providedVatClass || calculatedVatClass;

          // Log VAT class information
          logger.info(
            `  - Provided OptionVATClass: ${providedVatClass || "None"}`
          );
          logger.info(`  - Calculated OptionVATClass: ${calculatedVatClass}`);
          logger.info(`  - Final OptionVATClass: ${vatClass}`);

          // Set HS code based on exemption status
          let hsCode = "";
          if (isExempt) {
            // Always use the default HS code for exempt items
            hsCode = "0016.21.00";
            logger.info(
              `Raw item ${item.NamePLU || item.name} is VAT exempt, using HS code 0016.21.00`
            );
          } else {
            hsCode = item.HSCode || "";
          }

          // Format VAT rate - string "0" for exempt, string "16" for taxable
          const formattedVatRate = isExempt ? "0" : "16";

          // Handle discount information for raw items
          let discountValue = "";
          if (item.DiscAddP) {
            // Use provided DiscAddP if available
            discountValue = item.DiscAddP;
          } else if (item.discount_percentage && parseFloat(item.discount_percentage) > 0) {
            // Format discount percentage as negative string
            discountValue = `-${parseFloat(item.discount_percentage)}`;
          }

          // Log discount information
          logger.info(
            `Raw item ${item.NamePLU || item.name} discount: DiscAddP="${discountValue}"`
          );

          return {
            NamePLU: item.NamePLU || item.name || "Unknown Product",
            OptionVATClass: vatClass,
            Price: parseFloat(item.Price || item.price || 0),
            MeasureUnit: item.MeasureUnit || "Pcs",
            HSCode: hsCode,
            HSName: item.HSName || "",
            VATGrRate: formattedVatRate,
            Quantity: parseFloat(item.Quantity || item.quantity || 0),
            DiscAddP: discountValue,
          };
        });
      } else {
        // Format the sale data for KRA using our standard method
        formattedItems = this.formatItemsForKRA(saleData);
      }

      if (rawCustomerInfo) {
        // Use the raw customer info directly from the mobile app
        logger.info(
          `Using raw customer info from mobile app for sale ID ${saleData.id}`
        );

        // Ensure the sale ID is properly formatted for the TraderSystemInvNum
        const saleId = saleData.id || "";
        const traderSystemInvNum = `INV${saleId}`;

        logger.info(
          `Generated TraderSystemInvNum: ${traderSystemInvNum} for sale ID ${saleId}`
        );

        customerInfo = {
          CompanyName:
            rawCustomerInfo.CompanyName ||
            rawCustomerInfo.company_name ||
            "Walk-in Customer",
          ClientPINnum:
            rawCustomerInfo.ClientPINnum || rawCustomerInfo.pin_number || "",
          HeadQuarters: rawCustomerInfo.HeadQuarters || "",
          Address: rawCustomerInfo.Address || rawCustomerInfo.address || "",
          PostalCodeAndCity:
            rawCustomerInfo.PostalCodeAndCity || "00100 Nairobi",
          ExemptionNum: rawCustomerInfo.ExemptionNum || "",
          TraderSystemInvNum: traderSystemInvNum,
        };
      } else {
        // Format the customer info using our standard method
        customerInfo = this.formatCustomerInfoForKRA(saleData);
      }

      // Prepare the request data
      const requestData = {
        items: formattedItems,
        customerInfo,
        saleId: saleData.id, // Include the sale ID in the request
        id: saleData.id, // Also include as 'id' for compatibility
      };

      logger.info(
        `Formatted KRA request data: ${JSON.stringify(requestData, null, 2)}`
      );

      // Make the request to the new KRA endpoint
      const kraEndpoint = process.env.KRA_PROCESS_SALE_ENDPOINT;

      // Validate that the KRA endpoint is configured
      if (!kraEndpoint) {
        const error = new Error('KRA_PROCESS_SALE_ENDPOINT environment variable is not configured');
        logger.error(`KRA endpoint not configured: ${error.message}`);
        throw error;
      }

      logger.info(`Using KRA endpoint from environment variable: ${kraEndpoint}`);

      // Create request options
      const requestOptions = {
        headers: {
          "Content-Type": "application/json",
        },
      };

      // Send the request
      logger.info(`Sending request to KRA endpoint: ${kraEndpoint}`);
      const response = await axios.post(
        kraEndpoint,
        requestData,
        requestOptions
      );

      logger.info(
        `KRA response received: ${JSON.stringify(response.data, null, 2)}`
      );

      // Extract the KRA data from the response
      const kraData = response.data.kraData || response.data;

      logger.info(`Received KRA data for sale ID ${saleData.id}`);

      // Clean up the QR code URL (remove trailing spaces)
      const qrCodeUrl = kraData.qrCode ? kraData.qrCode.trim() : "";

      logger.info(`QR Code URL (after trimming): "${qrCodeUrl}"`);

      // Generate QR code image
      logger.info(`Generating QR code image for URL: ${qrCodeUrl}`);
      const qrCodeImage = await this.generateQRCodeForUrl(qrCodeUrl);

      // Log whether QR code image was generated
      logger.info(`QR code image generated: ${qrCodeImage ? "Yes" : "No"}`);
      if (!qrCodeImage) {
        logger.warn(`Failed to generate QR code image for URL: ${qrCodeUrl}`);

        // If QR code generation failed but we have a URL, try again with a simplified URL
        if (qrCodeUrl) {
          logger.info(`Retrying QR code generation with simplified URL`);
          const simplifiedUrl = qrCodeUrl.split("?")[0]; // Remove query parameters
          const retryQrCodeImage =
            await this.generateQRCodeForUrl(simplifiedUrl);

          if (retryQrCodeImage) {
            logger.info(
              `Successfully generated QR code with simplified URL: ${simplifiedUrl}`
            );
            return {
              success: true,
              verificationCode: kraData.cuNumber || "",
              fiscalReceiptNumber: kraData.invoiceNum || "",
              verificationUrl: qrCodeUrl, // Keep the original URL for verification
              qrCodeUrl: qrCodeUrl, // Also store as qrCodeUrl for compatibility
              qrCodeImage: retryQrCodeImage,
              timestamp: kraData.dateTime
                ? new Date(kraData.dateTime)
                : new Date(),
              offline: false,
              fullResponseData: JSON.stringify(kraData),
            };
          }
        }
      }

      // Return the KRA data in the expected format
      return {
        success: true,
        verificationCode: kraData.cuNumber || "",
        fiscalReceiptNumber: kraData.invoiceNum || "",
        verificationUrl: qrCodeUrl,
        qrCodeUrl: qrCodeUrl, // Also store as qrCodeUrl for compatibility
        qrCodeImage,
        timestamp: kraData.dateTime ? new Date(kraData.dateTime) : new Date(),
        offline: false,
        fullResponseData: JSON.stringify(kraData),
      };
    } catch (error) {
      logger.error(`KRA API error for sale ID ${saleData.id}: ${error.message}`);
      logger.error(error.stack);

      // Do not generate test data - return the actual error from KRA API
      logger.error(`KRA integration failed for sale ID ${saleData.id}. Returning actual error instead of test data.`);
      throw error; // Re-throw the original KRA API error
    }
  }

  /**
   * Format sale items for KRA
   * @param {Object} saleData - The sale data
   * @returns {Array} - Formatted items
   */
  formatItemsForKRA(saleData) {
    const { SaleItems } = saleData;

    // Log the sale data for debugging
    logger.info(`Formatting items for KRA from sale ID ${saleData.id}`);
    logger.info(`Sale items count: ${SaleItems?.length || 0}`);

    if (!SaleItems || SaleItems.length === 0) {
      logger.warn(`No sale items found for sale ID ${saleData.id}`);
      return [];
    }

    return SaleItems.map((item) => {
      const product = item.Product;

      if (!product) {
        logger.warn(
          `Product not found for sale item in sale ID ${saleData.id}`
        );
      }

      // Simplified VAT logic: Only use is_vat_exempt boolean field
      // If is_vat_exempt === true, VAT rate = 0%, Tax class = "E"
      // If is_vat_exempt !== true, VAT rate = 16%, Tax class = "A"
      const isExempt = product?.is_vat_exempt === true;
      const vatRate = isExempt ? 0 : 16;

      // Log the raw VAT rate for debugging
      logger.info(
        `Raw VAT rate for product ${product?.name || "Unknown"}: ${vatRate}`
      );

      // Set VAT class based on exemption status
      const vatClass = isExempt ? "E" : "A"; // A for taxable, E for exempt

      // Log detailed exemption status
      logger.info(
        `Product ${product?.name || "Unknown"} (ID: ${product?.id || "N/A"}):`
      );
      logger.info(
        `  - is_vat_exempt property: ${isExempt ? "Yes" : "No"}`
      );
      logger.info(
        `  - Final exemption status: ${isExempt ? "Exempt" : "Taxable"}`
      );
      logger.info(`  - OptionVATClass: ${vatClass}`);

      // Extract product details
      const productName =
        product?.name || item.product_name || "Unknown Product";

      // Set HSCode based on VAT status
      let hsCode = "";
      if (isExempt) {
        // Always use the default HS code for exempt items
        hsCode = "0016.21.00";
        logger.info(
          `Item ${productName} is VAT exempt, using HS code 0016.21.00`
        );
      } else {
        hsCode = product?.hs_code || item.hs_code || "";
      }

      // Extract pricing and quantity
      const unitPrice = parseFloat(item.unit_price) || 0;
      const quantity = parseFloat(item.quantity) || 0;

      // Format VAT rate - string "0" for exempt, string "16" for taxable
      const formattedVatRate = isExempt ? "0" : "16";

      // Log the formatted item for debugging
      logger.info(
        `Formatted KRA item: ${productName}, Price: ${unitPrice}, Qty: ${quantity}, VAT: ${formattedVatRate}, Class: ${vatClass}, HSCode: ${hsCode}`
      );

      return {
        NamePLU: productName,
        OptionVATClass: vatClass,
        Price: unitPrice,
        MeasureUnit: "Pcs",
        HSCode: hsCode,
        HSName: "",
        VATGrRate: formattedVatRate,
        Quantity: quantity,
        DiscAddP: item.discount_amount
          ? ((item.discount_amount / (unitPrice * quantity)) * 100).toFixed(2)
          : "",
      };
    });
  }

  /**
   * Format customer info for KRA
   * @param {Object} saleData - The sale data
   * @returns {Object} - Formatted customer info
   */
  formatCustomerInfoForKRA(saleData) {
    const { Customer, Branch, customer_name, customer_pin } = saleData;

    // Log the customer data for debugging
    logger.info(`Formatting customer info for KRA from sale ID ${saleData.id}`);

    // Prioritize customer data from the sale request
    const companyName = customer_name || Customer?.name || "Walk-in Customer";
    const pinNumber = customer_pin || Customer?.pin_number || "";
    const branchName = Branch?.name || "";
    const address = Customer?.address || Branch?.location || "";
    const postalCode = Customer?.postal_code || "00100 Nairobi";

    // Log the formatted customer info for debugging
    logger.info(`Formatted KRA customer: ${companyName}, PIN: ${pinNumber}`);
    logger.info(
      `Customer PIN sources - customer_pin: ${customer_pin}, Customer.pin_number: ${Customer?.pin_number}`
    );

    // Ensure the sale ID is properly formatted for the TraderSystemInvNum
    const saleId = saleData.id || "";
    const traderSystemInvNum = `INV${saleId}`;

    logger.info(
      `Generated TraderSystemInvNum: ${traderSystemInvNum} for sale ID ${saleId}`
    );

    return {
      CompanyName: companyName,
      ClientPINnum: pinNumber,
      HeadQuarters: branchName,
      Address: address,
      PostalCodeAndCity: postalCode,
      ExemptionNum: "",
      TraderSystemInvNum: traderSystemInvNum,
    };
  }
}

module.exports = KraIntegrationService;
