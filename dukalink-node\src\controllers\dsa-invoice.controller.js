const { Op } = require('sequelize');
const sequelize = require('../../config/database');
const DsaStockAssignment = require('../models/dsa-stock-assignment.model');
const DsaStockReconciliation = require('../models/dsa-stock-reconciliation.model');
const DsaPayment = require('../models/dsa-payment.model');
const Customer = require('../models/customer.model');
const Branch = require('../models/branch.model');
const Product = require('../models/product.model');
const User = require('../models/user.model');
const AppError = require('../utils/error');
const DsaPricingService = require("../services/DsaPricingService");
const EnhancedDsaBalanceService = require("../services/EnhancedDsaBalanceService");
const logger = require('../utils/logger');

/**
 * Get all DSA invoices (grouped by assignment_identifier)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getDsaInvoices = async (req, res, next) => {
  try {
    const { branch_id, branch_only = false } = req.query;

    // Validate branch_id if branch_only is true
    if (branch_only && !branch_id) {
      throw new AppError(400, 'Branch ID is required when branch_only is true');
    }

    // Build the where clause
    const whereClause = {};
    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    // Get all unique assignment identifiers
    const assignmentIdentifiers = await DsaStockAssignment.findAll({
      attributes: [
        'assignment_identifier',
        [sequelize.fn('MAX', sequelize.col('created_at')), 'latest_date'],
        [sequelize.fn('MIN', sequelize.col('customer_id')), 'customer_id'],
        [sequelize.fn('MIN', sequelize.col('branch_id')), 'branch_id']
      ],
      where: whereClause,
      group: ['assignment_identifier'],
      raw: true
    });

    // Fetch detailed data for each assignment identifier
    const invoicesData = await Promise.all(assignmentIdentifiers.map(async (identifier) => {
      // Get all assignments for this identifier
      const assignments = await DsaStockAssignment.findAll({
        where: { assignment_identifier: identifier.assignment_identifier },
        attributes: [
          'id', 'customer_id', 'user_id', 'branch_id', 'product_id',
          'quantity_assigned', 'quantity_returned', 'quantity_sold',
          'assignment_identifier', 'assigned_at', 'reconciled_at',
          'created_by', 'last_updated_by', 'reconciled', 'reconciliation_id',
          'payment_status', 'total_amount', 'amount_paid', 'balance',
          'last_payment_date', 'sale_id', 'created_at', 'updated_at'
        ],
        include: [
          { model: Product },
          { model: Customer },
          { model: Branch }
        ],
        order: [['created_at', 'DESC']]
      });

      // Get the latest reconciliation for this identifier
      const reconciliation = await DsaStockReconciliation.findOne({
        where: { assignment_identifier: identifier.assignment_identifier },
        order: [['created_at', 'DESC']]
      });

      // Get all payments for this identifier
      const payments = await DsaPayment.findAll({
        where: { assignment_identifier: identifier.assignment_identifier },
        order: [['payment_date', 'DESC']]
      });

      // Calculate totals
      let totalAssigned = 0;
      let totalReturned = 0;
      let totalValue = 0;
      let remainingValue = 0;
      let remainingItems = 0;

      // Process each assignment to calculate totals
      const items = assignments.map(assignment => {
        const quantityAssigned = assignment.quantity_assigned || 0;
        const quantityReturned = assignment.quantity_returned || 0;
        const remainingQuantity = Math.max(0, quantityAssigned - quantityReturned);
        const wholesalePrice = assignment.default_wholesale_price ||
                              (assignment.Product ? assignment.Product.default_wholesale_price : 0) || 0;

        const itemValue = wholesalePrice * quantityAssigned;
        const itemRemainingValue = wholesalePrice * remainingQuantity;

        totalAssigned += quantityAssigned;
        totalReturned += quantityReturned;
        totalValue += itemValue;
        remainingValue += itemRemainingValue;
        remainingItems += remainingQuantity;

        return {
          product_id: assignment.product_id,
          product_name: assignment.Product ? assignment.Product.name : 'Unknown Product',
          quantity_assigned: quantityAssigned,
          quantity_returned: quantityReturned,
          remaining_quantity: remainingQuantity,
          wholesale_price: wholesalePrice,
          total_value: itemValue,
          remaining_value: itemRemainingValue
        };
      });

      // Get payment information
      const totalPaid = payments.reduce((sum, payment) => {
        return sum + (parseFloat(payment.cash_amount) || 0) + (parseFloat(payment.paybill_amount) || 0);
      }, 0);

      // Calculate balance
      const balance = Math.max(0, remainingValue - totalPaid);

      // Determine payment status
      let paymentStatus = 'UNPAID';
      if (totalPaid > 0) {
        paymentStatus = balance > 0 ? 'PARTIALLY_PAID' : 'FULLY_PAID';
      }

      // Use reconciliation status if available
      if (reconciliation && reconciliation.payment_status) {
        paymentStatus = reconciliation.payment_status;
      }

      // Get DSA agent information
      const dsaAgent = assignments.length > 0 ?
        (assignments[0].Customer || { id: identifier.customer_id, name: 'Unknown DSA' }) :
        { id: identifier.customer_id, name: 'Unknown DSA' };

      // Get branch information
      const branch = assignments.length > 0 ?
        (assignments[0].Branch || { id: identifier.branch_id, name: 'Unknown Branch' }) :
        { id: identifier.branch_id, name: 'Unknown Branch' };

      // Format the invoice data
      return {
        invoice_id: identifier.assignment_identifier,
        dsa_agent: {
          id: dsaAgent.id,
          name: dsaAgent.name,
          phone: dsaAgent.phone
        },
        branch: {
          id: branch.id,
          name: branch.name
        },
        date: assignments.length > 0 ? assignments[0].created_at : identifier.latest_date,
        total_assigned: totalAssigned,
        total_returned: totalReturned,
        remaining_items: remainingItems,
        total_value: totalValue,
        remaining_value: remainingValue,
        amount_paid: totalPaid,
        balance: balance,
        payment_status: paymentStatus,
        items: items,
        payments: payments.map(payment => ({
          id: payment.id,
          date: payment.payment_date,
          cash_amount: parseFloat(payment.cash_amount) || 0,
          paybill_amount: parseFloat(payment.paybill_amount) || 0,
          total_amount: parseFloat(payment.total_amount) || 0,
          notes: payment.notes
        }))
      };
    }));

    // Return the invoices data
    res.status(200).json({
      success: true,
      count: invoicesData.length,
      data: invoicesData
    });
  } catch (error) {
    logger.error(`Error fetching DSA invoices: ${error.message}`);
    next(error);
  }
};

/**
 * Get a single DSA invoice by assignment_identifier
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getDsaInvoiceById = async (req, res, next) => {
  try {
    const { assignment_identifier } = req.params;

    if (!assignment_identifier) {
      throw new AppError(400, 'Assignment identifier is required');
    }

    // Get all assignments for this identifier
    const assignments = await DsaStockAssignment.findAll({
      where: { assignment_identifier },
      attributes: [
        'id', 'customer_id', 'user_id', 'branch_id', 'product_id',
        'quantity_assigned', 'quantity_returned', 'quantity_sold',
        'assignment_identifier', 'assigned_at', 'reconciled_at',
        'created_by', 'last_updated_by', 'reconciled', 'reconciliation_id',
        'payment_status', 'total_amount', 'amount_paid', 'balance',
        'last_payment_date', 'sale_id', 'created_at', 'updated_at'
      ],
      include: [
        { model: Product },
        { model: Customer },
        { model: Branch }
      ],
      order: [['created_at', 'DESC']]
    });

    if (assignments.length === 0) {
      throw new AppError(404, 'DSA invoice not found');
    }

    // Get the latest reconciliation for this identifier
    const reconciliation = await DsaStockReconciliation.findOne({
      where: { assignment_identifier },
      order: [['created_at', 'DESC']]
    });

    // Get all payments for this identifier
    const payments = await DsaPayment.findAll({
      where: { assignment_identifier },
      order: [['payment_date', 'DESC']]
    });

    // Calculate totals
    let totalAssigned = 0;
    let totalReturned = 0;
    let totalValue = 0;
    let remainingValue = 0;
    let remainingItems = 0;

    // Process each assignment to calculate totals
    const items = assignments.map(assignment => {
      const quantityAssigned = assignment.quantity_assigned || 0;
      const quantityReturned = assignment.quantity_returned || 0;
      const remainingQuantity = Math.max(0, quantityAssigned - quantityReturned);
      const wholesalePrice = assignment.default_wholesale_price ||
                            (assignment.Product ? assignment.Product.default_wholesale_price : 0) || 0;

      const itemValue = wholesalePrice * quantityAssigned;
      const itemRemainingValue = wholesalePrice * remainingQuantity;

      totalAssigned += quantityAssigned;
      totalReturned += quantityReturned;
      totalValue += itemValue;
      remainingValue += itemRemainingValue;
      remainingItems += remainingQuantity;

      return {
        product_id: assignment.product_id,
        product_name: assignment.Product ? assignment.Product.name : 'Unknown Product',
        quantity_assigned: quantityAssigned,
        quantity_returned: quantityReturned,
        remaining_quantity: remainingQuantity,
        wholesale_price: wholesalePrice,
        total_value: itemValue,
        remaining_value: itemRemainingValue
      };
    });

    // Get payment information
    const totalPaid = payments.reduce((sum, payment) => {
      return sum + (parseFloat(payment.cash_amount) || 0) + (parseFloat(payment.paybill_amount) || 0);
    }, 0);

    // Calculate balance
    const balance = Math.max(0, remainingValue - totalPaid);

    // Determine payment status
    let paymentStatus = 'UNPAID';
    if (totalPaid > 0) {
      paymentStatus = balance > 0 ? 'PARTIALLY_PAID' : 'FULLY_PAID';
    }

    // Use reconciliation status if available
    if (reconciliation && reconciliation.payment_status) {
      paymentStatus = reconciliation.payment_status;
    }

    // Get DSA agent information
    const dsaAgent = assignments.length > 0 ?
      (assignments[0].Customer || { id: assignments[0].customer_id, name: 'Unknown DSA' }) :
      { id: 0, name: 'Unknown DSA' };

    // Get branch information
    const branch = assignments.length > 0 ?
      (assignments[0].Branch || { id: assignments[0].branch_id, name: 'Unknown Branch' }) :
      { id: 0, name: 'Unknown Branch' };

    // Return the invoice data
    res.status(200).json({
      success: true,
      data: {
        invoice_id: assignment_identifier,
        dsa_agent: {
          id: dsaAgent.id,
          name: dsaAgent.name,
          phone: dsaAgent.phone
        },
        branch: {
          id: branch.id,
          name: branch.name
        },
        date: assignments.length > 0 ? assignments[0].created_at : null,
        total_assigned: totalAssigned,
        total_returned: totalReturned,
        remaining_items: remainingItems,
        total_value: totalValue,
        remaining_value: remainingValue,
        amount_paid: totalPaid,
        balance: balance,
        payment_status: paymentStatus,
        items: items,
        payments: payments.map(payment => ({
          id: payment.id,
          date: payment.payment_date,
          cash_amount: parseFloat(payment.cash_amount) || 0,
          paybill_amount: parseFloat(payment.paybill_amount) || 0,
          total_amount: parseFloat(payment.total_amount) || 0,
          notes: payment.notes
        })),
        reconciliation: reconciliation ? {
          id: reconciliation.id,
          date: reconciliation.reconciled_at,
          total_amount: parseFloat(reconciliation.total_amount) || 0,
          amount_paid: parseFloat(reconciliation.amount_paid) || 0,
          balance: parseFloat(reconciliation.balance) || 0,
          payment_status: reconciliation.payment_status,
          notes: reconciliation.notes
        } : null
      }
    });
  } catch (error) {
    logger.error(`Error fetching DSA invoice: ${error.message}`);
    next(error);
  }
};

module.exports = exports;
