const {
  Sale,
  SaleItem,
  PosSession,
  User,
  Branch,
  Product,
  StockItem,
  PaymentMethod,
  Role,
  DsaStockAssignment,
  Customer,

  CreditPartner,
  InventoryItem,
  SoldBarcode,
  ProductDiscount,
  ProductCategory,
} = require("../models");
const {
  validateBarcode,
  validateSerialNumber,
} = require("../utils/barcode-generator");
const AppError = require("../utils/error");
const logger = require("../utils/logger");
const sequelize = require("../../config/database");
const { Op } = require("sequelize");
const KraIntegrationService = require("../services/kra-integration.service");

/**
 * Get all sales
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllSales = async (req, res, next) => {
  try {
    const {
      pos_session_id,
      user_id,
      branch_id,
      region_id,
      payment_method_id,
      status,
      kra_integration_status,
      start_date,
      end_date,
      is_dsa,
      include_customer,
    } = req.query;

    const whereClause = { deleted_at: null };

    // Filter by branch_id based on user role
    // company_admin can see all branches, other users can only see their branch
    if (req.user.role_name !== "company_admin") {
      // Non-company_admin users can only see their branch's sales
      whereClause.branch_id = req.user.branch_id;
    } else if (branch_id) {
      // company_admin can filter by specific branch if requested
      whereClause.branch_id = branch_id;
    }

    if (pos_session_id) {
      whereClause.pos_session_id = pos_session_id;
    }

    if (user_id) {
      whereClause.user_id = user_id;
    }

    if (payment_method_id) {
      whereClause.payment_method_id = payment_method_id;
    }

    if (status) {
      whereClause.status = status;
    }

    if (kra_integration_status) {
      whereClause.kra_integration_status = kra_integration_status;
    }

    // Filter by DSA sales
    if (is_dsa !== undefined) {
      whereClause.is_dsa = is_dsa === "true";
    }

    // Date range filtering
    if (start_date || end_date) {
      whereClause.created_at = {};

      if (start_date) {
        // Set time to start of day (00:00:00) to include the start date
        const startDateTime = new Date(start_date);
        startDateTime.setHours(0, 0, 0, 0);
        whereClause.created_at[Op.gte] = startDateTime;
      }

      if (end_date) {
        // Set time to end of day (23:59:59.999) to include the end date
        const endDateTime = new Date(end_date);
        endDateTime.setHours(23, 59, 59, 999);
        whereClause.created_at[Op.lte] = endDateTime;
      }
    }

    // Prepare include array
    const includeModels = [
      {
        model: PosSession,
        attributes: ["id", "start_time", "end_time", "status"],
      },
      {
        model: User,
        attributes: ["id", "name", "email", "role_id"],
      },
      {
        model: Branch,
        attributes: ["id", "name", "location", "region_id"],
        include: region_id
          ? [
              {
                model: require("../models").Region,
                attributes: ["id", "name", "code"],
              },
            ]
          : [],
      },
      {
        model: PaymentMethod,
        attributes: ["id", "name", "code", "requires_reference"],
      },
      {
        model: CreditPartner,
        attributes: ["id", "name", "code", "description", "requires_reference"],
      },

      {
        model: SaleItem,
        paranoid: false, // Disable paranoid for SaleItem since it doesn't have deleted_at
        include: [
          {
            model: Product,
            attributes: ["id", "name", "sku", "has_serial", "category_id"],
            include: [
              {
                model: ProductCategory,
                attributes: ["id", "name"],
              },
            ],
          },
        ],
      },
    ];

    // Always include Customer model
    includeModels.push({
      model: Customer,
      attributes: ["id", "name", "phone", "email", "pin_number"],
    });

    // Handle region filtering
    let options = {
      where: whereClause,
      include: includeModels,
      order: [["created_at", "DESC"]],
    };

    // If region_id is provided, we need to filter by branches in that region
    if (region_id && req.user.role_name === "company_admin") {
      // Get all branches in the specified region
      const branchesInRegion = await Branch.findAll({
        where: { region_id, deleted_at: null },
        attributes: ["id"],
      });

      // Extract branch IDs
      const branchIds = branchesInRegion.map((branch) => branch.id);

      // If we found branches in this region, filter sales by those branch IDs
      if (branchIds.length > 0) {
        // Remove any existing branch_id filter
        delete whereClause.branch_id;

        // Add the branch_id IN clause
        whereClause.branch_id = {
          [Op.in]: branchIds,
        };

        // Update the options with the new where clause
        options.where = whereClause;
      }
    }

    const sales = await Sale.findAll(options);

    // Add customer_pin and ensure KRA details are included in each sale
    const salesWithDetails = sales.map((sale) => {
      const saleData = sale.toJSON();

      // Try to parse the KRA response data if it's a string
      let parsedKraData = null;
      if (saleData.kra_response_data && typeof saleData.kra_response_data === 'string') {
        try {
          parsedKraData = JSON.parse(saleData.kra_response_data);
        } catch (e) {
          // Silently ignore parsing errors for list view
        }
      }

      // Add customer_pin for backward compatibility
      // Prioritize ClientPINnum from KRA response data, then Customer.pin_number
      if (parsedKraData && parsedKraData.customerInfo && parsedKraData.customerInfo.ClientPINnum) {
        saleData.customer_pin = parsedKraData.customerInfo.ClientPINnum;
      } else if (saleData.Customer && saleData.Customer.pin_number) {
        saleData.customer_pin = saleData.Customer.pin_number;
      }

      // Ensure KRA details are included
      const kraDetails = {
        kra_verification_code: saleData.kra_verification_code || "",
        kra_fiscal_receipt_number: saleData.kra_fiscal_receipt_number || "",
        kra_verification_url: saleData.kra_verification_url || "",
        kra_verification_timestamp: saleData.kra_verification_timestamp || null,
        kra_integration_status: saleData.kra_integration_status || "pending",
        kra_response_data: saleData.kra_response_data || "",
      };

      // Add KRA details and VAT fields to the sale data
      return {
        ...saleData,
        ...kraDetails,
        // Ensure VAT fields are included for receipt printing
        total_excluding_vat: parseFloat(saleData.total_excluding_vat || 0),
        total_vat_amount: parseFloat(saleData.total_vat_amount || 0),
        total_discount: parseFloat(saleData.total_discount || 0),
      };
    });

    return res.status(200).json(salesWithDetails);
  } catch (error) {
    logger.error(`Error fetching sales: ${error.message}`);
    next(error);
  }
};

/**
 * Get formatted receipt data for a sale
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getReceiptData = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Build the where clause
    const whereClause = { id, deleted_at: null };

    // Non-company_admin users can only see their branch's sales
    if (req.user.role_name !== 'company_admin') {
      whereClause.branch_id = req.user.branch_id;
    }

    const sale = await Sale.findOne({
      where: whereClause,
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'phone', 'pin_number']
        },
        {
          model: User,
          attributes: ['id', 'name']
        },
        {
          model: PaymentMethod,
          attributes: ['id', 'name', 'code']
        },
        {
          model: SaleItem,
          include: [
            {
              model: Product,
              attributes: ['id', 'name', 'sku', 'has_serial']
            }
          ]
        }
      ]
    });

    if (!sale) {
      return next(new AppError('Sale not found or you do not have permission to view it', 404));
    }

    // Extract customer PIN using standardized method
    const customerPIN = extractCustomerPIN(sale);

    // Extract KRA data using standardized method
    const kraData = extractKRAData(sale);

    logger.info(`Receipt data for sale ${id}:`, {
      sale_id: sale.id,
      customer_name: sale.Customer?.name || 'Walk-in Customer',
      customer_pin: customerPIN,
      total_amount: sale.total_amount,
      items_count: sale.SaleItems.length,
      kra_data_available: Object.keys(kraData).filter(key => kraData[key] && kraData[key] !== '').length > 0
    });

    // Format receipt data consistently
    const receiptData = {
      id: sale.id,
      receipt_number: sale.receipt_number || `RCP-${sale.id}`,
      date: sale.created_at,
      customer_name: sale.Customer?.name || 'Walk-in Customer',
      customer_pin: customerPIN,
      cashier_name: sale.User?.name || 'Unknown',
      payment_method: sale.PaymentMethod?.name || 'Cash',
      payment_reference: sale.payment_reference || '',
      total_amount: parseFloat(sale.total_amount),
      // Include VAT fields for receipt printing
      total_excluding_vat: parseFloat(sale.total_excluding_vat || 0),
      total_vat_amount: parseFloat(sale.total_vat_amount || 0),
      total_discount: parseFloat(sale.total_discount || 0),
      items: sale.SaleItems.map(item => ({
        name: item.Product?.name || 'Unknown Item',
        quantity: item.quantity,
        unit_price: parseFloat(item.unit_price),
        total_price: parseFloat(item.total_price),
        serial_number: item.serial_number || '',
        has_serial: item.Product?.has_serial || false,
        barcode: item.Product?.barcode || '',
        sku: item.Product?.sku || '',
        original_price: item.original_price ? parseFloat(item.original_price) : null,
        is_discounted: item.is_discounted || false,
        discount_amount: item.discount_amount ? parseFloat(item.discount_amount) : 0,
        vat_rate: item.vat_rate ? parseFloat(item.vat_rate) : 0.16,
        vat_amount: item.vat_amount ? parseFloat(item.vat_amount) : 0
      })),
      kra_data: kraData,
      formatted_for_printing: true
    };


    return res.status(200).json(receiptData);
  } catch (error) {
    logger.error(`Error fetching receipt data: ${error.message}`);
    next(error);
  }
};

/**
 * Standardized customer PIN extraction
 * @param {Object} sale - Sale object with associations
 * @returns {string} - Customer PIN or empty string
 */
const extractCustomerPIN = (sale) => {
  // CORRECTED Priority order for customer PIN (as per requirements):
  // 1. kraData.customerInfo.ClientPINnum (from parsed KRA response - most reliable for tax compliance)
  // 2. customer_pin field (direct field in sale)
  // 3. Customer.pin_number (from customer object)

  // First: Check KRA response data
  if (sale.kra_response_data) {
    try {
      const kraData = JSON.parse(sale.kra_response_data);
      if (kraData.customerInfo?.ClientPINnum) {
        return kraData.customerInfo.ClientPINnum;
      }
    } catch (e) {
      logger.warn(`Failed to parse KRA response data for PIN extraction: ${e.message}`);
    }
  }

  // Second: Check customer_pin field (direct field in sale)
  if (sale.customer_pin && sale.customer_pin.trim()) {
    return sale.customer_pin.trim();
  }

  // Third: Check Customer object pin_number
  if (sale.Customer?.pin_number && sale.Customer.pin_number.trim()) {
    return sale.Customer.pin_number.trim();
  }

  return '';
};

/**
 * Standardized KRA data extraction with enhanced parsing
 * @param {Object} sale - Sale object
 * @returns {Object} - KRA data object
 */
const extractKRAData = (sale) => {
  logger.info(`Extracting KRA data for sale ${sale.id}:`, {
    kra_verification_url: sale.kra_verification_url || 'not set',
    kra_verification_code: sale.kra_verification_code || 'not set',
    kra_fiscal_receipt_number: sale.kra_fiscal_receipt_number || 'not set',
    kra_integration_status: sale.kra_integration_status || 'not set',
    has_kra_response_data: !!sale.kra_response_data,
    kra_response_data_length: sale.kra_response_data ? sale.kra_response_data.length : 0
  });

  // Start with direct fields from the sale
  let kraData = {
    verification_url: sale.kra_verification_url || '',
    verification_code: sale.kra_verification_code || '',
    fiscal_receipt_number: sale.kra_fiscal_receipt_number || '',
    integration_status: sale.kra_integration_status || 'pending',
    verification_timestamp: sale.kra_verification_timestamp || null
  };

  // If we have kra_response_data, try to extract additional information
  if (sale.kra_response_data) {
    try {
      const parsedKraData = JSON.parse(sale.kra_response_data);
      logger.info(`Parsed KRA response data for sale ${sale.id}:`, {
        qrCodeUrl: parsedKraData.qrCodeUrl || 'not available',
        verificationCode: parsedKraData.verificationCode || 'not available',
        fiscalReceiptNumber: parsedKraData.fiscalReceiptNumber || 'not available',
        invoiceNum: parsedKraData.invoiceNum || 'not available',
        cuNumber: parsedKraData.cuNumber || 'not available'
      });

      // Override with data from kra_response_data if available
      if (parsedKraData.qrCodeUrl && !kraData.verification_url) {
        kraData.verification_url = parsedKraData.qrCodeUrl;
      }
      if (parsedKraData.verificationCode && !kraData.verification_code) {
        kraData.verification_code = parsedKraData.verificationCode;
      }
      if (parsedKraData.fiscalReceiptNumber && !kraData.fiscal_receipt_number) {
        kraData.fiscal_receipt_number = parsedKraData.fiscalReceiptNumber;
      }
      if (parsedKraData.invoiceNum && !kraData.fiscal_receipt_number) {
        kraData.fiscal_receipt_number = parsedKraData.invoiceNum;
      }

      // Add additional fields from parsed data
      kraData.cu_number = parsedKraData.cuNumber || '';
      kraData.invoice_number = parsedKraData.invoiceNum || '';
      kraData.date_time = parsedKraData.dateTime || '';

    } catch (e) {
      logger.warn(`Failed to parse KRA response data for sale ${sale.id}: ${e.message}`);
    }
  }

  logger.info(`Final KRA data for sale ${sale.id}:`, kraData);
  return kraData;
};

/**
 * Get sale by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getSaleById = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Build the where clause
    const whereClause = { id, deleted_at: null };

    // Non-company_admin users can only see their branch's sales
    if (req.user.role_name !== "company_admin") {
      whereClause.branch_id = req.user.branch_id;
    }

    const sale = await Sale.findOne({
      where: whereClause,
      include: [
        {
          model: PosSession,
          attributes: ["id", "start_time", "end_time", "status"],
        },
        {
          model: User,
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          attributes: ["id", "name", "location"],
        },
        {
          model: PaymentMethod,
          attributes: ["id", "name", "code", "requires_reference"],
        },
        {
          model: CreditPartner,
          attributes: [
            "id",
            "name",
            "code",
            "description",
            "requires_reference",
          ],
        },
        {
          model: Customer,
          attributes: ["id", "name", "phone", "email", "pin_number"],
        },

        {
          model: SaleItem,
          paranoid: false, // Disable paranoid for SaleItem since it doesn't have deleted_at
          include: [
            {
              model: Product,
              attributes: ["id", "name", "sku", "has_serial", "category_id"],
              include: [
                {
                  model: ProductCategory,
                  attributes: ["id", "name"],
                },
              ],
            },
          ],
        },
      ],
    });

    if (!sale) {
      return next(
        new AppError(
          "Sale not found or you do not have permission to view it",
          404
        )
      );
    }

    // Add customer_pin and ensure KRA details are included in the response
    const responseData = sale.toJSON();

    // Try to parse the KRA response data if it's a string
    let parsedKraData = null;
    if (responseData.kra_response_data && typeof responseData.kra_response_data === 'string') {
      try {
        parsedKraData = JSON.parse(responseData.kra_response_data);
      } catch (e) {
        logger.warn(`Failed to parse KRA response data for sale ${id}: ${e.message}`);
      }
    }

    // Add customer_pin for backward compatibility using standardized extraction
    responseData.customer_pin = extractCustomerPIN(responseData);

    // Ensure KRA details are included
    const kraDetails = {
      kra_verification_code: responseData.kra_verification_code || "",
      kra_fiscal_receipt_number: responseData.kra_fiscal_receipt_number || "",
      kra_verification_url: responseData.kra_verification_url || "",
      kra_verification_timestamp:
        responseData.kra_verification_timestamp || null,
      kra_integration_status: responseData.kra_integration_status || "pending",
      kra_response_data: responseData.kra_response_data || "",
    };

    // Add KRA details and VAT fields to the response data
    const enhancedResponseData = {
      ...responseData,
      ...kraDetails,
      // Include parsed KRA data if available
      kraData: parsedKraData,
      // Ensure VAT fields are included for receipt printing
      total_excluding_vat: parseFloat(responseData.total_excluding_vat || 0),
      total_vat_amount: parseFloat(responseData.total_vat_amount || 0),
      total_discount: parseFloat(responseData.total_discount || 0),
    };

    return res.status(200).json(enhancedResponseData);
  } catch (error) {
    logger.error(`Error fetching sale: ${error.message}`);
    next(error);
  }
};

/**
 * Create a new sale
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createSale = async (req, res, next) => {
  // Start a transaction
  const transaction = await sequelize.transaction();

  try {
    const {
      customer_id,
      employee_id,
      payment_method_id,
      payment_reference,
      credit_partner_id,
      notes,
      items,
      // For backward compatibility, still accept these but don't store them
      customer_name,
      customer_phone,
      customer_pin,
    } = req.body;



    // Validate required fields
    if (!payment_method_id || !items || !items.length) {
      await transaction.rollback();
      return next(
        new AppError("Payment method ID and items are required", 400)
      );
    }

    // Validate that employee_id (seller) is present
    if (!employee_id) {
      await transaction.rollback();
      return next(new AppError("Seller ID (employee_id) is required", 400));
    }

    // Set user_id directly to employee_id
    const user_id = employee_id;

    // Check if payment method exists
    const paymentMethod = await PaymentMethod.findOne({
      where: { id: payment_method_id, is_active: true, deleted_at: null },
    });

    if (!paymentMethod) {
      await transaction.rollback();
      return next(new AppError("Payment method not found or inactive", 404));
    }

    // Check if payment reference is required but not provided
    // Special case for M-PESA: don't require reference even if requires_reference is true
    if (
      paymentMethod.requires_reference &&
      !payment_reference &&
      paymentMethod.code.toLowerCase() !== "mpesa"
    ) {
      await transaction.rollback();
      return next(
        new AppError(
          `Payment reference is required for ${paymentMethod.name} payment method`,
          400
        )
      );
    }

    // Check if credit partner is required for credit payment method
    // Skip this validation for DSA sales (is_dsa flag is true)
    if (
      paymentMethod.code.toLowerCase() === "credit" &&
      !credit_partner_id &&
      !req.body.is_dsa
    ) {
      await transaction.rollback();
      return next(
        new AppError(
          "Credit partner is required for Credit payment method",
          400
        )
      );
    }

    // Validate credit partner if provided
    if (credit_partner_id) {
      const creditPartner = await CreditPartner.findOne({
        where: { id: credit_partner_id, is_active: true, deleted_at: null },
      });

      if (!creditPartner) {
        await transaction.rollback();
        return next(new AppError("Credit partner not found or inactive", 404));
      }
    }

    // Check if user exists
    const user = await User.findOne({
      where: { id: user_id, deleted_at: null },
    });

    if (!user) {
      await transaction.rollback();
      return next(new AppError("User not found", 404));
    }

    // All authenticated users can create sales

    // Get the user's branch - prioritize the branch_id from the request body
    let userRecord = null;

    // If branch_id is explicitly provided in the request, use it
    if (req.body.branch_id) {
      // Create a temporary user record with the branch_id from the request
      userRecord = {
        id: user_id,
        branch_id: req.body.branch_id,
      };

    }
    // Then try to use the authenticated user making the request
    else if (req.user && req.user.branch_id) {
      userRecord = req.user;

      logger.info(
        `Using branch_id ${req.user.branch_id} from authenticated user making the request`
      );
    }
    // Fall back to the user specified in the sale
    else {
      userRecord = await User.findByPk(user_id);
    }

    if (!userRecord || !userRecord.branch_id) {
      await transaction.rollback();
      return next(
        new AppError(
          "User or branch information not found. Please ensure your user account has a branch assigned.",
          400
        )
      );
    }

    // Check if there's an active POS session for the branch instead of the user
    // Skip this check for DSA sales
    let posSession = null;
    if (!req.body.is_dsa) {
      // Use the branch_id from the request body if available, otherwise use the userRecord.branch_id
      const branchIdForSession = req.body.branch_id || userRecord.branch_id;

      posSession = await PosSession.findOne({
        where: {
          branch_id: branchIdForSession,
          status: "open",
          deleted_at: null,
        },
      });

      if (!posSession) {
        await transaction.rollback();
        return next(
          new AppError(
            "There is no open POS session for this branch. Please start a session before creating sales.",
            400
          )
        );
      }
    }

    // Calculate total amount
    let totalAmount = 0;
    for (const item of items) {
      totalAmount += item.quantity * item.unit_price;
    }

    // We'll calculate total VAT amount after creating sale items

    // Handle customer information
    let effectiveCustomerId = customer_id;

    // If no customer_id but we have customer details, try to find or create a customer
    if (!effectiveCustomerId && (customer_name || customer_phone)) {
      // Try to find customer by phone if provided
      if (customer_phone) {
        const existingCustomer = await Customer.findOne({
          where: { phone: customer_phone, deleted_at: null },
        });
        if (existingCustomer) {
          effectiveCustomerId = existingCustomer.id;
        } else if (customer_name) {
          // Create new customer if we have a name
          const newCustomer = await Customer.create(
            {
              name: customer_name,
              phone: customer_phone,
              pin_number: customer_pin || null, // Include PIN number if provided
              tenant_id: req.user?.tenant_id,
              created_by: req.user?.id,
              last_updated_by: req.user?.id,
            },
            { transaction }
          );

          effectiveCustomerId = newCustomer.id;
        }
      } else if (customer_name) {
        // Create customer with just a name
        const newCustomer = await Customer.create(
          {
            name: customer_name,
            pin_number: customer_pin || null, // Include PIN number if provided
            tenant_id: req.user?.tenant_id,
            created_by: req.user?.id,
            last_updated_by: req.user?.id,
          },
          { transaction }
        );

        effectiveCustomerId = newCustomer.id;
      }
    }

    // If still no customer_id, use the default Walk-in customer
    if (!effectiveCustomerId) {
      const walkInCustomer = await Customer.findOne({
        where: { name: "Walk-in", deleted_at: null },
      });

      if (walkInCustomer) {
        effectiveCustomerId = walkInCustomer.id;
      } else {
        // Create default Walk-in customer if it doesn't exist
        const defaultCustomer = await Customer.create(
          {
            name: "Walk-in",
            created_by: req.user?.id,
            last_updated_by: req.user?.id,
          },
          { transaction }
        );

        effectiveCustomerId = defaultCustomer.id;
      }
    }

    // Generate receipt number
    const receipt_number = `SALE-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

    // Determine branch_id
    let effectiveBranchId = null;

    // First check if branch_id is explicitly provided in the request
    if (req.body.branch_id) {
      // Ensure branch_id is a number
      effectiveBranchId = parseInt(req.body.branch_id, 10);

    }
    // Then check if the authenticated user has a branch_id
    else if (req.user && req.user.branch_id) {
      effectiveBranchId = parseInt(req.user.branch_id, 10);

    }
    // Then check if there's an active POS session with a branch_id
    else if (posSession && posSession.branch_id) {
      effectiveBranchId = parseInt(posSession.branch_id, 10);

    }
    // Finally, fall back to the user's branch_id from the sale
    else if (userRecord && userRecord.branch_id) {
      effectiveBranchId = parseInt(userRecord.branch_id, 10);
    }


    // Validate that we have a branch_id
    if (!effectiveBranchId) {
      logger.error("No branch_id found for sale creation");
      throw new AppError(
        "Branch ID is required for creating a sale. Please ensure your user account has a branch assigned.",
        400
      );
    }

    // Ensure branch_id is a valid number
    if (isNaN(effectiveBranchId)) {
      logger.error(
        `Invalid branch_id: ${effectiveBranchId} (${typeof effectiveBranchId})`
      );
      throw new AppError(
        "Invalid Branch ID. Please ensure your user account has a valid branch assigned.",
        400
      );
    }

    // Create sale record
    const sale = await Sale.create(
      {
        pos_session_id: posSession ? posSession.id : null,
        user_id,
        branch_id: effectiveBranchId,
        customer_id: effectiveCustomerId,
        employee_id, // Employee ID is required
        total_amount: totalAmount,
        payment_method_id,
        payment_reference,
        receipt_number, // Add receipt number
        credit_partner_id, // Include credit partner ID if provided
        status: "completed",
        notes,
        is_dsa: req.body.is_dsa || false, // Mark as DSA sale if applicable
        created_by: employee_id, // Set created_by to employee_id
        last_updated_by: employee_id, // Set last_updated_by to employee_id
      },
      { transaction }
    );

    // Update running balances based on payment method
    if (paymentMethod.code.toLowerCase() === "mpesa") {
      // For MPESA sales, update running_mpesa_balance
      await posSession.increment("running_mpesa_balance", {
        by: totalAmount,
        transaction,
      });
    } else if (paymentMethod.code.toLowerCase() === 'cash') {

      // For cash sales, update running_cash_balance
      await posSession.increment("running_cash_balance", {
        by: totalAmount,
        transaction,
      });

    }

    // Validate stock availability for all items first (batch validation)
    const stockValidationErrors = [];
    const stockItemsMap = new Map();

    // Pre-fetch all stock items for validation
    for (const item of items) {
      if (!item.product_id || !item.quantity || item.unit_price === undefined || item.unit_price === null) {
        await transaction.rollback();
        return next(
          new AppError(
            "Product ID, quantity, and unit price are required for each item",
            400
          )
        );
      }

      const stockItem = await StockItem.findOne({
        where: {
          branch_id: effectiveBranchId,
          product_id: item.product_id,
          deleted_at: null,
        },
        include: [
          {
            model: Product,
            attributes: [
              'id', 'name', 'sku', 'has_serial',
              'is_vat_exempt',
              'suggested_buying_price', 'warranty_period'
            ],
            required: false
          }
        ]
      });

      if (!stockItem) {
        const productName = `Product ID ${item.product_id}`;
        stockValidationErrors.push(`${productName} is not available in this branch`);
        continue;
      }

      const availableQuantity = stockItem.quantity_available || stockItem.quantity || 0;
      if (availableQuantity < item.quantity) {
        const productName = stockItem.Product?.name || `Product ID ${item.product_id}`;
        stockValidationErrors.push(`Insufficient stock for ${productName}. Available: ${availableQuantity}, Requested: ${item.quantity}`);
        continue;
      }

      // Store valid stock items for later processing
      stockItemsMap.set(item.product_id, stockItem);
    }

    // If there are any stock validation errors, return them all at once
    if (stockValidationErrors.length > 0) {
      await transaction.rollback();
      const errorMessage = stockValidationErrors.length === 1
        ? stockValidationErrors[0]
        : `Stock validation failed:\n\n${stockValidationErrors.map(error => `• ${error}`).join('\n')}`;

      return next(new AppError(errorMessage, 400));
    }

    // Create sale items and update stock
    const saleItems = [];

    // Check if we're using KRA items format
    const isUsingKraFormat = req.body.kra_items && Array.isArray(req.body.kra_items) && req.body.kra_items.length > 0;



    // If using KRA format, convert the items to the standard format
    if (isUsingKraFormat) {


      // Map KRA items to standard items
      const convertedItems = [];

      for (const kraItem of req.body.kra_items) {
        // Find the product by name or SKU
        let product;

        if (kraItem.product_id) {
          // If product_id is provided, use it directly
          product = await Product.findOne({
            where: { id: kraItem.product_id, deleted_at: null }
          });
        } else {
          // Otherwise, try to find by name
          product = await Product.findOne({
            where: {
              name: { [Op.like]: `%${kraItem.NamePLU}%` },
              deleted_at: null
            }
          });

          // If not found by name, try to find by SKU
          if (!product) {
            product = await Product.findOne({
              where: {
                sku: { [Op.like]: `%${kraItem.NamePLU}%` },
                deleted_at: null
              }
            });
          }
        }

        if (!product) {
          logger.warn(`Product not found for KRA item: ${kraItem.NamePLU}`);
          continue;
        }

        // Convert to standard format
        convertedItems.push({
          product_id: product.id,
          quantity: parseFloat(kraItem.Quantity) || 1,
          unit_price: parseFloat(kraItem.Price) || 0,
          vat_rate: parseFloat(kraItem.VATGrRate) || 16
        });


      }

      // Replace the items array with the converted items
      if (convertedItems.length > 0) {
        items = convertedItems;

      } else {
        await transaction.rollback();
        return next(new AppError('No valid products found in KRA items', 400));
      }
    }

    for (const item of items) {
      // Get the pre-validated stock item
      const stockItem = stockItemsMap.get(item.product_id);

      if (!stockItem) {
        // This should not happen as we've already validated, but safety check
        await transaction.rollback();
        return next(new AppError(`Stock item not found for product ID ${item.product_id}`, 400));
      }

      // Convert unit_price to a number and ensure it's not negative
      item.unit_price = parseFloat(item.unit_price);
      if (isNaN(item.unit_price) || item.unit_price < 0) {
        logger.error(`Invalid unit price: ${item.unit_price} for product ID ${item.product_id}`);
        await transaction.rollback();
        return next(new AppError('Unit price must be a non-negative number', 400));
      }

      // Use the product data from the pre-validated stock item to avoid duplicate queries
      const product = stockItem.Product;

      if (!product) {
        await transaction.rollback();
        return next(
          new AppError(`Product with ID ${item.product_id} not found`, 404)
        );
      }

      // Ensure we have the VAT-related fields - if not, fetch them explicitly
      let productWithVatFields = product;
      if (product.is_vat_exempt === undefined || product.is_vat_exempt === null) {
        logger.warn(`Product ${product.id} missing is_vat_exempt field, fetching complete product data`);
        productWithVatFields = await Product.findByPk(item.product_id, {
          attributes: ['id', 'name', 'sku', 'has_serial', 'is_vat_exempt', 'is_vat_inclusive', 'suggested_buying_price', 'warranty_period'],
          transaction
        });

        if (!productWithVatFields) {
          await transaction.rollback();
          return next(new AppError(`Product with ID ${item.product_id} not found`, 404));
        }
      }

      // Check if product requires a serial number but none is provided
      if (
        productWithVatFields.has_serial &&
        (!item.serial_number || item.serial_number.trim() === "")
      ) {
        await transaction.rollback();
        return next(
          new AppError(
            `Product "${productWithVatFields.name}" requires a serial number/IMEI`,
            400
          )
        );
      }

      // Validate serial number if provided
      if (item.serial_number && item.serial_number.trim() !== "") {
        // Validate the serial number
        const serialValidation = await validateSerialNumber(item.serial_number);

        if (!serialValidation.valid) {
          // Check if the serial number doesn't exist in inventory
          if (serialValidation.message === 'Serial number not found in inventory') {
            // Create a new inventory item for this serial number
            try {
              const { generateUniqueBarcode } = require('../utils/barcode-generator');

              // Generate a unique barcode for the new inventory item
              const barcode = await generateUniqueBarcode(productWithVatFields);

              // Create the inventory item
              const newInventoryItem = await InventoryItem.create({
                stock_item_id: stockItem.id,
                product_id: item.product_id,
                barcode: barcode,
                serial_number: item.serial_number,
                status: 'in_stock',
                created_by: employee_id,
                last_updated_by: employee_id
              }, { transaction });

              // Note: We do NOT increment stock quantity here as this inventory item
              // represents an existing physical item that was already counted in stock
              // but just didn't have a serial number recorded yet

              // Set the barcode for the sale item
              if (!item.barcode) {
                item.barcode = barcode;
                item.barcode_just_generated = true; // Flag to skip barcode validation
              }

              console.log(`Created new inventory item for serial number: ${item.serial_number}`);
            } catch (createError) {
              await transaction.rollback();
              return next(
                new AppError(
                  `Failed to create inventory item for serial number ${item.serial_number}: ${createError.message}`,
                  400
                )
              );
            }
          } else {
            // Other validation errors (like already sold)
            await transaction.rollback();
            return next(
              new AppError(
                `Invalid serial number for product ID ${item.product_id}: ${serialValidation.message}`,
                400
              )
            );
          }
        } else {
          // Serial number exists and is valid
          // Check if the serial number belongs to the correct product
          if (
            serialValidation.item &&
            serialValidation.item.product_id !== item.product_id
          ) {
            await transaction.rollback();
            return next(
              new AppError(
                `Serial number ${item.serial_number} does not belong to product ID ${item.product_id}`,
                400
              )
            );
          }

          // If no barcode is provided but we have a serial number, use the barcode from the inventory item
          if (!item.barcode && serialValidation.item) {
            item.barcode = serialValidation.item.barcode;
          }
        }

        // Update inventory item status to 'sold' (for both existing and newly created items)
        await InventoryItem.update(
          { status: "sold" },
          {
            where: { serial_number: item.serial_number },
            transaction,
          }
        );
      }

      // Stock validation already completed above - stockItem is pre-validated and available

      // Get the product's buying price from stock item or product
      const productBuyingPrice =
        stockItem.default_buying_price ||
        stockItem.buying_price ||
        productWithVatFields.suggested_buying_price ||
        0;

      // Check if barcode is provided and validate it
      // Skip validation if this barcode was just generated for a new serial number
      if (item.barcode && !item.barcode_just_generated) {
        // Validate the barcode
        const barcodeValidation = await validateBarcode(item.barcode);

        if (!barcodeValidation.valid) {
          await transaction.rollback();
          return next(
            new AppError(
              `Invalid barcode for product ID ${item.product_id}: ${barcodeValidation.message}`,
              400
            )
          );
        }

        // Check if the barcode belongs to the correct product
        if (barcodeValidation.item.product_id !== item.product_id) {
          await transaction.rollback();
          return next(
            new AppError(
              `Barcode ${item.barcode} does not belong to product ID ${item.product_id}`,
              400
            )
          );
        }

        // Update inventory item status to 'sold'
        await InventoryItem.update(
          { status: "sold" },
          {
            where: { barcode: item.barcode },
            transaction,
          }
        );
      }

      // Get branch and region information for discount calculation
      const branch = await Branch.findByPk(effectiveBranchId); // Use the effective branch ID
      const regionId = branch ? branch.region_id : null;

      // Use the PriceCalculator to calculate the price with discounts
      // This will prioritize branch discounts first, then region discounts, then global discounts
      const PriceCalculator = require("../utils/price-calculator");

      // Create a merged object with both product and stock item data
      const productWithPricing = {
        ...(productWithVatFields.dataValues || productWithVatFields), // Handle both Sequelize instances and plain objects
        default_selling_price: stockItem.default_selling_price,
        default_buying_price: stockItem.default_buying_price,
        default_wholesale_price: stockItem.default_wholesale_price,
        StockItem: stockItem,
      };

      const priceDetails = await PriceCalculator.calculatePrice(
        productWithPricing,
        item.quantity,
        null, // customer
        {
          date: new Date(),
          branchId: effectiveBranchId, // Use the effective branch ID
          regionId: regionId,
        }
      );

      // Calculate VAT for the product after applying discount
      // Make sure we're using the final price after discount for VAT calculation
      const vatDetails = PriceCalculator.calculateVAT(
        priceDetails,
        productWithPricing
      );

      // Extract discount information
      let appliedDiscount = null;
      let discountAmount = 0;

      if (priceDetails.applied_discount_id) {
        appliedDiscount = await ProductDiscount.findByPk(
          priceDetails.applied_discount_id
        );
        discountAmount = priceDetails.discount_amount || 0;


        // Update remaining quantity if needed
        if (
          appliedDiscount &&
          appliedDiscount.max_quantity !== null &&
          appliedDiscount.remaining_quantity !== null
        ) {
          // Determine how many items get the discount
          const discountedQuantity = Math.min(
            item.quantity,
            appliedDiscount.remaining_quantity
          );

          // Update the remaining quantity
          await appliedDiscount.update(
            {
              remaining_quantity:
                appliedDiscount.remaining_quantity - discountedQuantity,
            },
            { transaction }
          );

          // Adjust discount amount if not all items get the discount
          if (discountedQuantity < item.quantity) {
            discountAmount =
              (discountAmount * discountedQuantity) / item.quantity;
          }
        }
      }

      // Use the final price from priceDetails instead of manually calculating
      const originalPrice = priceDetails.original_price || item.unit_price;
      const finalPrice =
        priceDetails.final_price || item.unit_price - discountAmount;
      const discountPercentage = priceDetails.discount_percentage || 0;


      // Create sale item with discount information and VAT details
      const saleItemData = {
        sale_id: sale.id,
        product_id: item.product_id,
        quantity: item.quantity,
        unit_price: finalPrice, // Use the final price after discount
        original_price: originalPrice, // Store the original price from priceDetails
        discount_amount: discountAmount * item.quantity,
        discount_id: appliedDiscount ? appliedDiscount.id : null,
        discount_type: priceDetails.discount_type || null,
        discount_source: priceDetails.discount_source || null,
        total_price: item.quantity * finalPrice,
        buying_price: productBuyingPrice,
        serial_number: item.serial_number,
        barcode: item.barcode, // Add barcode to sale item
        // Add VAT-related fields
        vat_rate: vatDetails.vat_rate || 0,
        vat_amount: vatDetails.vat_amount || 0,
        price_excluding_vat: vatDetails.price_excluding_vat || 0,
        is_vat_inclusive: vatDetails.is_vat_inclusive || true,
        is_vat_exempt: vatDetails.is_vat_exempt || false,
      };



      const saleItem = await SaleItem.create(saleItemData, { transaction });

      saleItems.push(saleItem);

      // Update stock
      await stockItem.update(
        {
          quantity: stockItem.quantity - item.quantity,
        },
        { transaction }
      );

      // If barcode is provided, register it as sold
      if (item.barcode) {
        // Calculate warranty expiry date if product has warranty
        let warrantyExpiry = null;
        if (productWithVatFields.warranty_period && productWithVatFields.warranty_period > 0) {
          warrantyExpiry = new Date();
          warrantyExpiry.setDate(
            warrantyExpiry.getDate() + productWithVatFields.warranty_period
          );
        }

        // Check if barcode already exists in sold_barcodes table
        const existingSoldBarcode = await SoldBarcode.findOne({
          where: { barcode: item.barcode },
          transaction,
        });

        if (existingSoldBarcode) {
          // If the barcode is already in the sold_barcodes table, log a warning but continue
          logger.warn(
            `Barcode ${item.barcode} already exists in sold_barcodes table. Skipping insertion.`
          );
        } else {
          // Register barcode as sold
          await SoldBarcode.create(
            {
              barcode: item.barcode,
              product_id: item.product_id,
              sale_item_id: saleItem.id,
              sale_id: sale.id,
              sold_at: new Date(),
              warranty_expiry: warrantyExpiry,
              created_by: employee_id, // Set created_by to employee_id
              last_updated_by: employee_id, // Set last_updated_by to employee_id
            },
            { transaction }
          );
        }
      }
    }

    // Calculate total VAT amount and total excluding VAT
    let totalVatAmount = 0;
    let totalExcludingVat = 0;
    let totalDiscount = 0;

    for (const saleItem of saleItems) {
      totalVatAmount +=
        parseFloat(saleItem.vat_amount || 0) * saleItem.quantity;
      totalExcludingVat +=
        parseFloat(saleItem.price_excluding_vat || 0) * saleItem.quantity;
      totalDiscount += parseFloat(saleItem.discount_amount || 0);
    }


    // Update the sale with VAT information
    await sale.update(
      {
        total_vat_amount: totalVatAmount,
        total_excluding_vat: totalExcludingVat,
        total_discount: totalDiscount,
      },
      { transaction }
    );

    // Fetch the complete sale with all associations for KRA integration AFTER VAT update
    const completeSale = await Sale.findOne({
      where: { id: sale.id },
      include: [
        {
          model: PosSession,
          attributes: ["id", "start_time", "end_time", "status"],
        },
        {
          model: User,
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          attributes: ["id", "name", "location"],
        },
        {
          model: PaymentMethod,
          attributes: ["id", "name", "code", "requires_reference"],
        },
        {
          model: CreditPartner,
          attributes: [
            "id",
            "name",
            "code",
            "description",
            "requires_reference",
          ],
        },
        {
          model: Customer,
          attributes: ["id", "name", "phone", "email", "pin_number"],
        },

        {
          model: SaleItem,
          paranoid: false, // Disable paranoid for SaleItem since it doesn't have deleted_at
          include: [
            {
              model: Product,
              attributes: [
                "id", "name", "sku", "has_serial", "category_id",
                "is_vat_exempt"
              ],
              include: [
                {
                  model: ProductCategory,
                  attributes: ["id", "name"],
                },
              ],
            },
          ],
        },
      ],
      transaction // Include in the transaction
    });

    // Register sale with KRA BEFORE committing the transaction
    try {
      // Check if raw KRA data was provided in the request
      if (req.body.kra_items || req.body.kra_customer_info) {
        // Add raw KRA data to the sale object
        completeSale.raw_items = req.body.kra_items;
        completeSale.raw_customer_info = req.body.kra_customer_info;
      }

      // CRITICAL FIX: Pass the customer PIN from the request to the KRA service
      // This ensures the customer PIN is available for KRA integration
      if (customer_pin && customer_pin.trim()) {
        completeSale.customer_pin = customer_pin.trim();
      }

      logger.info(`Attempting KRA registration for sale ${sale.id} before committing transaction`);
      const kraService = new KraIntegrationService();
      const kraResponse = await kraService.registerSale(completeSale);

      if (kraResponse && kraResponse.success) {
        // Save KRA data to the sale record within the transaction

        // Create update data object
        const updateData = {
          kra_verification_code: kraResponse.verificationCode,
          kra_fiscal_receipt_number: kraResponse.fiscalReceiptNumber,
          kra_verification_url: kraResponse.qrCodeUrl || kraResponse.verificationUrl,
          kra_verification_timestamp: kraResponse.timestamp,
          kra_integration_status: kraResponse.offline ? 'offline' : 'completed',
          kra_response_data: kraResponse.fullResponseData
        };

        // Store QR code image in the response data for the mobile app if available
        if (kraResponse.qrCodeImage) {
          try {
            const kraData = JSON.parse(kraResponse.fullResponseData || '{}');
            kraData.qrCodeImage = kraResponse.qrCodeImage;
            updateData.kra_response_data = JSON.stringify(kraData);
          } catch (e) {
            logger.error(`Error updating KRA response data with QR code image: ${e.message}`);
          }
        }

        await Sale.update(updateData, {
          where: { id: sale.id },
          transaction // Include in the transaction
        });




        // Update the completeSale object with KRA data for the response
        completeSale.kra_verification_code = kraResponse.verificationCode;
        completeSale.kra_fiscal_receipt_number = kraResponse.fiscalReceiptNumber;
        completeSale.kra_verification_url = kraResponse.qrCodeUrl || kraResponse.verificationUrl;

        completeSale.kra_verification_timestamp = kraResponse.timestamp;
        completeSale.kra_integration_status = kraResponse.offline
          ? "offline"
          : "completed";
        completeSale.kra_response_data = kraResponse.fullResponseData;

        // Include QR code image in the response data for the mobile app
        try {
          const kraData = JSON.parse(completeSale.kra_response_data || '{}');
          if (kraResponse.qrCodeImage) {
            kraData.qrCodeImage = kraResponse.qrCodeImage;
            completeSale.kra_response_data = JSON.stringify(kraData);
          }
        } catch (e) {
          logger.error(`Error updating completeSale KRA response data with QR code image: ${e.message}`);
        }

        // Ensure the QR code URL is properly set
        if (!completeSale.kra_verification_url && kraResponse.qrCodeUrl) {
          completeSale.kra_verification_url = kraResponse.qrCodeUrl;
        }

        logger.info(`Sale ${sale.id} successfully registered with KRA`);
      } else {
        logger.error(`KRA registration failed for sale ${sale.id}: No success response`);
        throw new AppError('KRA registration failed: Unable to register sale with tax authority. Please try again.', 400);
      }
    } catch (kraError) {
      logger.error(`KRA registration failed for sale ${sale.id}: ${kraError.message}`);

      // Rollback the transaction since KRA failed
      await transaction.rollback();

      // Return a user-friendly error message based on the KRA error
      const userMessage = kraError.message.includes('VATGrRate')
        ? 'Tax calculation error: Invalid VAT rate. Please contact support.'
        : kraError.message.includes('timeout') || kraError.message.includes('network') || kraError.message.includes('ECONNREFUSED')
        ? 'Unable to connect to tax authority. Please check your internet connection and try again.'
        : kraError.message.includes('Invalid') || kraError.message.includes('validation')
        ? 'Invalid sale data: Please check your sale details and try again.'
        : 'Unable to register sale with tax authority. Please try again or contact support.';

      return next(new AppError(userMessage, 400));
    }

    // Only commit the transaction if KRA registration was successful
    await transaction.commit();

    // Add customer_pin and ensure KRA details are included in the response
    const responseData = completeSale.toJSON();

    // Try to parse the KRA response data if it's a string
    let parsedKraData = null;
    if (responseData.kra_response_data && typeof responseData.kra_response_data === 'string') {
      try {
        parsedKraData = JSON.parse(responseData.kra_response_data);
      } catch (e) {
        logger.warn(`Failed to parse KRA response data: ${e.message}`);
      }
    }

    // Add customer_pin for backward compatibility
    // Prioritize ClientPINnum from KRA response data, then Customer.pin_number
    if (parsedKraData && parsedKraData.customerInfo && parsedKraData.customerInfo.ClientPINnum) {
      responseData.customer_pin = parsedKraData.customerInfo.ClientPINnum;
    } else if (responseData.Customer && responseData.Customer.pin_number) {
      responseData.customer_pin = responseData.Customer.pin_number;
    }

    // Ensure KRA details are included
    const kraDetails = {
      kra_verification_code: responseData.kra_verification_code || "",
      kra_fiscal_receipt_number: responseData.kra_fiscal_receipt_number || "",
      kra_verification_url: responseData.kra_verification_url || "",
      kra_verification_timestamp:
        responseData.kra_verification_timestamp || null,
      kra_integration_status: responseData.kra_integration_status || "pending",
      kra_response_data: responseData.kra_response_data || "",
      kra_qr_code_image: responseData.kra_qr_code_image || "",
    };

    // Add KRA details to the response data
    const enhancedResponseData = {
      ...responseData,
      ...kraDetails,
      // Include parsed KRA data if available
      kraData: parsedKraData,
      // Ensure VAT fields are included in the response
      total_excluding_vat: responseData.total_excluding_vat || 0,
      total_vat_amount: responseData.total_vat_amount || 0,
      total_discount: responseData.total_discount || 0
    };

    // COMPREHENSIVE SALE RESPONSE DATA LOG
    console.log('=== SALE RESPONSE DATA ===');
    console.log(JSON.stringify(enhancedResponseData, null, 2));
    console.log('=== END SALE RESPONSE DATA ===');

    // Transaction is already committed above (line ~794)
    return res.status(201).json(enhancedResponseData);
  } catch (error) {
    // Rollback transaction in case of error
    try {
      // Only roll back if the transaction is still active
      if (transaction && transaction.finished !== "commit") {
        await transaction.rollback();
      }
    } catch (rollbackError) {
      logger.error(`Error rolling back transaction: ${rollbackError.message}`);
    }

    logger.error(`Error creating sale: ${error.message}`);
    next(error);
  }
};

/**
 * Update a sale status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateSaleStatus = async (req, res, next) => {
  // Start a transaction
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;
    const { status, notes } = req.body;

    if (!status || !["completed", "cancelled", "refunded"].includes(status)) {
      return next(
        new AppError(
          "Valid status is required (completed, cancelled, or refunded)",
          400
        )
      );
    }

    const sale = await Sale.findOne({
      where: { id, deleted_at: null },
      include: [{ model: SaleItem, paranoid: false }],
    });

    if (!sale) {
      return next(new AppError("Sale not found", 404));
    }

    // If changing to cancelled or refunded, restore stock
    if (
      (status === "cancelled" || status === "refunded") &&
      sale.status === "completed"
    ) {
      // Restore stock for each item
      for (const item of sale.SaleItems) {
        const stockItem = await StockItem.findOne({
          where: {
            branch_id: sale.branch_id,
            product_id: item.product_id,
            deleted_at: null,
          },
        });

        if (stockItem) {
          await stockItem.update(
            {
              quantity: stockItem.quantity + item.quantity,
            },
            { transaction }
          );
        }

        // If item has a barcode, update inventory item status and remove from sold_barcodes
        if (item.barcode) {
          // Update inventory item status back to 'in_stock'
          await InventoryItem.update(
            { status: "in_stock" },
            {
              where: { barcode: item.barcode },
              transaction,
            }
          );

          // Remove from sold_barcodes (soft delete)
          await SoldBarcode.destroy({
            where: {
              barcode: item.barcode,
              sale_item_id: item.id,
            },
            transaction,
          });
        }
      }
    }

    // Update sale status
    await sale.update(
      {
        status,
        notes: notes || sale.notes,
      },
      { transaction }
    );

    // If changing to cancelled or refunded, update running balances
    if (
      (status === "cancelled" || status === "refunded") &&
      sale.status === "completed"
    ) {
      // Get the payment method
      const paymentMethod = await PaymentMethod.findByPk(
        sale.payment_method_id
      );

      if (paymentMethod) {
        // Get the active session
        const posSession = await PosSession.findOne({
          where: {
            branch_id: sale.branch_id,
            status: "open",
            deleted_at: null,
          },
        });

        if (posSession) {
          // Update running balances based on payment method
          if (paymentMethod.code.toLowerCase() === "mpesa") {
            // For MPESA sales, decrement running_mpesa_balance
            await posSession.decrement("running_mpesa_balance", {
              by: sale.total_amount,
              transaction,
            });
            logger.info(
              `Decremented running_mpesa_balance by ${sale.total_amount} for session ${posSession.id} (cancelled/refunded MPESA sale)`
            );
          } else if (paymentMethod.code.toLowerCase() === "cash") {
            // For cash sales, decrement running_cash_balance
            await posSession.decrement("running_cash_balance", {
              by: sale.total_amount,
              transaction,
            });
            logger.info(
              `Decremented running_cash_balance by ${sale.total_amount} for session ${posSession.id} (cancelled/refunded cash sale)`
            );
          }
        }
      }
    }

    // Commit transaction
    await transaction.commit();

    // Return updated sale
    const updatedSale = await Sale.findOne({
      where: { id },
      include: [
        {
          model: PosSession,
          attributes: ["id", "start_time", "end_time", "status"],
        },
        {
          model: User,
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          attributes: ["id", "name", "location"],
        },
        {
          model: PaymentMethod,
          attributes: ["id", "name", "code", "requires_reference"],
        },
        {
          model: Customer,
          attributes: ["id", "name", "phone", "email", "pin_number"],
        },
        {
          model: SaleItem,
          paranoid: false, // Disable paranoid for SaleItem since it doesn't have deleted_at
          include: [
            {
              model: Product,
              attributes: ["id", "name", "sku", "has_serial", "category_id"],
              include: [
                {
                  model: ProductCategory,
                  attributes: ["id", "name"],
                },
              ],
            },
          ],
        },
      ],
    });

    // Add customer_pin and ensure KRA details are included in the response
    const responseData = updatedSale.toJSON();

    // Try to parse the KRA response data if it's a string
    let parsedKraData = null;
    if (responseData.kra_response_data && typeof responseData.kra_response_data === 'string') {
      try {
        parsedKraData = JSON.parse(responseData.kra_response_data);
      } catch (e) {
        logger.warn(`Failed to parse KRA response data for updated sale: ${e.message}`);
      }
    }

    // Add customer_pin for backward compatibility
    // Prioritize ClientPINnum from KRA response data, then Customer.pin_number
    if (parsedKraData && parsedKraData.customerInfo && parsedKraData.customerInfo.ClientPINnum) {
      responseData.customer_pin = parsedKraData.customerInfo.ClientPINnum;
    } else if (responseData.Customer && responseData.Customer.pin_number) {
      responseData.customer_pin = responseData.Customer.pin_number;
    }

    // Ensure KRA details are included
    const kraDetails = {
      kra_verification_code: responseData.kra_verification_code || "",
      kra_fiscal_receipt_number: responseData.kra_fiscal_receipt_number || "",
      kra_verification_url: responseData.kra_verification_url || "",
      kra_verification_timestamp:
        responseData.kra_verification_timestamp || null,
      kra_integration_status: responseData.kra_integration_status || "pending",
      kra_response_data: responseData.kra_response_data || "",
    };

    // Add KRA details to the response data
    const enhancedResponseData = {
      ...responseData,
      ...kraDetails,
    };

    return res.status(200).json(enhancedResponseData);
  } catch (error) {
    // Rollback transaction in case of error
    await transaction.rollback();
    logger.error(`Error updating sale status: ${error.message}`);
    next(error);
  }
};

/**
 * Delete a sale (soft delete)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteSale = async (req, res, next) => {
  // Start a transaction
  const transaction = await sequelize.transaction();

  try {
    const { id } = req.params;

    const sale = await Sale.findOne({
      where: { id, deleted_at: null },
      include: [{ model: SaleItem, paranoid: false }],
    });

    if (!sale) {
      return next(new AppError("Sale not found", 404));
    }

    // If sale is completed, restore stock
    if (sale.status === "completed") {
      // Restore stock for each item
      for (const item of sale.SaleItems) {
        const stockItem = await StockItem.findOne({
          where: {
            branch_id: sale.branch_id,
            product_id: item.product_id,
            deleted_at: null,
          },
        });

        if (stockItem) {
          await stockItem.update(
            {
              quantity: stockItem.quantity + item.quantity,
            },
            { transaction }
          );
        }
      }
    }

    // If sale is completed, update running balances
    if (sale.status === "completed") {
      // Get the payment method
      const paymentMethod = await PaymentMethod.findByPk(
        sale.payment_method_id
      );

      if (paymentMethod) {
        // Get the active session
        const posSession = await PosSession.findOne({
          where: {
            branch_id: sale.branch_id,
            status: "open",
            deleted_at: null,
          },
        });

        if (posSession) {
          // Update running balances based on payment method
          if (paymentMethod.code.toLowerCase() === "mpesa") {
            // For MPESA sales, decrement running_mpesa_balance
            await posSession.decrement("running_mpesa_balance", {
              by: sale.total_amount,
              transaction,
            });
            logger.info(
              `Decremented running_mpesa_balance by ${sale.total_amount} for session ${posSession.id} (deleted MPESA sale)`
            );
          } else if (paymentMethod.code.toLowerCase() === "cash") {
            // For cash sales, decrement running_cash_balance
            await posSession.decrement("running_cash_balance", {
              by: sale.total_amount,
              transaction,
            });
            logger.info(
              `Decremented running_cash_balance by ${sale.total_amount} for session ${posSession.id} (deleted cash sale)`
            );
          }
        }
      }
    }

    // Soft delete the sale
    await sale.update({ deleted_at: new Date() }, { transaction });

    // Commit transaction
    await transaction.commit();

    return res.status(200).json({
      message: "Sale deleted successfully",
    });
  } catch (error) {
    // Rollback transaction in case of error
    await transaction.rollback();
    logger.error(`Error deleting sale: ${error.message}`);
    next(error);
  }
};

/**
 * Create a new DSA sale
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createDsaSale = async (req, res, next) => {
  try {
    // Extract variables from request body, but allow them to be modified
    let { product_id, quantity_sold, sale_amount, notes, employee_id, payment_method_id, payment_reference, credit_partner_id } = req.body;
    // User ID is handled separately in the DSA user validation section

    // Check if we're using KRA items format
    const isUsingKraFormat = req.body.kra_items && Array.isArray(req.body.kra_items) && req.body.kra_items.length > 0;



    // If using KRA format, extract the product_id, quantity_sold, and sale_amount from the first KRA item
    if (isUsingKraFormat) {


      const kraItem = req.body.kra_items[0]; // Use the first item for DSA sale

      // Find the product by name or SKU if product_id is not provided
      if (!product_id && kraItem.NamePLU) {
        // Try to find by name
        const product = await Product.findOne({
          where: {
            name: { [Op.like]: `%${kraItem.NamePLU}%` },
            deleted_at: null
          }
        });

        // If found, use its ID
        if (product) {
          product_id = product.id;

        }
      }

      // Extract quantity and sale amount if not provided
      if (!quantity_sold && kraItem.Quantity) {
        quantity_sold = parseFloat(kraItem.Quantity) || 1;

      }

      if (!sale_amount && kraItem.Price && kraItem.Quantity) {
        sale_amount = parseFloat(kraItem.Price) * parseFloat(kraItem.Quantity);

      }
    }

    // Validate required fields
    if (!product_id || !quantity_sold || !sale_amount) {
      logger.error(`Invalid DSA sale data: product_id=${product_id}, quantity_sold=${quantity_sold}, sale_amount=${sale_amount}`);
      return next(new AppError('Product ID, quantity sold, and sale amount are required', 400));

    }

    // Validate that employee_id (seller) is present
    if (!employee_id) {
      return next(new AppError("Seller ID (employee_id) is required", 400));
    }

    // Set effectiveUserId directly to employee_id
    const effectiveUserId = employee_id;

    // Find DSA agent role
    const dsaRole = await Role.findOne({
      where: { name: "dsa_agent" },
    });

    if (!dsaRole) {
      return next(new AppError("DSA agent role not found", 404));
    }

    // Check if DSA user exists
    const dsaUser = await User.findOne({
      where: {
        id: effectiveUserId,
        role_id: dsaRole.id,
        is_dsa: true,
        deleted_at: null,
      },
      include: [
        {
          model: Branch,
          attributes: ["id", "name"],
        },
      ],
    });

    if (!dsaUser) {
      return next(new AppError("DSA user not found", 404));
    }

    // Check if product exists
    const product = await Product.findOne({
      where: { id: product_id, deleted_at: null },
    });

    if (!product) {
      return next(new AppError("Product not found", 404));
    }

    // Check if product requires a serial number
    if (product.has_serial) {
      return next(
        new AppError(
          `Product "${product.name}" requires a serial number/IMEI and cannot be sold through DSA sales`,
          400
        )
      );
    }

    // For DSA sales, we don't require an active POS session
    // But we'll use it if it exists
    const posSession = await PosSession.findOne({
      where: {
        branch_id: dsaUser.branch_id,
        status: "open",
        deleted_at: null,
      },
    });

    // Get the stock item for this product in the branch to get the selling price
    const stockItem = await StockItem.findOne({
      where: {
        branch_id: dsaUser.branch_id,
        product_id,
      },
    });

    // Check if the DSA user has this product assigned and has enough stock
    const dsaStockAssignment = await DsaStockAssignment.findOne({
      where: {
        user_id: effectiveUserId,
        product_id,
        reconciled: false,
      },
    });

    if (!dsaStockAssignment) {
      return next(
        new AppError(
          `Product with ID ${product_id} is not assigned to this DSA agent`,
          400
        )
      );
    }

    // Calculate available quantity (assigned - sold - returned)
    // Use the quantity_sold field from the DSA stock assignment
    const availableQuantity =
      dsaStockAssignment.quantity_assigned -
      dsaStockAssignment.quantity_sold -
      dsaStockAssignment.quantity_returned;

    if (availableQuantity < quantity_sold) {
      return next(
        new AppError(
          `Insufficient DSA stock for product ID ${product_id}. Available: ${availableQuantity}, Requested: ${quantity_sold}`,
          400
        )
      );
    }

    // Find payment method (default to Cash if not specified)
    const effectivePaymentMethodId = payment_method_id || 1; // Default to Cash (ID 1)

    const paymentMethod = await PaymentMethod.findOne({
      where: { id: effectivePaymentMethodId },
    });

    if (!paymentMethod) {
      return next(new AppError("Payment method not found", 404));
    }

    // Check if credit partner is required for credit payment method
    // Skip this validation for DSA sales (which this function is specifically for)
    // DSA sales can use CREDIT payment method without a credit partner
    if (
      paymentMethod.code.toLowerCase() === "credit" &&
      !credit_partner_id &&
      !req.body.is_dsa
    ) {
      return next(
        new AppError(
          "Credit partner is required for Credit payment method",
          400
        )
      );
    }

    // Validate credit partner if provided
    let creditPartner = null;
    if (credit_partner_id) {
      creditPartner = await CreditPartner.findOne({
        where: { id: credit_partner_id, is_active: true, deleted_at: null },
      });

      if (!creditPartner) {
        return next(new AppError("Credit partner not found or inactive", 404));
      }
    }

    // Start a transaction
    const transaction = await sequelize.transaction();

    try {
      // Get the default Walk-in customer
      const walkInCustomer = await Customer.findOne({
        where: { name: "Walk-in", deleted_at: null },
      });

      let walkInCustomerId = null;

      if (walkInCustomer) {
        walkInCustomerId = walkInCustomer.id;
      } else {
        // Create default Walk-in customer if it doesn't exist
        const defaultCustomer = await Customer.create(
          {
            name: "Walk-in",
            created_by: req.user ? req.user.id : null,
            last_updated_by: req.user ? req.user.id : null,
          },
          { transaction }
        );

        walkInCustomerId = defaultCustomer.id;
      }

      // Generate receipt number for DSA sale
      const receipt_number = `DSA-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // Create sale in the main sales table
      const dsaSale = await Sale.create(
        {
          pos_session_id: posSession ? posSession.id : null, // Link to the active POS session if it exists
          user_id: effectiveUserId,
          branch_id: dsaUser.branch_id,
          customer_id: walkInCustomerId,
          employee_id, // Employee ID is required
          total_amount: sale_amount,
          payment_method_id: paymentMethod.id,
          payment_reference:
            payment_reference ||
            (paymentMethod.code.toLowerCase() === "credit" ? "" : "DSA Sale"),
          receipt_number, // Add receipt number
          credit_partner_id: credit_partner_id || null,
          status: "completed",
          notes: notes || `DSA Sale created on ${new Date().toISOString()}`,
          is_dsa: true,
          created_by: employee_id, // Set created_by to employee_id
          last_updated_by: employee_id, // Set last_updated_by to employee_id
        },
        { transaction }
      );

      // Update running balances based on payment method
      if (paymentMethod.code.toLowerCase() === "mpesa") {
        // For MPESA sales, update running_mpesa_balance
        await posSession.increment("running_mpesa_balance", {
          by: sale_amount,
          transaction,
        });
      } else if (paymentMethod.code.toLowerCase() === 'cash') {

        // For cash sales, update running_cash_balance
        await posSession.increment("running_cash_balance", {
          by: sale_amount,
          transaction,
        });
      }

      // Get branch and region information for discount calculation
      const branch = await Branch.findByPk(dsaUser.branch_id);
      const regionId = branch ? branch.region_id : null;

      // Use the PriceCalculator to calculate the price with discounts
      const PriceCalculator = require("../utils/price-calculator");

      // Create a merged object with both product and stock item data
      const productWithPricing = {
        ...(product.dataValues || product), // Handle both Sequelize instances and plain objects
        default_selling_price: stockItem
          ? stockItem.default_selling_price
          : null,
        default_buying_price: stockItem ? stockItem.default_buying_price : null,
        default_wholesale_price: stockItem
          ? stockItem.default_wholesale_price
          : null,
        StockItem: stockItem,
      };

      const priceDetails = await PriceCalculator.calculatePrice(
        productWithPricing,
        quantity_sold,
        null, // customer
        {
          date: new Date(),
          branchId: dsaUser.branch_id,
          regionId: regionId,
        }
      );

      // Calculate VAT for the product after applying discount
      // Make sure we're using the final price after discount for VAT calculation
      const vatDetails = PriceCalculator.calculateVAT(
        priceDetails,
        productWithPricing
      );

      // Use the price details from the price calculator
      const originalPrice =
        priceDetails.original_price ||
        (stockItem?.default_selling_price
          ? parseFloat(stockItem.default_selling_price)
          : sale_amount / quantity_sold);
      const finalPrice =
        priceDetails.final_price ||
        originalPrice - (priceDetails.discount_amount || 0);
      const discountPercentage = priceDetails.discount_percentage || 0;

      // Get the product's buying price
      const productBuyingPrice =
        stockItem?.default_buying_price || product.buying_price || 0;

      // Extract discount information
      let appliedDiscount = null;
      let discountAmount = priceDetails.discount_amount || 0;

      if (priceDetails.applied_discount_id) {
        appliedDiscount = await ProductDiscount.findByPk(
          priceDetails.applied_discount_id
        );
        // Update remaining quantity if needed
        if (
          appliedDiscount &&
          appliedDiscount.max_quantity !== null &&
          appliedDiscount.remaining_quantity !== null
        ) {
          // Determine how many items get the discount
          const discountedQuantity = Math.min(
            quantity_sold,
            appliedDiscount.remaining_quantity
          );

          // Update the remaining quantity
          await appliedDiscount.update(
            {
              remaining_quantity:
                appliedDiscount.remaining_quantity - discountedQuantity,
            },
            { transaction }
          );
        }
      }

      // Calculate final total price
      const finalTotalPrice = quantity_sold * finalPrice;

      // Create sale item with discount information and VAT details
      await SaleItem.create({
        sale_id: dsaSale.id,
        product_id,
        quantity: quantity_sold,
        unit_price: finalPrice, // Use the final price after discount
        original_price: originalPrice, // Store the original price from priceDetails
        discount_amount: discountAmount * quantity_sold,
        discount_id: appliedDiscount ? appliedDiscount.id : null,
        discount_type: priceDetails.discount_type || null,
        discount_source: priceDetails.discount_source || null,
        total_price: finalTotalPrice,
        buying_price: productBuyingPrice,
        created_by: employee_id, // Set created_by to employee_id
        last_updated_by: employee_id, // Set last_updated_by to employee_id
        // Add VAT-related fields
        vat_rate: vatDetails.vat_rate || 0,
        vat_amount: vatDetails.vat_amount || 0,
        price_excluding_vat: vatDetails.price_excluding_vat || 0,
        is_vat_inclusive: vatDetails.is_vat_inclusive || true,
        is_vat_exempt: vatDetails.is_vat_exempt || false
      }, { transaction });


      // Update DSA stock assignment to reflect the sale
      // We'll track the sold quantity in a new field called quantity_sold
      // This will allow us to keep track of sales without waiting for reconciliation
      if (dsaStockAssignment) {
        // Calculate the new quantity_sold value
        const newQuantitySold =
          (dsaStockAssignment.quantity_sold || 0) + quantity_sold;

        // Update the DSA stock assignment
        await dsaStockAssignment.update(
          {
            quantity_sold: newQuantitySold,
            sale_id: dsaSale.id, // Link to the sale that caused this update
            last_updated_by: employee_id, // Set last_updated_by to employee_id
          },
          { transaction }
        );
      }

      // IMPORTANT: We do NOT update the main stock here
      // Stock will only be updated during reconciliation

      // Update the sale with VAT information
      const vatAmount = vatDetails.vat_amount || 0;
      const priceExcludingVat = vatDetails.price_excluding_vat || 0;

      await dsaSale.update(
        {
          total_vat_amount: vatAmount * quantity_sold,
          total_excluding_vat: priceExcludingVat * quantity_sold,
          total_discount: discountAmount * quantity_sold,
        },
        { transaction }
      );

      // Fetch the complete sale with associations for KRA integration BEFORE committing
      const completeSale = await Sale.findOne({
        where: { id: dsaSale.id },
        include: [
          {
            model: User,
            attributes: ["id", "name", "phone", "email"],
          },
          {
            model: SaleItem,
            paranoid: false, // Disable paranoid for SaleItem since it doesn't have deleted_at
            include: [
              {
                model: Product,
                attributes: ["id", "name", "sku", "category_id"],
                include: [
                  {
                    model: ProductCategory,
                    attributes: ["id", "name"],
                  },
                ],
              },
            ],
          },
          {
            model: Branch,
            attributes: ["id", "name", "location"],
          },
          {
            model: PaymentMethod,
            attributes: ["id", "name", "code", "requires_reference"],
          },
          {
            model: CreditPartner,
            attributes: [
              "id",
              "name",
              "code",
              "description",
              "requires_reference",
            ],
          },
          {
            model: Customer,
            attributes: ["id", "name", "phone", "email"],
          },

          {
            model: User,
            as: "SaleCreator",
            attributes: ["id", "name"],
          },
          {
            model: User,
            as: "SaleUpdater",
            attributes: ["id", "name"],
          },
        ],
        transaction // Include in the transaction
      });

      // Register DSA sale with KRA BEFORE committing the transaction
      try {
        // Check if raw KRA data was provided in the request
        if (req.body.kra_items || req.body.kra_customer_info) {
          // Add raw KRA data to the sale object
          completeSale.raw_items = req.body.kra_items;
          completeSale.raw_customer_info = req.body.kra_customer_info;
        }

        // CRITICAL FIX: Pass the customer PIN from the request to the KRA service
        // This ensures the customer PIN is available for KRA integration
        if (req.body.customer_pin && req.body.customer_pin.trim()) {
          completeSale.customer_pin = req.body.customer_pin.trim();
        }

        const kraService = new KraIntegrationService();
        const kraResponse = await kraService.registerSale(completeSale);

        // Validate KRA response has required data
        if (!kraResponse) {
          throw new Error('KRA service returned null or undefined response');
        }

        if (!kraResponse.success) {
          throw new Error('KRA service returned unsuccessful response');
        }

        if (!kraResponse.verificationCode) {
          throw new Error('KRA service did not return a verification code');
        }

        if (!kraResponse.fiscalReceiptNumber) {
          throw new Error('KRA service did not return a fiscal receipt number');
        }

        // Log successful KRA response for debugging
        logger.info(`KRA response validation passed for DSA sale ${dsaSale.id}:`, {
          verificationCode: kraResponse.verificationCode,
          fiscalReceiptNumber: kraResponse.fiscalReceiptNumber,
          hasQrCodeUrl: !!kraResponse.qrCodeUrl,
          hasFullResponseData: !!kraResponse.fullResponseData
        });

        if (kraResponse && kraResponse.success) {
          // Create update data object
          const updateData = {
            kra_verification_code: kraResponse.verificationCode,
            kra_fiscal_receipt_number: kraResponse.fiscalReceiptNumber,
            kra_verification_url: kraResponse.qrCodeUrl || kraResponse.verificationUrl,
            kra_verification_timestamp: kraResponse.timestamp,
            kra_integration_status: kraResponse.offline ? 'offline' : 'completed',
            kra_response_data: kraResponse.fullResponseData
          };

          // Log QR code image availability but don't save it to the database yet
          if (kraResponse.qrCodeImage) {
            // Store QR code image in the response data for the mobile app
            try {
              const kraData = JSON.parse(kraResponse.fullResponseData || '{}');
              kraData.qrCodeImage = kraResponse.qrCodeImage;
              updateData.kra_response_data = JSON.stringify(kraData);
            } catch (e) {
              logger.error(`Error updating KRA response data with QR code image for DSA sale: ${e.message}`);
            }
          }

          await Sale.update(updateData, {
            where: { id: dsaSale.id },
            transaction // Include in the transaction
          });


          // Update the completeSale object with KRA data for the response
          completeSale.kra_verification_code = kraResponse.verificationCode;
          completeSale.kra_fiscal_receipt_number = kraResponse.fiscalReceiptNumber;
          completeSale.kra_verification_url = kraResponse.qrCodeUrl || kraResponse.verificationUrl;

          completeSale.kra_verification_timestamp = kraResponse.timestamp;
          completeSale.kra_integration_status = kraResponse.offline
            ? "offline"
            : "completed";
          completeSale.kra_response_data = kraResponse.fullResponseData;
          // Include QR code image in the response data for the mobile app
          try {
            const kraData = JSON.parse(completeSale.kra_response_data || '{}');
            if (kraResponse.qrCodeImage) {
              kraData.qrCodeImage = kraResponse.qrCodeImage;
              completeSale.kra_response_data = JSON.stringify(kraData);
            }
          } catch (e) {
            logger.error(`Error updating DSA completeSale KRA response data with QR code image: ${e.message}`);
          }

          // Ensure the QR code URL is properly set
          if (!completeSale.kra_verification_url && kraResponse.qrCodeUrl) {
            completeSale.kra_verification_url = kraResponse.qrCodeUrl;
          }

          logger.info(`DSA sale ${dsaSale.id} successfully registered with KRA`);
        } else {
          logger.error(`KRA registration failed for DSA sale ${dsaSale.id}: No success response`);
          throw new AppError('KRA registration failed: Unable to register DSA sale with tax authority. Please try again.', 400);
        }
      } catch (kraError) {
        logger.error(`KRA registration failed for DSA sale ${dsaSale.id}: ${kraError.message}`);

        // Rollback the transaction since KRA failed
        await transaction.rollback();

        // Return a user-friendly error message based on the KRA error
        const userMessage = kraError.message.includes('VATGrRate')
          ? 'Tax calculation error: Invalid VAT rate. Please contact support.'
          : kraError.message.includes('timeout') || kraError.message.includes('network') || kraError.message.includes('ECONNREFUSED')
          ? 'Unable to connect to tax authority. Please check your internet connection and try again.'
          : kraError.message.includes('Invalid') || kraError.message.includes('validation')
          ? 'Invalid sale data: Please check your sale details and try again.'
          : 'Unable to register DSA sale with tax authority. Please try again or contact support.';

        return next(new AppError(userMessage, 400));
      }

      // Only commit the transaction if KRA registration was successful
      await transaction.commit();

      // Add customer_pin and ensure KRA details are included in the response
      const responseData = completeSale.toJSON();

      // Try to parse the KRA response data if it's a string
      let parsedKraData = null;
      if (responseData.kra_response_data && typeof responseData.kra_response_data === 'string') {
        try {
          parsedKraData = JSON.parse(responseData.kra_response_data);
        } catch (e) {
          logger.warn(`Failed to parse KRA response data for DSA sale: ${e.message}`);
        }
      }

      // Add customer_pin for backward compatibility
      // Prioritize ClientPINnum from KRA response data, then Customer.pin_number
      if (parsedKraData && parsedKraData.customerInfo && parsedKraData.customerInfo.ClientPINnum) {
        responseData.customer_pin = parsedKraData.customerInfo.ClientPINnum;
      } else if (responseData.Customer && responseData.Customer.pin_number) {
        responseData.customer_pin = responseData.Customer.pin_number;
      }

      // Ensure KRA details are included
      const kraDetails = {
        kra_verification_code: responseData.kra_verification_code || "",
        kra_fiscal_receipt_number: responseData.kra_fiscal_receipt_number || "",
        kra_verification_url: responseData.kra_verification_url || "",
        kra_verification_timestamp:
          responseData.kra_verification_timestamp || null,
        kra_integration_status:
          responseData.kra_integration_status || "pending",
        kra_response_data: responseData.kra_response_data || "",
        kra_qr_code_image: responseData.kra_qr_code_image || "",
      };

      // Add KRA details to the response data
      const enhancedResponseData = {
        ...responseData,
        ...kraDetails,
        // Include parsed KRA data if available
        kraData: parsedKraData

      };

      // Transaction is already committed above (line ~1549)
      return res.status(201).json(enhancedResponseData);
    } catch (error) {
      // Rollback transaction in case of error
      try {
        // Only roll back if the transaction is still active
        if (transaction && transaction.finished !== "commit") {
          await transaction.rollback();
        }
      } catch (rollbackError) {
        logger.error(
          `Error rolling back transaction: ${rollbackError.message}`
        );
      }
      throw error;
    }
  } catch (error) {
    logger.error(`Error creating DSA sale: ${error.message}`);
    next(error);
  }
};

/**
 * Update sale with KRA data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateSaleKRAData = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      kra_verification_code,
      kra_fiscal_receipt_number,
      kra_verification_url,
      kra_verification_timestamp,
      kra_integration_status,
      kra_response_data,
      kra_qr_code_image

    } = req.body;

    // Find the sale
    const sale = await Sale.findOne({
      where: { id, deleted_at: null },
    });

    if (!sale) {
      return next(new AppError("Sale not found", 404));
    }

    // Update the sale with KRA data
    const updateData = {
      kra_verification_code,
      kra_fiscal_receipt_number,
      kra_verification_url,
      kra_verification_timestamp,
      kra_integration_status,
      kra_response_data
    };

    // Include QR code image in the response data if provided
    if (kra_qr_code_image) {
      logger.info(`QR code image provided in manual KRA data update for sale ID ${id}`);
      try {
        const kraData = JSON.parse(updateData.kra_response_data || '{}');
        kraData.qrCodeImage = kra_qr_code_image;
        updateData.kra_response_data = JSON.stringify(kraData);
        logger.info(`Included QR code image in KRA response data for sale ID ${id}`);
      } catch (e) {
        logger.error(`Error updating KRA response data with QR code image in manual update: ${e.message}`);
      }
    }

    await sale.update(updateData);


    // Return the updated sale
    const updatedSale = await Sale.findOne({
      where: { id },
      include: [
        {
          model: PosSession,
          attributes: ["id", "start_time", "end_time", "status"],
        },
        {
          model: User,
          attributes: ["id", "name", "email", "role_id"],
        },
        {
          model: Branch,
          attributes: ["id", "name", "location"],
        },
        {
          model: PaymentMethod,
          attributes: ["id", "name", "code", "requires_reference"],
        },
        {
          model: CreditPartner,
          attributes: [
            "id",
            "name",
            "code",
            "description",
            "requires_reference",
          ],
        },
        {
          model: Customer,
          attributes: ["id", "name", "phone", "email", "pin_number"],
        },

        {
          model: SaleItem,
          paranoid: false, // Disable paranoid for SaleItem since it doesn't have deleted_at
          include: [
            {
              model: Product,
              attributes: ["id", "name", "sku", "has_serial", "category_id"],
              include: [
                {
                  model: ProductCategory,
                  attributes: ["id", "name"],
                },
              ],
            },
          ],
        },
      ],
    });

    return res.status(200).json(updatedSale);
  } catch (error) {
    logger.error(`Error updating sale with KRA data: ${error.message}`);
    next(error);
  }
};

module.exports = {
  getAllSales,
  getReceiptData,
  getSaleById,
  createSale,
  updateSaleStatus,
  deleteSale,
  createDsaSale,
  updateSaleKRAData,
};
