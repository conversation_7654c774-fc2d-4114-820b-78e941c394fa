"use client";

import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { format } from "date-fns";
import { Plus, Trash2, ArrowLeft, Save, CalendarIcon } from "lucide-react";

import {
  CreateCreditNoteRequest,
  CreditNoteReason,
  CreditNoteReferenceType
} from '@/types/credit-note';
import { CreditNoteService } from '../api/credit-note-service';
import invoiceService from '@/features/invoices/api/invoice-service';
import salesService from '@/features/sales/api/sales-service';
import customerService from '@/features/customers/api/customer-service';
import { productService } from '@/features/products/api';
import { supplierService } from '@/features/inventory/api/supplier-service';
import { formatCurrency, cn } from '@/lib/utils';

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

// Define the form schema
const creditNoteFormSchema = z.object({
  reference_type: z.enum(["invoice", "sale"]),
  reference_id: z.number().optional(),
  reference_number: z.string().optional(),
  credit_note_date: z.date(),
  customer_id: z.number().optional().nullable(),
  supplier_id: z.number().optional().nullable(),
  reason: z.enum(["return", "price_adjustment", "quantity_adjustment", "discount", "cancellation", "other"]),
  reason_details: z.string().optional(),
  notes: z.string().optional(),
  items: z.array(
    z.object({
      description: z.string().min(1, "Description is required"),
      quantity: z.number().min(0.01, "Quantity must be greater than 0"),
      unit_price: z.number().min(0, "Unit price must be a valid number"),
      total_price: z.number().min(0, "Total price must be a valid number"),
      product_id: z.number().optional().nullable(),
      vat_rate: z.union([z.number(), z.string().transform(val => parseFloat(val))]).optional().nullable(),
      vat_amount: z.number().optional().nullable(),
      is_vat_exempt: z.boolean().optional().default(false),
      reference_item_id: z.number().optional(),
      reference_item_type: z.string().optional(),
    })
  ).min(1, "At least one item is required"),
  subtotal: z.number().min(0),
  vat_amount: z.number().min(0),
  total_amount: z.number().min(0),
});

type CreditNoteFormValues = z.infer<typeof creditNoteFormSchema>;

interface CreditNoteFormProps {
  creditNoteId?: number;
  isEdit?: boolean;
}

/**
 * Credit Note Form Component
 */
export const CreditNoteForm: React.FC<CreditNoteFormProps> = ({
  creditNoteId,
  isEdit = false,
}) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [referenceType, setReferenceType] =
    useState<CreditNoteReferenceType>("invoice");
  const [referenceId, setReferenceId] = useState<number | null>(null);
  const [isLoadingReference, setIsLoadingReference] = useState(false);


  // Form setup
  const form = useForm<CreditNoteFormValues>({
    resolver: zodResolver(creditNoteFormSchema),
    defaultValues: {
      reference_type: "invoice",
      credit_note_date: new Date(),
      items: [],
      subtotal: 0,
      vat_amount: 0,
      total_amount: 0,
      reason: "return",
    },
  });


  // Field array for items
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });


  // Watch form values for calculations
  const items = form.watch('items');
  const watchReferenceType = form.watch('reference_type');

  // Queries for data
  const {
    data: creditNote,
    isLoading: isLoadingCreditNote,
    isError: isErrorCreditNote,
  } = useQuery({
    queryKey: ["creditNote", creditNoteId],
    queryFn: () => CreditNoteService.getCreditNoteById(creditNoteId as number),
    enabled: isEdit && !!creditNoteId,
  });


  const { data: invoices } = useQuery({
    queryKey: ["invoices", { status: "sent", kra_integration_status: "completed" }],
    queryFn: () => invoiceService.getInvoices({
      status: "sent",
      kra_integration_status: "completed",
      limit: 100
    }),
    enabled: watchReferenceType === "invoice",
  });


  const { data: sales } = useQuery({
    queryKey: ["sales", { status: "completed", kra_integration_status: "completed" }],
    queryFn: () => salesService.getSales({
      status: "completed",
      kra_integration_status: "completed",
      limit: 100
    }),
    enabled: watchReferenceType === "sale",
  });


  const { data: customers } = useQuery({
    queryKey: ["customers"],
    queryFn: () => customerService.getCustomers({ limit: 100 }),
  });


  const { data: suppliers } = useQuery({
    queryKey: ["suppliers"],
    queryFn: () => supplierService.getSuppliers({ limit: 100 }),
  });


  const { data: products } = useQuery({
    queryKey: ["products"],
    queryFn: () => productService.getProducts({ limit: 100 }),
  });


  // Mutations for create/update
  const createMutation = useMutation({
    mutationFn: (data: CreateCreditNoteRequest) =>
      CreditNoteService.createCreditNote(data),
    onSuccess: () => {
      toast.success("Credit note created successfully");
      queryClient.invalidateQueries({ queryKey: ["creditNotes"] });
      router.push("/credit-notes");
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to create credit note"
      );
    },
  });


  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number, data: any }) =>
      CreditNoteService.updateCreditNote(id, data),
    onSuccess: () => {
      toast.success("Credit note updated successfully");
      queryClient.invalidateQueries({ queryKey: ["creditNotes"] });
      queryClient.invalidateQueries({ queryKey: ["creditNote", creditNoteId] });
      router.push(`/credit-notes/${creditNoteId}`);
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to update credit note"
      );
    },
  });


  // Load reference data (invoice or sale) - optimized to prevent performance issues
  const loadReferenceData = useCallback(async (
    type: CreditNoteReferenceType,
    id: number
  ) => {
    setIsLoadingReference(true);
    try {
      if (type === "invoice") {
        const invoice = await invoiceService.getInvoiceById(id);

        // Batch form updates to prevent multiple re-renders
        form.setValue('reference_id', invoice.id);
        form.setValue('reference_number', invoice.invoice_number);
        form.setValue('customer_id', invoice.customer_id);
        form.setValue('supplier_id', invoice.supplier_id);

        // Clear existing items efficiently
        form.setValue('items', []);

        // Prepare new items array
        const newItems = invoice.items?.map((item) => ({
          description: item.description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.quantity * item.unit_price,
          product_id: item.product_id,
          vat_rate: item.vat_rate,
          vat_amount: item.vat_amount,
          is_vat_exempt: item.is_vat_exempt,
          reference_item_id: item.id,
          reference_item_type: "invoice_item",
        })) || [];

        // Set all items at once
        form.setValue('items', newItems);

        // Calculate totals with the new items
        calculateTotals(newItems);
      } else if (type === "sale") {
        const sale = await salesService.getSaleById(id);

        // Batch form updates to prevent multiple re-renders
        form.setValue('reference_id', sale.id);
        form.setValue('reference_number', sale.receipt_number);
        form.setValue('customer_id', sale.customer_id);

        // Clear existing items efficiently
        form.setValue('items', []);

        // Prepare new items array
        const newItems = sale.items?.map((item) => ({
          description: item.product?.name || "Unknown Product",
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.total_price,
          product_id: item.product_id,
          vat_rate: item.vat_rate,
          vat_amount: item.vat_amount,
          is_vat_exempt: false, // Assuming sales don't have VAT exempt flag
          reference_item_id: item.id,
          reference_item_type: "sale_item",
        })) || [];

        // Set all items at once
        form.setValue('items', newItems);

        // Calculate totals with the new items
        calculateTotals(newItems);
      }
    } catch (error) {
      console.error("Error loading reference data:", error);
      toast.error("Failed to load reference data");
    } finally {
      setIsLoadingReference(false);
    }
  }, [form, calculateTotals]);


  // Calculate totals based on items - memoized to prevent infinite re-renders
  const calculateTotals = useCallback((itemsToCalculate?: any[]) => {
    const currentItems = itemsToCalculate || form.getValues('items');

    if (!currentItems || currentItems.length === 0) {
      form.setValue("subtotal", 0, { shouldValidate: false });
      form.setValue("vat_amount", 0, { shouldValidate: false });
      form.setValue("total_amount", 0, { shouldValidate: false });
      return;
    }

    const subtotal = currentItems.reduce((sum, item) => sum + (item.total_price || 0), 0);
    const vatAmount = currentItems.reduce((sum, item) => sum + (item.vat_amount || 0), 0);
    const totalAmount = subtotal + vatAmount;

    form.setValue('subtotal', subtotal, { shouldValidate: false });
    form.setValue('vat_amount', vatAmount, { shouldValidate: false });
    form.setValue('total_amount', totalAmount, { shouldValidate: false });
  }, [form]);


  // Add a new empty item
  const handleAddItem = () => {
    append({
      description: "",
      quantity: 1,
      unit_price: 0,
      total_price: 0,
      vat_rate: 0,
      vat_amount: 0,
      is_vat_exempt: false,
    });
  };


  // Handle form submission
  const onSubmit = (data: CreditNoteFormValues) => {
    console.log('🎯 Credit note form submitted with data:', data);
    console.log('🎯 Form validation state:', form.formState);
    console.log('🎯 Form errors:', form.formState.errors);

    // Format the date and ensure all numbers are properly converted
    const formattedData = {
      ...data,
      credit_note_date: format(data.credit_note_date, 'yyyy-MM-dd'),
      items: data.items.map(item => ({
        ...item,
        // Fix VAT rate handling - don't convert 0 to null
        vat_rate: item.vat_rate !== undefined && item.vat_rate !== null
          ? (typeof item.vat_rate === 'string' ? parseFloat(item.vat_rate) : item.vat_rate)
          : null,
        quantity: Number(item.quantity),
        unit_price: Number(item.unit_price),
        total_price: Number(item.total_price),
        vat_amount: item.vat_amount !== undefined && item.vat_amount !== null ? Number(item.vat_amount) : 0,
        is_vat_exempt: Boolean(item.is_vat_exempt)
      }))
    };

    console.log('🎯 Formatted data for API:', formattedData);

    if (isEdit && creditNoteId) {
      // For edit, we only update certain fields
      const updateData = {
        credit_note_date: formattedData.credit_note_date,
        reason: formattedData.reason,
        reason_details: formattedData.reason_details,
        notes: formattedData.notes,
      };


      updateMutation.mutate({ id: creditNoteId, data: updateData });
    } else {
      // For create, we send the full data
      createMutation.mutate(formattedData as CreateCreditNoteRequest);
    }
  };


  // Initialize form with credit note data for edit mode
  useEffect(() => {
    if (isEdit && creditNote) {
      form.reset({
        reference_type: creditNote.reference_type as CreditNoteReferenceType,
        reference_id: creditNote.reference_id,
        reference_number: creditNote.reference_number,
        credit_note_date: new Date(creditNote.credit_note_date),
        customer_id: creditNote.customer_id,
        supplier_id: creditNote.supplier_id,
        reason: creditNote.reason as CreditNoteReason,
        reason_details: creditNote.reason_details,
        subtotal: creditNote.subtotal,
        vat_amount: creditNote.vat_amount,
        total_amount: creditNote.total_amount,
        notes: creditNote.notes,
        items:
          creditNote.items?.map((item) => ({
            description: item.description,
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.total_price,
            product_id: item.product_id,
            vat_rate: item.vat_rate,
            vat_amount: item.vat_amount,
            is_vat_exempt: item.is_vat_exempt,
            reference_item_id: item.reference_item_id,
            reference_item_type: item.reference_item_type,
          })) || [],
      });


      setReferenceType(creditNote.reference_type as CreditNoteReferenceType);
      setReferenceId(creditNote.reference_id);
    }
  }, [isEdit, creditNote, form]);


  // Recalculate totals when items change - optimized to prevent infinite loops
  useEffect(() => {
    calculateTotals();
  }, [items, calculateTotals]);

  // Show loading state
  if (isEdit && isLoadingCreditNote) {
    return (
      <div className="flex items-center justify-center h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }


  // Show error state
  if (isEdit && isErrorCreditNote) {
    return (
      <div className="flex flex-col items-center justify-center h-[400px] space-y-4">
        <h3 className="text-xl font-semibold">Error Loading Credit Note</h3>
        <p className="text-gray-600 mb-4">Unable to load credit note data.</p>
        <Button
          onClick={() =>
            queryClient.invalidateQueries({
              queryKey: ["creditNote", creditNoteId],
            })
          }
        >
          Try Again
        </Button>
      </div>
    );
  }


  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          onClick={() =>
            router.push(
              isEdit ? `/credit-notes/${creditNoteId}` : "/credit-notes"
            )
          }
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          {isEdit ? "Back to Credit Note" : "Back to Credit Notes"}
        </Button>

        {/* Debug Test Button */}
        <Button
          variant="secondary"
          onClick={() => {
            console.log('🧪 TEST BUTTON CLICKED - React is working!');
            console.log('🧪 Form values:', form.getValues());
            console.log('🧪 Form errors:', form.formState.errors);
            alert('Test button clicked! Check console for details.');
          }}
        >
          🧪 Test Button
        </Button>
      </div>


      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>
                {isEdit ? "Edit Credit Note" : "Create Credit Note"}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Reference Type and Reference Selection */}
              {!isEdit && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="reference_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Reference Type</FormLabel>
                        <Select
                          onValueChange={(value) => {
                            field.onChange(value);
                            setReferenceType(value as CreditNoteReferenceType);
                            setReferenceId(null);
                            form.setValue('reference_id', undefined);
                            form.setValue('reference_number', '');
                            form.setValue('customer_id', undefined);
                            form.setValue('supplier_id', undefined);

                            // Clear items
                            while (fields.length > 0) {
                              remove(0);
                            }
                          }}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select reference type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="invoice">Invoice</SelectItem>
                            <SelectItem value="sale">Sale</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {referenceType === 'invoice' && (
                    <FormField
                      control={form.control}
                      name="reference_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Select Invoice</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              const numValue = parseInt(value, 10);
                              field.onChange(numValue);
                              setReferenceId(numValue);
                              if (numValue) {
                                loadReferenceData("invoice", numValue);
                              }
                            }}
                            defaultValue={field.value?.toString()}
                            disabled={isLoadingReference}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select an invoice" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {invoices?.data?.map((invoice) => (
                                <SelectItem
                                  key={invoice.id}
                                  value={invoice.id.toString()}
                                >
                                  {invoice.invoice_number} -{" "}
                                  {invoice.customer?.name ||
                                    invoice.supplier?.name ||
                                    "Unknown"}{" "}
                                  ({formatCurrency(invoice.total_amount)})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {referenceType === 'sale' && (
                    <FormField
                      control={form.control}
                      name="reference_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Select Sale</FormLabel>
                          <Select
                            onValueChange={(value) => {
                              const numValue = parseInt(value, 10);
                              field.onChange(numValue);
                              setReferenceId(numValue);
                              if (numValue) {
                                loadReferenceData("sale", numValue);
                              }
                            }}
                            defaultValue={field.value?.toString()}
                            disabled={isLoadingReference}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a sale" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {sales?.data?.map((sale) => (
                                <SelectItem
                                  key={sale.id}
                                  value={sale.id.toString()}
                                >
                                  {sale.receipt_number} -{" "}
                                  {sale.customer?.name || "Walk-in Customer"} (
                                  {formatCurrency(sale.total_amount)})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              )}


              {/* Credit Note Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="credit_note_date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Credit Note Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date > new Date() || date < new Date("1900-01-01")
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />


                <FormField
                  control={form.control}
                  name="reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reason</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select reason" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="return">Return</SelectItem>
                          <SelectItem value="price_adjustment">Price Adjustment</SelectItem>
                          <SelectItem value="quantity_adjustment">Quantity Adjustment</SelectItem>
                          <SelectItem value="discount">Discount</SelectItem>
                          <SelectItem value="cancellation">Cancellation</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>


              <FormField
                control={form.control}
                name="reason_details"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reason Details</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Provide additional details about the reason for this credit note"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />


              {/* Items Table */}
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium">Items</h3>
                  {!isEdit && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleAddItem}
                    >
                      <Plus className="mr-2 h-4 w-4" /> Add Item
                    </Button>
                  )}
                </div>


                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Description</TableHead>
                        <TableHead className="w-[100px]">Quantity</TableHead>
                        <TableHead className="w-[120px]">Unit Price</TableHead>
                        <TableHead className="w-[100px]">VAT Rate (%)</TableHead>
                        <TableHead className="w-[120px]">VAT Amount</TableHead>
                        <TableHead className="w-[120px]">Total</TableHead>
                        {!isEdit && (
                          <TableHead className="w-[80px]"></TableHead>
                        )}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {fields.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={isEdit ? 6 : 7} className="text-center py-4 text-muted-foreground">
                            {isEdit ? "No items found" : "Add items to this credit note"}
                          </TableCell>
                        </TableRow>
                      ) : (
                        fields.map((field, index) => (
                          <TableRow key={field.id}>
                            <TableCell>
                              <Input
                                {...form.register(`items.${index}.description` as const)}
                                disabled={isEdit}
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                {...form.register(`items.${index}.quantity` as const, {
                                  valueAsNumber: true,
                                  onChange: () => calculateTotals()
                                })}
                                disabled={isEdit}
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                step="0.01"
                                {...form.register(`items.${index}.unit_price` as const, {
                                  valueAsNumber: true,
                                  onChange: () => calculateTotals()
                                })}
                                disabled={isEdit}
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                step="0.01"
                                placeholder="0"
                                {...form.register(`items.${index}.vat_rate` as const, {
                                  valueAsNumber: true,
                                  onChange: () => calculateTotals()
                                })}
                                disabled={isEdit}
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                step="0.01"
                                {...form.register(
                                  `items.${index}.vat_amount` as const,
                                  {
                                    valueAsNumber: true,
                                    onChange: () => calculateTotals(),
                                  }
                                )}
                                disabled={isEdit}
                              />
                            </TableCell>
                            <TableCell>
                              <Input
                                type="number"
                                step="0.01"
                                {...form.register(
                                  `items.${index}.total_price` as const,
                                  {
                                    valueAsNumber: true,
                                  }
                                )}
                                disabled={true}
                              />
                            </TableCell>
                            {!isEdit && (
                              <TableCell>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => {
                                    remove(index);
                                    calculateTotals();
                                  }}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </TableCell>
                            )}
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>


              {/* Totals */}
              <div className="flex flex-col gap-2 items-end">
                <div className="flex justify-between w-full max-w-xs">
                  <span className="text-muted-foreground">Subtotal:</span>
                  <span>{formatCurrency(form.watch("subtotal"))}</span>
                </div>
                <div className="flex justify-between w-full max-w-xs">
                  <span className="text-muted-foreground">VAT:</span>
                  <span>{formatCurrency(form.watch("vat_amount"))}</span>
                </div>
                <div className="flex justify-between w-full max-w-xs font-medium text-lg">
                  <span>Total:</span>
                  <span>{formatCurrency(form.watch("total_amount"))}</span>
                </div>
              </div>


              {/* Notes */}
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any additional notes or information"
                        className="resize-none"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={createMutation.isPending || updateMutation.isPending || isLoadingReference}
              onClick={(e) => {
                console.log('🔥 Button clicked!', e);
                console.log('🔥 Form state:', form.formState);
                console.log('🔥 Form values:', form.getValues());
                console.log('🔥 Form errors:', form.formState.errors);
              }}
            >
              <Save className="mr-2 h-4 w-4" />
              {isEdit ? 'Update Credit Note' : 'Create Credit Note'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
