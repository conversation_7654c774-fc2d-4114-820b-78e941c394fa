"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { inventoryService } from "../api/inventory-service";
import {
  CreateInventoryTransactionRequest,
  CreateInventoryAdjustmentRequest,
  UpdateInventoryAdjustmentStatusRequest,
  InventoryReportFilters,
  StockAdjustmentType,
  StockItem,
} from "@/types/inventory";
import {
  InventoryReportSummary,
  StockValuationReport,
  StockValuationReportFilters
} from "../types";
import { toast } from "sonner";

// Hook for HQ inventory (for admin users)
export function useHQInventory(
  params?: Record<string, any>,
  isEnabled?: boolean
) {
  return useQuery({
    queryKey: ["hqInventory", params],
    queryFn: async () => {
      try {
        // Extract pagination parameters
        const page = params?.page || 1;
        const limit = params?.limit || 10;

        const result = await inventoryService.getHQInventory(params);

        // Use pagination data directly from the API response
        if (!result.pagination) {
          result.pagination = {
            total: result.data.length,
            page: parseInt(page as string, 10),
            limit: parseInt(limit as string, 10),
            pages: Math.ceil(result.data.length / parseInt(limit as string, 10)) || 1
          };
        }

        // Ensure totalPages is set for backward compatibility
        result.pagination.totalPages = result.pagination.pages;

        console.log("HQ Inventory API response:", result);
        console.log("Pagination data:", result.pagination);
        return result;
      } catch (error) {
        console.error("Error fetching HQ inventory:", error);
        throw error;
      }
    },
    enabled: isEnabled !== undefined ? isEnabled : true,
    placeholderData: "keep" as any,
    retry: 1,
    refetchOnWindowFocus: false,
    select: (data) => {
      if (!data) {
        return {
          data: [],
          pagination: {
            total: 0,
            page: 1,
            limit: 0,
            pages: 1,
          },
        };
      }
      return data;
    },
  });
}

// Hook for branch-specific inventory
export function useBranchInventory(
  branchId: number,
  params?: Record<string, any>,
  isEnabled?: boolean
) {
  console.log("useBranchInventory called with:", {
    branchId,
    params,
    isEnabled,
  });

  return useQuery({
    queryKey: ["branchInventory", branchId, params],
    // Log when the query is triggered
    onFetch: () => {
      console.log("Branch inventory query triggered:", {
        branchId,
        params
      });
    },
    queryFn: async () => {
      try {
        console.log("Fetching inventory for branch:", branchId);
        console.log("With params:", params);

        // This hook is now only for specific branches, not HQ
        if (branchId === -1) {
          console.log("This is a request for ALL branches inventory");
        } else {
          console.log(`This is a request for branch ${branchId} inventory`);
        }

        // Extract pagination parameters
        const page = params?.page || 1;
        const limit = params?.limit || 10;
        console.log(`Pagination: page=${page}, limit=${limit}`);

        const result = await inventoryService.getBranchInventory(
          branchId,
          params
        );

        // Use pagination data directly from the API response
        if (!result.pagination) {
          console.log("Adding pagination data to result");
          result.pagination = {
            total: result.data.length,
            page: parseInt(page as string, 10),
            limit: parseInt(limit as string, 10),
            pages: Math.ceil(result.data.length / parseInt(limit as string, 10)) || 1
          };
        }

        // Ensure totalPages is set for backward compatibility
        result.pagination.totalPages = result.pagination.pages;

        console.log("Branch Inventory API response:", result);
        console.log("Pagination data:", result.pagination);
        return result;
      } catch (error) {
        console.error("Error fetching branch inventory:", error);
        throw error;
      }
    },
    enabled: (() => {
      // Always enable the query if isEnabled is explicitly set
      if (isEnabled !== undefined) return isEnabled;

      // Otherwise, enable for any valid branchId (including -1 for All Branches)
      return branchId !== undefined;
    })(),
    placeholderData: "keep" as any,
    retry: 1,
    refetchOnWindowFocus: false,
    select: (data) => {
      if (!data) {
        return {
          data: [],
          pagination: {
            total: 0,
            page: 1,
            limit: 0,
            pages: 1,
          },
        };
      }
      return data;
    },
  });
}

export function useProductInventory(
  productId: number,
  params?: Record<string, any>
) {
  return useQuery({
    queryKey: ["productInventory", productId, params],
    queryFn: async () => {
      if (!productId) {
        return {
          data: [],
          pagination: {
            total: 0,
            page: 1,
            limit: 0,
            pages: 1,
          },
        };
      }

      try {
        const result = await inventoryService.getProductInventory(
          productId,
          params
        );
        return result;
      } catch (error) {
        console.error("Error fetching product inventory:", error);
        throw error;
      }
    },
    enabled: !!productId,
    // In v5, this is the equivalent of keepPreviousData
    placeholderData: "keep" as any,
    retry: 1,
    refetchOnWindowFocus: false,
    // Provide a default value for data to prevent undefined errors
    select: (data) => {
      if (!data) {
        return {
          data: [],
          pagination: {
            total: 0,
            page: 1,
            limit: 0,
            pages: 1,
          },
        };
      }
      return data;
    },
  });
}

export function useInventoryTransactions(params?: Record<string, any>) {
  return useQuery({
    queryKey: ["inventoryTransactions", params],
    queryFn: async () => {
      try {
        const result = await inventoryService.getInventoryTransactions(params);
        return result;
      } catch (error) {
        console.error("Error fetching inventory transactions:", error);
        throw error;
      }
    },
    // In v5, this is the equivalent of keepPreviousData
    placeholderData: "keep" as any,
    retry: 1,
    refetchOnWindowFocus: false,
    // Provide a default value for data to prevent undefined errors
    select: (data) => {
      if (!data) {
        return {
          data: [],
          pagination: {
            total: 0,
            page: 1,
            limit: 0,
            pages: 1,
          },
        };
      }
      return data;
    },
  });
}

export function useInventoryTransaction(id: number) {
  return useQuery({
    queryKey: ["inventoryTransaction", id],
    queryFn: () => inventoryService.getInventoryTransactionById(id),
    enabled: !!id,
  });
}

export function useCreateInventoryTransaction() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (transaction: CreateInventoryTransactionRequest) =>
      inventoryService.createInventoryTransaction(transaction),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inventoryTransactions"] });
      queryClient.invalidateQueries({ queryKey: ["branchInventory"] });
      queryClient.invalidateQueries({ queryKey: ["productInventory"] });
      toast.success("Transaction created", {
        description: "The inventory transaction has been created successfully.",
      });
    },
    onError: (error: any) => {
      toast.error("Error creating transaction", {
        description:
          error.message || "An error occurred while creating the transaction.",
      });
    },
  });
}

export function useInventoryAdjustments(params?: Record<string, any>) {
  return useQuery({
    queryKey: ["inventoryAdjustments", params],
    queryFn: () => inventoryService.getInventoryAdjustments(params),
    // In v5, this is the equivalent of keepPreviousData
    placeholderData: "keep" as any,
    retry: 1,
    refetchOnWindowFocus: false,
  });
}

export function useInventoryAdjustment(id: number) {
  return useQuery({
    queryKey: ["inventoryAdjustment", id],
    queryFn: () => inventoryService.getInventoryAdjustmentById(id),
    enabled: !!id,
  });
}

export function useCreateInventoryAdjustment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (adjustment: CreateInventoryAdjustmentRequest) =>
      inventoryService.createInventoryAdjustment(adjustment),
    onSuccess: (data, variables) => {
      // Invalidate all inventory-related queries to refresh data
      queryClient.invalidateQueries({ queryKey: ["inventoryAdjustments"] });
      queryClient.invalidateQueries({ queryKey: ["inventoryTransactions"] });
      queryClient.invalidateQueries({ queryKey: ["branchInventory"] });
      queryClient.invalidateQueries({ queryKey: ["hqInventory"] });
      queryClient.invalidateQueries({ queryKey: ["productInventory"] });

      // Specifically invalidate stock items for the branch that was adjusted
      if (variables.branch_id) {
        queryClient.invalidateQueries({ queryKey: ["stockItems", variables.branch_id] });
      }

      toast.success("Adjustment created", {
        description: "The inventory adjustment has been created successfully and inventory has been updated.",
      });
    },
    onError: (error: any) => {
      toast.error("Error creating adjustment", {
        description:
          error.message || "An error occurred while creating the adjustment.",
      });
    },
  });
}

export function useUpdateInventoryAdjustmentStatus(id: number) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (status: UpdateInventoryAdjustmentStatusRequest) =>
      inventoryService.updateInventoryAdjustmentStatus(id, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["inventoryAdjustments"] });
      queryClient.invalidateQueries({ queryKey: ["inventoryAdjustment", id] });
      queryClient.invalidateQueries({ queryKey: ["branchInventory"] });
      queryClient.invalidateQueries({ queryKey: ["productInventory"] });
      toast.success("Adjustment status updated", {
        description:
          "The inventory adjustment status has been updated successfully.",
      });
    },
    onError: (error: any) => {
      toast.error("Error updating adjustment status", {
        description:
          error.message ||
          "An error occurred while updating the adjustment status.",
      });
    },
  });
}

export function useInventoryReportSummary(filters?: InventoryReportFilters) {
  return useQuery<InventoryReportSummary>({
    queryKey: ["inventoryReportSummary", filters],
    queryFn: async () => {
      try {
        console.log("Fetching inventory report summary with filters:", filters);
        const result = await inventoryService.getInventoryReportSummary(
          filters
        );
        console.log(
          "Inventory report summary API response:",
          JSON.stringify(result, null, 2)
        );
        return result as InventoryReportSummary;
      } catch (error) {
        console.error("Error fetching inventory report summary:", error);
        // Return default data structure on error
        return {
          total_products: 0,
          total_value: 0,
          low_stock_count: 0,
          out_of_stock_count: 0,
          top_products: [],
          by_category: [],
          by_branch: [],
        };
      }
    },
    retry: 1,
    refetchOnWindowFocus: false,
  });
}

export function useStockValuationReport(filters?: StockValuationReportFilters) {
  return useQuery<StockValuationReport>({
    queryKey: ["stockValuationReport", filters],
    queryFn: async () => {
      try {
        console.log("Fetching stock valuation report with filters:", filters);
        const result = await inventoryService.getStockValuationReport(
          filters
        );
        console.log(
          "Stock valuation report API response:",
          JSON.stringify(result, null, 2)
        );
        return result as StockValuationReport;
      } catch (error) {
        console.error("Error fetching stock valuation report:", error);
        // Return default data structure on error
        return {
          summary: {
            total_items: 0,
            total_quantity: 0,
            total_value: 0,
            valuation_methods: {
              FIFO: { count: 0, value: 0 },
              LIFO: { count: 0, value: 0 },
              WEIGHTED_AVERAGE: { count: 0, value: 0 }
            }
          },
          items: [],
          by_category: [],
          by_branch: [],
          by_valuation_method: []
        };
      }
    },
    retry: 1,
    refetchOnWindowFocus: false,
  });
}

export function useStockAdjustmentTypes() {
  return useQuery<StockAdjustmentType[]>({
    queryKey: ["stockAdjustmentTypes"],
    queryFn: async () => {
      try {
        const result = await inventoryService.getStockAdjustmentTypes();
        return result;
      } catch (error) {
        console.error("Error fetching stock adjustment types:", error);
        throw error;
      }
    },
    retry: 1,
    refetchOnWindowFocus: false,
  });
}

export function useStockItemsByBranch(branch_id: number | null) {
  return useQuery<StockItem[]>({
    queryKey: ["stockItems", branch_id],
    queryFn: async () => {
      if (!branch_id) return [];
      try {
        const result = await inventoryService.getStockItemsByBranch(branch_id);
        return Array.isArray(result) ? result : result.data || [];
      } catch (error) {
        console.error("Error fetching stock items:", error);
        return [];
      }
    },
    enabled: !!branch_id,
    retry: 1,
    refetchOnWindowFocus: false,
  });
}
