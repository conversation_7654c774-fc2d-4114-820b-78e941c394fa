const express = require("express");
const router = express.Router();
const stockLevelsExportController = require("../../controllers/report/stock-levels-export.controller");
const { authenticate, rbac } = require("../../middleware/auth.middleware");

/**
 * @swagger
 * tags:
 *   name: Stock Levels Export
 *   description: Enhanced Excel export functionality for stock levels with comprehensive data
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     StockLevelsExportParams:
 *       type: object
 *       properties:
 *         branch_id:
 *           type: integer
 *           description: Filter by specific branch
 *         region_id:
 *           type: integer
 *           description: Filter by specific region
 *         category_id:
 *           type: integer
 *           description: Filter by specific category
 *         include_zero_stock:
 *           type: boolean
 *           default: true
 *           description: Include products with zero stock
 *         include_inactive:
 *           type: boolean
 *           default: false
 *           description: Include inactive products
 *         search:
 *           type: string
 *           description: Search term for product name or SKU
 *         sort_by:
 *           type: string
 *           enum: [name, quantity, value, category]
 *           default: name
 *           description: Sort field
 *         sort_direction:
 *           type: string
 *           enum: [asc, desc]
 *           default: asc
 *           description: Sort direction
 *         include_summary:
 *           type: boolean
 *           default: true
 *           description: Include summary sheet
 *         include_alerts:
 *           type: boolean
 *           default: true
 *           description: Include stock alerts sheet
 *         include_categories:
 *           type: boolean
 *           default: true
 *           description: Include category breakdown sheet
 *         include_branches:
 *           type: boolean
 *           default: true
 *           description: Include branch breakdown sheet
 */

/**
 * @swagger
 * /api/v1/reports/stock-levels-export/all:
 *   get:
 *     summary: Export all stock levels to Excel with comprehensive data
 *     description: |
 *       Export complete stock levels data to Excel with multiple sheets including:
 *       - Summary metrics and applied filters
 *       - Detailed products list with all stock information
 *       - Category breakdown with totals
 *       - Branch breakdown with totals
 *       - Stock alerts (low stock and out of stock items)
 *       
 *       This endpoint bypasses pagination and exports ALL matching data.
 *     tags: [Stock Levels Export]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         description: Filter by specific branch (omit for all branches)
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         description: Filter by specific region (omit for all regions)
 *       - in: query
 *         name: category_id
 *         schema:
 *           type: integer
 *         description: Filter by specific category (omit for all categories)
 *       - in: query
 *         name: include_zero_stock
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include products with zero stock
 *       - in: query
 *         name: include_inactive
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include inactive products
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for product name or SKU
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *           enum: [name, quantity, value, category]
 *           default: name
 *         description: Sort field
 *       - in: query
 *         name: sort_direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: asc
 *         description: Sort direction
 *       - in: query
 *         name: include_summary
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include summary sheet with metrics and filters
 *       - in: query
 *         name: include_alerts
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include stock alerts sheet
 *       - in: query
 *         name: include_categories
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include category breakdown sheet
 *       - in: query
 *         name: include_branches
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include branch breakdown sheet
 *     responses:
 *       200:
 *         description: Excel file download
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *         headers:
 *           Content-Disposition:
 *             description: Attachment filename
 *             schema:
 *               type: string
 *               example: 'attachment; filename=stock-levels-2024-01-15.xlsx'
 *       400:
 *         description: Bad request - Invalid parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Invalid filter parameters
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *       403:
 *         description: Forbidden - Insufficient permissions
 *       500:
 *         description: Server error during export
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Failed to export stock levels
 */
router.get(
  "/all",
  authenticate,
  rbac.checkPermission("stock_reports", "read"),
  stockLevelsExportController.exportAllStockLevels
);

/**
 * @swagger
 * /api/v1/reports/stock-levels-export/custom:
 *   get:
 *     summary: Export stock levels with custom options
 *     description: |
 *       Export stock levels data with customizable format and column selection.
 *       Supports both detailed and summary export formats.
 *     tags: [Stock Levels Export]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         description: Filter by specific branch
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         description: Filter by specific region
 *       - in: query
 *         name: category_id
 *         schema:
 *           type: integer
 *         description: Filter by specific category
 *       - in: query
 *         name: include_zero_stock
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include products with zero stock
 *       - in: query
 *         name: include_inactive
 *         schema:
 *           type: boolean
 *           default: false
 *         description: Include inactive products
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for product name or SKU
 *       - in: query
 *         name: sort_by
 *         schema:
 *           type: string
 *           enum: [name, quantity, value, category]
 *           default: name
 *         description: Sort field
 *       - in: query
 *         name: sort_direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: asc
 *         description: Sort direction
 *       - in: query
 *         name: columns
 *         schema:
 *           type: string
 *           default: all
 *         description: Comma-separated list of columns to include or "all"
 *       - in: query
 *         name: format_type
 *         schema:
 *           type: string
 *           enum: [detailed, summary]
 *           default: detailed
 *         description: Export format type
 *     responses:
 *       200:
 *         description: Excel file download
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Forbidden
 *       500:
 *         description: Server error
 */
router.get(
  "/custom",
  authenticate,
  rbac.checkPermission("stock_reports", "read"),
  stockLevelsExportController.exportCustomStockLevels
);

module.exports = router;
