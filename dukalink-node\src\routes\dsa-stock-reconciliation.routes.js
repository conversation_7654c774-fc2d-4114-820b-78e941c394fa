const express = require('express');
const router = express.Router();
const { authenticate, rbac } = require('../middleware/auth.middleware');
const dsaStockReconciliationController = require('../controllers/dsa-stock-reconciliation.controller');
const { Op } = require('sequelize');

/**
 * @swagger
 * components:
 *   schemas:
 *     DsaStockReconciliation:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The DSA stock reconciliation ID
 *         user_id:
 *           type: integer
 *           description: The DSA user ID
 *         total_assigned:
 *           type: integer
 *           description: The total quantity assigned
 *         total_sold:
 *           type: integer
 *           description: The total quantity sold
 *         total_returned:
 *           type: integer
 *           description: The total quantity returned
 *         cash_received:
 *           type: number
 *           format: float
 *           description: The cash amount received from the agent
 *         paybill_amount:
 *           type: number
 *           format: float
 *           description: The amount paid through paybill
 *         notes:
 *           type: string
 *           nullable: true
 *           description: Additional notes about the reconciliation
 *         reconciled_at:
 *           type: string
 *           format: date-time
 *           description: The date and time of reconciliation
 *         created_by:
 *           type: integer
 *           nullable: true
 *           description: The ID of the user who created the record
 *         last_updated_by:
 *           type: integer
 *           nullable: true
 *           description: The ID of the user who last updated the record
 *       example:
 *         id: 1
 *         user_id: 1
 *         total_assigned: 10
 *         total_sold: 5
 *         total_returned: 5
 *         cash_received: 5000
 *         paybill_amount: 2500
 *         notes: End of month reconciliation
 *         reconciled_at: 2023-01-31T00:00:00.000Z
 *         created_by: 2
 *         last_updated_by: 2
 */

/**
 * @swagger
 * /dsa-stock-reconciliations:
 *   get:
 *     summary: Get all DSA stock reconciliations
 *     tags: [DSA Stock Reconciliations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: customer_id
 *         schema:
 *           type: integer
 *         description: Filter by DSA customer ID
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by start date (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by end date (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: List of DSA stock reconciliations
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/DsaStockReconciliation'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authenticate, rbac.checkPermission('unknown', 'read'), dsaStockReconciliationController.getAllDsaStockReconciliations);

/**
 * @swagger
 * /dsa-stock-reconciliations/{id}:
 *   get:
 *     summary: Get DSA stock reconciliation by ID
 *     tags: [DSA Stock Reconciliations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: DSA stock reconciliation ID
 *     responses:
 *       200:
 *         description: DSA stock reconciliation details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DsaStockReconciliation'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA stock reconciliation not found
 *       500:
 *         description: Server error
 */
/**
 * @swagger
 * /dsa-stock-reconciliations/customer/{customerId}:
 *   get:
 *     summary: Get all reconciliations for a specific DSA customer
 *     tags: [DSA Stock Reconciliations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: integer
 *         description: DSA customer ID
 *     responses:
 *       200:
 *         description: List of DSA stock reconciliations for the specified customer
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/DsaStockReconciliation'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA customer not found
 *       500:
 *         description: Server error
 */
router.get('/customer/:customerId', authenticate, rbac.checkPermission('customer', 'read'), dsaStockReconciliationController.getReconciliationsByCustomerId);

router.get('/:id', authenticate, rbac.checkPermission(':id', 'read'), dsaStockReconciliationController.getDsaStockReconciliationById);

/**
 * @swagger
 * /dsa-stock-reconciliations:
 *   post:
 *     summary: Create a new DSA stock reconciliation
 *     tags: [DSA Stock Reconciliations]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - customer_id
 *               - total_assigned
 *               - total_sold
 *               - total_returned
 *             properties:
 *               customer_id:
 *                 type: integer
 *               total_assigned:
 *                 type: integer
 *               total_sold:
 *                 type: integer
 *               total_returned:
 *                 type: integer
 *               cash_received:
 *                 type: number
 *                 format: float
 *               paybill_amount:
 *                 type: number
 *                 format: float
 *               notes:
 *                 type: string
 *               products:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     product_id:
 *                       type: integer
 *                     quantity_sold:
 *                       type: integer
 *                     quantity_returned:
 *                       type: integer
 *                     unit_price:
 *                       type: number
 *                       format: float
 *     responses:
 *       201:
 *         description: DSA stock reconciliation created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DsaStockReconciliation'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', authenticate, rbac.checkPermission('dsa_reconciliations', 'create'), dsaStockReconciliationController.createDsaStockReconciliation);

/**
 * @swagger
 * /dsa-stock-reconciliations/{id}:
 *   put:
 *     summary: Update a DSA stock reconciliation
 *     tags: [DSA Stock Reconciliations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: DSA stock reconciliation ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               total_assigned:
 *                 type: integer
 *               total_sold:
 *                 type: integer
 *               total_returned:
 *                 type: integer
 *               cash_received:
 *                 type: number
 *                 format: float
 *               paybill_amount:
 *                 type: number
 *                 format: float
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: DSA stock reconciliation updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DsaStockReconciliation'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA stock reconciliation not found
 *       500:
 *         description: Server error
 */
router.put('/:id', authenticate, rbac.checkPermission(':id', 'read'), dsaStockReconciliationController.updateDsaStockReconciliation);

/**
 * @swagger
 * /dsa-stock-reconciliations/{id}:
 *   delete:
 *     summary: Delete a DSA stock reconciliation
 *     tags: [DSA Stock Reconciliations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: DSA stock reconciliation ID
 *     responses:
 *       200:
 *         description: DSA stock reconciliation deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA stock reconciliation not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', authenticate, rbac.checkPermission(':id', 'read'), dsaStockReconciliationController.deleteDsaStockReconciliation);

module.exports = router;
