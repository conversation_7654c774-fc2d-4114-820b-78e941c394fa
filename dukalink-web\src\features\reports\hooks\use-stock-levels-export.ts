import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import stockLevelsExportService, {
  StockLevelsExportParams,
  CustomExportParams,
} from "../api/stock-levels-export-service";

/**
 * Enhanced hook for comprehensive stock levels export
 * Exports all data with multiple sheets
 */
export const useStockLevelsExportAll = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: StockLevelsExportParams) => {
      // Validate parameters
      const validation = stockLevelsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await stockLevelsExportService.exportAllStockLevels(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = stockLevelsExportService.generateFilename(params, 'comprehensive');
        stockLevelsExportService.downloadBlob(blob, filename);
        
        toast.success("Stock levels exported successfully!", {
          description: `Comprehensive report with all data sheets downloaded as ${filename}`,
          duration: 5000,
        });

        // Invalidate related queries to refresh any cached data
        queryClient.invalidateQueries({ queryKey: ["stock-levels-report"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Export error:', error);
      
      let errorMessage = "Failed to export stock levels";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export stock reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try filtering the data or use summary export.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for custom stock levels export
 * Allows format and column selection
 */
export const useStockLevelsExportCustom = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: CustomExportParams) => {
      // Validate parameters
      const validation = stockLevelsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await stockLevelsExportService.exportCustomStockLevels(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const formatType = params.format_type || 'detailed';
        const filename = stockLevelsExportService.generateFilename(params, formatType);
        stockLevelsExportService.downloadBlob(blob, filename);
        
        toast.success("Custom stock levels export completed!", {
          description: `${formatType.charAt(0).toUpperCase() + formatType.slice(1)} report downloaded as ${filename}`,
          duration: 5000,
        });

        queryClient.invalidateQueries({ queryKey: ["stock-levels-report"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Custom export error:', error);
      
      let errorMessage = "Failed to export custom stock levels";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export stock reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try using summary format or filtering the data.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Custom Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for quick summary export
 * Lightweight export with essential data only
 */
export const useStockLevelsExportSummary = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: StockLevelsExportParams) => {
      const validation = stockLevelsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await stockLevelsExportService.exportSummaryStockLevels(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = stockLevelsExportService.generateFilename(params, 'summary');
        stockLevelsExportService.downloadBlob(blob, filename);
        
        toast.success("Summary export completed!", {
          description: `Quick summary report downloaded as ${filename}`,
          duration: 4000,
        });

        queryClient.invalidateQueries({ queryKey: ["stock-levels-report"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Summary export error:', error);
      
      let errorMessage = "Failed to export summary stock levels";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export stock reports";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Summary Export Failed", {
        description: errorMessage,
        duration: 6000,
      });
    },
  });
};

/**
 * Hook to get export recommendations based on data size
 */
export const useExportRecommendation = (estimatedRecords: number) => {
  return stockLevelsExportService.getExportRecommendation(estimatedRecords);
};

/**
 * Hook to get export format options for UI
 */
export const useExportFormatOptions = () => {
  return stockLevelsExportService.getExportFormatOptions();
};

/**
 * Hook to get column options for custom export
 */
export const useExportColumnOptions = () => {
  return stockLevelsExportService.getColumnOptions();
};

/**
 * Combined hook that provides all export functionality
 * Convenient single hook for components that need multiple export options
 */
export const useStockLevelsExportSuite = () => {
  const exportAll = useStockLevelsExportAll();
  const exportCustom = useStockLevelsExportCustom();
  const exportSummary = useStockLevelsExportSummary();

  const isAnyExporting = exportAll.isPending || exportCustom.isPending || exportSummary.isPending;

  return {
    // Individual export methods
    exportAll: exportAll.mutateAsync,
    exportCustom: exportCustom.mutateAsync,
    exportSummary: exportSummary.mutateAsync,
    
    // Loading states
    isExportingAll: exportAll.isPending,
    isExportingCustom: exportCustom.isPending,
    isExportingSummary: exportSummary.isPending,
    isAnyExporting,
    
    // Error states
    exportAllError: exportAll.error,
    exportCustomError: exportCustom.error,
    exportSummaryError: exportSummary.error,
    
    // Utility functions
    getRecommendation: stockLevelsExportService.getExportRecommendation,
    getFormatOptions: stockLevelsExportService.getExportFormatOptions,
    getColumnOptions: stockLevelsExportService.getColumnOptions,
    validateParams: stockLevelsExportService.validateExportParams,
    generateFilename: stockLevelsExportService.generateFilename,
    
    // Reset functions
    resetAll: () => {
      exportAll.reset();
      exportCustom.reset();
      exportSummary.reset();
    },
  };
};
