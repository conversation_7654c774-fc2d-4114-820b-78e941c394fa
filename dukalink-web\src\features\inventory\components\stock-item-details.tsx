"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { BranchInventory } from "@/types/inventory";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatCurrency } from "@/lib/utils";
import { useUpdateStockItem } from "../hooks/use-stock-items";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useProduct } from "@/features/products/hooks/use-products";
import {
  Package,
  Truck,
  DollarSign,
  AlertTriangle,
  Calendar,
  Edit,
  Trash,
  ExternalLink
} from "lucide-react";
import Link from "next/link";

interface StockItemDetailsProps {
  stockItem: BranchInventory;
}

export function StockItemDetails({ stockItem }: StockItemDetailsProps) {
  const router = useRouter();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [quantity, setQuantity] = useState(stockItem.quantity?.toString() || "0");
  const [buyingPrice, setBuyingPrice] = useState(
    stockItem.default_buying_price?.toString() ||
    stockItem.buying_price?.toString() ||
    "0"
  );
  const [sellingPrice, setSellingPrice] = useState(
    stockItem.default_selling_price?.toString() ||
    stockItem.selling_price?.toString() ||
    "0"
  );

  const updateStockItem = useUpdateStockItem(stockItem.id);
  const { data: product } = useProduct(stockItem.product_id);

  const handleUpdateStockItem = async () => {
    try {
      await updateStockItem.mutateAsync({
        quantity: parseInt(quantity),
        buying_price: parseFloat(buyingPrice),
        selling_price: parseFloat(sellingPrice),
      });
      setIsEditDialogOpen(false);
    } catch (error) {
      toast.error("Failed to update stock item");
    }
  };

  const isLowStock =
    stockItem.reorder_level &&
    stockItem.quantity <= stockItem.reorder_level;

  const getStockStatusBadge = () => {
    if (stockItem.quantity <= 0) {
      return (
        <Badge variant="destructive">Out of Stock</Badge>
      );
    } else if (isLowStock) {
      return (
        <Badge variant="warning" className="bg-amber-500">Low Stock</Badge>
      );
    } else {
      return (
        <Badge variant="success" className="bg-green-500">In Stock</Badge>
      );
    }
  };

  // Format dates
  const formatDate = (dateString?: string) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString();
  };

  // Calculate values
  const totalValue =
    (parseFloat(stockItem.default_selling_price?.toString() || "0") *
    (stockItem.quantity || 0)).toFixed(2);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Quantity
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stockItem.quantity || 0}
            </div>
            <div className="flex items-center mt-1">
              {getStockStatusBadge()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Value
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(parseFloat(totalValue))}
            </div>
            <p className="text-xs text-muted-foreground">
              Total retail value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Reorder Level
            </CardTitle>
            <AlertTriangle className={`h-4 w-4 ${isLowStock ? "text-amber-500" : "text-muted-foreground"}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stockItem.reorder_level || "N/A"}
            </div>
            <p className="text-xs text-muted-foreground">
              Minimum stock level
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Stock Item Details</CardTitle>
            <CardDescription>
              Basic information about this stock item
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Product Name
                </p>
                <p className="font-medium">{product?.name || stockItem.Product?.name || "Unknown"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  SKU
                </p>
                <p>{product?.sku || stockItem.Product?.sku || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Branch
                </p>
                <p>{stockItem.Branch?.name || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Last Updated
                </p>
                <p>{formatDate(stockItem.updated_at)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Order Number
                </p>
                <p>{stockItem.batch_number || "N/A"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Expiry Date
                </p>
                <p>{formatDate(stockItem.expiry_date)}</p>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => router.push(`/products/${stockItem.product_id}`)}
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              View Product
            </Button>
            <Button onClick={() => setIsEditDialogOpen(true)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Stock Item
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Pricing Information</CardTitle>
            <CardDescription>
              Pricing details for this stock item
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Retail Price
                </p>
                <p className="font-medium">
                  {formatCurrency(parseFloat(stockItem.default_selling_price?.toString() || "0"))}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Wholesale Price
                </p>
                <p>
                  {formatCurrency(parseFloat(stockItem.default_wholesale_price?.toString() || "0"))}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Buying Price
                </p>
                <p>
                  {formatCurrency(parseFloat(stockItem.default_buying_price?.toString() || "0"))}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Profit Margin
                </p>
                <p>
                  {(() => {
                    const buyingPrice = parseFloat(stockItem.default_buying_price?.toString() || "0");
                    const sellingPrice = parseFloat(stockItem.default_selling_price?.toString() || "0");
                    if (buyingPrice === 0) return "N/A";
                    const margin = ((sellingPrice - buyingPrice) / sellingPrice) * 100;
                    return `${margin.toFixed(2)}%`;
                  })()}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  VAT Rate
                </p>
                <p>
                  {stockItem.buying_vat_rate ? `${stockItem.buying_vat_rate}%` : "N/A"}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  VAT Amount
                </p>
                <p>
                  {stockItem.buying_vat_amount
                    ? formatCurrency(parseFloat(stockItem.buying_vat_amount.toString()))
                    : "N/A"}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Stock Item</DialogTitle>
            <DialogDescription>
              Update the details of this stock item
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="quantity">Quantity</Label>
              <Input
                id="quantity"
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="buyingPrice">Buying Price</Label>
              <Input
                id="buyingPrice"
                type="number"
                step="0.01"
                value={buyingPrice}
                onChange={(e) => setBuyingPrice(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="sellingPrice">Selling Price</Label>
              <Input
                id="sellingPrice"
                type="number"
                step="0.01"
                value={sellingPrice}
                onChange={(e) => setSellingPrice(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateStockItem} disabled={updateStockItem.isPending}>
              {updateStockItem.isPending ? "Updating..." : "Update Stock Item"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
