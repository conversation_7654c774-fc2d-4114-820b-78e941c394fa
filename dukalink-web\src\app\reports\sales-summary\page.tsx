"use client";

import { MainLayout } from "@/components/layouts/main-layout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ReportChart } from "@/features/reports/components/report-chart";
import { ReportDataTable } from "@/features/reports/components/report-data-table";
import { ReportFilters } from "@/features/reports/components/report-filters";
import { ReportStatCard } from "@/features/reports/components/report-stat-card";
import { SalesSummaryExportDialog } from "@/features/reports/components/sales-summary-export-dialog";
import { ExportPerformanceNotice } from "@/features/reports/components/export-performance-notice";
import { useSalesSummaryApi } from "@/features/reports/hooks/use-reports-api";
import { formatCurrency } from "@/lib/utils";
import { SalesSummaryParams } from "@/types/reports-api";
import { format, subDays } from "date-fns";
import {
  Download,
  TrendingUp,
  ShoppingCart,
  Package,
  DollarSign,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { ColumnDef } from "@tanstack/react-table";

export default function SalesSummaryPage() {
  // Initialize with default filters (last 7 days)
  const today = new Date();
  const sevenDaysAgo = subDays(today, 7);

  const [filters, setFilters] = useState<SalesSummaryParams>({
    start_date: format(sevenDaysAgo, "yyyy-MM-dd"),
    end_date: format(today, "yyyy-MM-dd"),
  });

  const { data, isLoading, error } = useSalesSummaryApi(filters);

  const handleFilterChange = (newFilters: any) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  // Column definitions for sales details
  const salesColumns: ColumnDef<any>[] = [
    {
      accessorKey: "id",
      header: "ID",
    },
    {
      accessorKey: "receipt_number",
      header: "Receipt",
      cell: ({ row }) => (
        <span className="font-mono text-sm">
          {row.getValue("receipt_number")}
        </span>
      ),
    },
    {
      accessorKey: "created_at",
      header: "Date",
      cell: ({ row }) =>
        format(new Date(row.getValue("created_at")), "MMM dd, yyyy HH:mm"),
    },
    {
      accessorKey: "branch_name",
      header: "Branch",
    },
    {
      accessorKey: "region_name",
      header: "Region",
      cell: ({ row }) => row.getValue("region_name") || "Unknown",
    },
    {
      accessorKey: "user_name",
      header: "User",
    },
    {
      accessorKey: "total_amount",
      header: "Total Amount",
      cell: ({ row }) => formatCurrency(row.getValue("total_amount")),
    },
    {
      accessorKey: "discount_amount",
      header: "Discount",
      cell: ({ row }) => formatCurrency(row.getValue("discount_amount")),
    },
    {
      accessorKey: "net_amount",
      header: "Net Amount",
      cell: ({ row }) => (
        <span className="font-medium">
          {formatCurrency(row.getValue("net_amount"))}
        </span>
      ),
    },
    {
      accessorKey: "payment_method",
      header: "Payment Method",
      cell: ({ row }) => (
        <Badge variant="outline">{row.getValue("payment_method")}</Badge>
      ),
    },
  ];

  // Column definitions for by product breakdown
  const productColumns: ColumnDef<any>[] = [
    {
      accessorKey: "product_name",
      header: "Product",
    },
    {
      accessorKey: "product_sku",
      header: "SKU",
      cell: ({ row }) => (
        <span className="font-mono text-sm">{row.getValue("product_sku")}</span>
      ),
    },
    {
      accessorKey: "category_name",
      header: "Category",
    },
    {
      accessorKey: "quantity",
      header: "Quantity",
      cell: ({ row }) => (
        <span className="font-medium">{row.getValue("quantity")}</span>
      ),
    },
    {
      accessorKey: "total_amount",
      header: "Total Amount",
      cell: ({ row }) => formatCurrency(row.getValue("total_amount")),
    },
    {
      accessorKey: "discount_amount",
      header: "Discount",
      cell: ({ row }) => formatCurrency(row.getValue("discount_amount")),
    },
    {
      accessorKey: "net_amount",
      header: "Net Amount",
      cell: ({ row }) => (
        <span className="font-medium">
          {formatCurrency(row.getValue("net_amount"))}
        </span>
      ),
    },
  ];

  // Column definitions for by branch breakdown
  const branchColumns: ColumnDef<any>[] = [
    {
      accessorKey: "branch_name",
      header: "Branch",
    },
    {
      accessorKey: "branch_location",
      header: "Location",
    },
    {
      accessorKey: "region_name",
      header: "Region",
      cell: ({ row }) => row.getValue("region_name") || "Unknown",
    },
    {
      accessorKey: "total_sales",
      header: "Total Sales",
      cell: ({ row }) => (
        <span className="font-medium">{row.getValue("total_sales")}</span>
      ),
    },
    {
      accessorKey: "total_amount",
      header: "Total Amount",
      cell: ({ row }) => formatCurrency(row.getValue("total_amount")),
    },
    {
      accessorKey: "discount_amount",
      header: "Discount",
      cell: ({ row }) => formatCurrency(row.getValue("discount_amount")),
    },
    {
      accessorKey: "net_amount",
      header: "Net Amount",
      cell: ({ row }) => (
        <span className="font-medium">
          {formatCurrency(row.getValue("net_amount"))}
        </span>
      ),
    },
  ];

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              Sales Summary Report
            </h1>
            <p className="text-muted-foreground">
              Comprehensive sales analysis with product and branch breakdowns
            </p>
          </div>
          <div className="flex gap-2">
            <SalesSummaryExportDialog
              filters={filters}
              totalRecords={data?.sales?.length || 0}
              trigger={
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export Options
                </Button>
              }
            />
            <SalesSummaryExportDialog
              filters={filters}
              totalRecords={data?.sales?.length || 0}
              trigger={
                <Button>
                  <Zap className="mr-2 h-4 w-4" />
                  Quick Export
                </Button>
              }
            />
          </div>
        </div>

        {/* Export Performance Notice */}
        <ExportPerformanceNotice
          totalRecords={data?.sales?.length || 0}
          hasDateFilter={!!(filters.start_date && filters.end_date)}
          className="mb-6"
        />

        <ReportFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          showTimeFilter={true}
          showBranchFilter={true}
          showRegionFilter={true}
          showProductFilter={true}
          showCategoryFilter={true}
          showLocationFilter={true}
          showUserFilter={false}
          showPaymentMethodFilter={false}
          showSessionFilter={false}
          showStatusFilter={false}
          showDsaFilter={false}
        />

        {isLoading ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
            </div>
            <Skeleton className="h-[250px]" />
            <Skeleton className="h-96" />
          </div>
        ) : error ? (
          <div className="rounded-md bg-destructive/10 p-4 text-destructive">
            Error loading report data. Please try again.
          </div>
        ) : data ? (
          <>
            {/* Summary Statistics Cards */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <ReportStatCard
                title="Total Sales"
                value={data.summary.total_sales}
                icon={<ShoppingCart className="h-4 w-4" />}
                description="Number of transactions"
              />
              <ReportStatCard
                title="Total Amount"
                value={data.summary.total_amount}
                icon={<DollarSign className="h-4 w-4" />}
                isCurrency={true}
                description="Gross sales amount"
              />
              <ReportStatCard
                title="Net Amount"
                value={data.summary.net_amount}
                icon={<TrendingUp className="h-4 w-4" />}
                isCurrency={true}
                description="After discounts"
              />
              <ReportStatCard
                title="Average Sale"
                value={data.summary.average_sale_value}
                icon={<Package className="h-4 w-4" />}
                isCurrency={true}
                description="Per transaction"
              />
            </div>

            {/* Enhanced Tabs with Summary, By Product, By Branch */}
            <Tabs defaultValue="summary" className="space-y-4">
              <TabsList>
                <TabsTrigger value="summary">Summary</TabsTrigger>
                <TabsTrigger value="by-product">By Product</TabsTrigger>
                <TabsTrigger value="by-branch">By Branch</TabsTrigger>
              </TabsList>

              <TabsContent value="summary" className="space-y-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-medium">Sales Details</h3>
                    <p className="text-sm text-muted-foreground">
                      Detailed list of all sales transactions
                    </p>
                  </div>
                  <SalesSummaryExportDialog
                    filters={{
                      ...filters,
                      include_sales_details: true,
                      include_summary: false,
                      include_product_breakdown: false,
                      include_branch_breakdown: false,
                      include_payment_breakdown: false,
                    }}
                    totalRecords={data?.sales?.length || 0}
                    trigger={
                      <Button variant="outline" size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        Export Details
                      </Button>
                    }
                  />
                </div>
                <ReportDataTable
                  columns={salesColumns}
                  data={data.sales || []}
                  title=""
                  description=""
                  searchColumn="receipt_number"
                  searchPlaceholder="Search by receipt number..."
                  exportFilename="sales-summary"
                  showExport={false}
                />
              </TabsContent>

              <TabsContent value="by-product" className="space-y-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-medium">Sales by Product</h3>
                    <p className="text-sm text-muted-foreground">
                      Sales breakdown by individual products
                    </p>
                  </div>
                  <SalesSummaryExportDialog
                    filters={{
                      ...filters,
                      include_sales_details: false,
                      include_summary: false,
                      include_product_breakdown: true,
                      include_branch_breakdown: false,
                      include_payment_breakdown: false,
                    }}
                    totalRecords={data?.by_product?.length || 0}
                    trigger={
                      <Button variant="outline" size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        Export Products
                      </Button>
                    }
                  />
                </div>
                <ReportDataTable
                  columns={productColumns}
                  data={data.by_product || []}
                  title=""
                  description=""
                  searchColumn="product_name"
                  searchPlaceholder="Search by product name..."
                  exportFilename="sales-by-product"
                  showExport={false}
                />
              </TabsContent>

              <TabsContent value="by-branch" className="space-y-6">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-medium">Sales by Branch</h3>
                    <p className="text-sm text-muted-foreground">
                      Sales breakdown by branch location
                    </p>
                  </div>
                  <SalesSummaryExportDialog
                    filters={{
                      ...filters,
                      include_sales_details: false,
                      include_summary: false,
                      include_product_breakdown: false,
                      include_branch_breakdown: true,
                      include_payment_breakdown: false,
                    }}
                    totalRecords={data?.by_branch?.length || 0}
                    trigger={
                      <Button variant="outline" size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        Export Branches
                      </Button>
                    }
                  />
                </div>
                <ReportDataTable
                  columns={branchColumns}
                  data={data.by_branch || []}
                  title=""
                  description=""
                  searchColumn="branch_name"
                  searchPlaceholder="Search by branch name..."
                  exportFilename="sales-by-branch"
                  showExport={false}
                />
              </TabsContent>
            </Tabs>
          </>
        ) : null}
      </div>
    </MainLayout>
  );
}
