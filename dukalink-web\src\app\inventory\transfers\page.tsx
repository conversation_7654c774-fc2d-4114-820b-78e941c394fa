"use client";

import { MainLayout } from "@/components/layouts/main-layout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { StockTransfersTable } from "@/features/inventory/components/stock-transfers-table";
import { useStockTransfers } from "@/features/inventory/hooks/use-stock-transfers";
import { CheckCircle, Clock, TruckIcon, XCircle, AlertCircle, RefreshCw } from "lucide-react";
import { useState, useEffect } from "react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { useRouter, useSearchParams } from "next/navigation";

export default function StockTransfersPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const reference = searchParams.get('reference');

  const [queryParams, setQueryParams] = useState<Record<string, any>>({
    reference_number: reference || undefined
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(100);
  const [isSearching, setIsSearching] = useState(false);

  const { data: transfersData, isLoading, error, refetch } = useStockTransfers({
    ...queryParams,
    page: currentPage,
    limit: itemsPerPage,
  });

  console.log("Raw transfersData:", transfersData);

  // Ensure we have a valid array of transfers
  const transfers = transfersData?.data || [];

  // Get total pages from pagination (not meta)
  const totalPages = transfersData?.pagination?.totalPages || 1;
  const totalRecords = transfersData?.pagination?.total || transfers.length;

  // Reset search loading state when data changes
  useEffect(() => {
    if (!isLoading && isSearching) {
      setIsSearching(false);
    }
  }, [isLoading, isSearching]);

  // Check if we have any data
  const hasNoData = !isLoading && (!transfers || transfers.length === 0);

  const handleSearch = (query: string) => {
    setIsSearching(true);
    setQueryParams((prev) => ({
      ...prev,
      search: query || undefined,
    }));
    setCurrentPage(1);
    console.log("Searching transfers with query:", query);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(parseInt(value));
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Count transfers by status - handle different status formats and variations
  const pendingTransfers =
    transfers.length > 0
      ? transfers.filter((t) => {
          const status = (t.status || '').toUpperCase();
          return status === "PENDING" || status === "REQUESTED" || status === "PARTIALLY_APPROVED" || status === "FULLY_APPROVED";
        }).length
      : 0;

  const inTransitTransfers =
    transfers.length > 0
      ? transfers.filter((t) => {
          const status = (t.status || '').toUpperCase();
          return status === "IN_TRANSIT" || status === "DISPATCHED";
        }).length
      : 0;

  const completedTransfers =
    transfers.length > 0
      ? transfers.filter((t) => {
          const status = (t.status || '').toUpperCase();
          return status === "COMPLETED" || status === "APPROVED" || status === "FULLY_RECEIVED" || status === "PARTIALLY_RECEIVED";
        }).length
      : 0;

  const cancelledTransfers =
    transfers.length > 0
      ? transfers.filter((t) => {
          const status = (t.status || '').toUpperCase();
          return status === "CANCELLED" || status === "REJECTED";
        }).length
      : 0;

  console.log("Transfers stats:", {
    total: transfers.length,
    pending: pendingTransfers,
    inTransit: inTransitTransfers,
    completed: completedTransfers,
    cancelled: cancelledTransfers,
    pagination: transfersData?.pagination,
  });

  return (
    <MainLayout>
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Stock Transfers</h1>
            <p className="text-muted-foreground">
              Manage stock movements between branches using the stock-movements
              API
            </p>
          </div>
          <Button
            variant="outline"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>

        {reference ? (
          <Alert className="bg-green-50 border-green-200">
            <AlertCircle className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Filtered by Reference</AlertTitle>
            <AlertDescription className="text-green-700">
              Showing stock transfers with reference number: <strong>{reference}</strong>
              <Button
                variant="link"
                className="p-0 h-auto text-green-700 underline ml-2"
                onClick={() => {
                  router.push('/inventory/transfers');
                  setQueryParams({});
                }}
              >
                Clear filter
              </Button>
            </AlertDescription>
          </Alert>
        ) : (
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-700">
              <strong>Note:</strong> This page displays processed stock requests between branches.
            </p>
          </div>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error instanceof Error ? error.message : "Failed to load stock transfers"}
            </AlertDescription>
          </Alert>
        )}

        {hasNoData && !error && (
          <Alert variant="warning">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No Data</AlertTitle>
            <AlertDescription>
              No stock transfers found.
            </AlertDescription>
          </Alert>
        )}

        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoading ? "..." : pendingTransfers}
              </div>
              <p className="text-xs text-muted-foreground">
                Awaiting processing
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Transit</CardTitle>
              <TruckIcon className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoading ? "..." : inTransitTransfers}
              </div>
              <p className="text-xs text-muted-foreground">On the way</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoading ? "..." : completedTransfers}
              </div>
              <p className="text-xs text-muted-foreground">
                Successfully delivered
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Cancelled</CardTitle>
              <XCircle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoading ? "..." : cancelledTransfers}
              </div>
              <p className="text-xs text-muted-foreground">
                Cancelled transfers
              </p>
            </CardContent>
          </Card>
        </div>

        <StockTransfersTable
          transfers={transfers}
          isLoading={isLoading}
          onSearch={handleSearch}
          pagination={{
            currentPage,
            totalPages,
            onPageChange: handlePageChange,
            total: totalRecords,
            limit: itemsPerPage,
            onItemsPerPageChange: handleItemsPerPageChange,
          }}
          highlightReference={reference || undefined}
          currentFilters={{
            search: queryParams.search,
            reference_number: queryParams.reference_number,
          }}
          isSearching={isSearching}
        />
      </div>
    </MainLayout>
  );
}
