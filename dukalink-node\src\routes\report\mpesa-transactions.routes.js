const express = require('express');
const router = express.Router();
const { exportAllMpesaTransactions, exportCustomMpesaTransactions, exportLightweightMpesaTransactions } = require('../../controllers/report/mpesa-transactions-export.controller');
const { authenticate, rbac } = require('../../middleware/auth.middleware');

/**
 * @swagger
 * /api/v1/reports/mpesa-transactions/export/all:
 *   get:
 *     summary: Export comprehensive MPESA transactions to Excel
 *     description: Export all MPESA transaction data with multiple sheets including summary, details, and breakdowns
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by region ID
 *       - in: query
 *         name: transaction_type
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by transaction type (deposit, withdrawal)
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by status
 *       - in: query
 *         name: mpesa_code
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by MPESA code
 *     responses:
 *       200:
 *         description: Excel file with comprehensive MPESA transactions data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No transaction data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/all',
  authenticate,
  rbac.checkPermission('mpesa_reports', 'read'),
  exportAllMpesaTransactions
);

/**
 * @swagger
 * /api/v1/reports/mpesa-transactions/export/custom:
 *   get:
 *     summary: Export custom MPESA transactions to Excel
 *     description: Export MPESA transaction data with customizable sheets and format options
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by region ID
 *       - in: query
 *         name: transaction_type
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by transaction type (deposit, withdrawal)
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by status
 *       - in: query
 *         name: mpesa_code
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by MPESA code
 *       - in: query
 *         name: include_summary
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include summary sheet
 *       - in: query
 *         name: include_details
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include transaction details sheet
 *       - in: query
 *         name: include_daily_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include daily breakdown sheet
 *       - in: query
 *         name: include_branch_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include branch breakdown sheet
 *       - in: query
 *         name: include_type_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include type breakdown sheet
 *       - in: query
 *         name: include_status_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include status breakdown sheet
 *       - in: query
 *         name: include_charts
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include charts and graphs
 *       - in: query
 *         name: format_type
 *         schema:
 *           type: string
 *           enum: [detailed, summary]
 *           default: detailed
 *         description: Format type for export
 *     responses:
 *       200:
 *         description: Excel file with custom MPESA transactions data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No transaction data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/custom',
  authenticate,
  rbac.checkPermission('mpesa_reports', 'read'),
  exportCustomMpesaTransactions
);

/**
 * @swagger
 * /api/v1/reports/mpesa-transactions/export/lightweight:
 *   get:
 *     summary: Export lightweight MPESA transactions to Excel (fastest option)
 *     description: Export basic MPESA transaction data only for fastest processing
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by region ID
 *       - in: query
 *         name: transaction_type
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by transaction type (deposit, withdrawal)
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by status
 *       - in: query
 *         name: mpesa_code
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by MPESA code
 *     responses:
 *       200:
 *         description: Excel file with basic MPESA transaction data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No transaction data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/lightweight',
  authenticate,
  rbac.checkPermission('mpesa_reports', 'read'),
  exportLightweightMpesaTransactions
);

module.exports = router;
