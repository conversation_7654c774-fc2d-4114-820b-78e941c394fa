const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkDsaDuplicatesAllRecords() {
  let connection;
  
  try {
    console.log('🔍 Checking for duplicate DSA sale items in ALL RECORDS...');
    console.log('📅 Analysis Date:', new Date().toISOString());
    
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT || 3306,
    });

    console.log('✅ Connected to database successfully');

    // First, check what columns exist in the sales table
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'sales' 
      AND TABLE_SCHEMA = DATABASE()
      ORDER BY COLUMN_NAME;
    `);
    
    console.log('📋 Available columns in sales table:');
    columns.forEach(col => console.log(`   - ${col.COLUMN_NAME}`));
    console.log('');

    // Query to find duplicate DSA sales (all records)
    const duplicateQuery = `
      SELECT 
        s1.id as sale1_id,
        s1.receipt_number as sale1_receipt,
        s1.customer_id,
        s1.sale_type,
        s1.total_amount,
        s1.payment_method_id,
        s1.created_at as sale1_created,
        s2.id as sale2_id,
        s2.receipt_number as sale2_receipt,
        s2.sale_type as sale2_type,
        s2.total_amount as sale2_amount,
        s2.payment_method_id as sale2_payment,
        s2.created_at as sale2_created,
        c.name as customer_name,
        TIMESTAMPDIFF(SECOND, s1.created_at, s2.created_at) as time_diff_seconds,
        DATE(s1.created_at) as sale_date
      FROM sales s1
      JOIN sales s2 ON s1.customer_id = s2.customer_id 
        AND s1.id < s2.id
        AND ABS(s1.total_amount - s2.total_amount) < 1
      LEFT JOIN customers c ON s1.customer_id = c.id
      WHERE (s1.is_dsa = 1 OR s2.is_dsa = 1)
        AND TIMESTAMPDIFF(MINUTE, s1.created_at, s2.created_at) < 120
      ORDER BY s1.created_at DESC, s1.customer_id;
    `;

    console.log('🔍 Searching for duplicate DSA sales across all records...');
    const [duplicates] = await connection.execute(duplicateQuery);

    if (duplicates.length === 0) {
      console.log('✅ No duplicate DSA sales found in the entire database!');
    } else {
      console.log(`⚠️  Found ${duplicates.length} potential duplicate DSA sales:`);
      console.log('');
      
      // Group by date for better analysis
      const duplicatesByDate = {};
      duplicates.forEach(dup => {
        const date = dup.sale_date;
        if (!duplicatesByDate[date]) {
          duplicatesByDate[date] = [];
        }
        duplicatesByDate[date].push(dup);
      });

      Object.entries(duplicatesByDate).forEach(([date, dateDuplicates]) => {
        console.log(`📅 Date: ${date} (${dateDuplicates.length} duplicates)`);
        dateDuplicates.forEach((dup, index) => {
          console.log(`   ${index + 1}. Customer: ${dup.customer_name} (ID: ${dup.customer_id})`);
          console.log(`      Sale 1: ID ${dup.sale1_id}, Receipt: ${dup.sale1_receipt || 'NULL'}, Type: ${dup.sale_type}, Amount: ${dup.total_amount}, Payment: ${dup.payment_method_id}`);
          console.log(`      Sale 2: ID ${dup.sale2_id}, Receipt: ${dup.sale2_receipt || 'NULL'}, Type: ${dup.sale2_type}, Amount: ${dup.sale2_amount}, Payment: ${dup.sale2_payment}`);
          console.log(`      Time Difference: ${dup.time_diff_seconds} seconds`);
          console.log(`      Created: ${dup.sale1_created} -> ${dup.sale2_created}`);
        });
        console.log('   ---');
      });
    }

    // Query to find DSA sales by sale type for all records
    const salesByTypeQuery = `
      SELECT 
        sale_type,
        COUNT(*) as count,
        SUM(total_amount) as total_amount,
        MIN(created_at) as earliest_sale,
        MAX(created_at) as latest_sale,
        COUNT(DISTINCT customer_id) as unique_customers
      FROM sales 
      WHERE is_dsa = 1
      GROUP BY sale_type
      ORDER BY count DESC;
    `;

    console.log('');
    console.log('📊 DSA Sales Summary by Type (All Records):');
    const [salesByType] = await connection.execute(salesByTypeQuery);
    
    if (salesByType.length === 0) {
      console.log('   No DSA sales found in database');
    } else {
      salesByType.forEach(type => {
        console.log(`   ${type.sale_type || 'NULL'}: ${type.count} sales, Total: ${type.total_amount} KES, Customers: ${type.unique_customers}`);
        console.log(`      Period: ${type.earliest_sale} to ${type.latest_sale}`);
      });
    }

    // Query to find DSA sales with same payment reference (all records)
    const paymentRefDuplicatesQuery = `
      SELECT 
        s.payment_reference,
        s.customer_id,
        COUNT(*) as sale_count,
        GROUP_CONCAT(s.id ORDER BY s.created_at) as sale_ids,
        GROUP_CONCAT(s.receipt_number ORDER BY s.created_at) as receipt_numbers,
        GROUP_CONCAT(s.sale_type ORDER BY s.created_at) as sale_types,
        GROUP_CONCAT(s.total_amount ORDER BY s.created_at) as amounts,
        MIN(s.created_at) as first_sale,
        MAX(s.created_at) as last_sale,
        c.name as customer_name,
        DATE(MIN(s.created_at)) as sale_date
      FROM sales s
      LEFT JOIN customers c ON s.customer_id = c.id
      WHERE s.is_dsa = 1
        AND s.payment_reference IS NOT NULL
        AND s.payment_reference != ''
        AND s.payment_reference LIKE '%DSA%'
      GROUP BY s.payment_reference, s.customer_id
      HAVING COUNT(*) > 1
      ORDER BY sale_count DESC, s.customer_id;
    `;

    console.log('');
    console.log('🔍 DSA Sales with Same Payment Reference (All Records):');
    const [paymentRefDuplicates] = await connection.execute(paymentRefDuplicatesQuery);
    
    if (paymentRefDuplicates.length === 0) {
      console.log('   ✅ No duplicate payment references found');
    } else {
      console.log(`   ⚠️  Found ${paymentRefDuplicates.length} payment references with multiple sales:`);
      console.log('');
      
      // Group by date for better analysis
      const refDuplicatesByDate = {};
      paymentRefDuplicates.forEach(dup => {
        const date = dup.sale_date;
        if (!refDuplicatesByDate[date]) {
          refDuplicatesByDate[date] = [];
        }
        refDuplicatesByDate[date].push(dup);
      });

      Object.entries(refDuplicatesByDate).forEach(([date, dateDuplicates]) => {
        console.log(`📅 Date: ${date} (${dateDuplicates.length} assignment ID reuses)`);
        dateDuplicates.forEach((dup, index) => {
          console.log(`   ${index + 1}. Customer: ${dup.customer_name} (ID: ${dup.customer_id})`);
          console.log(`      Payment Reference: ${dup.payment_reference}`);
          console.log(`      Sales Count: ${dup.sale_count}`);
          console.log(`      Sale IDs: ${dup.sale_ids}`);
          console.log(`      Receipt Numbers: ${dup.receipt_numbers}`);
          console.log(`      Sale Types: ${dup.sale_types}`);
          console.log(`      Amounts: ${dup.amounts}`);
          console.log(`      Time Range: ${dup.first_sale} to ${dup.last_sale}`);
        });
        console.log('   ---');
      });
    }

    // Query to check for null receipt sales (all records)
    const nullReceiptQuery = `
      SELECT 
        COUNT(*) as null_receipt_count,
        SUM(total_amount) as null_receipt_total,
        MIN(created_at) as earliest_null,
        MAX(created_at) as latest_null,
        COUNT(DISTINCT customer_id) as customers_with_null
      FROM sales 
      WHERE is_dsa = 1 
        AND receipt_number IS NULL;
    `;

    console.log('');
    console.log('🔍 Null Receipt DSA Sales Analysis (All Records):');
    const [nullReceipts] = await connection.execute(nullReceiptQuery);
    
    if (nullReceipts[0].null_receipt_count === 0) {
      console.log('   ✅ No DSA sales with null receipts found');
    } else {
      const nullData = nullReceipts[0];
      console.log(`   ⚠️  Found ${nullData.null_receipt_count} DSA sales with null receipts`);
      console.log(`   Total Value: ${nullData.null_receipt_total} KES`);
      console.log(`   Customers Affected: ${nullData.customers_with_null}`);
      console.log(`   Period: ${nullData.earliest_null} to ${nullData.latest_null}`);
    }

    // Summary statistics
    const summaryQuery = `
      SELECT 
        COUNT(*) as total_dsa_sales,
        SUM(total_amount) as total_dsa_value,
        COUNT(DISTINCT customer_id) as total_dsa_customers,
        MIN(created_at) as earliest_dsa_sale,
        MAX(created_at) as latest_dsa_sale,
        COUNT(CASE WHEN receipt_number IS NOT NULL THEN 1 END) as sales_with_receipts,
        COUNT(CASE WHEN receipt_number IS NULL THEN 1 END) as sales_without_receipts
      FROM sales 
      WHERE is_dsa = 1;
    `;

    console.log('');
    console.log('📈 Overall DSA Sales Statistics:');
    const [summary] = await connection.execute(summaryQuery);
    
    if (summary.length > 0) {
      const stats = summary[0];
      console.log(`   Total DSA Sales: ${stats.total_dsa_sales}`);
      console.log(`   Total DSA Value: ${stats.total_dsa_value} KES`);
      console.log(`   Unique DSA Customers: ${stats.total_dsa_customers}`);
      console.log(`   Sales with Receipts: ${stats.sales_with_receipts}`);
      console.log(`   Sales without Receipts: ${stats.sales_without_receipts}`);
      console.log(`   Period: ${stats.earliest_dsa_sale} to ${stats.latest_dsa_sale}`);
      
      const duplicatePercentage = duplicates.length > 0 ? ((duplicates.length / stats.total_dsa_sales) * 100).toFixed(2) : 0;
      console.log(`   Duplicate Rate: ${duplicatePercentage}% (${duplicates.length}/${stats.total_dsa_sales})`);
    }

  } catch (error) {
    console.error('❌ Error checking DSA duplicates:', error.message);
    console.error('Full error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the check
checkDsaDuplicatesAllRecords();
