'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // This migration populates the sale_id field for existing DSA stock assignments
    // by finding the latest sale for each DSA agent and product combination

    const transaction = await queryInterface.sequelize.transaction();

    try {
      console.log('Starting to populate sale_id for existing DSA stock assignments...');

      // Get all DSA stock assignments that don't have a sale_id
      const assignments = await queryInterface.sequelize.query(`
        SELECT
          dsa.id,
          dsa.customer_id,
          dsa.product_id,
          dsa.branch_id,
          dsa.quantity_sold,
          dsa.created_at
        FROM dsa_stock_assignments dsa
        WHERE dsa.sale_id IS NULL
        ORDER BY dsa.customer_id, dsa.product_id, dsa.created_at DESC
      `, {
        type: Sequelize.QueryTypes.SELECT,
        transaction
      });

      console.log(`Found ${assignments.length} DSA assignments without sale_id`);

      let updatedCount = 0;

      // Process each assignment
      for (const assignment of assignments) {
        try {
          // Find the latest DSA sale for this customer and product
          const latestSale = await queryInterface.sequelize.query(`
            SELECT s.id, s.created_at as sale_created_at
            FROM sales s
            INNER JOIN sale_items si ON s.id = si.sale_id
            WHERE s.is_dsa = 1
              AND s.customer_id = :customer_id
              AND si.product_id = :product_id
              AND s.branch_id = :branch_id
              AND s.deleted_at IS NULL
            ORDER BY s.created_at DESC
            LIMIT 1
          `, {
            type: Sequelize.QueryTypes.SELECT,
            replacements: {
              customer_id: assignment.customer_id,
              product_id: assignment.product_id,
              branch_id: assignment.branch_id
            },
            transaction
          });

          if (latestSale.length > 0) {
            // Update the assignment with the sale_id
            await queryInterface.sequelize.query(`
              UPDATE dsa_stock_assignments
              SET sale_id = :sale_id, updated_at = NOW()
              WHERE id = :assignment_id
            `, {
              replacements: {
                sale_id: latestSale[0].id,
                assignment_id: assignment.id
              },
              transaction
            });

            updatedCount++;

            if (updatedCount % 100 === 0) {
              console.log(`Updated ${updatedCount} assignments so far...`);
            }
          }
        } catch (error) {
          console.error(`Error processing assignment ${assignment.id}:`, error.message);
          // Continue with other assignments
        }
      }

      console.log(`Successfully updated ${updatedCount} DSA stock assignments with sale_id`);

      await transaction.commit();
    } catch (error) {
      console.error('Error in migration:', error);
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Remove all sale_id values from DSA stock assignments
    await queryInterface.sequelize.query(`
      UPDATE dsa_stock_assignments
      SET sale_id = NULL, updated_at = NOW()
      WHERE sale_id IS NOT NULL
    `);

    console.log('Removed all sale_id values from DSA stock assignments');
  }
};
