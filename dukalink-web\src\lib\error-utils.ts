/**
 * Error handling utilities for the frontend
 */

export interface ApiError {
  message: string;
  type?: string;
  field?: string;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

export interface ParsedError {
  title: string;
  message: string;
  field?: string;
}

/**
 * Parse API error response and return user-friendly error information
 * @param error - The error object from API response
 * @param defaultTitle - Default title for the error
 * @returns Parsed error information
 */
export const parseApiError = (error: any, defaultTitle = "Error"): ParsedError => {
  console.error("Parsing API error:", error);

  // Default error response
  let result: ParsedError = {
    title: defaultTitle,
    message: "An unexpected error occurred. Please try again.",
  };

  // Extract error data from response
  const errorData = error?.response?.data;

  if (errorData) {
    // Handle validation errors with specific field information
    if (errorData.type === 'validation' && errorData.field) {
      result = {
        title: "Validation Error",
        message: errorData.message || "Please check your input and try again.",
        field: errorData.field,
      };
    }
    // Handle multiple validation errors
    else if (errorData.errors && Array.isArray(errorData.errors)) {
      const fieldErrors = errorData.errors
        .map((err: any) => `${err.field?.replace('_', ' ') || 'Field'}: ${err.message}`)
        .join(', ');

      result = {
        title: "Validation Errors",
        message: fieldErrors,
      };
    }
    // Handle date-specific errors
    else if (errorData.message && errorData.message.includes('date')) {
      result = {
        title: "Date Error",
        message: errorData.message,
        field: errorData.field || 'date',
      };
    }
    // Handle general API errors
    else if (errorData.message) {
      result = {
        title: defaultTitle,
        message: errorData.message,
      };
    }
  }
  // Handle network errors
  else if (error?.code === 'NETWORK_ERROR' || !error?.response) {
    result = {
      title: "Network Error",
      message: "Unable to connect to the server. Please check your internet connection.",
    };
  }
  // Handle timeout errors
  else if (error?.code === 'ECONNABORTED') {
    result = {
      title: "Timeout Error",
      message: "The request took too long to complete. Please try again.",
    };
  }

  return result;
};

/**
 * Parse procurement-specific errors
 * @param error - The error object from API response
 * @returns Parsed error information
 */
export const parseProcurementError = (error: any): ParsedError => {
  const errorData = error?.response?.data;

  // Handle procurement-specific errors
  if (errorData?.message) {
    // Branch ID validation errors
    if (errorData.message.includes('Invalid delivery location') ||
        errorData.message.includes('branch_id cannot be null')) {
      return {
        title: "Invalid Delivery Location",
        message: "There's an issue with the delivery location configuration. Please contact support.",
        field: "delivery_location",
      };
    }

    // Date validation errors
    if (errorData.message.includes('Incorrect date value') && errorData.message.includes('expiry_date')) {
      return {
        title: "Invalid Expiry Date",
        message: "Please enter a valid expiry date or leave the field empty.",
        field: "expiry_date",
      };
    }

    // Quantity validation errors
    if (errorData.message.includes('Invalid quantity received')) {
      return {
        title: "Invalid Quantity",
        message: errorData.message,
        field: "quantity_received",
      };
    }

    // Permission errors
    if (errorData.message.includes('permission')) {
      return {
        title: "Permission Denied",
        message: errorData.message,
      };
    }

    // Request not found errors
    if (errorData.message.includes('not found')) {
      return {
        title: "Not Found",
        message: errorData.message,
      };
    }
  }

  // Fall back to general error parsing
  return parseApiError(error, "Procurement Error");
};

/**
 * Format field name for display
 * @param fieldName - The field name to format
 * @returns Formatted field name
 */
export const formatFieldName = (fieldName: string): string => {
  return fieldName
    .replace(/_/g, ' ')
    .replace(/\b\w/g, (char) => char.toUpperCase());
};

/**
 * Check if error is a validation error
 * @param error - The error object
 * @returns True if it's a validation error
 */
export const isValidationError = (error: any): boolean => {
  const errorData = error?.response?.data;
  return errorData?.type === 'validation' ||
         (errorData?.errors && Array.isArray(errorData.errors));
};

/**
 * Check if error is a network error
 * @param error - The error object
 * @returns True if it's a network error
 */
export const isNetworkError = (error: any): boolean => {
  return error?.code === 'NETWORK_ERROR' ||
         error?.code === 'ECONNABORTED' ||
         !error?.response;
};

/**
 * Get error status code
 * @param error - The error object
 * @returns HTTP status code or null
 */
export const getErrorStatusCode = (error: any): number | null => {
  return error?.response?.status || null;
};
