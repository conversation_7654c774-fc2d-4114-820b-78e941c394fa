const sequelize = require('./config/database');

async function checkMigrationResults() {
  try {
    console.log('Checking migration results...');
    
    // Check total DSA assignments
    const totalAssignments = await sequelize.query(`
      SELECT COUNT(*) as total
      FROM dsa_stock_assignments
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });

    console.log(`Total DSA assignments: ${totalAssignments[0].total}`);

    // Check assignments with sale_id
    const assignmentsWithSaleId = await sequelize.query(`
      SELECT COUNT(*) as total
      FROM dsa_stock_assignments
      WHERE sale_id IS NOT NULL
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });

    console.log(`Assignments with sale_id: ${assignmentsWithSaleId[0].total}`);

    // Check assignments without sale_id
    const assignmentsWithoutSaleId = await sequelize.query(`
      SELECT COUNT(*) as total
      FROM dsa_stock_assignments
      WHERE sale_id IS NULL
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });

    console.log(`Assignments without sale_id: ${assignmentsWithoutSaleId[0].total}`);

    // Show some sample assignments with sale_id
    const sampleAssignments = await sequelize.query(`
      SELECT 
        dsa.id,
        dsa.customer_id,
        dsa.product_id,
        dsa.branch_id,
        dsa.quantity_assigned,
        dsa.quantity_sold,
        dsa.sale_id,
        s.total_amount,
        s.created_at as sale_created_at,
        dsa.created_at as assignment_created_at
      FROM dsa_stock_assignments dsa
      LEFT JOIN sales s ON dsa.sale_id = s.id
      WHERE dsa.sale_id IS NOT NULL
      ORDER BY dsa.id
      LIMIT 10
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });

    console.log('\nSample assignments with sale_id:');
    console.table(sampleAssignments);

    // Check total DSA sales
    const totalDsaSales = await sequelize.query(`
      SELECT COUNT(*) as total
      FROM sales
      WHERE is_dsa = 1 AND deleted_at IS NULL
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });

    console.log(`\nTotal DSA sales: ${totalDsaSales[0].total}`);

    // Check DSA sales by branch
    const dsaSalesByBranch = await sequelize.query(`
      SELECT 
        s.branch_id,
        b.name as branch_name,
        COUNT(*) as sale_count
      FROM sales s
      LEFT JOIN branches b ON s.branch_id = b.id
      WHERE s.is_dsa = 1 AND s.deleted_at IS NULL
      GROUP BY s.branch_id, b.name
      ORDER BY sale_count DESC
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });

    console.log('\nDSA sales by branch:');
    console.table(dsaSalesByBranch);

  } catch (error) {
    console.error('Error checking migration results:', error);
  } finally {
    await sequelize.close();
  }
}

checkMigrationResults();
