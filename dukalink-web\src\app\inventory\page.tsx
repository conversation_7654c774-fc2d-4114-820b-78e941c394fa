"use client";

import { useState, useEffect, use<PERSON>emo } from "react";
import Link from "next/link";
import { MainLayout } from "@/components/layouts/main-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { InventoryTable } from "@/features/inventory/components/inventory-table";
import { DsaStockTable } from "@/features/inventory/components/dsa-stock-table";
import {
  useBranchInventory,
  useHQInventory,
} from "@/features/inventory/hooks/use-inventory";
import { useBranches } from "@/features/branches/hooks/use-branches";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package, AlertTriangle, DollarSign } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatCurrency } from "@/lib/utils";
import { useCurrentUser } from "@/features/auth/hooks/use-auth";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { Loader2 } from "lucide-react";

export default function InventoryPage() {
  // Get current user and ensure authentication is set up
  const { data: userData, isLoading: isLoadingUser } = useCurrentUser();

  // Log authentication status for debugging
  console.log("Auth status:", { userData, isLoadingUser });
  const { data: branchesData, isLoading: isLoadingBranches } = useBranches();
  const [selectedBranchId, setSelectedBranchId] = useState<
    number | undefined
  >();
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(100);
  const [isSearching, setIsSearching] = useState(false);

  // Debug logging
  console.log("branchesData:", branchesData);
  console.log("branchesData?.data:", branchesData?.data);

  // Use the first branch as default if no branch is selected
  // Ensure we have a valid branch ID for the API call
  const branches = useMemo(() => {
    const result = Array.isArray(branchesData?.data) ? branchesData.data : [];
    console.log("Available branches:", result);
    return result;
  }, [branchesData?.data]);

  // Check if user is super_admin or company_admin
  const isAdminUser = useMemo(() => {
    const result =
      userData?.role_name &&
      [
        "super_admin",
        "company_admin",
        "tenant_admin", // Added tenant_admin
      ].includes(userData.role_name.toLowerCase());
    console.log("User role:", userData?.role_name, "isAdminUser:", result);
    return result;
  }, [userData?.role_name]);

  // Use useEffect to set the selectedBranchId when user data or branches change
  // This prevents infinite re-renders
  useEffect(() => {
    // Only set the selectedBranchId if it's not already set and we have the necessary data
    if (
      selectedBranchId === undefined &&
      !isLoadingUser &&
      !isLoadingBranches
    ) {
      // Check if user is company_admin or super_admin
      const isCompanyAdmin =
        userData?.role_name?.toLowerCase() === "company_admin";
      const isSuperAdmin = userData?.role_name?.toLowerCase() === "super_admin";
      const isTenantAdmin =
        userData?.role_name?.toLowerCase() === "tenant_admin";

      if (isCompanyAdmin || isSuperAdmin || isTenantAdmin) {
        console.log(
          "Setting selectedBranchId to All Branches (-1) for admin user"
        );
        setSelectedBranchId(-1); // Use -1 to indicate All Branches for admin users
      } else if (branches.length > 0 && branches[0]?.id) {
        console.log(
          "Setting selectedBranchId to first branch:",
          branches[0].id
        );
        setSelectedBranchId(branches[0].id);
      }
    }

    // Force a log of the current state for debugging
    console.log("Current state:", {
      selectedBranchId,
      isAdminUser,
      userRole: userData?.role_name,
      branchesCount: branches.length,
    });
  }, [
    isLoadingUser,
    isLoadingBranches,
    isAdminUser,
    branches,
    selectedBranchId,
    userData?.role_name,
  ]);

  // Use selectedBranchId or first branch id if available
  // If selectedBranchId is -1, it means "All Branches"
  const effectiveBranchId = useMemo(() => {
    const result =
      selectedBranchId === -1
        ? -1
        : selectedBranchId ||
          (branches.length > 0 ? branches[0]?.id : undefined);
    console.log("effectiveBranchId:", result);
    return result;
  }, [selectedBranchId, branches]);

  // Check if we should enable the inventory query
  const shouldEnableInventoryQuery = useMemo(() => {
    const result =
      !isLoadingUser &&
      !!userData &&
      (!!effectiveBranchId || effectiveBranchId === -1);
    console.log("Should enable inventory query:", result, {
      isLoadingUser,
      hasUserData: !!userData,
      effectiveBranchId,
    });
    return result;
  }, [isLoadingUser, userData, effectiveBranchId]);

  // Determine which hook to use based on the user's role and selected branch
  const useHQQuery = isAdminUser && selectedBranchId === 1; // Only use HQ query when explicitly selecting branch_id=1
  console.log("Using HQ query:", useHQQuery, { isAdminUser, selectedBranchId });

  // Build query parameters for API calls
  const queryParams = {
    page: currentPage,
    limit: itemsPerPage,
    ...(searchQuery && { search: searchQuery }),
  };

  // For HQ inventory (admin users viewing HQ)
  const { data: hqInventoryData, isLoading: isLoadingHQInventory } =
    useHQInventory(
      queryParams,
      // Only enable when user data is loaded and we're using HQ query
      Boolean(shouldEnableInventoryQuery && useHQQuery)
    );

  // For branch-specific inventory
  const { data: branchInventoryData, isLoading: isLoadingBranchInventory } =
    useBranchInventory(
      typeof effectiveBranchId === "number" ? effectiveBranchId : 0,
      queryParams,
      // Only enable when user data is loaded, we have a branch ID, and we're not using HQ query
      shouldEnableInventoryQuery && !useHQQuery
    );

  // Combine the data based on which query is active
  const inventoryData = useHQQuery ? hqInventoryData : branchInventoryData;
  const isLoadingInventory = useHQQuery
    ? isLoadingHQInventory
    : isLoadingBranchInventory;

  // Reset search loading state when inventory data changes
  useEffect(() => {
    if (!isLoadingInventory && isSearching) {
      setIsSearching(false);
    }
  }, [isLoadingInventory, isSearching]);

  console.log("inventoryData:", inventoryData);

  // We already have branches data defined above
  console.log("Processed branches:", branches);

  // Safely handle inventory data
  const inventory = useMemo(() => {
    return Array.isArray(inventoryData?.data) ? inventoryData.data : [];
  }, [inventoryData?.data]);
  console.log("Processed inventory:", inventory);

  // Check pagination structure for compatibility

  const handleBranchChange = (branchId: string) => {
    const parsedBranchId = parseInt(branchId, 10);
    setSelectedBranchId(parsedBranchId);
    setCurrentPage(1);

    // Log additional information for debugging
    if (parsedBranchId === 1) {
      console.log("Headquarters (HQ) branch selected");
    } else if (parsedBranchId === -1) {
      console.log("All branches selected");
    } else {
      console.log(`Branch with ID ${parsedBranchId} selected`);
    }
  };

  const handleSearch = (query: string) => {
    setIsSearching(true);
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page when searching
    console.log("Searching inventory by product name:", query);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    console.log(`Changing page to ${page}`);
  };

  const handleItemsPerPageChange = (value: string) => {
    const numValue = parseInt(value, 10);
    setItemsPerPage(numValue);
    setCurrentPage(1); // Reset to first page when changing items per page
    console.log(`Changing items per page to ${numValue}`);
  };

  // Ensure we have valid pagination values
  const paginationTotal = inventoryData?.pagination?.total || 0;
  // API returns 'pages' not 'totalPages'
  const totalPages =
    Math.max(
      inventoryData?.pagination?.pages || 0,
      inventoryData?.pagination?.totalPages || 0,
      Math.ceil(paginationTotal / itemsPerPage)
    ) || 1;

  // Calculate low stock items
  const lowStockItems =
    inventory.length > 0
      ? inventory.filter(
          (item: any) =>
            item.reorder_level && item.quantity <= item.reorder_level
        ).length
      : 0;

  // Calculate out of stock items
  const outOfStockItems =
    inventory.length > 0
      ? inventory.filter((item: any) => item.quantity === 0).length
      : 0;

  // Calculate total inventory value
  const totalValue =
    inventory.length > 0
      ? inventory.reduce((total: number, item: any) => {
          // Parse selling_price from string to number, preferring default_selling_price if available
          const sellingPrice =
            (item.default_selling_price
              ? parseFloat(item.default_selling_price)
              : 0) ||
            (item.selling_price ? parseFloat(item.selling_price) : 0) ||
            0;
          return total + sellingPrice * item.quantity;
        }, 0)
      : 0;

  // Show loading state if user data or branches are still loading
  if (isLoadingUser || isLoadingBranches) {
    return (
      <MainLayout>
        <div className="flex h-full items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">Loading...</h2>
            <p className="text-muted-foreground">
              Please wait while we load your inventory data.
            </p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-4">
        {/* Loading indicator at the top of the screen */}
        {isLoadingInventory && (
          <div className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-sm">
            <div className="flex items-center justify-center p-2 border-b bg-background">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                Loading inventory items...
              </div>
            </div>
            <Progress value={undefined} className="h-1 rounded-none" />
          </div>
        )}

        <div>
          <h1 className="text-2xl font-bold tracking-tight">
            Inventory Management
          </h1>
          <p className="text-muted-foreground">
            Manage your inventory across branches
          </p>
        </div>

        <Tabs defaultValue="inventory" className="space-y-4">
          <TabsList>
            <TabsTrigger value="inventory">Inventory</TabsTrigger>
            <TabsTrigger value="dsa-stock">DSA Stock</TabsTrigger>
            {/* Removed Transfers tab as requested */}
            {/* Removed Adjustments tab as requested */}
          </TabsList>

          <TabsContent value="inventory" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Items
                  </CardTitle>
                  <Package className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoadingInventory ? (
                      <Skeleton className="h-8 w-16" />
                    ) : (
                      inventoryData?.pagination?.total || 0
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Products in inventory
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Low Stock Items
                  </CardTitle>
                  <AlertTriangle className="h-4 w-4 text-amber-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoadingInventory ? (
                      <Skeleton className="h-8 w-16" />
                    ) : (
                      lowStockItems
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Items below reorder level
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Out of Stock
                  </CardTitle>
                  <AlertTriangle className="h-4 w-4 text-destructive" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoadingInventory ? (
                      <Skeleton className="h-8 w-16" />
                    ) : (
                      outOfStockItems
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Items with zero stock
                  </p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Value
                  </CardTitle>
                  <DollarSign className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoadingInventory ? (
                      <Skeleton className="h-8 w-24" />
                    ) : (
                      formatCurrency(totalValue)
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Total inventory value
                  </p>
                </CardContent>
              </Card>
            </div>

            <div className="flex items-center justify-between">
              <div className="w-full max-w-xs">
                {/* Branch selection dropdown */}
                {branches.length > 0 ? (
                  <Select
                    value={
                      selectedBranchId !== undefined
                        ? selectedBranchId.toString()
                        : "-1" // Default to All Branches (-1) if no branch is selected
                    }
                    onValueChange={handleBranchChange}
                    disabled={isLoadingBranches || branches.length === 0}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select branch" />
                    </SelectTrigger>
                    <SelectContent>
                      {/* Removed custom HQ option */}
                      {/* Add "All Branches" option only for super_admin and company_admin */}
                      {isAdminUser && (
                        <SelectItem key="all-branches" value="-1">
                          All Branches
                        </SelectItem>
                      )}
                      {branches.map((branch) => (
                        <SelectItem
                          key={branch.id}
                          value={branch.id?.toString() || ""}
                        >
                          {branch.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="p-2 border rounded-md bg-muted">
                    <p className="text-sm text-muted-foreground">
                      No branches available
                    </p>
                  </div>
                )}
              </div>
              {/* Removed Manage HQ Stock button as requested */}
            </div>

            {branches.length > 0 ? (
              effectiveBranchId ? (
                <InventoryTable
                  inventory={inventory}
                  isLoading={isLoadingInventory || isLoadingBranches}
                  onSearch={handleSearch}
                  pagination={{
                    page: currentPage,
                    totalPages: totalPages,
                    onPageChange: handlePageChange,
                    limit: itemsPerPage,
                    onItemsPerPageChange: handleItemsPerPageChange,
                    total: paginationTotal,
                  }}
                  currentFilters={{
                    branch_id: effectiveBranchId === -1 ? undefined : effectiveBranchId,
                    search: searchQuery,
                    include_zero_stock: true,
                    include_inactive: false,
                  }}
                  isSearching={isSearching}
                />
              ) : (
                <div className="p-8 text-center">
                  <p>Error loading branch data. Please try again.</p>
                </div>
              )
            ) : (
              <div className="p-8 border rounded-md bg-muted text-center">
                <h3 className="text-lg font-medium mb-2">
                  No Branches Available
                </h3>
                <p className="text-muted-foreground mb-4">
                  You need to create at least one branch to manage inventory.
                </p>
                <Button asChild>
                  <Link href="/branches/create">Create Branch</Link>
                </Button>
              </div>
            )}
          </TabsContent>

          <TabsContent value="dsa-stock" className="space-y-4">
            <div className="flex items-center justify-between mb-4">
              <div className="w-full max-w-xs">
                {/* Branch selection dropdown */}
                {branches.length > 0 ? (
                  <Select
                    value={
                      selectedBranchId !== undefined
                        ? selectedBranchId.toString()
                        : "-1" // Default to All Branches (-1) if no branch is selected
                    }
                    onValueChange={handleBranchChange}
                    disabled={isLoadingBranches || branches.length === 0}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select branch" />
                    </SelectTrigger>
                    <SelectContent>
                      {/* Removed custom HQ option */}
                      {/* Add "All Branches" option only for super_admin and company_admin */}
                      {isAdminUser && (
                        <SelectItem key="all-branches" value="-1">
                          All Branches
                        </SelectItem>
                      )}
                      {branches.map((branch) => (
                        <SelectItem
                          key={branch.id}
                          value={branch.id?.toString() || ""}
                        >
                          {branch.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="p-2 border rounded-md bg-muted">
                    <p className="text-sm text-muted-foreground">
                      No branches available
                    </p>
                  </div>
                )}
              </div>
            </div>
            <DsaStockTable
              branchId={
                effectiveBranchId === -1 ? undefined : effectiveBranchId
              }
            />
          </TabsContent>

          {/* Removed Transfers tab content as requested */}

          {/* Removed Adjustments tab content as requested */}
        </Tabs>
      </div>
    </MainLayout>
  );
}
