import { useState, useEffect, useCallback } from 'react';
import { reportService, CashStatusReportParams, CashStatusReportResponse } from '@/services/report-service';
import { useToast } from '@/components/ui/use-toast';

interface UseCashStatusReportProps {
  startDate: string;
  endDate: string;
  regionId?: number;
  branchId?: number;
  includeBreakdown?: boolean;
  enabled?: boolean;
  page?: number;
  limit?: number;
}

export function useCashStatusReport({
  startDate,
  endDate,
  regionId,
  branchId,
  includeBreakdown = true,
  enabled = true,
  page = 1,
  limit = 50
}: UseCashStatusReportProps) {
  const [data, setData] = useState<CashStatusReportResponse['data'] | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();

  const fetchReport = useCallback(async () => {
    if (!startDate || !endDate) {
      console.log('Missing required date parameters, skipping fetch');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Make sure we're using the correct parameter names expected by the backend
      const params: CashStatusReportParams = {
        start_date: startDate,
        end_date: endDate,
        include_breakdown: includeBreakdown,
        page,
        limit
      };

      // Only add region_id if it's a valid number and not null/undefined
      if (regionId !== null && regionId !== undefined) {
        params.region_id = regionId;
        console.log(`Adding region filter: ${regionId}`);
      }

      // Only add branch_id if it's a valid number and not null/undefined
      if (branchId !== null && branchId !== undefined) {
        params.branch_id = branchId;
        console.log(`Adding branch filter: ${branchId}`);
      }

      console.log('Sending params to backend:', JSON.stringify(params, null, 2));
      console.log('Date parameters:', {
        startDate: typeof startDate === 'string' ? startDate : 'not a string',
        endDate: typeof endDate === 'string' ? endDate : 'not a string',
        startDateFormat: startDate.includes('T') ? 'includes time' : 'date only',
        endDateFormat: endDate.includes('T') ? 'includes time' : 'date only'
      });

      // Add a small delay to ensure console logs are visible
      await new Promise(resolve => setTimeout(resolve, 100));

      const response = await reportService.getCashStatusReport(params);
      console.log('Received response from backend:', JSON.stringify(response, null, 2));

      // Check if we have valid data
      if (response && response.data) {
        // Log summary values to debug
        console.log('Summary values:', {
          totalOpeningCash: response.data.summary?.totalOpeningCash,
          totalClosingCash: response.data.summary?.totalClosingCash,
          totalInflows: response.data.summary?.totalInflows,
          totalOutflows: response.data.summary?.totalOutflows,
          netCashChange: response.data.summary?.netCashChange
        });

        setData(response.data);
      } else {
        console.error('Invalid response format or empty data received');
        setError(new Error('Invalid response format or empty data received'));
        toast({
          title: 'Warning',
          description: 'Received empty data from server. There might be no data for the selected period.',
          variant: 'warning'
        });
      }
    } catch (err) {
      console.error('Error fetching cash status report:', err);

      // Extract more detailed error information
      let errorMessage = 'Failed to fetch cash status report';

      if (err instanceof Error) {
        errorMessage = err.message;
        console.error('Error details:', {
          name: err.name,
          message: err.message,
          stack: err.stack
        });

        // Check for axios specific error properties
        const axiosError = err as any;
        if (axiosError.isAxiosError) {
          console.error('Axios error details:', {
            status: axiosError.response?.status,
            statusText: axiosError.response?.statusText,
            data: axiosError.response?.data,
            config: {
              url: axiosError.config?.url,
              method: axiosError.config?.method,
              params: axiosError.config?.params,
              headers: axiosError.config?.headers
            }
          });

          // Use server error message if available
          if (axiosError.response?.data?.message) {
            const serverMessage = axiosError.response.data.message;

            // Handle specific database connection errors
            if (serverMessage.includes('Database connection is not available') ||
                serverMessage.includes('database connection') ||
                serverMessage.includes('sequelize')) {
              errorMessage = `Database connection error: ${serverMessage}. Please contact your system administrator.`;

              // Log additional information that might help diagnose the issue
              console.error('Database connection error detected. Server details:', {
                message: serverMessage,
                status: axiosError.response.status,
                timestamp: new Date().toISOString()
              });
            } else {
              errorMessage = `Server error: ${serverMessage}`;
            }
          } else if (axiosError.response?.status === 500) {
            errorMessage = 'Server error: The server encountered an internal error. Please try again later or contact support.';
          }
        }
      }

      setError(err instanceof Error ? err : new Error(errorMessage));

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  }, [startDate, endDate, regionId, branchId, includeBreakdown, page, limit, toast]);

  // Use a ref to track if we've already attempted to fetch with these parameters
  const [hasAttempted, setHasAttempted] = useState(false);

  useEffect(() => {
    // Reset the attempt flag when parameters change
    console.log('Parameters changed, resetting attempt flag');
    console.log('New parameters:', { startDate, endDate, regionId, branchId, includeBreakdown });
    setHasAttempted(false);
  }, [startDate, endDate, regionId, branchId, includeBreakdown, page, limit]);

  useEffect(() => {
    if (enabled && !hasAttempted) {
      console.log('Fetching report with parameters:', { startDate, endDate, regionId, branchId, includeBreakdown, page, limit });
      setHasAttempted(true);
      fetchReport();
    }
  }, [enabled, hasAttempted, fetchReport, startDate, endDate, regionId, branchId, includeBreakdown, page, limit]);

  return {
    data,
    isLoading,
    error,
    refetch: fetchReport
  };
}
