const mysql = require('mysql2/promise');
require('dotenv').config();

async function checkDsaDuplicatesToday() {
  let connection;

  try {
    console.log('🔍 Checking for duplicate DSA sale items today...');
    console.log('📅 Date:', new Date().toISOString().split('T')[0]);

    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      port: process.env.DB_PORT || 3306,
    });

    console.log('✅ Connected to database successfully');

    // Get today's date in MySQL format
    const today = new Date().toISOString().split('T')[0];

    // First, check what columns exist in the sales table
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'sales'
      AND TABLE_SCHEMA = DATABASE()
      ORDER BY COLUMN_NAME;
    `);

    console.log('📋 Available columns in sales table:');
    columns.forEach(col => console.log(`   - ${col.COLUMN_NAME}`));
    console.log('');

    // Query to find duplicate DSA sales for today (without assignment_identifier)
    const duplicateQuery = `
      SELECT
        s1.id as sale1_id,
        s1.receipt_number as sale1_receipt,
        s1.customer_id,
        s1.sale_type,
        s1.total_amount,
        s1.payment_method_id,
        s1.created_at as sale1_created,
        s2.id as sale2_id,
        s2.receipt_number as sale2_receipt,
        s2.sale_type as sale2_type,
        s2.total_amount as sale2_amount,
        s2.payment_method_id as sale2_payment,
        s2.created_at as sale2_created,
        c.name as customer_name,
        TIMESTAMPDIFF(SECOND, s1.created_at, s2.created_at) as time_diff_seconds
      FROM sales s1
      JOIN sales s2 ON s1.customer_id = s2.customer_id
        AND s1.id < s2.id
        AND ABS(s1.total_amount - s2.total_amount) < 1
      LEFT JOIN customers c ON s1.customer_id = c.id
      WHERE DATE(s1.created_at) = ?
        AND DATE(s2.created_at) = ?
        AND (s1.is_dsa = 1 OR s2.is_dsa = 1)
        AND TIMESTAMPDIFF(MINUTE, s1.created_at, s2.created_at) < 120
      ORDER BY s1.customer_id, s1.created_at;
    `;

    console.log('🔍 Searching for duplicate DSA sales...');
    const [duplicates] = await connection.execute(duplicateQuery, [today, today]);

    if (duplicates.length === 0) {
      console.log('✅ No duplicate DSA sales found for today!');
    } else {
      console.log(`⚠️  Found ${duplicates.length} potential duplicate DSA sales:`);
      console.log('');

      duplicates.forEach((dup, index) => {
        console.log(`${index + 1}. Potential Duplicate DSA Sales:`);
        console.log(`   Customer: ${dup.customer_name} (ID: ${dup.customer_id})`);
        console.log(`   Sale 1: ID ${dup.sale1_id}, Receipt: ${dup.sale1_receipt}, Type: ${dup.sale_type}, Amount: ${dup.total_amount}, Payment: ${dup.payment_method_id}`);
        console.log(`   Sale 2: ID ${dup.sale2_id}, Receipt: ${dup.sale2_receipt}, Type: ${dup.sale2_type}, Amount: ${dup.sale2_amount}, Payment: ${dup.sale2_payment}`);
        console.log(`   Time Difference: ${dup.time_diff_seconds} seconds`);
        console.log('   ---');
      });
    }

    // Query to find DSA sales by sale type for today
    const salesByTypeQuery = `
      SELECT
        sale_type,
        COUNT(*) as count,
        SUM(total_amount) as total_amount,
        GROUP_CONCAT(DISTINCT customer_id) as customer_ids
      FROM sales
      WHERE DATE(created_at) = ?
        AND (is_dsa = 1 OR sale_type LIKE '%dsa%')
      GROUP BY sale_type
      ORDER BY count DESC;
    `;

    console.log('📊 DSA Sales Summary by Type (Today):');
    const [salesByType] = await connection.execute(salesByTypeQuery, [today]);

    if (salesByType.length === 0) {
      console.log('   No DSA sales found for today');
    } else {
      salesByType.forEach(type => {
        console.log(`   ${type.sale_type || 'NULL'}: ${type.count} sales, Total: ${type.total_amount}`);
      });
    }

    // Query to find DSA sales with same customer and similar payment references
    const paymentRefDuplicatesQuery = `
      SELECT
        payment_reference,
        customer_id,
        COUNT(*) as sale_count,
        GROUP_CONCAT(s.id ORDER BY s.created_at) as sale_ids,
        GROUP_CONCAT(s.receipt_number ORDER BY s.created_at) as receipt_numbers,
        GROUP_CONCAT(s.sale_type ORDER BY s.created_at) as sale_types,
        GROUP_CONCAT(s.total_amount ORDER BY s.created_at) as amounts,
        MIN(s.created_at) as first_sale,
        MAX(s.created_at) as last_sale,
        c.name as customer_name
      FROM sales s
      LEFT JOIN customers c ON s.customer_id = c.id
      WHERE DATE(s.created_at) = ?
        AND s.is_dsa = 1
        AND s.payment_reference IS NOT NULL
        AND s.payment_reference != ''
        AND s.payment_reference LIKE '%DSA%'
      GROUP BY payment_reference, customer_id
      HAVING COUNT(*) > 1
      ORDER BY sale_count DESC;
    `;

    console.log('');
    console.log('🔍 DSA Sales with Same Payment Reference (Today):');
    const [paymentRefDuplicates] = await connection.execute(paymentRefDuplicatesQuery, [today]);

    if (paymentRefDuplicates.length === 0) {
      console.log('   ✅ No duplicate payment references found');
    } else {
      console.log(`   ⚠️  Found ${paymentRefDuplicates.length} payment references with multiple sales:`);
      console.log('');

      paymentRefDuplicates.forEach((dup, index) => {
        console.log(`   ${index + 1}. Customer: ${dup.customer_name} (ID: ${dup.customer_id})`);
        console.log(`      Payment Reference: ${dup.payment_reference}`);
        console.log(`      Sales Count: ${dup.sale_count}`);
        console.log(`      Sale IDs: ${dup.sale_ids}`);
        console.log(`      Receipt Numbers: ${dup.receipt_numbers}`);
        console.log(`      Sale Types: ${dup.sale_types}`);
        console.log(`      Amounts: ${dup.amounts}`);
        console.log(`      Time Range: ${dup.first_sale} to ${dup.last_sale}`);
        console.log('      ---');
      });
    }

    // Query to check for sales with different sale_types but same customer and similar amounts
    const suspiciousQuery = `
      SELECT
        s1.id as sale1_id,
        s1.sale_type as sale1_type,
        s1.total_amount as sale1_amount,
        s1.payment_method_id as sale1_payment,
        s2.id as sale2_id,
        s2.sale_type as sale2_type,
        s2.total_amount as sale2_amount,
        s2.payment_method_id as sale2_payment,
        s1.customer_id,
        c.name as customer_name,
        TIMESTAMPDIFF(MINUTE, s1.created_at, s2.created_at) as time_diff_minutes
      FROM sales s1
      JOIN sales s2 ON s1.customer_id = s2.customer_id
        AND s1.id != s2.id
        AND ABS(s1.total_amount - s2.total_amount) < 1
      LEFT JOIN customers c ON s1.customer_id = c.id
      WHERE DATE(s1.created_at) = ?
        AND DATE(s2.created_at) = ?
        AND (s1.is_dsa = 1 OR s2.is_dsa = 1)
        AND ABS(TIMESTAMPDIFF(MINUTE, s1.created_at, s2.created_at)) < 60
      ORDER BY s1.customer_id, s1.created_at;
    `;

    console.log('');
    console.log('🕵️  Suspicious Similar Sales (Same customer, similar amount, within 1 hour):');
    const [suspicious] = await connection.execute(suspiciousQuery, [today, today]);

    if (suspicious.length === 0) {
      console.log('   ✅ No suspicious similar sales found');
    } else {
      console.log(`   ⚠️  Found ${suspicious.length} suspicious similar sales:`);
      console.log('');

      suspicious.forEach((sus, index) => {
        console.log(`   ${index + 1}. Customer: ${sus.customer_name} (ID: ${sus.customer_id})`);
        console.log(`      Sale 1: ID ${sus.sale1_id}, Type: ${sus.sale1_type}, Amount: ${sus.sale1_amount}, Payment: ${sus.sale1_payment}`);
        console.log(`      Sale 2: ID ${sus.sale2_id}, Type: ${sus.sale2_type}, Amount: ${sus.sale2_amount}, Payment: ${sus.sale2_payment}`);
        console.log(`      Time Difference: ${sus.time_diff_minutes} minutes`);
        console.log('      ---');
      });
    }

  } catch (error) {
    console.error('❌ Error checking DSA duplicates:', error.message);
    console.error('Full error:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 Database connection closed');
    }
  }
}

// Run the check
checkDsaDuplicatesToday();
