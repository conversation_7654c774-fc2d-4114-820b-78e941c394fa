"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Download,
  FileSpreadsheet,
  Settings,
  Zap,
  Clock,
  AlertCircle,
  CheckCircle,
} from "lucide-react";
import {
  useStockLevelsExportSuite,
  useExportRecommendation,
} from "../hooks/use-stock-levels-export";
import { StockLevelsExportParams, CustomExportParams } from "../api/stock-levels-export-service";

interface StockLevelsExportDialogProps {
  filters: any; // Current filters from the stock levels page
  totalRecords?: number;
  trigger?: React.ReactNode;
}

export function StockLevelsExportDialog({
  filters,
  totalRecords = 0,
  trigger,
}: StockLevelsExportDialogProps) {
  const [open, setOpen] = useState(false);
  const [exportType, setExportType] = useState<"all" | "custom" | "summary">("all");
  const [customOptions, setCustomOptions] = useState({
    include_summary: true,
    include_alerts: true,
    include_categories: true,
    include_branches: true,
    format_type: "detailed" as "detailed" | "summary",
    columns: "all",
  });

  const exportSuite = useStockLevelsExportSuite();
  const recommendation = useExportRecommendation(totalRecords);

  const handleExport = async () => {
    try {
      const baseParams: StockLevelsExportParams = {
        branch_id: filters.branch_id,
        region_id: filters.region_id,
        category_id: filters.category_id,
        search: filters.search,
        include_zero_stock: filters.include_zero_stock,
        include_inactive: filters.include_inactive,
        sort_by: filters.sort_by,
        sort_direction: filters.sort_direction,
      };

      switch (exportType) {
        case "all":
          await exportSuite.exportAll({
            ...baseParams,
            include_summary: true,
            include_alerts: true,
            include_categories: true,
            include_branches: true,
          });
          break;

        case "custom":
          const customParams: CustomExportParams = {
            ...baseParams,
            ...customOptions,
          };
          await exportSuite.exportCustom(customParams);
          break;

        case "summary":
          await exportSuite.exportSummary(baseParams);
          break;
      }

      setOpen(false);
    } catch (error) {
      // Error handling is done in the hooks
      console.error("Export failed:", error);
    }
  };

  const getExportIcon = (type: string) => {
    switch (type) {
      case "all":
        return <FileSpreadsheet className="h-4 w-4" />;
      case "custom":
        return <Settings className="h-4 w-4" />;
      case "summary":
        return <Zap className="h-4 w-4" />;
      default:
        return <Download className="h-4 w-4" />;
    }
  };

  const getEstimatedTime = () => {
    if (totalRecords <= 1000) return "< 30 seconds";
    if (totalRecords <= 5000) return "30-60 seconds";
    return "1-2 minutes";
  };

  const isExporting = exportSuite.isAnyExporting;

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" disabled={isExporting}>
            {isExporting ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Export to Excel
              </>
            )}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Export Stock Levels
          </DialogTitle>
          <DialogDescription>
            Choose your export format and options. Exporting {totalRecords.toLocaleString()} records.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Recommendation Banner */}
          <div className="rounded-lg border bg-blue-50 p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">Recommendation</h4>
                <p className="text-sm text-blue-700 mt-1">{recommendation.message}</p>
                <div className="flex items-center gap-2 mt-2">
                  <Clock className="h-4 w-4 text-blue-600" />
                  <span className="text-sm text-blue-700">
                    Estimated time: {recommendation.estimatedTime}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Export Type Selection */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Export Format</Label>
            <div className="grid grid-cols-1 gap-3">
              {/* Comprehensive Export */}
              <div
                className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                  exportType === "all"
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setExportType("all")}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    <input
                      type="radio"
                      checked={exportType === "all"}
                      onChange={() => setExportType("all")}
                      className="h-4 w-4"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <FileSpreadsheet className="h-4 w-4" />
                      <span className="font-medium">Comprehensive Export</span>
                      {recommendation.recommended === "all" && (
                        <Badge variant="secondary" className="text-xs">
                          Recommended
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Complete report with summary, products, categories, branches, and alerts
                    </p>
                  </div>
                </div>
              </div>

              {/* Custom Export */}
              <div
                className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                  exportType === "custom"
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setExportType("custom")}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    <input
                      type="radio"
                      checked={exportType === "custom"}
                      onChange={() => setExportType("custom")}
                      className="h-4 w-4"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Settings className="h-4 w-4" />
                      <span className="font-medium">Custom Export</span>
                      {recommendation.recommended === "custom" && (
                        <Badge variant="secondary" className="text-xs">
                          Recommended
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Select specific sheets and data to include
                    </p>
                  </div>
                </div>
              </div>

              {/* Summary Export */}
              <div
                className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                  exportType === "summary"
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                }`}
                onClick={() => setExportType("summary")}
              >
                <div className="flex items-start gap-3">
                  <div className="mt-1">
                    <input
                      type="radio"
                      checked={exportType === "summary"}
                      onChange={() => setExportType("summary")}
                      className="h-4 w-4"
                    />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4" />
                      <span className="font-medium">Summary Export</span>
                      {recommendation.recommended === "summary" && (
                        <Badge variant="secondary" className="text-xs">
                          Recommended
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mt-1">
                      Essential data only for quick analysis
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Custom Options */}
          {exportType === "custom" && (
            <>
              <Separator />
              <div className="space-y-4">
                <Label className="text-base font-medium">Custom Options</Label>
                
                {/* Format Type */}
                <div className="space-y-2">
                  <Label className="text-sm">Format Type</Label>
                  <Select
                    value={customOptions.format_type}
                    onValueChange={(value: "detailed" | "summary") =>
                      setCustomOptions({ ...customOptions, format_type: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="detailed">Detailed (All columns)</SelectItem>
                      <SelectItem value="summary">Summary (Essential columns)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Include Sheets */}
                <div className="space-y-3">
                  <Label className="text-sm">Include Sheets</Label>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_summary"
                        checked={customOptions.include_summary}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_summary: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_summary" className="text-sm">
                        Summary Sheet
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_alerts"
                        checked={customOptions.include_alerts}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_alerts: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_alerts" className="text-sm">
                        Stock Alerts
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_categories"
                        checked={customOptions.include_categories}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_categories: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_categories" className="text-sm">
                        Category Breakdown
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include_branches"
                        checked={customOptions.include_branches}
                        onCheckedChange={(checked) =>
                          setCustomOptions({
                            ...customOptions,
                            include_branches: checked as boolean,
                          })
                        }
                      />
                      <Label htmlFor="include_branches" className="text-sm">
                        Branch Breakdown
                      </Label>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Current Filters Display */}
          {(filters.branch_id || filters.region_id || filters.category_id || filters.search) && (
            <>
              <Separator />
              <div className="space-y-2">
                <Label className="text-sm font-medium">Applied Filters</Label>
                <div className="flex flex-wrap gap-2">
                  {filters.branch_id && (
                    <Badge variant="outline">Branch: {filters.branch_id}</Badge>
                  )}
                  {filters.region_id && (
                    <Badge variant="outline">Region: {filters.region_id}</Badge>
                  )}
                  {filters.category_id && (
                    <Badge variant="outline">Category: {filters.category_id}</Badge>
                  )}
                  {filters.search && (
                    <Badge variant="outline">Search: "{filters.search}"</Badge>
                  )}
                  {!filters.include_zero_stock && (
                    <Badge variant="outline">Excluding zero stock</Badge>
                  )}
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)} disabled={isExporting}>
            Cancel
          </Button>
          <Button onClick={handleExport} disabled={isExporting}>
            {isExporting ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
                Exporting...
              </>
            ) : (
              <>
                {getExportIcon(exportType)}
                <span className="ml-2">Export ({getEstimatedTime()})</span>
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
