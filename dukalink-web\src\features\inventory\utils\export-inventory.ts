import * as XLSX from "xlsx";
import { BranchInventory } from "@/types/inventory";
import { formatCurrency } from "@/lib/utils";

/**
 * Export inventory data to Excel
 * @param inventory List of inventory items to export
 * @param filename Filename for the Excel file (without extension)
 */
export function exportInventoryToExcel(
  inventory: BranchInventory[],
  filename: string = "inventory-export"
) {
  // Define headers for the Excel file
  const headers = [
    "Product Name",
    "SKU",
    "Barcode",
    "Branch",
    "Quantity",
    "Reorder Level",
    "Retail Price",
    "Wholesale Price",
    "Total Retail Value",
    "Order Number",
    "Expiry Date",
    "Last Updated"
  ];

  // Map inventory data to rows
  const rows = inventory.map(item => {
    // Get product details from either Product or product property
    const product = item.Product || item.product || {};

    // Get branch details from either Branch or branch property
    const branch = item.Branch || item.branch || {};

    // Calculate prices
    const retailPrice = parseFloat(item.default_selling_price) || parseFloat(item.selling_price) || 0;
    const wholesalePrice = parseFloat(item.default_wholesale_price) || 0;
    const totalRetailValue = retailPrice * (item.quantity || 0);

    // Format dates
    const lastUpdated = item.updated_at || item.last_updated
      ? new Date(item.updated_at || item.last_updated || "").toLocaleDateString()
      : "N/A";

    const expiryDate = item.expiry_date
      ? new Date(item.expiry_date).toLocaleDateString()
      : "N/A";

    // Return row data
    return [
      product.name || "Unknown Product",
      product.sku || "N/A",
      product.barcode || "N/A",
      branch.name || "N/A",
      item.quantity || 0,
      item.reorder_level || "N/A",
      retailPrice,
      wholesalePrice,
      totalRetailValue,
      item.batch_number || "N/A",
      expiryDate,
      lastUpdated
    ];
  });

  // Create worksheet
  const data = [headers, ...rows];
  const ws = XLSX.utils.aoa_to_sheet(data);

  // Set column widths
  const colWidths = [
    { wch: 30 },   // Product Name
    { wch: 15 },   // SKU
    { wch: 15 },   // Barcode
    { wch: 20 },   // Branch
    { wch: 10 },   // Quantity
    { wch: 15 },   // Reorder Level
    { wch: 15 },   // Retail Price
    { wch: 15 },   // Wholesale Price
    { wch: 15 },   // Total Retail Value
    { wch: 15 },   // Order Number
    { wch: 15 },   // Expiry Date
    { wch: 15 },   // Last Updated
  ];
  ws["!cols"] = colWidths;

  // Apply styles to header row
  const headerStyle = {
    font: { bold: true },
    fill: { fgColor: { rgb: "EFEFEF" } },
    alignment: { horizontal: "center", vertical: "center" }
  };

  // Apply styles to the first row (headers)
  for (let i = 0; i < headers.length; i++) {
    const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
    if (!ws[cellRef]) ws[cellRef] = {};
    ws[cellRef].s = headerStyle;
  }

  // Create workbook
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "Inventory");

  // Generate Excel file and trigger download
  XLSX.writeFile(wb, `${filename}.xlsx`);
}
