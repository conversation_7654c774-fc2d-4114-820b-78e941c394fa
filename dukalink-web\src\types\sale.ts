/**
 * Sale
 */
export interface Sale {
  id: number;
  pos_session_id: number;
  user_id: number;
  branch_id: number;
  customer_id: number;
  employee_id?: number;
  sale_employee_id?: number;
  employee_name?: string;
  sale_employee_name?: string;
  total_amount: string;
  payment_method_id: number;
  payment_reference: string | null;
  credit_partner_id: number | null;
  status: "completed" | "pending" | "cancelled";
  notes: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  is_dsa: boolean;
  sale_type?: "normal" | "dsa" | "corporate";
  created_by: number | null;
  last_updated_by: number | null;
  PosSession?: {
    id: number;
    start_time: string;
    end_time: string | null;
    status: "open" | "closed";
  };
  User?: {
    id: number;
    name: string;
    email: string | null;
    role: string | null;
  };
  Branch?: {
    id: number;
    name: string;
    location: string;
    region_id?: number;
    Region?: {
      id: number;
      name: string;
      code?: string;
    };
  };
  PaymentMethod?: {
    id: number;
    name: string;
    code: string;
    requires_reference: boolean;
  };
  CreditPartner?: {
    id: number;
    name: string;
    code: string;
    description: string | null;
    requires_reference: boolean;
  };
  Employee?: {
    id: number;
    name: string;
    email?: string | null;
    phone?: string | null;
  };
  SaleItems?: SaleItem[];
}

/**
 * Sale Item
 */
export interface SaleItem {
  id: number;
  sale_id: number;
  product_id: number;
  quantity: number;
  unit_price: string;
  total_price: string;
  buying_price: string;
  serial_number: string | null;
  created_at: string;
  updated_at: string;
  Product?: {
    id: number;
    name: string;
    sku: string;
    has_serial: boolean;
    category_id?: number;
    ProductCategory?: {
      id: number;
      name: string;
    };
  };
}

/**
 * Sale Filters
 */
export interface SaleFilters {
  branch_id?: number;
  user_id?: number;
  pos_session_id?: number;
  payment_method_id?: number;
  status?: string;
  kra_integration_status?: string;
  start_date?: string;
  end_date?: string;
  is_dsa?: boolean;
  page?: number;
  limit?: number;
}
