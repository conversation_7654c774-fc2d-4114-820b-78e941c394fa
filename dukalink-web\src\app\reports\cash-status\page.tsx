"use client";

import { MainLayout } from "@/components/layouts/main-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { BreakdownTables } from "@/features/reports/components/cash-status/breakdown-tables";
import { ReportFilters } from "@/features/reports/components/cash-status/report-filters";
import { SummaryCards } from "@/features/reports/components/cash-status/summary-cards";
import { useCashStatusReport } from "@/features/reports/hooks/useCashStatusReport";
import { exportCashStatusToExcel } from "@/lib/excel-export";
import { format } from "date-fns";
import { Download, Printer, RefreshCw } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

export default function CashStatusReportPage() {
  // Get date range for the last 30 days by default instead of current month
  const today = new Date();
  const thirtyDaysAgo = new Date(today);
  thirtyDaysAgo.setDate(today.getDate() - 30);

  // State for filters
  const [startDate, setStartDate] = useState<Date>(thirtyDaysAgo);
  const [endDate, setEndDate] = useState<Date>(today);
  const [regionId, setRegionId] = useState<number | null>(null);
  const [branchId, setBranchId] = useState<number | null>(null);
  const [view, setView] = useState<"region" | "branch" | "details">("details");

  // Pagination state
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(50);

  // Enhanced setters with logging
  const handleRegionChange = (id: number | null) => {
    console.log(`Setting regionId to: ${id}`);
    setRegionId(id);
    // Reset branch when region changes
    if (branchId !== null) {
      console.log("Resetting branchId because region changed");
      setBranchId(null);
    }
  };

  const handleBranchChange = (id: number | null) => {
    console.log(`Setting branchId to: ${id}`);
    setBranchId(id);
  };

  // Pagination handlers
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset to first page when page size changes
  };

  // Fetch report data
  const { data, isLoading, error, refetch } = useCashStatusReport({
    startDate: format(startDate, "yyyy-MM-dd"),
    endDate: format(endDate, "yyyy-MM-dd"),
    regionId,
    branchId,
    includeBreakdown: true,
    page,
    limit,
  });

  // Log filter state for debugging
  console.log("Current filters:", {
    startDate: format(startDate, "yyyy-MM-dd"),
    endDate: format(endDate, "yyyy-MM-dd"),
    regionId,
    branchId,
  });

  // Handle print
  const handlePrint = () => {
    window.print();
  };

  // Handle export
  const handleExport = () => {
    if (!data) {
      toast.error("No data available to export");
      return;
    }

    try {
      // Export the data
      const filename = exportCashStatusToExcel(data, { startDate, endDate });

      // Show success toast
      toast.success(`Exported to ${filename}`);
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      toast.error("Failed to export data. Please try again.");
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              Cash Status Report
            </h1>
            <p className="text-muted-foreground">
              View cash position and movements across all locations
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handlePrint}>
              <Printer className="mr-2 h-4 w-4" />
              Print
            </Button>
            <Button variant="outline" onClick={handleExport}>
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button
              variant="default"
              onClick={() => {
                // Force a hard refresh of the page to bypass cache
                window.location.href =
                  window.location.href.split("?")[0] +
                  "?t=" +
                  new Date().getTime();
              }}
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Force Refresh
            </Button>
          </div>
        </div>

        <ReportFilters
          startDate={startDate}
          endDate={endDate}
          regionId={regionId}
          branchId={branchId}
          onStartDateChange={setStartDate}
          onEndDateChange={setEndDate}
          onRegionChange={handleRegionChange}
          onBranchChange={handleBranchChange}
          onRefresh={refetch}
          isLoading={isLoading}
        />

        {error ? (
          <div className="bg-destructive/10 text-destructive p-4 rounded-md">
            <div className="flex items-start">
              <div className="flex-1">
                <p className="font-medium text-lg">Error loading report</p>
                <p className="text-sm mt-1">{error.message}</p>

                {/* Show helpful information for database errors */}
                {error.message.includes("Database connection") ||
                  (error.message.includes("database connection") && (
                    <div className="mt-4 border-t border-destructive/20 pt-4">
                      <p className="font-medium text-sm">
                        Database Connection Issue:
                      </p>
                      <p className="text-xs mt-2">
                        The application is unable to connect to the database.
                        This could be due to:
                      </p>
                      <ul className="list-disc list-inside mt-1 text-xs">
                        <li>The database server is not running</li>
                        <li>Database credentials are incorrect</li>
                        <li>Network connectivity issues</li>
                        <li>The backend server needs to be restarted</li>
                      </ul>
                      <p className="text-xs mt-2 font-medium">
                        Recommended actions:
                      </p>
                      <ul className="list-disc list-inside mt-1 text-xs">
                        <li>Contact your system administrator</li>
                        <li>Check if the backend server is running</li>
                        <li>Try restarting the backend server</li>
                        <li>Verify database connection settings</li>
                      </ul>
                    </div>
                  ))}
              </div>
              <div className="ml-4">
                <Button onClick={() => refetch()} className="px-4 py-2">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <>
            <SummaryCards
              summary={data?.summary || null}
              isLoading={isLoading}
            />

            <Tabs
              value={view}
              onValueChange={(v) => setView(v as any)}
              className="mt-6"
            >
              <TabsList>
                <TabsTrigger value="details">Detailed Breakdown</TabsTrigger>
                <TabsTrigger value="region" disabled={!data?.byRegion?.length}>
                  By Region
                </TabsTrigger>
                <TabsTrigger value="branch" disabled={!data?.byBranch?.length}>
                  By Branch
                </TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="mt-6">
                <BreakdownTables
                  data={data}
                  view="details"
                  isLoading={isLoading}
                  pagination={{
                    page,
                    limit,
                    onPageChange: handlePageChange,
                    onPageSizeChange: handlePageSizeChange,
                  }}
                />
              </TabsContent>

              <TabsContent value="region" className="mt-6">
                <BreakdownTables
                  data={data}
                  view="region"
                  isLoading={isLoading}
                  pagination={{
                    page,
                    limit,
                    onPageChange: handlePageChange,
                    onPageSizeChange: handlePageSizeChange,
                  }}
                />
              </TabsContent>

              <TabsContent value="branch" className="mt-6">
                <BreakdownTables
                  data={data}
                  view="branch"
                  isLoading={isLoading}
                  pagination={{
                    page,
                    limit,
                    onPageChange: handlePageChange,
                    onPageSizeChange: handlePageSizeChange,
                  }}
                />
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </MainLayout>
  );
}
