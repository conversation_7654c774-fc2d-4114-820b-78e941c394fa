const {
  StockItem,
  Product,
  ProductCategory,
  Branch,
  Brand,
  Region,
} = require("../../models");
const { Op } = require("sequelize");
const sequelize = require("../../../config/database");
const AppError = require("../../utils/error");
const logger = require("../../utils/logger");
const ExcelJS = require("exceljs");

/**
 * Stock Levels Controller
 * Provides comprehensive stock levels reporting with analytics and insights
 */
class StockLevelsController {
  /**
   * Get comprehensive stock levels report
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async getStockLevels(req, res, next) {
    try {
      const {
        branch_id,
        region_id,
        category_id,
        include_zero_stock = "true",
        include_inactive = "false",
        low_stock_threshold,
        page = 1,
        limit = 100,
        search,
        sort_by = "name",
        sort_direction = "asc",
        format = "json",
        export_all = "false",
      } = req.query;

      // For Excel export, handle unlimited data if export_all is true
      let pageInt, limitInt, offset;

      if (format === "excel" && export_all === "true") {
        // For export all, we don't use pagination
        pageInt = 1;
        limitInt = null; // No limit for export all
        offset = 0;
      } else {
        // Validate parameters for normal pagination
        pageInt = parseInt(page);
        limitInt = Math.min(parseInt(limit), 500); // Max 500 as per spec
        offset = (pageInt - 1) * limitInt;
      }

      // Build filters object for response
      const filters = {
        branch_id: branch_id ? parseInt(branch_id) : null,
        region_id: region_id ? parseInt(region_id) : null,
        category_id: category_id ? parseInt(category_id) : null,
        include_zero_stock: include_zero_stock === "true",
        include_inactive: include_inactive === "true",
        low_stock_threshold: low_stock_threshold
          ? parseInt(low_stock_threshold)
          : null,
        search: search || null,
        sort_by,
        sort_direction,
      };

      // Get summary metrics
      const summary =
        await StockLevelsController.calculateSummaryMetrics(filters);

      // Get category breakdown
      const by_category =
        await StockLevelsController.getCategoryBreakdown(filters);

      // Get branch breakdown
      const by_branch = await StockLevelsController.getBranchBreakdown(filters);

      // Get stock alerts
      const stock_alerts = await StockLevelsController.getStockAlerts(filters);

      // Get detailed products with pagination
      const { products, totalCount } =
        await StockLevelsController.getDetailedProducts(
          filters,
          offset,
          limitInt
        );

      // Calculate pagination info
      const pagination = {
        page: pageInt,
        limit: limitInt,
        total: totalCount,
        pages: Math.ceil(totalCount / limitInt),
        has_next: pageInt < Math.ceil(totalCount / limitInt),
        has_prev: pageInt > 1,
      };

      const responseData = {
        status: "success",
        filters,
        summary,
        by_category,
        by_branch,
        stock_alerts,
        products,
        pagination,
      };

      // Handle Excel export
      if (format === "excel") {
        return await StockLevelsController.exportToExcel(responseData, res);
      }

      res.json(responseData);
    } catch (error) {
      logger.error("Error getting stock levels:", error);
      next(error);
    }
  }

  /**
   * Calculate summary metrics
   * @param {Object} filters - Applied filters
   * @returns {Object} Summary metrics
   */
  static async calculateSummaryMetrics(filters) {
    try {
      const stockItemWhere =
        StockLevelsController.buildStockItemWhereClause(filters);
      const productWhere =
        StockLevelsController.buildProductWhereClause(filters);

      // Get total products count
      const totalProducts = await StockItem.count({
        where: stockItemWhere,
        include: [
          {
            model: Product,
            where: productWhere,
            required: true,
          },
        ],
        distinct: true,
      });

      // Get total value and quantity using raw query for better performance
      const valueAndQuantityResult = await sequelize.query(
        `
        SELECT
          COALESCE(SUM(si.quantity), 0) as total_quantity,
          COALESCE(SUM(si.quantity * si.default_buying_price), 0) as total_value
        FROM stock_items si
        INNER JOIN products p ON si.product_id = p.id
        ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
        WHERE 1=1
          ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
          ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
      `,
        { type: sequelize.QueryTypes.SELECT }
      );

      const totalQuantity = parseInt(
        valueAndQuantityResult[0]?.total_quantity || 0
      );
      const totalValue = parseFloat(
        valueAndQuantityResult[0]?.total_value || 0
      );

      // Get stock status counts
      const stockStatusResult = await sequelize.query(
        `
        SELECT
          COUNT(CASE WHEN si.quantity > COALESCE(si.reorder_level, 0) THEN 1 END) as in_stock_count,
          COUNT(CASE WHEN si.quantity > 0 AND si.quantity <= COALESCE(si.reorder_level, 0) THEN 1 END) as low_stock_count,
          COUNT(CASE WHEN si.quantity = 0 THEN 1 END) as out_of_stock_count
        FROM stock_items si
        INNER JOIN products p ON si.product_id = p.id
        ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
        WHERE 1=1
          ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
      `,
        { type: sequelize.QueryTypes.SELECT }
      );

      const inStockCount = parseInt(stockStatusResult[0]?.in_stock_count || 0);
      const lowStockCount = parseInt(
        stockStatusResult[0]?.low_stock_count || 0
      );
      const outOfStockCount = parseInt(
        stockStatusResult[0]?.out_of_stock_count || 0
      );

      // Get categories count
      let categoriesCount = 0;
      if (!filters.category_id) {
        const categoriesResult = await sequelize.query(
          `
          SELECT COUNT(DISTINCT p.category_id) as categories_count
          FROM stock_items si
          INNER JOIN products p ON si.product_id = p.id
          ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
          WHERE 1=1
            ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
            ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
            ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
            ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
            ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
            AND p.category_id IS NOT NULL
        `,
          { type: sequelize.QueryTypes.SELECT }
        );
        categoriesCount = parseInt(categoriesResult[0]?.categories_count || 0);
      } else {
        categoriesCount = 1; // If filtering by specific category
      }

      // Get branches count
      let branchesCount = 0;
      if (!filters.branch_id) {
        const branchesResult = await sequelize.query(
          `
          SELECT COUNT(DISTINCT si.branch_id) as branches_count
          FROM stock_items si
          INNER JOIN products p ON si.product_id = p.id
          ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
          WHERE 1=1
            ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
            ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
            ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
            ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
            ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
        `,
          { type: sequelize.QueryTypes.SELECT }
        );
        branchesCount = parseInt(branchesResult[0]?.branches_count || 0);
      } else {
        branchesCount = 1; // If filtering by specific branch
      }

      return {
        total_products: totalProducts,
        total_value: totalValue,
        total_quantity: totalQuantity,
        in_stock_count: inStockCount,
        low_stock_count: lowStockCount,
        out_of_stock_count: outOfStockCount,
        categories_count: categoriesCount,
        branches_count: branchesCount,
        last_updated: new Date().toISOString(),
      };
    } catch (error) {
      logger.error("Error calculating summary metrics:", error);
      throw error;
    }
  }

  /**
   * Get category breakdown
   * @param {Object} filters - Applied filters
   * @returns {Array} Category breakdown data
   */
  static async getCategoryBreakdown(filters) {
    try {
      // If filtering by specific category, return breakdown for that category only
      if (filters.category_id) {
        const categoryResult = await sequelize.query(
          `
          SELECT
            pc.id as category_id,
            pc.name as category_name,
            COUNT(DISTINCT si.product_id) as product_count,
            COALESCE(SUM(si.quantity), 0) as total_quantity,
            COALESCE(SUM(si.quantity * si.default_buying_price), 0) as total_value,
            COUNT(CASE WHEN si.quantity > 0 AND si.quantity <= COALESCE(si.reorder_level, 0) THEN 1 END) as low_stock_count,
            COUNT(CASE WHEN si.quantity = 0 THEN 1 END) as out_of_stock_count
          FROM product_categories pc
          INNER JOIN products p ON pc.id = p.category_id
          INNER JOIN stock_items si ON p.id = si.product_id
          ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
          WHERE pc.id = ${filters.category_id}
            ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
            ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
            ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
            ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
            ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
          GROUP BY pc.id, pc.name
        `,
          { type: sequelize.QueryTypes.SELECT }
        );

        return categoryResult.map((cat) => ({
          category_id: cat.category_id,
          category_name: cat.category_name,
          product_count: parseInt(cat.product_count),
          total_quantity: parseInt(cat.total_quantity),
          total_value: parseFloat(cat.total_value),
          low_stock_count: parseInt(cat.low_stock_count),
          out_of_stock_count: parseInt(cat.out_of_stock_count),
        }));
      }

      // Get breakdown for all categories
      const categoryBreakdown = await sequelize.query(
        `
        SELECT
          pc.id as category_id,
          pc.name as category_name,
          COUNT(DISTINCT si.product_id) as product_count,
          COALESCE(SUM(si.quantity), 0) as total_quantity,
          COALESCE(SUM(si.quantity * si.default_buying_price), 0) as total_value,
          COUNT(CASE WHEN si.quantity > 0 AND si.quantity <= COALESCE(si.reorder_level, 0) THEN 1 END) as low_stock_count,
          COUNT(CASE WHEN si.quantity = 0 THEN 1 END) as out_of_stock_count
        FROM product_categories pc
        INNER JOIN products p ON pc.id = p.category_id
        INNER JOIN stock_items si ON p.id = si.product_id
        ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
        WHERE 1=1
          ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
        GROUP BY pc.id, pc.name
        ORDER BY pc.name ASC
      `,
        { type: sequelize.QueryTypes.SELECT }
      );

      return categoryBreakdown.map((cat) => ({
        category_id: cat.category_id,
        category_name: cat.category_name,
        product_count: parseInt(cat.product_count),
        total_quantity: parseInt(cat.total_quantity),
        total_value: parseFloat(cat.total_value),
        low_stock_count: parseInt(cat.low_stock_count),
        out_of_stock_count: parseInt(cat.out_of_stock_count),
      }));
    } catch (error) {
      logger.error("Error getting category breakdown:", error);
      throw error;
    }
  }

  /**
   * Get branch breakdown
   * @param {Object} filters - Applied filters
   * @returns {Array} Branch breakdown data
   */
  static async getBranchBreakdown(filters) {
    try {
      // If filtering by specific branch, return breakdown for that branch only
      if (filters.branch_id) {
        const branchResult = await sequelize.query(
          `
          SELECT
            b.id as branch_id,
            b.name as branch_name,
            b.location as branch_location,
            COUNT(DISTINCT si.product_id) as product_count,
            COALESCE(SUM(si.quantity), 0) as total_quantity,
            COALESCE(SUM(si.quantity * si.default_buying_price), 0) as total_value,
            COUNT(CASE WHEN si.quantity > 0 AND si.quantity <= COALESCE(si.reorder_level, 0) THEN 1 END) as low_stock_count,
            COUNT(CASE WHEN si.quantity = 0 THEN 1 END) as out_of_stock_count
          FROM branches b
          INNER JOIN stock_items si ON b.id = si.branch_id
          INNER JOIN products p ON si.product_id = p.id
          WHERE b.id = ${filters.branch_id}
            ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
            ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
            ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
            ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
          GROUP BY b.id, b.name, b.location
        `,
          { type: sequelize.QueryTypes.SELECT }
        );

        return branchResult.map((branch) => ({
          branch_id: branch.branch_id,
          branch_name: branch.branch_name,
          branch_location: branch.branch_location || "",
          product_count: parseInt(branch.product_count),
          total_quantity: parseInt(branch.total_quantity),
          total_value: parseFloat(branch.total_value),
          low_stock_count: parseInt(branch.low_stock_count),
          out_of_stock_count: parseInt(branch.out_of_stock_count),
        }));
      }

      // Get breakdown for all branches (filtered by region if specified)
      const branchBreakdown = await sequelize.query(
        `
        SELECT
          b.id as branch_id,
          b.name as branch_name,
          b.location as branch_location,
          COUNT(DISTINCT si.product_id) as product_count,
          COALESCE(SUM(si.quantity), 0) as total_quantity,
          COALESCE(SUM(si.quantity * si.default_buying_price), 0) as total_value,
          COUNT(CASE WHEN si.quantity > 0 AND si.quantity <= COALESCE(si.reorder_level, 0) THEN 1 END) as low_stock_count,
          COUNT(CASE WHEN si.quantity = 0 THEN 1 END) as out_of_stock_count
        FROM branches b
        INNER JOIN stock_items si ON b.id = si.branch_id
        INNER JOIN products p ON si.product_id = p.id
        WHERE 1=1
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
          ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
        GROUP BY b.id, b.name, b.location
        ORDER BY b.name ASC
      `,
        { type: sequelize.QueryTypes.SELECT }
      );

      return branchBreakdown.map((branch) => ({
        branch_id: branch.branch_id,
        branch_name: branch.branch_name,
        branch_location: branch.branch_location || "",
        product_count: parseInt(branch.product_count),
        total_quantity: parseInt(branch.total_quantity),
        total_value: parseFloat(branch.total_value),
        low_stock_count: parseInt(branch.low_stock_count),
        out_of_stock_count: parseInt(branch.out_of_stock_count),
      }));
    } catch (error) {
      logger.error("Error getting branch breakdown:", error);
      throw error;
    }
  }

  /**
   * Get stock alerts
   * @param {Object} filters - Applied filters
   * @returns {Object} Stock alerts data
   */
  static async getStockAlerts(filters) {
    try {
      // Get critical low stock items (quantity > 0 but <= reorder level)
      const criticalLowStockQuery = `
        SELECT
          p.id as product_id,
          p.name as product_name,
          p.sku,
          si.quantity as current_quantity,
          COALESCE(si.reorder_level, 0) as min_stock_level,
          b.name as branch_name,
          b.location as branch_location,
          si.updated_at as last_updated
        FROM stock_items si
        INNER JOIN products p ON si.product_id = p.id
        INNER JOIN branches b ON si.branch_id = b.id
        WHERE si.quantity > 0
          AND si.quantity <= COALESCE(si.reorder_level, 0)
          AND si.reorder_level > 0
          ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
        ORDER BY (si.quantity / NULLIF(si.reorder_level, 0)) ASC, p.name ASC
        LIMIT 50
      `;

      const criticalLowStock = await sequelize.query(criticalLowStockQuery, {
        type: sequelize.QueryTypes.SELECT,
      });

      // Get out of stock items
      const outOfStockQuery = `
        SELECT
          p.id as product_id,
          p.name as product_name,
          p.sku,
          b.name as branch_name,
          b.location as branch_location,
          si.updated_at as last_updated,
          (
            SELECT MAX(s.created_at)
            FROM sales s
            INNER JOIN sale_items sit ON s.id = sit.sale_id
            WHERE sit.product_id = p.id AND s.branch_id = si.branch_id
          ) as last_sale_date
        FROM stock_items si
        INNER JOIN products p ON si.product_id = p.id
        INNER JOIN branches b ON si.branch_id = b.id
        WHERE si.quantity = 0
          ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
        ORDER BY si.updated_at DESC, p.name ASC
        LIMIT 50
      `;

      const outOfStock = await sequelize.query(outOfStockQuery, {
        type: sequelize.QueryTypes.SELECT,
      });

      // Format critical low stock alerts
      const formattedCriticalLowStock = criticalLowStock.map((item) => ({
        product_id: item.product_id,
        product_name: item.product_name,
        sku: item.sku,
        current_quantity: item.current_quantity,
        min_stock_level: item.min_stock_level,
        branch_name: item.branch_name,
        branch_location: item.branch_location || "",
        urgency_level:
          item.current_quantity === 0
            ? "critical"
            : item.current_quantity <= item.min_stock_level * 0.5
              ? "high"
              : "medium",
        last_updated: item.last_updated,
      }));

      // Format out of stock alerts
      const formattedOutOfStock = outOfStock.map((item) => ({
        product_id: item.product_id,
        product_name: item.product_name,
        sku: item.sku,
        branch_name: item.branch_name,
        branch_location: item.branch_location || "",
        last_sale_date: item.last_sale_date,
        days_out_of_stock: item.last_updated
          ? Math.floor(
              (new Date() - new Date(item.last_updated)) / (1000 * 60 * 60 * 24)
            )
          : null,
        last_updated: item.last_updated,
      }));

      return {
        critical_low_stock: formattedCriticalLowStock,
        out_of_stock: formattedOutOfStock,
      };
    } catch (error) {
      logger.error("Error getting stock alerts:", error);
      throw error;
    }
  }

  /**
   * Get detailed products with pagination
   * @param {Object} filters - Applied filters
   * @param {number} offset - Pagination offset
   * @param {number} limit - Pagination limit
   * @returns {Object} Products data and total count
   */
  static async getDetailedProducts(filters, offset, limit) {
    try {
      const stockItemWhere =
        StockLevelsController.buildStockItemWhereClause(filters);
      const productWhere =
        StockLevelsController.buildProductWhereClause(filters);
      const sortOrder = StockLevelsController.getSortOrder(
        filters.sort_by,
        filters.sort_direction
      );

      // Get stock items with detailed product information
      const { rows: stockItems, count: totalCount } =
        await StockItem.findAndCountAll({
          where: stockItemWhere,
          include: [
            {
              model: Product,
              where: productWhere,
              required: true,
              include: [
                {
                  model: ProductCategory,
                  attributes: ["id", "name", "description", "parent_id"],
                },
                {
                  model: Brand,
                  attributes: ["id", "name"],
                },
              ],
            },
            {
              model: Branch,
              attributes: ["id", "name", "location", "region_id"],
              where: filters.region_id ? { region_id: filters.region_id } : {},
              required: true,
              include: [
                {
                  model: Region,
                  attributes: ["id", "name"],
                  required: false,
                },
              ],
            },
          ],
          order: sortOrder,
          offset,
          limit,
          distinct: true,
        });

      // Format products according to specification
      const products = stockItems.map((stockItem) => {
        const product = stockItem.Product;
        const category = product.ProductCategory;
        const brand = product.Brand;
        const branch = stockItem.Branch;

        // Calculate stock status
        const currentQuantity = stockItem.quantity || 0;
        const minStockLevel = stockItem.reorder_level || 0;
        const lowStockThreshold = filters.low_stock_threshold || minStockLevel;

        let stockStatus = "in_stock";
        if (currentQuantity === 0) {
          stockStatus = "out_of_stock";
        } else if (currentQuantity <= lowStockThreshold) {
          stockStatus = "low_stock";
        }

        // Calculate pricing and values
        const buyingPrice = parseFloat(stockItem.default_buying_price || 0);
        const sellingPrice = parseFloat(stockItem.default_selling_price || 0);
        const totalValue = currentQuantity * buyingPrice;
        const marginPercentage =
          sellingPrice > 0
            ? ((sellingPrice - buyingPrice) / sellingPrice) * 100
            : 0;

        return {
          id: product.id,
          name: product.name,
          sku: product.sku,
          barcode: product.barcode,
          category: category
            ? {
                id: category.id,
                name: category.name,
                parent_category: category.parent_id ? "Parent Category" : null,
              }
            : null,
          brand: brand
            ? {
                id: brand.id,
                name: brand.name,
              }
            : null,
          stock_info: {
            current_quantity: currentQuantity,
            min_stock_level: minStockLevel,
            max_stock_level: stockItem.max_stock_level || null,
            reorder_point: stockItem.reorder_level || 0,
            stock_status: stockStatus,
            days_of_stock: null, // Will be calculated in future enhancement
            last_restocked: stockItem.updated_at,
          },
          pricing: {
            buying_price: buyingPrice,
            selling_price: sellingPrice,
            margin_percentage: marginPercentage.toFixed(2),
            total_value: totalValue.toFixed(2),
          },
          location: {
            branch_id: branch.id,
            branch_name: branch.name,
            branch_location: branch.location,
            region_id: branch.region_id,
            region_name: branch.Region?.name || null,
            warehouse_location: stockItem.warehouse_location || null,
          },
          movement_summary: {
            sales_last_30_days: null, // Will be implemented in future enhancement
            purchases_last_30_days: null,
            adjustments_last_30_days: null,
            transfers_in_last_30_days: null,
            transfers_out_last_30_days: null,
          },
          product_details: {
            is_active: product.is_active,
            is_serialized: product.has_serial || false,
            has_expiry: product.has_expiry || false,
            weight: product.weight || null,
            dimensions: product.dimensions || null,
            supplier: product.supplier || null,
            created_at: product.created_at,
            updated_at: product.updated_at,
          },
        };
      });

      return {
        products,
        totalCount,
      };
    } catch (error) {
      logger.error("Error getting detailed products:", error);
      throw error;
    }
  }

  /**
   * Export data to Excel
   * @param {Object} data - Response data
   * @param {Object} res - Express response object
   */
  static async exportToExcel(data, res) {
    try {
      const workbook = new ExcelJS.Workbook();

      // Products Sheet (only sheet as requested)
      const productsSheet = workbook.addWorksheet("Products");
      productsSheet.columns = [
        { header: "Product ID", key: "id", width: 12 },
        { header: "Name", key: "name", width: 30 },
        { header: "SKU", key: "sku", width: 15 },
        { header: "Category", key: "category", width: 20 },
        { header: "Brand", key: "brand", width: 15 },
        { header: "Current Qty", key: "quantity", width: 12 },
        { header: "Stock Status", key: "status", width: 15 },
        { header: "Buying Price", key: "buying_price", width: 15 },
        { header: "Selling Price", key: "selling_price", width: 15 },
        { header: "Total Value", key: "total_value", width: 15 },
        { header: "Branch", key: "branch", width: 20 },
        { header: "Region", key: "region", width: 20 },
        { header: "Min Stock Level", key: "min_stock", width: 15 },
      ];

      const productRows = data.products.map((product) => ({
        id: product.id,
        name: product.name,
        sku: product.sku,
        category: product.category?.name || "N/A",
        brand: product.brand?.name || "N/A",
        quantity: product.stock_info.current_quantity,
        status: product.stock_info.stock_status,
        buying_price: product.pricing.buying_price.toFixed(2),
        selling_price: product.pricing.selling_price.toFixed(2),
        total_value: parseFloat(product.pricing.total_value).toFixed(2),
        branch: product.location.branch_name,
        region: product.location.region_name || "N/A",
        min_stock: product.stock_info.min_stock_level,
      }));

      productsSheet.addRows(productRows);
      productsSheet.getRow(1).font = { bold: true };

      // Set response headers
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=stock-levels-${new Date().toISOString().split("T")[0]}.xlsx`
      );

      // Write to response
      await workbook.xlsx.write(res);
      res.end();
    } catch (error) {
      logger.error("Error exporting to Excel:", error);
      throw new AppError("Failed to export to Excel", 500);
    }
  }

  /**
   * Build where clause for stock items
   * @param {Object} filters - Applied filters
   * @returns {Object} Sequelize where clause
   */
  static buildStockItemWhereClause(filters) {
    const whereClause = {};

    if (filters.branch_id) {
      whereClause.branch_id = filters.branch_id;
    }

    // Region filtering will be handled in the include clause for Branch model
    // since we need to join with branches table

    if (!filters.include_zero_stock) {
      whereClause.quantity = { [Op.gt]: 0 };
    }

    return whereClause;
  }

  /**
   * Build where clause for products
   * @param {Object} filters - Applied filters
   * @returns {Object} Sequelize where clause
   */
  static buildProductWhereClause(filters) {
    const whereClause = {};

    if (filters.category_id) {
      whereClause.category_id = filters.category_id;
    }

    if (!filters.include_inactive) {
      whereClause.is_active = true;
    }

    if (filters.search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${filters.search}%` } },
        { sku: { [Op.like]: `%${filters.search}%` } },
      ];
    }

    return whereClause;
  }

  /**
   * Get sort order for queries
   * @param {string} sort_by - Sort field
   * @param {string} sort_direction - Sort direction
   * @returns {Array} Sequelize order clause
   */
  static getSortOrder(sort_by, sort_direction) {
    const direction = sort_direction.toUpperCase() === "DESC" ? "DESC" : "ASC";

    switch (sort_by) {
      case "quantity":
        return [["quantity", direction]];
      case "value":
        return [
          [sequelize.literal("(quantity * default_selling_price)"), direction],
        ];
      case "category":
        return [
          [
            { model: Product, as: "Product" },
            { model: ProductCategory },
            "name",
            direction,
          ],
        ];
      case "name":
      default:
        return [[{ model: Product, as: "Product" }, "name", direction]];
    }
  }
}

module.exports = StockLevelsController;
