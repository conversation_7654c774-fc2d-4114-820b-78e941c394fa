import apiClient from "@/lib/api-client";

/**
 * MPESA Transactions Export Service
 * Provides methods for comprehensive Excel export functionality for MPESA transactions data
 */

export interface MpesaTransactionsExportParams {
  start_date?: string;
  end_date?: string;
  branch_id?: number;
  region_id?: number;
  transaction_type?: string;
  status?: string;
  mpesa_code?: string;
  include_summary?: boolean;
  include_details?: boolean;
  include_daily_breakdown?: boolean;
  include_branch_breakdown?: boolean;
  include_type_breakdown?: boolean;
  include_status_breakdown?: boolean;
  include_charts?: boolean;
}

export interface CustomMpesaTransactionsExportParams extends MpesaTransactionsExportParams {
  columns?: string; // comma-separated list or "all"
  format_type?: 'detailed' | 'summary';
}

const mpesaTransactionsExportService = {
  /**
   * Export all MPESA transactions data with comprehensive sheets
   */
  exportAllMpesaTransactions: async (params?: MpesaTransactionsExportParams): Promise<Blob> => {
    try {
      console.log("Starting comprehensive MPESA transactions export...", params);

      const response: any = await apiClient.get("/reports/mpesa-transactions/export/all", {
        params: {
          ...params,
          include_summary: params?.include_summary ?? true,
          include_details: params?.include_details ?? true,
          include_daily_breakdown: params?.include_daily_breakdown ?? true,
          include_branch_breakdown: params?.include_branch_breakdown ?? true,
          include_type_breakdown: params?.include_type_breakdown ?? true,
          include_status_breakdown: params?.include_status_breakdown ?? true,
          include_charts: params?.include_charts ?? true,
        },
        responseType: "blob",
        timeout: 60000, // 1 minute timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting all MPESA transactions:", error);
      
      // Provide more specific error messages
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export MPESA reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. Try filtering the data or use summary export.");
      } else {
        throw new Error(error.message || "Failed to export MPESA transactions");
      }
    }
  },

  /**
   * Export MPESA transactions with custom options
   */
  exportCustomMpesaTransactions: async (params?: CustomMpesaTransactionsExportParams): Promise<Blob> => {
    try {
      console.log("Starting custom MPESA transactions export...", params);

      const response: any = await apiClient.get("/reports/mpesa-transactions/export/custom", {
        params: {
          ...params,
          columns: params?.columns || "all",
          format_type: params?.format_type || "detailed",
        },
        responseType: "blob",
        timeout: 45000, // 45 seconds timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting custom MPESA transactions:", error);
      
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export MPESA reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. Try using summary format or filtering the data.");
      } else {
        throw new Error(error.message || "Failed to export custom MPESA transactions");
      }
    }
  },

  /**
   * Export lightweight MPESA transactions data (fastest option)
   */
  exportLightweightMpesaTransactions: async (params?: MpesaTransactionsExportParams): Promise<Blob> => {
    try {
      console.log("Starting lightweight MPESA transactions export...", params);

      const response: any = await apiClient.get("/reports/mpesa-transactions/export/lightweight", {
        params: {
          ...params,
        },
        responseType: "blob",
        timeout: 20000, // 20 seconds timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting lightweight MPESA transactions:", error);
      
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export MPESA reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. The system may be busy, please try again.");
      } else {
        throw new Error(error.message || "Failed to export lightweight MPESA transactions");
      }
    }
  },

  /**
   * Download blob as file with proper filename
   */
  downloadBlob: (blob: Blob, filename?: string): void => {
    try {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Generate filename if not provided
      if (!filename) {
        const timestamp = new Date().toISOString().split('T')[0];
        filename = `mpesa-transactions-export-${timestamp}.xlsx`;
      }
      
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      console.log(`File downloaded: ${filename}`);
    } catch (error) {
      console.error('Error downloading file:', error);
      throw new Error('Failed to download the export file');
    }
  },

  /**
   * Generate descriptive filename based on filters
   */
  generateFilename: (params?: MpesaTransactionsExportParams, formatType: string = 'comprehensive'): string => {
    const timestamp = new Date().toISOString().split('T')[0];
    const parts = ['mpesa-transactions'];
    
    if (formatType !== 'comprehensive') {
      parts.push(formatType);
    }
    
    if (params?.branch_id) {
      parts.push(`branch-${params.branch_id}`);
    }
    
    if (params?.region_id) {
      parts.push(`region-${params.region_id}`);
    }
    
    if (params?.transaction_type) {
      parts.push(`type-${params.transaction_type}`);
    }
    
    if (params?.status) {
      parts.push(`status-${params.status}`);
    }
    
    if (params?.start_date && params?.end_date) {
      parts.push(`${params.start_date}-to-${params.end_date}`);
    }
    
    parts.push(timestamp);
    
    return `${parts.join('-')}.xlsx`;
  },

  /**
   * Estimate export size and provide recommendations
   */
  getExportRecommendation: (estimatedRecords: number): {
    recommended: 'all' | 'custom' | 'lightweight';
    message: string;
    estimatedTime: string;
  } => {
    if (estimatedRecords <= 1000) {
      return {
        recommended: 'all',
        message: 'Small dataset - comprehensive export recommended',
        estimatedTime: '< 30 seconds'
      };
    } else if (estimatedRecords <= 5000) {
      return {
        recommended: 'custom',
        message: 'Medium dataset - custom export with selected sheets recommended',
        estimatedTime: '30-60 seconds'
      };
    } else {
      return {
        recommended: 'lightweight',
        message: 'Large dataset - lightweight export recommended for faster processing',
        estimatedTime: '< 30 seconds'
      };
    }
  },

  /**
   * Validate export parameters
   */
  validateExportParams: (params?: MpesaTransactionsExportParams): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (params?.branch_id && (params.branch_id < 1 || !Number.isInteger(params.branch_id))) {
      errors.push('Branch ID must be a positive integer');
    }
    
    if (params?.region_id && (params.region_id < 1 || !Number.isInteger(params.region_id))) {
      errors.push('Region ID must be a positive integer');
    }
    
    if (params?.start_date && params?.end_date) {
      const fromDate = new Date(params.start_date);
      const toDate = new Date(params.end_date);
      if (fromDate > toDate) {
        errors.push('Start date must be before end date');
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Get export format options for UI
   */
  getExportFormatOptions: () => [
    {
      value: 'lightweight',
      label: 'Quick Export',
      description: 'MPESA transaction summary data only - fastest option',
      icon: 'Zap',
      estimatedTime: '< 30 seconds'
    },
    {
      value: 'custom',
      label: 'Custom Export',
      description: 'Select specific sheets and data to include',
      icon: 'Settings',
      estimatedTime: '30-60 seconds'
    },
    {
      value: 'all',
      label: 'Comprehensive Export',
      description: 'All MPESA data with summary, details, and breakdowns',
      icon: 'FileSpreadsheet',
      estimatedTime: '60-90 seconds'
    }
  ],

  /**
   * Get available column options for custom export
   */
  getColumnOptions: () => [
    { value: 'summary', label: 'Transaction Summary', category: 'basic' },
    { value: 'transaction_details', label: 'Transaction Details', category: 'basic' },
    { value: 'daily_breakdown', label: 'Daily Breakdown', category: 'analysis' },
    { value: 'branch_breakdown', label: 'Branch Breakdown', category: 'analysis' },
    { value: 'type_breakdown', label: 'Type Breakdown', category: 'analysis' },
    { value: 'status_breakdown', label: 'Status Breakdown', category: 'analysis' },
    { value: 'branch_info', label: 'Branch Information', category: 'location' },
    { value: 'region_info', label: 'Region Information', category: 'location' },
    { value: 'user_info', label: 'User Information', category: 'details' },
    { value: 'mpesa_codes', label: 'MPESA Code Details', category: 'details' },
    { value: 'amounts', label: 'Amount Details', category: 'financial' },
    { value: 'dates', label: 'Date Information', category: 'details' },
  ]
};

export default mpesaTransactionsExportService;
