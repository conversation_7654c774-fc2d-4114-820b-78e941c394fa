const ExcelJS = require("exceljs");
const { Op } = require("sequelize");
const sequelize = require("../../../config/database");
const { StockItem, Product, ProductCategory, Branch, Brand, Region } = require("../../models");
const AppError = require("../../utils/error");
const logger = require("../../utils/logger");

/**
 * Stock Levels Export Controller
 * Dedicated controller for Excel export functionality with enhanced features
 */
class StockLevelsExportController {
  /**
   * Export all stock levels to Excel with comprehensive data
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportAllStockLevels(req, res, next) {
    try {
      const {
        branch_id,
        region_id,
        category_id,
        include_zero_stock = "true",
        include_inactive = "false",
        search,
        sort_by = "name",
        sort_direction = "asc",
        include_summary = "true",
        include_alerts = "true",
        include_categories = "true",
        include_branches = "true",
      } = req.query;

      // Build filters
      const filters = {
        branch_id: branch_id ? parseInt(branch_id) : null,
        region_id: region_id ? parseInt(region_id) : null,
        category_id: category_id ? parseInt(category_id) : null,
        include_zero_stock: include_zero_stock === "true",
        include_inactive: include_inactive === "true",
        search: search || null,
        sort_by,
        sort_direction,
      };

      logger.info("Starting comprehensive stock levels export", { filters });

      // Get all data without pagination for export
      const exportData = await StockLevelsExportController.getAllExportData(filters);

      // Create Excel workbook with multiple sheets
      const workbook = await StockLevelsExportController.createComprehensiveWorkbook(
        exportData,
        {
          include_summary: include_summary === "true",
          include_alerts: include_alerts === "true",
          include_categories: include_categories === "true",
          include_branches: include_branches === "true",
        }
      );

      // Generate filename with filters
      const filename = StockLevelsExportController.generateFilename(filters);

      // Set response headers
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader("Content-Disposition", `attachment; filename=${filename}`);

      // Write to response
      await workbook.xlsx.write(res);
      res.end();

      logger.info("Stock levels export completed successfully", {
        filename,
        productCount: exportData.products.length,
      });
    } catch (error) {
      logger.error("Error exporting stock levels:", error);
      next(error);
    }
  }

  /**
   * Export stock levels with custom options
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  static async exportCustomStockLevels(req, res, next) {
    try {
      const {
        branch_id,
        region_id,
        category_id,
        include_zero_stock = "true",
        include_inactive = "false",
        search,
        sort_by = "name",
        sort_direction = "asc",
        columns = "all", // comma-separated list of columns or "all"
        format_type = "detailed", // "detailed" or "summary"
      } = req.query;

      // Build filters
      const filters = {
        branch_id: branch_id ? parseInt(branch_id) : null,
        region_id: region_id ? parseInt(region_id) : null,
        category_id: category_id ? parseInt(category_id) : null,
        include_zero_stock: include_zero_stock === "true",
        include_inactive: include_inactive === "true",
        search: search || null,
        sort_by,
        sort_direction,
      };

      logger.info("Starting custom stock levels export", { filters, columns, format_type });

      // Get data based on format type
      const exportData = await StockLevelsExportController.getAllExportData(filters);

      // Create workbook based on format type
      const workbook = format_type === "summary"
        ? await StockLevelsExportController.createSummaryWorkbook(exportData, columns)
        : await StockLevelsExportController.createDetailedWorkbook(exportData, columns);

      // Generate filename
      const filename = StockLevelsExportController.generateFilename(filters, format_type);

      // Set response headers
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader("Content-Disposition", `attachment; filename=${filename}`);

      // Write to response
      await workbook.xlsx.write(res);
      res.end();

      logger.info("Custom stock levels export completed successfully", {
        filename,
        format_type,
        productCount: exportData.products.length,
      });
    } catch (error) {
      logger.error("Error exporting custom stock levels:", error);
      next(error);
    }
  }

  /**
   * Get all export data without pagination
   * @param {Object} filters - Applied filters
   * @returns {Object} Complete export data
   */
  static async getAllExportData(filters) {
    try {
      // Build where clauses
      const stockItemWhere = StockLevelsExportController.buildStockItemWhereClause(filters);
      const productWhere = StockLevelsExportController.buildProductWhereClause(filters);

      // Get summary data
      const summary = await StockLevelsExportController.getSummaryData(filters);

      // Get category breakdown
      const by_category = await StockLevelsExportController.getCategoryBreakdown(filters);

      // Get branch breakdown
      const by_branch = await StockLevelsExportController.getBranchBreakdown(filters);

      // Get stock alerts
      const stock_alerts = await StockLevelsExportController.getStockAlerts(filters);

      // Get all products without pagination
      const products = await StockItem.findAll({
        where: stockItemWhere,
        include: [
          {
            model: Product,
            where: productWhere,
            required: true,
            include: [
              {
                model: ProductCategory,
                attributes: ["id", "name", "description", "parent_id"],
              },
              {
                model: Brand,
                attributes: ["id", "name"],
              },
            ],
          },
          {
            model: Branch,
            attributes: ["id", "name", "location", "region_id"],
            where: filters.region_id ? { region_id: filters.region_id } : {},
            required: true,
            include: [
              {
                model: Region,
                attributes: ["id", "name"],
              },
            ],
          },
        ],
        order: StockLevelsExportController.getSortOrder(filters.sort_by, filters.sort_direction),
      });

      // Transform products to match expected format
      const transformedProducts = products.map((item) => {
        const product = item.Product;
        const branch = item.Branch;
        const category = product?.ProductCategory;
        const brand = product?.Brand;
        const region = branch?.Region;

        return {
          id: product?.id,
          name: product?.name,
          sku: product?.sku,
          category: category,
          brand: brand,
          stock_info: {
            current_quantity: item.quantity,
            min_stock_level: product?.min_stock_level || 0,
            max_stock_level: product?.max_stock_level || 0,
            reorder_point: item.reorder_level || 0,
            stock_status: item.quantity <= 0 ? "out_of_stock" :
                         item.quantity <= (item.reorder_level || 0) ? "low_stock" : "in_stock",
            days_of_stock: null, // Could be calculated if needed
            last_restocked: item.updated_at,
          },
          pricing: {
            buying_price: item.default_buying_price || 0,
            selling_price: item.default_selling_price || 0,
            margin_percentage: item.default_selling_price && item.default_buying_price
              ? (((item.default_selling_price - item.default_buying_price) / item.default_selling_price) * 100).toFixed(2)
              : "0.00",
            total_value: ((item.quantity || 0) * (item.default_buying_price || 0)).toFixed(2),
          },
          location: {
            branch_id: branch?.id,
            branch_name: branch?.name,
            branch_location: branch?.location || "",
            region_id: branch?.region_id,
            region_name: region?.name || null,
            warehouse_location: null,
          },
        };
      });

      return {
        summary,
        by_category,
        by_branch,
        stock_alerts,
        products: transformedProducts,
        filters,
      };
    } catch (error) {
      logger.error("Error getting export data:", error);
      throw error;
    }
  }

  /**
   * Generate filename based on filters and format
   * @param {Object} filters - Applied filters
   * @param {string} formatType - Export format type
   * @returns {string} Generated filename
   */
  static generateFilename(filters, formatType = "detailed") {
    const timestamp = new Date().toISOString().split('T')[0];
    const regionFilter = filters.region_id ? `-region-${filters.region_id}` : '';
    const branchFilter = filters.branch_id ? `-branch-${filters.branch_id}` : '';
    const categoryFilter = filters.category_id ? `-category-${filters.category_id}` : '';
    const searchFilter = filters.search ? `-search` : '';
    const typePrefix = formatType !== "detailed" ? `-${formatType}` : '';

    return `stock-levels${typePrefix}-${timestamp}${regionFilter}${branchFilter}${categoryFilter}${searchFilter}.xlsx`;
  }

  /**
   * Create comprehensive workbook with multiple sheets
   * @param {Object} data - Export data
   * @param {Object} options - Include options
   * @returns {ExcelJS.Workbook} Excel workbook
   */
  static async createComprehensiveWorkbook(data, options) {
    const workbook = new ExcelJS.Workbook();

    // Add metadata
    workbook.creator = "Dukalink POS System";
    workbook.created = new Date();
    workbook.modified = new Date();

    // 1. Summary Sheet
    if (options.include_summary) {
      await StockLevelsExportController.addSummarySheet(workbook, data);
    }

    // 2. Products Sheet (Main data)
    await StockLevelsExportController.addProductsSheet(workbook, data.products);

    // 3. Category Breakdown Sheet
    if (options.include_categories && data.by_category.length > 0) {
      await StockLevelsExportController.addCategorySheet(workbook, data.by_category);
    }

    // 4. Branch Breakdown Sheet
    if (options.include_branches && data.by_branch.length > 0) {
      await StockLevelsExportController.addBranchSheet(workbook, data.by_branch);
    }

    // 5. Stock Alerts Sheet
    if (options.include_alerts && (data.stock_alerts.critical_low_stock.length > 0 || data.stock_alerts.out_of_stock.length > 0)) {
      await StockLevelsExportController.addAlertsSheet(workbook, data.stock_alerts);
    }

    return workbook;
  }

  /**
   * Add summary sheet to workbook
   * @param {ExcelJS.Workbook} workbook - Excel workbook
   * @param {Object} data - Export data
   */
  static async addSummarySheet(workbook, data) {
    const sheet = workbook.addWorksheet("Summary");

    // Set column widths
    sheet.columns = [
      { header: "Metric", key: "metric", width: 25 },
      { header: "Value", key: "value", width: 20 },
    ];

    // Add summary data
    const summaryRows = [
      { metric: "Total Products", value: data.summary.total_products },
      { metric: "Total Inventory Value", value: `$${data.summary.total_value.toFixed(2)}` },
      { metric: "Total Quantity", value: data.summary.total_quantity },
      { metric: "Products In Stock", value: data.summary.in_stock_count },
      { metric: "Low Stock Items", value: data.summary.low_stock_count },
      { metric: "Out of Stock Items", value: data.summary.out_of_stock_count },
      { metric: "Categories", value: data.summary.categories_count },
      { metric: "Branches", value: data.summary.branches_count },
      { metric: "Report Generated", value: new Date().toLocaleString() },
    ];

    sheet.addRows(summaryRows);

    // Style the header
    sheet.getRow(1).font = { bold: true, size: 12 };
    sheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FF4472C4" },
    };
    sheet.getRow(1).font.color = { argb: "FFFFFFFF" };

    // Add filters info
    if (data.filters) {
      sheet.addRow({}); // Empty row
      sheet.addRow({ metric: "Applied Filters", value: "" });

      if (data.filters.branch_id) {
        sheet.addRow({ metric: "Branch ID", value: data.filters.branch_id });
      }
      if (data.filters.region_id) {
        sheet.addRow({ metric: "Region ID", value: data.filters.region_id });
      }
      if (data.filters.category_id) {
        sheet.addRow({ metric: "Category ID", value: data.filters.category_id });
      }
      if (data.filters.search) {
        sheet.addRow({ metric: "Search Term", value: data.filters.search });
      }
      sheet.addRow({ metric: "Include Zero Stock", value: data.filters.include_zero_stock ? "Yes" : "No" });
      sheet.addRow({ metric: "Include Inactive", value: data.filters.include_inactive ? "Yes" : "No" });
    }
  }

  /**
   * Add products sheet to workbook
   * @param {ExcelJS.Workbook} workbook - Excel workbook
   * @param {Array} products - Products data
   */
  static async addProductsSheet(workbook, products) {
    const sheet = workbook.addWorksheet("Products");

    // Define columns
    sheet.columns = [
      { header: "Product ID", key: "id", width: 12 },
      { header: "Name", key: "name", width: 30 },
      { header: "SKU", key: "sku", width: 15 },
      { header: "Category", key: "category", width: 20 },
      { header: "Brand", key: "brand", width: 15 },
      { header: "Current Qty", key: "quantity", width: 12 },
      { header: "Stock Status", key: "status", width: 15 },
      { header: "Buying Price", key: "buying_price", width: 15 },
      { header: "Selling Price", key: "selling_price", width: 15 },
      { header: "Margin %", key: "margin", width: 12 },
      { header: "Total Value", key: "total_value", width: 15 },
      { header: "Branch", key: "branch", width: 20 },
      { header: "Region", key: "region", width: 20 },
      { header: "Min Stock Level", key: "min_stock", width: 15 },
      { header: "Last Restocked", key: "last_restocked", width: 18 },
    ];

    // Add product rows
    const productRows = products.map((product) => ({
      id: product.id,
      name: product.name,
      sku: product.sku,
      category: product.category?.name || "N/A",
      brand: product.brand?.name || "N/A",
      quantity: product.stock_info.current_quantity,
      status: product.stock_info.stock_status.replace("_", " ").toUpperCase(),
      buying_price: parseFloat(product.pricing.buying_price).toFixed(2),
      selling_price: parseFloat(product.pricing.selling_price).toFixed(2),
      margin: `${product.pricing.margin_percentage}%`,
      total_value: parseFloat(product.pricing.total_value).toFixed(2),
      branch: product.location.branch_name,
      region: product.location.region_name || "N/A",
      min_stock: product.stock_info.min_stock_level,
      last_restocked: product.stock_info.last_restocked
        ? new Date(product.stock_info.last_restocked).toLocaleDateString()
        : "Never",
    }));

    sheet.addRows(productRows);

    // Style the header
    sheet.getRow(1).font = { bold: true, size: 11 };
    sheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FF70AD47" },
    };
    sheet.getRow(1).font.color = { argb: "FFFFFFFF" };

    // Add conditional formatting for stock status
    sheet.addConditionalFormatting({
      ref: `G2:G${products.length + 1}`,
      rules: [
        {
          type: "containsText",
          operator: "containsText",
          text: "OUT OF STOCK",
          style: {
            fill: {
              type: "pattern",
              pattern: "solid",
              bgColor: { argb: "FFFF0000" },
            },
            font: { color: { argb: "FFFFFFFF" } },
          },
        },
        {
          type: "containsText",
          operator: "containsText",
          text: "LOW STOCK",
          style: {
            fill: {
              type: "pattern",
              pattern: "solid",
              bgColor: { argb: "FFFFFF00" },
            },
          },
        },
      ],
    });
  }

  /**
   * Add category breakdown sheet to workbook
   * @param {ExcelJS.Workbook} workbook - Excel workbook
   * @param {Array} categories - Category breakdown data
   */
  static async addCategorySheet(workbook, categories) {
    const sheet = workbook.addWorksheet("Category Breakdown");

    sheet.columns = [
      { header: "Category ID", key: "category_id", width: 12 },
      { header: "Category Name", key: "category_name", width: 25 },
      { header: "Product Count", key: "product_count", width: 15 },
      { header: "Total Quantity", key: "total_quantity", width: 15 },
      { header: "Total Value", key: "total_value", width: 15 },
      { header: "Low Stock Count", key: "low_stock_count", width: 15 },
      { header: "Out of Stock Count", key: "out_of_stock_count", width: 18 },
    ];

    const categoryRows = categories.map((cat) => ({
      category_id: cat.category_id,
      category_name: cat.category_name,
      product_count: cat.product_count,
      total_quantity: cat.total_quantity,
      total_value: parseFloat(cat.total_value).toFixed(2),
      low_stock_count: cat.low_stock_count,
      out_of_stock_count: cat.out_of_stock_count,
    }));

    sheet.addRows(categoryRows);

    // Style the header
    sheet.getRow(1).font = { bold: true, size: 11 };
    sheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FFFF6600" },
    };
    sheet.getRow(1).font.color = { argb: "FFFFFFFF" };
  }

  /**
   * Add branch breakdown sheet to workbook
   * @param {ExcelJS.Workbook} workbook - Excel workbook
   * @param {Array} branches - Branch breakdown data
   */
  static async addBranchSheet(workbook, branches) {
    const sheet = workbook.addWorksheet("Branch Breakdown");

    sheet.columns = [
      { header: "Branch ID", key: "branch_id", width: 12 },
      { header: "Branch Name", key: "branch_name", width: 25 },
      { header: "Location", key: "branch_location", width: 30 },
      { header: "Product Count", key: "product_count", width: 15 },
      { header: "Total Quantity", key: "total_quantity", width: 15 },
      { header: "Total Value", key: "total_value", width: 15 },
      { header: "Low Stock Count", key: "low_stock_count", width: 15 },
      { header: "Out of Stock Count", key: "out_of_stock_count", width: 18 },
    ];

    const branchRows = branches.map((branch) => ({
      branch_id: branch.branch_id,
      branch_name: branch.branch_name,
      branch_location: branch.branch_location,
      product_count: branch.product_count,
      total_quantity: branch.total_quantity,
      total_value: parseFloat(branch.total_value).toFixed(2),
      low_stock_count: branch.low_stock_count,
      out_of_stock_count: branch.out_of_stock_count,
    }));

    sheet.addRows(branchRows);

    // Style the header
    sheet.getRow(1).font = { bold: true, size: 11 };
    sheet.getRow(1).fill = {
      type: "pattern",
      pattern: "solid",
      fgColor: { argb: "FF9966CC" },
    };
    sheet.getRow(1).font.color = { argb: "FFFFFFFF" };
  }

  /**
   * Add stock alerts sheet to workbook
   * @param {ExcelJS.Workbook} workbook - Excel workbook
   * @param {Object} alerts - Stock alerts data
   */
  static async addAlertsSheet(workbook, alerts) {
    const sheet = workbook.addWorksheet("Stock Alerts");

    // Add Low Stock section
    if (alerts.critical_low_stock.length > 0) {
      sheet.addRow({ alert_type: "CRITICAL LOW STOCK ALERTS" });
      sheet.getRow(sheet.rowCount).font = { bold: true, size: 14, color: { argb: "FFFF0000" } };

      sheet.addRow({}); // Empty row

      // Low stock headers
      sheet.addRow({
        alert_type: "Product Name",
        sku: "SKU",
        branch: "Branch",
        current_qty: "Current Qty",
        min_level: "Min Level",
        urgency: "Urgency",
        last_updated: "Last Updated"
      });

      const headerRow = sheet.getRow(sheet.rowCount);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFFFFF00" },
      };

      // Add low stock data
      alerts.critical_low_stock.forEach((alert) => {
        sheet.addRow({
          alert_type: alert.product_name,
          sku: alert.sku,
          branch: alert.branch_name,
          current_qty: alert.current_quantity,
          min_level: alert.min_stock_level,
          urgency: alert.urgency_level.toUpperCase(),
          last_updated: new Date(alert.last_updated).toLocaleDateString()
        });
      });
    }

    // Add Out of Stock section
    if (alerts.out_of_stock.length > 0) {
      sheet.addRow({}); // Empty row
      sheet.addRow({}); // Empty row

      sheet.addRow({ alert_type: "OUT OF STOCK ALERTS" });
      sheet.getRow(sheet.rowCount).font = { bold: true, size: 14, color: { argb: "FFFF0000" } };

      sheet.addRow({}); // Empty row

      // Out of stock headers
      sheet.addRow({
        alert_type: "Product Name",
        sku: "SKU",
        branch: "Branch",
        days_out: "Days Out",
        last_sale: "Last Sale",
        last_updated: "Last Updated"
      });

      const headerRow2 = sheet.getRow(sheet.rowCount);
      headerRow2.font = { bold: true };
      headerRow2.fill = {
        type: "pattern",
        pattern: "solid",
        fgColor: { argb: "FFFF0000" },
      };
      headerRow2.font.color = { argb: "FFFFFFFF" };

      // Add out of stock data
      alerts.out_of_stock.forEach((alert) => {
        sheet.addRow({
          alert_type: alert.product_name,
          sku: alert.sku,
          branch: alert.branch_name,
          days_out: alert.days_out_of_stock || "N/A",
          last_sale: alert.last_sale_date
            ? new Date(alert.last_sale_date).toLocaleDateString()
            : "No recent sales",
          last_updated: new Date(alert.last_updated).toLocaleDateString()
        });
      });
    }

    // Set column widths
    sheet.columns = [
      { header: "Product/Alert Type", key: "alert_type", width: 30 },
      { header: "SKU", key: "sku", width: 15 },
      { header: "Branch", key: "branch", width: 20 },
      { header: "Current Qty/Days Out", key: "current_qty", width: 18 },
      { header: "Min Level/Last Sale", key: "min_level", width: 20 },
      { header: "Urgency", key: "urgency", width: 12 },
      { header: "Last Updated", key: "last_updated", width: 15 },
    ];
  }

  /**
   * Create summary workbook with basic data
   * @param {Object} data - Export data
   * @param {string} columns - Columns to include
   * @returns {ExcelJS.Workbook} Excel workbook
   */
  static async createSummaryWorkbook(data, columns) {
    const workbook = new ExcelJS.Workbook();
    workbook.creator = "Dukalink POS System";
    workbook.created = new Date();

    // Add summary sheet
    await StockLevelsExportController.addSummarySheet(workbook, data);

    // Add simplified products sheet
    const sheet = workbook.addWorksheet("Products Summary");

    sheet.columns = [
      { header: "Product Name", key: "name", width: 30 },
      { header: "SKU", key: "sku", width: 15 },
      { header: "Category", key: "category", width: 20 },
      { header: "Current Qty", key: "quantity", width: 12 },
      { header: "Stock Status", key: "status", width: 15 },
      { header: "Branch", key: "branch", width: 20 },
    ];

    const summaryRows = data.products.map((product) => ({
      name: product.name,
      sku: product.sku,
      category: product.category?.name || "N/A",
      quantity: product.stock_info.current_quantity,
      status: product.stock_info.stock_status.replace("_", " ").toUpperCase(),
      branch: product.location.branch_name,
    }));

    sheet.addRows(summaryRows);
    sheet.getRow(1).font = { bold: true };

    return workbook;
  }

  /**
   * Create detailed workbook with custom columns
   * @param {Object} data - Export data
   * @param {string} columns - Columns to include
   * @returns {ExcelJS.Workbook} Excel workbook
   */
  static async createDetailedWorkbook(data, columns) {
    const workbook = new ExcelJS.Workbook();
    workbook.creator = "Dukalink POS System";
    workbook.created = new Date();

    // Add all sheets for detailed export
    await StockLevelsExportController.addSummarySheet(workbook, data);
    await StockLevelsExportController.addProductsSheet(workbook, data.products);

    if (data.by_category.length > 0) {
      await StockLevelsExportController.addCategorySheet(workbook, data.by_category);
    }

    if (data.by_branch.length > 0) {
      await StockLevelsExportController.addBranchSheet(workbook, data.by_branch);
    }

    if (data.stock_alerts.critical_low_stock.length > 0 || data.stock_alerts.out_of_stock.length > 0) {
      await StockLevelsExportController.addAlertsSheet(workbook, data.stock_alerts);
    }

    return workbook;
  }

  // Additional helper methods for other sheets and utilities
  static buildStockItemWhereClause(filters) {
    const whereClause = {};

    if (filters.branch_id) {
      whereClause.branch_id = filters.branch_id;
    }

    if (!filters.include_zero_stock) {
      whereClause.quantity = { [Op.gt]: 0 };
    }

    return whereClause;
  }

  static buildProductWhereClause(filters) {
    const whereClause = {};

    if (filters.category_id) {
      whereClause.category_id = filters.category_id;
    }

    if (!filters.include_inactive) {
      whereClause.is_active = true;
    }

    if (filters.search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${filters.search}%` } },
        { sku: { [Op.like]: `%${filters.search}%` } },
      ];
    }

    return whereClause;
  }

  static getSortOrder(sort_by, sort_direction) {
    const direction = sort_direction.toUpperCase() === "DESC" ? "DESC" : "ASC";

    switch (sort_by) {
      case "quantity":
        return [["quantity", direction]];
      case "value":
        return [
          [sequelize.literal("(quantity * default_selling_price)"), direction],
        ];
      case "category":
        return [
          [
            { model: Product, as: "Product" },
            { model: ProductCategory },
            "name",
            direction,
          ],
        ];
      case "name":
      default:
        return [[{ model: Product, as: "Product" }, "name", direction]];
    }
  }

  /**
   * Get summary metrics
   * @param {Object} filters - Applied filters
   * @returns {Object} Summary data
   */
  static async getSummaryData(filters) {
    try {
      // Get total products count
      const totalProducts = await StockItem.count({
        where: StockLevelsExportController.buildStockItemWhereClause(filters),
        include: [
          {
            model: Product,
            where: StockLevelsExportController.buildProductWhereClause(filters),
            required: true,
          },
          {
            model: Branch,
            where: filters.region_id ? { region_id: filters.region_id } : {},
            required: true,
          },
        ],
        distinct: true,
      });

      // Get total value and quantity using raw query for better performance
      const valueAndQuantityResult = await sequelize.query(
        `
        SELECT
          COALESCE(SUM(si.quantity), 0) as total_quantity,
          COALESCE(SUM(si.quantity * si.default_buying_price), 0) as total_value
        FROM stock_items si
        INNER JOIN products p ON si.product_id = p.id
        ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
        WHERE 1=1
          ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
          ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
      `,
        { type: sequelize.QueryTypes.SELECT }
      );

      const totalQuantity = parseInt(valueAndQuantityResult[0]?.total_quantity || 0);
      const totalValue = parseFloat(valueAndQuantityResult[0]?.total_value || 0);

      // Get stock status counts
      const stockStatusResult = await sequelize.query(
        `
        SELECT
          COUNT(CASE WHEN si.quantity > COALESCE(si.reorder_level, 0) THEN 1 END) as in_stock_count,
          COUNT(CASE WHEN si.quantity > 0 AND si.quantity <= COALESCE(si.reorder_level, 0) THEN 1 END) as low_stock_count,
          COUNT(CASE WHEN si.quantity = 0 THEN 1 END) as out_of_stock_count
        FROM stock_items si
        INNER JOIN products p ON si.product_id = p.id
        ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
        WHERE 1=1
          ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
      `,
        { type: sequelize.QueryTypes.SELECT }
      );

      const inStockCount = parseInt(stockStatusResult[0]?.in_stock_count || 0);
      const lowStockCount = parseInt(stockStatusResult[0]?.low_stock_count || 0);
      const outOfStockCount = parseInt(stockStatusResult[0]?.out_of_stock_count || 0);

      // Get categories count
      let categoriesCount = 0;
      if (!filters.category_id) {
        const categoriesResult = await sequelize.query(
          `
          SELECT COUNT(DISTINCT p.category_id) as categories_count
          FROM stock_items si
          INNER JOIN products p ON si.product_id = p.id
          ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
          WHERE 1=1
            ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
            ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
            ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
            ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
            ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
            AND p.category_id IS NOT NULL
        `,
          { type: sequelize.QueryTypes.SELECT }
        );
        categoriesCount = parseInt(categoriesResult[0]?.categories_count || 0);
      } else {
        categoriesCount = 1;
      }

      // Get branches count
      let branchesCount = 0;
      if (!filters.branch_id) {
        const branchesResult = await sequelize.query(
          `
          SELECT COUNT(DISTINCT si.branch_id) as branches_count
          FROM stock_items si
          INNER JOIN products p ON si.product_id = p.id
          ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
          WHERE 1=1
            ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
            ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
            ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
            ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
            ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
        `,
          { type: sequelize.QueryTypes.SELECT }
        );
        branchesCount = parseInt(branchesResult[0]?.branches_count || 0);
      } else {
        branchesCount = 1;
      }

      return {
        total_products: totalProducts,
        total_value: totalValue,
        total_quantity: totalQuantity,
        in_stock_count: inStockCount,
        low_stock_count: lowStockCount,
        out_of_stock_count: outOfStockCount,
        categories_count: categoriesCount,
        branches_count: branchesCount,
        last_updated: new Date().toISOString(),
      };
    } catch (error) {
      logger.error("Error calculating summary metrics:", error);
      throw error;
    }
  }

  /**
   * Get category breakdown
   * @param {Object} filters - Applied filters
   * @returns {Array} Category breakdown data
   */
  static async getCategoryBreakdown(filters) {
    try {
      if (filters.category_id) {
        const categoryResult = await sequelize.query(
          `
          SELECT
            pc.id as category_id,
            pc.name as category_name,
            COUNT(DISTINCT si.product_id) as product_count,
            COALESCE(SUM(si.quantity), 0) as total_quantity,
            COALESCE(SUM(si.quantity * si.default_buying_price), 0) as total_value,
            COUNT(CASE WHEN si.quantity > 0 AND si.quantity <= COALESCE(si.reorder_level, 0) THEN 1 END) as low_stock_count,
            COUNT(CASE WHEN si.quantity = 0 THEN 1 END) as out_of_stock_count
          FROM product_categories pc
          INNER JOIN products p ON pc.id = p.category_id
          INNER JOIN stock_items si ON p.id = si.product_id
          ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
          WHERE pc.id = ${filters.category_id}
            ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
            ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
            ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
            ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
            ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
          GROUP BY pc.id, pc.name
        `,
          { type: sequelize.QueryTypes.SELECT }
        );

        return categoryResult.map((cat) => ({
          category_id: cat.category_id,
          category_name: cat.category_name,
          product_count: parseInt(cat.product_count),
          total_quantity: parseInt(cat.total_quantity),
          total_value: parseFloat(cat.total_value),
          low_stock_count: parseInt(cat.low_stock_count),
          out_of_stock_count: parseInt(cat.out_of_stock_count),
        }));
      }

      const categoryBreakdown = await sequelize.query(
        `
        SELECT
          pc.id as category_id,
          pc.name as category_name,
          COUNT(DISTINCT si.product_id) as product_count,
          COALESCE(SUM(si.quantity), 0) as total_quantity,
          COALESCE(SUM(si.quantity * si.default_buying_price), 0) as total_value,
          COUNT(CASE WHEN si.quantity > 0 AND si.quantity <= COALESCE(si.reorder_level, 0) THEN 1 END) as low_stock_count,
          COUNT(CASE WHEN si.quantity = 0 THEN 1 END) as out_of_stock_count
        FROM product_categories pc
        INNER JOIN products p ON pc.id = p.category_id
        INNER JOIN stock_items si ON p.id = si.product_id
        ${filters.region_id ? "INNER JOIN branches b ON si.branch_id = b.id" : ""}
        WHERE 1=1
          ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
        GROUP BY pc.id, pc.name
        ORDER BY pc.name ASC
      `,
        { type: sequelize.QueryTypes.SELECT }
      );

      return categoryBreakdown.map((cat) => ({
        category_id: cat.category_id,
        category_name: cat.category_name,
        product_count: parseInt(cat.product_count),
        total_quantity: parseInt(cat.total_quantity),
        total_value: parseFloat(cat.total_value),
        low_stock_count: parseInt(cat.low_stock_count),
        out_of_stock_count: parseInt(cat.out_of_stock_count),
      }));
    } catch (error) {
      logger.error("Error getting category breakdown:", error);
      throw error;
    }
  }

  /**
   * Get branch breakdown
   * @param {Object} filters - Applied filters
   * @returns {Array} Branch breakdown data
   */
  static async getBranchBreakdown(filters) {
    try {
      if (filters.branch_id) {
        const branchResult = await sequelize.query(
          `
          SELECT
            b.id as branch_id,
            b.name as branch_name,
            b.location as branch_location,
            COUNT(DISTINCT si.product_id) as product_count,
            COALESCE(SUM(si.quantity), 0) as total_quantity,
            COALESCE(SUM(si.quantity * si.default_buying_price), 0) as total_value,
            COUNT(CASE WHEN si.quantity > 0 AND si.quantity <= COALESCE(si.reorder_level, 0) THEN 1 END) as low_stock_count,
            COUNT(CASE WHEN si.quantity = 0 THEN 1 END) as out_of_stock_count
          FROM branches b
          INNER JOIN stock_items si ON b.id = si.branch_id
          INNER JOIN products p ON si.product_id = p.id
          WHERE b.id = ${filters.branch_id}
            ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
            ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
            ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
            ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
          GROUP BY b.id, b.name, b.location
        `,
          { type: sequelize.QueryTypes.SELECT }
        );

        return branchResult.map((branch) => ({
          branch_id: branch.branch_id,
          branch_name: branch.branch_name,
          branch_location: branch.branch_location || "",
          product_count: parseInt(branch.product_count),
          total_quantity: parseInt(branch.total_quantity),
          total_value: parseFloat(branch.total_value),
          low_stock_count: parseInt(branch.low_stock_count),
          out_of_stock_count: parseInt(branch.out_of_stock_count),
        }));
      }

      const branchBreakdown = await sequelize.query(
        `
        SELECT
          b.id as branch_id,
          b.name as branch_name,
          b.location as branch_location,
          COUNT(DISTINCT si.product_id) as product_count,
          COALESCE(SUM(si.quantity), 0) as total_quantity,
          COALESCE(SUM(si.quantity * si.default_buying_price), 0) as total_value,
          COUNT(CASE WHEN si.quantity > 0 AND si.quantity <= COALESCE(si.reorder_level, 0) THEN 1 END) as low_stock_count,
          COUNT(CASE WHEN si.quantity = 0 THEN 1 END) as out_of_stock_count
        FROM branches b
        INNER JOIN stock_items si ON b.id = si.branch_id
        INNER JOIN products p ON si.product_id = p.id
        WHERE 1=1
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
          ${!filters.include_zero_stock ? "AND si.quantity > 0" : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
        GROUP BY b.id, b.name, b.location
        ORDER BY b.name ASC
      `,
        { type: sequelize.QueryTypes.SELECT }
      );

      return branchBreakdown.map((branch) => ({
        branch_id: branch.branch_id,
        branch_name: branch.branch_name,
        branch_location: branch.branch_location || "",
        product_count: parseInt(branch.product_count),
        total_quantity: parseInt(branch.total_quantity),
        total_value: parseFloat(branch.total_value),
        low_stock_count: parseInt(branch.low_stock_count),
        out_of_stock_count: parseInt(branch.out_of_stock_count),
      }));
    } catch (error) {
      logger.error("Error getting branch breakdown:", error);
      throw error;
    }
  }

  /**
   * Get stock alerts
   * @param {Object} filters - Applied filters
   * @returns {Object} Stock alerts data
   */
  static async getStockAlerts(filters) {
    try {
      // Get critical low stock items
      const criticalLowStockQuery = `
        SELECT
          p.id as product_id,
          p.name as product_name,
          p.sku,
          si.quantity as current_quantity,
          COALESCE(si.reorder_level, 0) as min_stock_level,
          b.name as branch_name,
          b.location as branch_location,
          si.updated_at as last_updated
        FROM stock_items si
        INNER JOIN products p ON si.product_id = p.id
        INNER JOIN branches b ON si.branch_id = b.id
        WHERE si.quantity > 0
          AND si.quantity <= COALESCE(si.reorder_level, 0)
          AND si.reorder_level > 0
          ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
        ORDER BY (si.quantity / NULLIF(si.reorder_level, 0)) ASC, p.name ASC
      `;

      const criticalLowStock = await sequelize.query(criticalLowStockQuery, {
        type: sequelize.QueryTypes.SELECT,
      });

      // Get out of stock items
      const outOfStockQuery = `
        SELECT
          p.id as product_id,
          p.name as product_name,
          p.sku,
          b.name as branch_name,
          b.location as branch_location,
          si.updated_at as last_updated,
          (
            SELECT MAX(s.created_at)
            FROM sales s
            INNER JOIN sale_items sit ON s.id = sit.sale_id
            WHERE sit.product_id = p.id AND s.branch_id = si.branch_id
          ) as last_sale_date
        FROM stock_items si
        INNER JOIN products p ON si.product_id = p.id
        INNER JOIN branches b ON si.branch_id = b.id
        WHERE si.quantity = 0
          ${filters.branch_id ? `AND si.branch_id = ${filters.branch_id}` : ""}
          ${filters.region_id ? `AND b.region_id = ${filters.region_id}` : ""}
          ${filters.category_id ? `AND p.category_id = ${filters.category_id}` : ""}
          ${!filters.include_inactive ? "AND p.is_active = 1" : ""}
          ${filters.search ? `AND (p.name LIKE '%${filters.search}%' OR p.sku LIKE '%${filters.search}%')` : ""}
        ORDER BY si.updated_at DESC, p.name ASC
      `;

      const outOfStock = await sequelize.query(outOfStockQuery, {
        type: sequelize.QueryTypes.SELECT,
      });

      // Format alerts
      const formattedCriticalLowStock = criticalLowStock.map((item) => ({
        product_id: item.product_id,
        product_name: item.product_name,
        sku: item.sku,
        current_quantity: item.current_quantity,
        min_stock_level: item.min_stock_level,
        branch_name: item.branch_name,
        branch_location: item.branch_location || "",
        urgency_level:
          item.current_quantity === 0
            ? "critical"
            : item.current_quantity <= item.min_stock_level * 0.5
              ? "high"
              : "medium",
        last_updated: item.last_updated,
      }));

      const formattedOutOfStock = outOfStock.map((item) => ({
        product_id: item.product_id,
        product_name: item.product_name,
        sku: item.sku,
        branch_name: item.branch_name,
        branch_location: item.branch_location || "",
        last_sale_date: item.last_sale_date,
        days_out_of_stock: item.last_updated
          ? Math.floor((new Date() - new Date(item.last_updated)) / (1000 * 60 * 60 * 24))
          : null,
        last_updated: item.last_updated,
      }));

      return {
        critical_low_stock: formattedCriticalLowStock,
        out_of_stock: formattedOutOfStock,
      };
    } catch (error) {
      logger.error("Error getting stock alerts:", error);
      throw error;
    }
  }
}

module.exports = StockLevelsExportController;
