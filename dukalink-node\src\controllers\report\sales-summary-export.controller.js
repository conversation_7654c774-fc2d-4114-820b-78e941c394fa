const ExcelJS = require('exceljs');
const { Sale, SaleItem, Branch, User, Product, Category, Region } = require('../../models');
const { Op } = require('sequelize');
const logger = require('../../utils/logger');

class SalesSummaryExportController {
  /**
   * Get all sales data with comprehensive filtering (optimized for large datasets)
   */
  async getAllSalesData(filters = {}, limit = 10000) {
    try {
      const {
        start_date,
        end_date,
        branch_id,
        region_id,
        product_id,
        category_id,
        location_id,
        user_id,
        payment_method
      } = filters;

      logger.info('Exporting sales summary data with filters:', filters);

      // Build where clause for sales
      const whereClause = {};

      // Default to last 30 days if no date range specified to prevent huge exports
      if (start_date && end_date) {
        whereClause.created_at = {
          [Op.between]: [new Date(start_date), new Date(end_date)]
        };
      } else if (start_date) {
        whereClause.created_at = {
          [Op.gte]: new Date(start_date)
        };
      } else if (end_date) {
        whereClause.created_at = {
          [Op.lte]: new Date(end_date)
        };
      } else {
        // Default to last 30 days to prevent massive exports
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        whereClause.created_at = {
          [Op.gte]: thirtyDaysAgo
        };
        logger.info('No date range specified, defaulting to last 30 days');
      }

      if (payment_method) {
        whereClause.payment_method = payment_method;
      }

      if (user_id) {
        whereClause.user_id = user_id;
      }

      if (branch_id) {
        whereClause.branch_id = branch_id;
      }

      // First, get count to check if we need to limit
      const totalCount = await Sale.count({ where: whereClause });
      logger.info(`Total sales matching criteria: ${totalCount}`);

      if (totalCount > limit) {
        logger.warn(`Large dataset detected (${totalCount} records). Limiting to ${limit} most recent records.`);
      }

      // Fetch sales data with basic includes only and limit for performance
      const sales = await Sale.findAll({
        where: whereClause,
        include: [
          {
            model: Branch,
            attributes: ['id', 'name', 'location'],
            required: false
          },
          {
            model: User,
            attributes: ['id', 'name', 'email'],
            required: false
          }
        ],
        order: [['created_at', 'DESC']],
        limit: Math.min(totalCount, limit),
        raw: false
      });

      logger.info(`Found ${sales.length} sales for export (limited from ${totalCount})`);

      // Transform sales data efficiently
      const transformedSales = sales.map(sale => ({
        id: sale.id,
        receipt_number: sale.receipt_number,
        created_at: sale.created_at,
        branch_name: sale.Branch?.name || 'Unknown',
        branch_location: sale.Branch?.location || '',
        region_name: 'Unknown', // Will be populated separately if needed
        user_name: sale.User?.name || 'Unknown',
        user_email: sale.User?.email || '',
        total_amount: parseFloat(sale.total_amount) || 0,
        discount_amount: parseFloat(sale.discount_amount) || 0,
        net_amount: parseFloat(sale.net_amount) || 0,
        payment_method: sale.payment_method || 'Unknown',
        items_count: 0, // Will be populated separately if needed
        // Additional calculated fields
        gross_profit: (parseFloat(sale.net_amount) || 0) - (parseFloat(sale.discount_amount) || 0),
        discount_percentage: sale.total_amount > 0 ? ((parseFloat(sale.discount_amount) || 0) / parseFloat(sale.total_amount) * 100).toFixed(2) : 0
      }));

      return transformedSales;
    } catch (error) {
      logger.error('Error getting sales data for export:', error);
      throw error;
    }
  }

  /**
   * Generate sales by product breakdown (optimized)
   */
  async getProductBreakdown(filters = {}, limit = 5000) {
    try {
      logger.info('Getting product breakdown with filters:', filters);

      // Get sales data first with limit
      const sales = await this.getAllSalesData(filters, limit);
      const saleIds = sales.map(sale => sale.id);

      if (saleIds.length === 0) {
        return [];
      }

      // Process in batches to avoid memory issues
      const batchSize = 1000;
      const productMap = new Map();

      for (let i = 0; i < saleIds.length; i += batchSize) {
        const batchIds = saleIds.slice(i, i + batchSize);

        // Fetch sale items for this batch
        const saleItems = await SaleItem.findAll({
          where: {
            sale_id: {
              [Op.in]: batchIds
            }
          },
          include: [
            {
              model: Product,
              attributes: ['id', 'name', 'sku'],
              required: false
            }
          ],
          raw: false
        });

        // Process this batch
        saleItems.forEach(item => {
          const productKey = item.Product?.id || item.product_id || 'unknown';
          const productName = item.Product?.name || `Product #${item.product_id}`;
          const productSku = item.Product?.sku || 'N/A';
          const categoryName = 'Uncategorized'; // Simplified for now

          if (!productMap.has(productKey)) {
            productMap.set(productKey, {
              product_id: productKey,
              product_name: productName,
              product_sku: productSku,
              category_name: categoryName,
              quantity: 0,
              total_amount: 0,
              discount_amount: 0,
              net_amount: 0,
              sales_count: 0
            });
          }

          const productData = productMap.get(productKey);
          productData.quantity += parseInt(item.quantity) || 0;
          productData.total_amount += parseFloat(item.total_amount) || 0;
          productData.discount_amount += parseFloat(item.discount_amount) || 0;
          productData.net_amount += parseFloat(item.net_amount) || 0;
          productData.sales_count += 1;
        });

        logger.info(`Processed batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(saleIds.length/batchSize)}`);
      }

      logger.info(`Product breakdown completed: ${productMap.size} unique products`);
      return Array.from(productMap.values()).sort((a, b) => b.net_amount - a.net_amount);
    } catch (error) {
      logger.error('Error getting product breakdown:', error);
      throw error;
    }
  }

  /**
   * Generate sales by branch breakdown
   */
  async getBranchBreakdown(filters = {}) {
    try {
      const sales = await this.getAllSalesData(filters);
      const branchMap = new Map();

      sales.forEach(sale => {
        const branchKey = sale.branch_name || 'Unknown';

        if (!branchMap.has(branchKey)) {
          branchMap.set(branchKey, {
            branch_name: sale.branch_name,
            branch_location: sale.branch_location,
            region_name: sale.region_name,
            total_sales: 0,
            total_amount: 0,
            discount_amount: 0,
            net_amount: 0,
            average_sale: 0
          });
        }

        const branchData = branchMap.get(branchKey);
        branchData.total_sales += 1;
        branchData.total_amount += sale.total_amount || 0;
        branchData.discount_amount += sale.discount_amount || 0;
        branchData.net_amount += sale.net_amount || 0;
      });

      // Calculate averages
      Array.from(branchMap.values()).forEach(branch => {
        branch.average_sale = branch.total_sales > 0 ? (branch.net_amount / branch.total_sales) : 0;
      });

      return Array.from(branchMap.values()).sort((a, b) => b.net_amount - a.net_amount);
    } catch (error) {
      logger.error('Error getting branch breakdown:', error);
      throw error;
    }
  }

  /**
   * Generate summary statistics
   */
  generateSummaryStats(sales, filters) {
    const stats = {
      total_sales: sales.length,
      total_amount: 0,
      total_discount: 0,
      net_amount: 0,
      average_sale_value: 0,
      by_payment_method: {},
      by_branch: {},
      date_range: {
        earliest: null,
        latest: null
      },
      filters_applied: filters
    };

    // Calculate totals and breakdowns
    sales.forEach(sale => {
      stats.total_amount += sale.total_amount || 0;
      stats.total_discount += sale.discount_amount || 0;
      stats.net_amount += sale.net_amount || 0;

      // Payment method breakdown
      const paymentMethod = sale.payment_method || 'Unknown';
      if (!stats.by_payment_method[paymentMethod]) {
        stats.by_payment_method[paymentMethod] = { count: 0, amount: 0 };
      }
      stats.by_payment_method[paymentMethod].count += 1;
      stats.by_payment_method[paymentMethod].amount += sale.net_amount || 0;

      // Branch breakdown
      const branch = sale.branch_name || 'Unknown';
      if (!stats.by_branch[branch]) {
        stats.by_branch[branch] = { count: 0, amount: 0 };
      }
      stats.by_branch[branch].count += 1;
      stats.by_branch[branch].amount += sale.net_amount || 0;
    });

    // Calculate average
    stats.average_sale_value = stats.total_sales > 0 ? (stats.net_amount / stats.total_sales) : 0;

    // Date range
    if (sales.length > 0) {
      const dates = sales.map(sale => new Date(sale.created_at)).sort();
      stats.date_range.earliest = dates[0];
      stats.date_range.latest = dates[dates.length - 1];
    }

    return stats;
  }

  /**
   * Create Excel workbook with multiple sheets (optimized for performance)
   */
  async createExcelWorkbook(sales, options = {}) {
    const {
      include_summary = true,
      include_sales_details = true,
      include_product_breakdown = true,
      include_branch_breakdown = true,
      include_payment_breakdown = true,
      filters = {}
    } = options;

    logger.info(`Creating Excel workbook with ${sales.length} sales records`);

    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'DukaLink POS System';
    workbook.created = new Date();

    // Limit data for performance
    const maxRecordsPerSheet = 10000;
    const limitedSales = sales.slice(0, maxRecordsPerSheet);

    if (sales.length > maxRecordsPerSheet) {
      logger.warn(`Large dataset detected. Limiting to ${maxRecordsPerSheet} records for performance.`);
    }

    // Generate summary statistics
    const stats = this.generateSummaryStats(sales, filters);

    // Summary Sheet
    if (include_summary) {
      const summarySheet = workbook.addWorksheet('Summary');

      // Title
      summarySheet.addRow(['Sales Summary Export']);
      summarySheet.addRow(['Generated on:', new Date().toLocaleString()]);
      summarySheet.addRow([]);

      // Key metrics
      summarySheet.addRow(['Total Sales:', stats.total_sales]);
      summarySheet.addRow(['Total Amount:', stats.total_amount]);
      summarySheet.addRow(['Total Discount:', stats.total_discount]);
      summarySheet.addRow(['Net Amount:', stats.net_amount]);
      summarySheet.addRow(['Average Sale Value:', stats.average_sale_value.toFixed(2)]);
      summarySheet.addRow(['Date Range:',
        stats.date_range.earliest ? stats.date_range.earliest.toLocaleDateString() : 'N/A',
        'to',
        stats.date_range.latest ? stats.date_range.latest.toLocaleDateString() : 'N/A'
      ]);
      summarySheet.addRow([]);

      // Applied filters
      summarySheet.addRow(['Applied Filters:']);
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          summarySheet.addRow([`${key}:`, value]);
        }
      });

      // Style the summary sheet
      summarySheet.getCell('A1').font = { bold: true, size: 16 };
      summarySheet.getColumn('A').width = 25;
      summarySheet.getColumn('B').width = 20;
    }

    // Sales Details Sheet
    if (include_sales_details) {
      const detailsSheet = workbook.addWorksheet('Sales Details');

      // Headers
      const headers = [
        'Receipt Number',
        'Date',
        'Branch',
        'Region',
        'User',
        'Total Amount',
        'Discount',
        'Net Amount',
        'Payment Method',
        'Items Count'
      ];

      detailsSheet.addRow(headers);

      // Data rows
      sales.forEach(sale => {
        detailsSheet.addRow([
          sale.receipt_number,
          sale.created_at ? new Date(sale.created_at).toLocaleDateString() : '',
          sale.branch_name,
          sale.region_name,
          sale.user_name,
          sale.total_amount,
          sale.discount_amount,
          sale.net_amount,
          sale.payment_method,
          sale.items_count
        ]);
      });

      // Style the headers
      const headerRow = detailsSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      // Auto-fit columns
      detailsSheet.columns.forEach(column => {
        column.width = 15;
      });
    }

    // Product Breakdown Sheet
    if (include_product_breakdown) {
      const productBreakdown = await this.getProductBreakdown(filters);
      const productSheet = workbook.addWorksheet('Product Breakdown');

      productSheet.addRow(['Product', 'SKU', 'Category', 'Quantity', 'Total Amount', 'Discount', 'Net Amount', 'Sales Count']);

      productBreakdown.forEach(product => {
        productSheet.addRow([
          product.product_name,
          product.product_sku,
          product.category_name,
          product.quantity,
          product.total_amount,
          product.discount_amount,
          product.net_amount,
          product.sales_count
        ]);
      });

      // Style headers
      const headerRow = productSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      productSheet.columns.forEach(column => {
        column.width = 15;
      });
    }

    // Branch Breakdown Sheet
    if (include_branch_breakdown) {
      const branchBreakdown = await this.getBranchBreakdown(filters);
      const branchSheet = workbook.addWorksheet('Branch Breakdown');

      branchSheet.addRow(['Branch', 'Location', 'Region', 'Total Sales', 'Total Amount', 'Discount', 'Net Amount', 'Average Sale']);

      branchBreakdown.forEach(branch => {
        branchSheet.addRow([
          branch.branch_name,
          branch.branch_location,
          branch.region_name,
          branch.total_sales,
          branch.total_amount,
          branch.discount_amount,
          branch.net_amount,
          branch.average_sale.toFixed(2)
        ]);
      });

      // Style headers
      const headerRow = branchSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      branchSheet.columns.forEach(column => {
        column.width = 15;
      });
    }

    // Payment Method Breakdown Sheet
    if (include_payment_breakdown && Object.keys(stats.by_payment_method).length > 0) {
      const paymentSheet = workbook.addWorksheet('Payment Methods');

      paymentSheet.addRow(['Payment Method', 'Count', 'Total Amount', 'Percentage']);

      Object.entries(stats.by_payment_method).forEach(([method, data]) => {
        const percentage = ((data.amount / stats.net_amount) * 100).toFixed(1);
        paymentSheet.addRow([method, data.count, data.amount, `${percentage}%`]);
      });

      // Style headers
      const headerRow = paymentSheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };

      paymentSheet.columns.forEach(column => {
        column.width = 20;
      });
    }

    return workbook;
  }
}

const salesSummaryExportController = new SalesSummaryExportController();

/**
 * Export all sales summary data (optimized for performance)
 */
const exportAllSalesSummary = async (req, res, next) => {
  // Set timeout for large exports
  const timeout = setTimeout(() => {
    if (!res.headersSent) {
      res.status(408).json({
        error: 'Export timeout',
        message: 'The export is taking too long. Try filtering your data or use summary export.'
      });
    }
  }, 30000); // 30 seconds timeout

  try {
    const filters = req.query;
    logger.info('Starting comprehensive sales summary export with filters:', filters);

    // Get sales data with smaller limit for faster processing
    const sales = await salesSummaryExportController.getAllSalesData(filters, 1000);

    if (sales.length === 0) {
      clearTimeout(timeout);
      return res.status(404).json({
        error: 'No sales data found for the specified criteria',
        message: 'Try adjusting your date range or removing filters to see more data.'
      });
    }

    logger.info(`Creating Excel workbook for ${sales.length} sales records`);

    // Create Excel workbook with limited features for speed
    const workbook = await salesSummaryExportController.createExcelWorkbook(sales, {
      include_summary: true,
      include_sales_details: true,
      include_product_breakdown: false, // Skip to improve performance
      include_branch_breakdown: false, // Skip to improve performance
      include_payment_breakdown: false, // Skip to improve performance
      filters
    });

    // Clear timeout since we're about to send response
    clearTimeout(timeout);

    // Set response headers
    const filename = `sales-summary-comprehensive-${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info(`Sales summary export completed successfully: ${sales.length} records exported as ${filename}`);
  } catch (error) {
    clearTimeout(timeout);
    logger.error('Error in exportAllSalesSummary:', error);

    // Send error response if headers haven't been sent
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to export sales summary',
        message: error.message || 'An unexpected error occurred during export'
      });
    }
  }
};

/**
 * Export custom sales summary data (optimized)
 */
const exportCustomSalesSummary = async (req, res, next) => {
  // Set timeout for exports
  const timeout = setTimeout(() => {
    if (!res.headersSent) {
      res.status(408).json({
        error: 'Export timeout',
        message: 'The export is taking too long. Try using summary format or filtering the data.'
      });
    }
  }, 20000); // 20 seconds timeout for custom exports

  try {
    const {
      include_summary = 'true',
      include_sales_details = 'true',
      include_product_breakdown = 'true',
      include_branch_breakdown = 'true',
      include_payment_breakdown = 'true',
      format_type = 'detailed',
      ...filters
    } = req.query;

    logger.info('Starting custom sales summary export with options:', {
      include_summary,
      include_sales_details,
      include_product_breakdown,
      include_branch_breakdown,
      include_payment_breakdown,
      format_type,
      filters
    });

    // Determine limit based on format type - reduced for better performance
    const limit = format_type === 'summary' ? 1500 : 500;

    // Get sales data with appropriate limit
    const sales = await salesSummaryExportController.getAllSalesData(filters, limit);

    if (sales.length === 0) {
      clearTimeout(timeout);
      return res.status(404).json({
        error: 'No sales data found for the specified criteria',
        message: 'Try adjusting your date range or removing filters to see more data.'
      });
    }

    // Create Excel workbook with custom options
    const workbook = await salesSummaryExportController.createExcelWorkbook(sales, {
      include_summary: include_summary === 'true',
      include_sales_details: include_sales_details === 'true',
      include_product_breakdown: include_product_breakdown === 'true',
      include_branch_breakdown: include_branch_breakdown === 'true',
      include_payment_breakdown: include_payment_breakdown === 'true',
      filters
    });

    // Clear timeout since we're about to send response
    clearTimeout(timeout);

    // Set response headers
    const formatSuffix = format_type === 'summary' ? 'summary' : 'custom';
    const filename = `sales-summary-${formatSuffix}-${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info(`Custom sales summary export completed: ${sales.length} records exported as ${filename}`);
  } catch (error) {
    clearTimeout(timeout);
    logger.error('Error in exportCustomSalesSummary:', error);

    // Send error response if headers haven't been sent
    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to export custom sales summary',
        message: error.message || 'An unexpected error occurred during export'
      });
    }
  }
};

/**
 * Export lightweight sales summary (fastest option)
 */
const exportLightweightSalesSummary = async (req, res, next) => {
  try {
    const filters = req.query;
    logger.info('Starting lightweight sales summary export with filters:', filters);

    // Get minimal sales data for fastest export
    const sales = await salesSummaryExportController.getAllSalesData(filters, 500);

    if (sales.length === 0) {
      return res.status(404).json({
        error: 'No sales data found for the specified criteria',
        message: 'Try adjusting your date range or removing filters to see more data.'
      });
    }

    logger.info(`Creating lightweight Excel workbook for ${sales.length} sales records`);

    // Create simple workbook with just sales data
    const workbook = new ExcelJS.Workbook();
    workbook.creator = 'DukaLink POS System';
    workbook.created = new Date();

    // Simple sales sheet only
    const salesSheet = workbook.addWorksheet('Sales Data');

    // Headers
    const headers = [
      'Receipt Number',
      'Date',
      'Branch',
      'User',
      'Total Amount',
      'Discount',
      'Net Amount',
      'Payment Method'
    ];

    salesSheet.addRow(headers);

    // Data rows
    sales.forEach(sale => {
      salesSheet.addRow([
        sale.receipt_number,
        sale.created_at ? new Date(sale.created_at).toLocaleDateString() : '',
        sale.branch_name,
        sale.user_name,
        sale.total_amount,
        sale.discount_amount,
        sale.net_amount,
        sale.payment_method
      ]);
    });

    // Style the headers
    const headerRow = salesSheet.getRow(1);
    headerRow.font = { bold: true };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Auto-fit columns
    salesSheet.columns.forEach(column => {
      column.width = 15;
    });

    // Set response headers
    const filename = `sales-data-${new Date().toISOString().split('T')[0]}.xlsx`;
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Write workbook to response
    await workbook.xlsx.write(res);
    res.end();

    logger.info(`Lightweight sales export completed: ${sales.length} records exported as ${filename}`);
  } catch (error) {
    logger.error('Error in exportLightweightSalesSummary:', error);

    if (!res.headersSent) {
      res.status(500).json({
        error: 'Failed to export sales data',
        message: error.message || 'An unexpected error occurred during export'
      });
    }
  }
};

module.exports = {
  salesSummaryExportController,
  exportAllSalesSummary,
  exportCustomSalesSummary,
  exportLightweightSalesSummary
};
