const DsaSaleService = require('../services/DsaSaleService');
const AppError = require('../utils/error');
const logger = require('../utils/logger');

/**
 * Improved DSA Sale Controller
 * Handles DSA sales separately from assignments
 */

/**
 * Create a DSA sale (when DSA agent sells to end customer)
 * This replaces the old createDsaSale that was confusing assignments with sales
 */
exports.createDsaSale = async (req, res, next) => {
  try {
    const {
      dsa_agent_id,
      product_id,
      quantity_sold,
      unit_price,
      payment_method_id,
      payment_reference,
      notes,
      employee_id
    } = req.body;

    // Validate required fields
    if (!dsa_agent_id || !product_id || !quantity_sold || !unit_price || !employee_id) {
      throw new AppError(400, 'DSA agent ID, product ID, quantity sold, unit price, and employee ID are required');
    }

    logger.info(`Creating DSA sale: agent=${dsa_agent_id}, product=${product_id}, qty=${quantity_sold}`);

    const sale = await DsaSaleService.createDsaSale({
      dsa_agent_id,
      product_id,
      quantity_sold: parseInt(quantity_sold),
      unit_price: parseFloat(unit_price),
      payment_method_id: payment_method_id || 1,
      payment_reference: payment_reference || '',
      notes: notes || '',
      employee_id
    });

    res.status(201).json({
      success: true,
      message: 'DSA sale created successfully',
      data: sale
    });
  } catch (error) {
    logger.error('Error creating DSA sale:', error);
    next(error);
  }
};

/**
 * Get DSA sales summary
 */
exports.getDsaSalesSummary = async (req, res, next) => {
  try {
    const { branch_id, dsa_agent_id, start_date, end_date } = req.query;

    const sales = await DsaSaleService.getDsaSalesSummary({
      branch_id,
      dsa_agent_id,
      start_date,
      end_date
    });

    res.status(200).json({
      success: true,
      data: sales
    });
  } catch (error) {
    logger.error('Error getting DSA sales summary:', error);
    next(error);
  }
};

/**
 * Reconcile DSA assignment
 * This handles when DSA agents return stock and make payments
 */
exports.reconcileAssignment = async (req, res, next) => {
  try {
    const {
      assignment_identifier,
      dsa_agent_id,
      items_returned,
      cash_received,
      paybill_amount,
      notes
    } = req.body;

    if (!assignment_identifier || !dsa_agent_id) {
      throw new AppError(400, 'Assignment identifier and DSA agent ID are required');
    }

    logger.info(`Reconciling DSA assignment: ${assignment_identifier} for agent ${dsa_agent_id}`);

    const result = await DsaSaleService.reconcileAssignment({
      assignment_identifier,
      dsa_agent_id,
      items_returned: items_returned || [],
      cash_received: parseFloat(cash_received || 0),
      paybill_amount: parseFloat(paybill_amount || 0),
      notes: notes || ''
    });

    res.status(200).json({
      success: true,
      message: 'Assignment reconciled successfully',
      data: result
    });
  } catch (error) {
    logger.error('Error reconciling DSA assignment:', error);
    next(error);
  }
};

/**
 * Get DSA assignment vs sales report
 * Shows the difference between what was assigned and what was sold
 */
exports.getAssignmentVsSalesReport = async (req, res, next) => {
  try {
    const { branch_id, dsa_agent_id } = req.query;

    // This would be implemented to show:
    // - Total assigned value
    // - Total sold value  
    // - Total returned value
    // - Outstanding balance
    // - Payment status

    res.status(200).json({
      success: true,
      message: 'Assignment vs sales report',
      data: {
        // Report data would go here
        note: 'This endpoint separates assignments from actual sales for clearer reporting'
      }
    });
  } catch (error) {
    logger.error('Error getting assignment vs sales report:', error);
    next(error);
  }
};
