"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layouts/main-layout";
import { Skeleton } from "@/components/ui/skeleton";
import { ReportChart } from "@/features/reports/components/report-chart";
import { ReportDataTable } from "@/features/reports/components/report-data-table";
import { ReportFilters } from "@/features/reports/components/report-filters";
import { ReportStatCard } from "@/features/reports/components/report-stat-card";
import { MpesaTransactionsExportDialog } from "@/features/reports/components/mpesa-transactions-export-dialog";
import { ExportPerformanceNotice } from "@/features/reports/components/export-performance-notice";
import { useMpesaTransactionsReport } from "@/features/reports/hooks/use-reports";
import { formatCurrency } from "@/lib/utils";
import { ReportFilterParams } from "@/types";
import { format, subDays } from "date-fns";
import { ColumnDef } from "@tanstack/react-table";
import { Tabs, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ArrowDownIcon, ArrowUpIcon, Banknote, Download, TrendingDown, TrendingUp, Zap, Smartphone } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { exportToExcel } from "@/lib/excel-export";

export default function MpesaTransactionsReportPage() {
  // Initialize with default filters (last 7 days)
  const today = new Date();
  const sevenDaysAgo = subDays(today, 7);

  const [filters, setFilters] = useState<ReportFilterParams>({
    start_date: format(sevenDaysAgo, "yyyy-MM-dd"),
    end_date: format(today, "yyyy-MM-dd"),
  });

  const { data, isLoading, error } = useMpesaTransactionsReport(filters);

  const handleFilterChange = (newFilters: ReportFilterParams) => {
    setFilters(newFilters);
  };

  // Define columns for the summary table
  const summaryColumns: ColumnDef<any>[] = [
    {
      accessorKey: "date",
      header: "Date",
    },
    {
      accessorKey: "deposits",
      header: "Deposits",
      cell: ({ row }) => formatCurrency(row.original.deposits),
    },
    {
      accessorKey: "withdrawals",
      header: "Withdrawals",
      cell: ({ row }) => formatCurrency(row.original.withdrawals),
    },
    {
      accessorKey: "net",
      header: "Net Cashflow",
      cell: ({ row }) => {
        const value = row.original.net;
        return (
          <div className={value >= 0 ? "text-green-600" : "text-red-600"}>
            {formatCurrency(value)}
          </div>
        );
      },
    },
  ];

  // Define columns for the branch summary table
  const branchColumns: ColumnDef<any>[] = [
    {
      accessorKey: "branchName",
      header: "Branch",
    },
    {
      accessorKey: "deposits",
      header: "Deposits",
      cell: ({ row }) => formatCurrency(row.original.deposits),
    },
    {
      accessorKey: "withdrawals",
      header: "Withdrawals",
      cell: ({ row }) => formatCurrency(row.original.withdrawals),
    },
    {
      accessorKey: "net",
      header: "Net Cashflow",
      cell: ({ row }) => {
        const value = row.original.net;
        return (
          <div className={value >= 0 ? "text-green-600" : "text-red-600"}>
            {formatCurrency(value)}
          </div>
        );
      },
    },
    {
      accessorKey: "transactionCount",
      header: "Transactions",
    },
  ];

  // Define columns for the transactions table
  const transactionColumns: ColumnDef<any>[] = [
    {
      accessorKey: "transaction_date",
      header: "Date",
      cell: ({ row }) => format(new Date(row.original.transaction_date), "MMM dd, yyyy"),
    },
    {
      accessorKey: "mpesa_code",
      header: "M-Pesa Code",
    },
    {
      accessorKey: "type",
      header: "Type",
      cell: ({ row }) => {
        const type = row.original.type;
        return (
          <Badge variant={type === "deposit" ? "success" : "destructive"}>
            {type === "deposit" ? (
              <ArrowUpIcon className="mr-1 h-3 w-3" />
            ) : (
              <ArrowDownIcon className="mr-1 h-3 w-3" />
            )}
            {type.charAt(0).toUpperCase() + type.slice(1)}
          </Badge>
        );
      },
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }) => formatCurrency(row.original.amount),
    },
    {
      accessorKey: "Branch.name",
      header: "Branch",
      cell: ({ row }) => row.original.Branch?.name || "N/A",
    },
    {
      accessorKey: "User.name",
      header: "User",
      cell: ({ row }) => row.original.User?.name || "N/A",
    },
  ];

  // Handle export to Excel
  const handleExportSummary = () => {
    if (!data) return;

    exportToExcel(
      data.summaryData.map(item => ({
        Date: item.date,
        Deposits: item.deposits,
        Withdrawals: item.withdrawals,
        "Net Cashflow": item.net
      })),
      "mpesa-transactions-summary"
    );
  };

  const handleExportBranches = () => {
    if (!data) return;

    exportToExcel(
      data.branchData.map(item => ({
        Branch: item.branchName,
        Deposits: item.deposits,
        Withdrawals: item.withdrawals,
        "Net Cashflow": item.net,
        Transactions: item.transactionCount
      })),
      "mpesa-transactions-by-branch"
    );
  };

  const handleExportTransactions = () => {
    if (!data) return;

    exportToExcel(
      data.transactionsData.map(item => ({
        Date: format(new Date(item.transaction_date), "MMM dd, yyyy"),
        "M-Pesa Code": item.mpesa_code,
        Type: item.type.charAt(0).toUpperCase() + item.type.slice(1),
        Amount: parseFloat(item.amount as string),
        Branch: item.Branch?.name || "N/A",
        User: item.User?.name || "N/A"
      })),
      "mpesa-transactions-details"
    );
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">M-Pesa Transactions Report</h1>
            <p className="text-muted-foreground">
              Analysis of M-Pesa deposits and withdrawals
            </p>
          </div>
          <div className="flex gap-2">
            <MpesaTransactionsExportDialog
              filters={filters}
              totalRecords={data?.transactionsData?.length || 0}
              trigger={
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export Options
                </Button>
              }
            />
            <MpesaTransactionsExportDialog
              filters={filters}
              totalRecords={data?.transactionsData?.length || 0}
              trigger={
                <Button>
                  <Zap className="mr-2 h-4 w-4" />
                  Quick Export
                </Button>
              }
            />
          </div>
        </div>

        {/* Export Performance Notice */}
        <ExportPerformanceNotice
          totalRecords={data?.transactionsData?.length || 0}
          hasDateFilter={!!(filters.start_date && filters.end_date)}
          className="mb-6"
        />

        <ReportFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          showTimeFilter={false}
          showSessionFilter={false}
          showUserFilter={false}
          showBranchFilter={true}
          showRegionFilter={true}
          showPaymentMethodFilter={false}
          showStatusFilter={false}
          showDsaFilter={false}
        />

        {isLoading ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
            </div>
            <Skeleton className="h-[250px]" />
            <Skeleton className="h-96" />
          </div>
        ) : error ? (
          <div className="rounded-md bg-destructive/10 p-4 text-destructive">
            Error loading report data. Please try again.
          </div>
        ) : data ? (
          <>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <ReportStatCard
                title="Total Deposits"
                value={formatCurrency(data.metrics.totalDeposits)}
                description="Total M-Pesa deposits"
                icon={<TrendingUp className="h-4 w-4 text-green-600" />}
                trend="up"
              />
              <ReportStatCard
                title="Total Withdrawals"
                value={formatCurrency(data.metrics.totalWithdrawals)}
                description="Total M-Pesa withdrawals"
                icon={<TrendingDown className="h-4 w-4 text-red-600" />}
                trend="down"
              />
              <ReportStatCard
                title="Net Cashflow"
                value={formatCurrency(data.metrics.netCashflow)}
                description="Deposits minus withdrawals"
                icon={<Banknote className="h-4 w-4" />}
                trend={data.metrics.netCashflow >= 0 ? "up" : "down"}
              />
              <ReportStatCard
                title="Total Transactions"
                value={data.metrics.totalTransactions.toString()}
                description="Number of M-Pesa transactions"
                icon="transaction"
                isCurrency={false}
              />
            </div>

            <ReportChart
              title="M-Pesa Transactions Trend"
              description="Daily deposits and withdrawals"
              data={data.chartData}
              chartTypes={["bar", "line"]}
              defaultChartType="bar"
              showLegend={true}
            />

            <Tabs defaultValue="summary">
              <div className="flex items-center justify-between mb-4">
                <TabsList>
                  <TabsTrigger value="summary">Daily Summary</TabsTrigger>
                  <TabsTrigger value="branches">Branch Summary</TabsTrigger>
                  <TabsTrigger value="transactions">All Transactions</TabsTrigger>
                </TabsList>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const activeTab = document.querySelector('[role="tabpanel"][data-state="active"]')?.getAttribute('data-value');
                      if (activeTab === 'summary') handleExportSummary();
                      else if (activeTab === 'branches') handleExportBranches();
                      else if (activeTab === 'transactions') handleExportTransactions();
                    }}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Export Current Tab
                  </Button>
                  <MpesaTransactionsExportDialog
                    filters={filters}
                    totalRecords={data?.transactionsData?.length || 0}
                    trigger={
                      <Button size="sm">
                        <Smartphone className="mr-2 h-4 w-4" />
                        Advanced Export
                      </Button>
                    }
                  />
                </div>
              </div>
              <TabsContent value="summary">
                <ReportDataTable
                  columns={summaryColumns}
                  data={data.summaryData}
                  title="Daily M-Pesa Transactions"
                  description="Summary of M-Pesa transactions by date"
                  searchColumn="date"
                  searchPlaceholder="Search by date..."
                  exportFilename="mpesa-transactions-summary"
                  showExport={false}
                />
              </TabsContent>
              <TabsContent value="branches">
                <ReportDataTable
                  columns={branchColumns}
                  data={data.branchData}
                  title="M-Pesa Transactions by Branch"
                  description="Summary of M-Pesa transactions by branch"
                  searchColumn="branchName"
                  searchPlaceholder="Search by branch name..."
                  exportFilename="mpesa-transactions-by-branch"
                  showExport={false}
                />
              </TabsContent>
              <TabsContent value="transactions">
                <ReportDataTable
                  columns={transactionColumns}
                  data={data.transactionsData}
                  title="M-Pesa Transaction Details"
                  description="List of all M-Pesa transactions"
                  searchColumn="mpesa_code"
                  searchPlaceholder="Search by M-Pesa code..."
                  exportFilename="mpesa-transactions-details"
                  showExport={false}
                />
              </TabsContent>
            </Tabs>
          </>
        ) : null}
      </div>
    </MainLayout>
  );
}
