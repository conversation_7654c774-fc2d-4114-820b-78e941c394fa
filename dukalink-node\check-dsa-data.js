const sequelize = require('./config/database');

async function checkDsaData() {
  try {
    console.log('Checking DSA assignments and sales data...');
    
    // Check total DSA assignments
    const totalAssignments = await sequelize.query(`
      SELECT COUNT(*) as count FROM dsa_stock_assignments
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });
    console.log(`Total DSA assignments: ${totalAssignments[0].count}`);

    // Check assignments with quantity_sold > 0
    const assignmentsWithSales = await sequelize.query(`
      SELECT COUNT(*) as count FROM dsa_stock_assignments WHERE quantity_sold > 0
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });
    console.log(`DSA assignments with quantity_sold > 0: ${assignmentsWithSales[0].count}`);

    // Check assignments without sale_id
    const assignmentsWithoutSaleId = await sequelize.query(`
      SELECT COUNT(*) as count FROM dsa_stock_assignments WHERE sale_id IS NULL
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });
    console.log(`DSA assignments without sale_id: ${assignmentsWithoutSaleId[0].count}`);

    // Check assignments without sale_id but with quantity_sold > 0
    const targetAssignments = await sequelize.query(`
      SELECT COUNT(*) as count FROM dsa_stock_assignments 
      WHERE sale_id IS NULL AND quantity_sold > 0
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });
    console.log(`DSA assignments without sale_id but with quantity_sold > 0: ${targetAssignments[0].count}`);

    // Check total DSA sales
    const totalDsaSales = await sequelize.query(`
      SELECT COUNT(*) as count FROM sales WHERE is_dsa = 1
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });
    console.log(`Total DSA sales: ${totalDsaSales[0].count}`);

    // Show sample DSA assignments
    const sampleAssignments = await sequelize.query(`
      SELECT 
        id, customer_id, product_id, branch_id, quantity_assigned, 
        quantity_sold, quantity_returned, sale_id, created_at
      FROM dsa_stock_assignments 
      ORDER BY created_at DESC 
      LIMIT 5
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });
    console.log('\nSample DSA assignments:');
    console.table(sampleAssignments);

    // Show sample DSA sales
    const sampleSales = await sequelize.query(`
      SELECT 
        s.id, s.customer_id, s.branch_id, s.total_amount, s.is_dsa, s.created_at,
        si.product_id, si.quantity
      FROM sales s
      LEFT JOIN sale_items si ON s.id = si.sale_id
      WHERE s.is_dsa = 1
      ORDER BY s.created_at DESC 
      LIMIT 5
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });
    console.log('\nSample DSA sales:');
    console.table(sampleSales);

    // Check if sale_id column exists
    const columns = await sequelize.query(`
      SHOW COLUMNS FROM dsa_stock_assignments LIKE 'sale_id'
    `, { 
      type: sequelize.Sequelize.QueryTypes.SELECT
    });
    console.log(`\nSale_id column exists: ${columns.length > 0}`);
    if (columns.length > 0) {
      console.table(columns);
    }

  } catch (error) {
    console.error('Error checking DSA data:', error);
  } finally {
    await sequelize.close();
  }
}

checkDsaData();
