const express = require('express');
const router = express.Router();
const salesSummaryController = require('../../controllers/report/sales-summary.controller');
const { exportAllSalesSummary, exportCustomSalesSummary, exportLightweightSalesSummary } = require('../../controllers/report/sales-summary-export.controller');
const { authenticate, rbac } = require('../../middleware/auth.middleware');

/**
 * @swagger
 * /api/v1/reports/sales-summary:
 *   get:
 *     summary: Get sales summary report
 *     description: Retrieve detailed sales data with filtering by product, location, and date
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: true
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: true
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: product_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by product ID
 *       - in: query
 *         name: category_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by product category ID
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: location_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by location ID
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, excel]
 *         required: false
 *         description: Response format (default is json)
 *     responses:
 *       200:
 *         description: Sales summary report
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 filters:
 *                   type: object
 *                 summary:
 *                   type: object
 *                 by_product:
 *                   type: array
 *                   items:
 *                     type: object
 *                 by_branch:
 *                   type: array
 *                   items:
 *                     type: object
 *                 sales:
 *                   type: array
 *                   items:
 *                     type: object
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/',
  authenticate,
  rbac.checkPermission('sales_reports', 'read'),
  salesSummaryController.getSalesSummaryReport
);

/**
 * @swagger
 * /api/v1/reports/sales-summary/export/all:
 *   get:
 *     summary: Export comprehensive sales summary to Excel
 *     description: Export all sales data with multiple sheets including summary, details, product breakdown, branch analysis, and payment methods
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by region ID
 *       - in: query
 *         name: product_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by product ID
 *       - in: query
 *         name: category_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by category ID
 *       - in: query
 *         name: payment_method
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by payment method
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by user ID
 *     responses:
 *       200:
 *         description: Excel file with comprehensive sales summary
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No sales data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/all',
  authenticate,
  rbac.checkPermission('sales_reports', 'read'),
  exportAllSalesSummary
);

/**
 * @swagger
 * /api/v1/reports/sales-summary/export/custom:
 *   get:
 *     summary: Export custom sales summary to Excel
 *     description: Export sales data with customizable sheets and format options
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: region_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by region ID
 *       - in: query
 *         name: product_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by product ID
 *       - in: query
 *         name: category_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by category ID
 *       - in: query
 *         name: payment_method
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by payment method
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by user ID
 *       - in: query
 *         name: include_summary
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include summary sheet
 *       - in: query
 *         name: include_sales_details
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include sales details sheet
 *       - in: query
 *         name: include_product_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include product breakdown sheet
 *       - in: query
 *         name: include_branch_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include branch breakdown sheet
 *       - in: query
 *         name: include_payment_breakdown
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include payment breakdown sheet
 *       - in: query
 *         name: format_type
 *         schema:
 *           type: string
 *           enum: [detailed, summary]
 *           default: detailed
 *         description: Format type for export
 *     responses:
 *       200:
 *         description: Excel file with custom sales summary
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No sales data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/custom',
  authenticate,
  rbac.checkPermission('sales_reports', 'read'),
  exportCustomSalesSummary
);

/**
 * @swagger
 * /api/v1/reports/sales-summary/export/lightweight:
 *   get:
 *     summary: Export lightweight sales data to Excel (fastest option)
 *     description: Export basic sales data only for fastest processing - limited to 500 records
 *     tags: [Reports]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: start_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: Start date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: end_date
 *         schema:
 *           type: string
 *           format: date
 *         required: false
 *         description: End date for the report (YYYY-MM-DD)
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by branch ID
 *       - in: query
 *         name: payment_method
 *         schema:
 *           type: string
 *         required: false
 *         description: Filter by payment method
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: integer
 *         required: false
 *         description: Filter by user ID
 *     responses:
 *       200:
 *         description: Excel file with basic sales data
 *         content:
 *           application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: No sales data found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get(
  '/export/lightweight',
  authenticate,
  rbac.checkPermission('sales_reports', 'read'),
  exportLightweightSalesSummary
);

module.exports = router;
