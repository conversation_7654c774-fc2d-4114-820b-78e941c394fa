import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import bankingTransactionsExportService, {
  BankingTransactionsExportParams,
  CustomBankingTransactionsExportParams,
} from "../api/banking-transactions-export-service";

/**
 * Hook for comprehensive banking transactions export
 * Exports all data with multiple sheets
 */
export const useBankingTransactionsExportAll = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: BankingTransactionsExportParams) => {
      // Validate parameters
      const validation = bankingTransactionsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await bankingTransactionsExportService.exportAllBankingTransactions(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = bankingTransactionsExportService.generateFilename(params, 'comprehensive');
        bankingTransactionsExportService.downloadBlob(blob, filename);
        
        toast.success("Banking transactions exported successfully!", {
          description: `Comprehensive report with all data sheets downloaded as ${filename}`,
          duration: 5000,
        });

        // Invalidate related queries to refresh any cached data
        queryClient.invalidateQueries({ queryKey: ["banking-transactions"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Export error:', error);
      
      let errorMessage = "Failed to export banking transactions";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export banking reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try filtering the data or use lightweight export.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for custom banking transactions export
 * Allows format and sheet selection
 */
export const useBankingTransactionsExportCustom = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: CustomBankingTransactionsExportParams) => {
      // Validate parameters
      const validation = bankingTransactionsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await bankingTransactionsExportService.exportCustomBankingTransactions(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const formatType = params.format_type || 'detailed';
        const filename = bankingTransactionsExportService.generateFilename(params, formatType);
        bankingTransactionsExportService.downloadBlob(blob, filename);
        
        toast.success("Custom banking transactions export completed!", {
          description: `${formatType.charAt(0).toUpperCase() + formatType.slice(1)} report downloaded as ${filename}`,
          duration: 5000,
        });

        queryClient.invalidateQueries({ queryKey: ["banking-transactions"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Custom export error:', error);
      
      let errorMessage = "Failed to export custom banking transactions";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export banking reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. Try using lightweight format or filtering the data.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Custom Export Failed", {
        description: errorMessage,
        duration: 8000,
      });
    },
  });
};

/**
 * Hook for lightweight banking transactions export
 * Fastest export with essential data only
 */
export const useBankingTransactionsExportLightweight = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: BankingTransactionsExportParams) => {
      const validation = bankingTransactionsExportService.validateExportParams(params);
      if (!validation.valid) {
        throw new Error(`Invalid parameters: ${validation.errors.join(', ')}`);
      }

      const blob = await bankingTransactionsExportService.exportLightweightBankingTransactions(params);
      return { blob, params };
    },
    onSuccess: ({ blob, params }) => {
      try {
        const filename = bankingTransactionsExportService.generateFilename(params, 'lightweight');
        bankingTransactionsExportService.downloadBlob(blob, filename);
        
        toast.success("Quick export completed!", {
          description: `Transaction summary report downloaded as ${filename}`,
          duration: 4000,
        });

        queryClient.invalidateQueries({ queryKey: ["banking-transactions"] });
        queryClient.invalidateQueries({ queryKey: ["reports"] });
      } catch (error) {
        console.error('Error downloading file:', error);
        toast.error("Export completed but failed to download file");
      }
    },
    onError: (error: any) => {
      console.error('Lightweight export error:', error);
      
      let errorMessage = "Failed to export banking transactions data";
      
      if (error.message.includes("permission")) {
        errorMessage = "You don't have permission to export banking reports";
      } else if (error.message.includes("timeout")) {
        errorMessage = "Export timed out. The system may be busy, please try again.";
      } else if (error.message.includes("Invalid parameters")) {
        errorMessage = error.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error("Quick Export Failed", {
        description: errorMessage,
        duration: 6000,
      });
    },
  });
};

/**
 * Hook to get export recommendations based on data size
 */
export const useBankingTransactionsExportRecommendation = (estimatedRecords: number) => {
  return bankingTransactionsExportService.getExportRecommendation(estimatedRecords);
};

/**
 * Hook to get export format options for UI
 */
export const useBankingTransactionsExportFormatOptions = () => {
  return bankingTransactionsExportService.getExportFormatOptions();
};

/**
 * Hook to get column options for custom export
 */
export const useBankingTransactionsExportColumnOptions = () => {
  return bankingTransactionsExportService.getColumnOptions();
};

/**
 * Combined hook that provides all export functionality
 * Convenient single hook for components that need multiple export options
 */
export const useBankingTransactionsExportSuite = () => {
  const exportAll = useBankingTransactionsExportAll();
  const exportCustom = useBankingTransactionsExportCustom();
  const exportLightweight = useBankingTransactionsExportLightweight();

  const isAnyExporting = exportAll.isPending || exportCustom.isPending || exportLightweight.isPending;

  return {
    // Individual export methods
    exportAll: exportAll.mutateAsync,
    exportCustom: exportCustom.mutateAsync,
    exportLightweight: exportLightweight.mutateAsync,
    
    // Loading states
    isExportingAll: exportAll.isPending,
    isExportingCustom: exportCustom.isPending,
    isExportingLightweight: exportLightweight.isPending,
    isAnyExporting,
    
    // Error states
    exportAllError: exportAll.error,
    exportCustomError: exportCustom.error,
    exportLightweightError: exportLightweight.error,
    
    // Utility functions
    getRecommendation: bankingTransactionsExportService.getExportRecommendation,
    getFormatOptions: bankingTransactionsExportService.getExportFormatOptions,
    getColumnOptions: bankingTransactionsExportService.getColumnOptions,
    validateParams: bankingTransactionsExportService.validateExportParams,
    generateFilename: bankingTransactionsExportService.generateFilename,
    
    // Reset functions
    resetAll: () => {
      exportAll.reset();
      exportCustom.reset();
      exportLightweight.reset();
    },
  };
};
