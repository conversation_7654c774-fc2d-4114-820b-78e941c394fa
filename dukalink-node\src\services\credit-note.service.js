const {
  CreditNote,
  CreditNoteItem,
  Invoice,
  InvoiceItem,
  Sale,
  SaleItem,
  Customer,
  Supplier,
  Product
} = require('../models');
const { generateReferenceNumber } = require('../utils/reference-number');
const { Op, literal, Transaction } = require('sequelize');
const logger = require('../utils/logger');
const sequelize = require('../../config/database');
const KraIntegrationService = require('./kra-integration.service');
const KraTimsService = require('./kra-tims.service');

/**
 * Credit Note Service
 */
class CreditNoteService {
  /**
   * Create a new credit note
   * @param {Object} creditNoteData - Credit note data
   * @returns {Promise<Object>} Created credit note
   */
  async createCreditNote(creditNoteData) {
    const transaction = await sequelize.transaction();
    let creditNoteId = null;

    try {
      // Generate a unique credit note number
      const creditNoteNumber = await this.generateCreditNoteNumber();

      // Create the credit note
      const creditNote = await CreditNote.create({
        ...creditNoteData,
        credit_note_number: creditNoteNumber,
        status: 'draft'
      }, { transaction });

      creditNoteId = creditNote.id;

      // Create credit note items
      if (creditNoteData.items && creditNoteData.items.length > 0) {
        // Log the raw items data for debugging
        logger.info(`Creating credit note items. Raw items data:`, JSON.stringify(creditNoteData.items, null, 2));

        const creditNoteItems = creditNoteData.items.map((item, index) => {
          // Log each item's VAT information
          logger.info(`Credit note item ${index + 1}:`);
          logger.info(`  - description: ${item.description}`);
          logger.info(`  - vat_rate: ${item.vat_rate} (type: ${typeof item.vat_rate})`);
          logger.info(`  - vat_amount: ${item.vat_amount} (type: ${typeof item.vat_amount})`);
          logger.info(`  - is_vat_exempt: ${item.is_vat_exempt} (type: ${typeof item.is_vat_exempt})`);

          return {
            ...item,
            credit_note_id: creditNote.id
          };
        });

        logger.info(`Formatted credit note items for database:`, JSON.stringify(creditNoteItems, null, 2));
        await CreditNoteItem.bulkCreate(creditNoteItems, { transaction });
      }

      // If the credit note is for an invoice, update the invoice status
      if (creditNoteData.reference_type === 'invoice') {
        // Logic for updating invoice will be implemented later
      }

      // If the credit note is for a sale, update the sale status
      if (creditNoteData.reference_type === 'sale') {
        // Logic for updating sale will be implemented later
      }

      await transaction.commit();

      // KRA integration happens AFTER transaction is committed
      // This prevents transaction rollback issues
      try {
        // Fetch the complete credit note with items and associations
        const completeCreditNote = await CreditNote.findByPk(creditNoteId, {
          include: [
            {
              model: CreditNoteItem,
              as: 'items',
              include: [{ model: Product, as: 'product' }]
            },
            { model: Customer, as: 'customer' },
            { model: Supplier, as: 'supplier' }
          ]
        });

        // Check if raw KRA data was provided in the request
        if (creditNoteData.kra_items || creditNoteData.kra_customer_info) {
          // Add raw KRA data to the credit note object
          completeCreditNote.raw_items = creditNoteData.kra_items;
          completeCreditNote.raw_customer_info = creditNoteData.kra_customer_info;
        }

        const kraService = new KraTimsService();
        logger.info(`Registering credit note ${completeCreditNote.credit_note_number} with KRA`);
        const kraResponse = await kraService.registerCreditNote(completeCreditNote);

        if (kraResponse && kraResponse.success) {
          // Update credit note with KRA data
          const updateData = {
            kra_verification_code: kraResponse.verification_code,
            kra_fiscal_receipt_number: kraResponse.fiscal_receipt_number,
            kra_verification_url: kraResponse.verification_url,
            kra_verification_timestamp: kraResponse.timestamp,
            kra_integration_status: kraResponse.offline ? 'offline' : 'completed',
            kra_response_data: kraResponse.fullResponseData,
            status: kraResponse.offline ? 'draft' : 'issued'
          };

          await CreditNote.update(updateData, { where: { id: creditNoteId } });

          logger.info(`Credit note ${completeCreditNote.credit_note_number} KRA integration ${kraResponse.offline ? 'queued for retry' : 'completed successfully'}`);
        } else {
          logger.warn(`Credit note ${completeCreditNote.credit_note_number} KRA integration failed, setting status to pending`);
          await CreditNote.update({
            status: 'draft',
            kra_integration_status: 'failed'
          }, { where: { id: creditNoteId } });
        }
      } catch (kraError) {
        logger.error(`Error during credit note KRA integration: ${kraError.message}`);
        // Set status to draft so it can be retried later
        await CreditNote.update({
          status: 'draft',
          kra_integration_status: 'failed'
        }, { where: { id: creditNoteId } });
      }

      // Return the final credit note with updated KRA data
      const finalCreditNote = await this.getCreditNoteById(creditNoteId);

      return {
        success: true,
        data: finalCreditNote
      };
    } catch (error) {
      // Only rollback if transaction hasn't been committed yet
      if (!transaction.finished) {
        await transaction.rollback();
      }
      logger.error(`Error creating credit note: ${error.message}`);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * Generate a unique credit note number
   * @returns {Promise<string>} Generated credit note number
   */
  async generateCreditNoteNumber() {
    const prefix = 'CN';
    const referenceNumber = await generateReferenceNumber(prefix, 'credit_notes', 'credit_note_number');
    return referenceNumber;
  }

  /**
   * Get credit note by ID
   * @param {number} id - Credit note ID
   * @returns {Promise<Object>} Credit note with items
   */
  async getCreditNoteById(id) {
    const creditNote = await CreditNote.findByPk(id, {
      include: [
        {
          model: CreditNoteItem,
          as: 'items',
          include: [
            {
              model: Product,
              as: 'product',
              attributes: ['id', 'name', 'sku', 'is_vat_exempt']
            }
          ]
        },
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'phone', 'email', 'address']
        },
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['id', 'name', 'phone', 'email', 'address']
        }
      ]
    });

    if (!creditNote) {
      throw new Error(`Credit note with ID ${id} not found`);
    }

    return creditNote;
  }

  /**
   * Get all credit notes with filtering
   * @param {Object} filters - Filters for credit notes
   * @returns {Promise<Object>} Credit notes with pagination
   */
  async getCreditNotes(filters = {}) {
    const {
      page = 1,
      limit = 20,
      status,
      customer_id,
      supplier_id,
      reference_type,
      start_date,
      end_date,
      search,
      sort_by = 'created_at',
      sort_direction = 'desc'
    } = filters;

    const whereClause = {};

    // Apply filters
    if (status && status !== 'all') {
      whereClause.status = status;
    }

    if (customer_id) {
      whereClause.customer_id = customer_id;
    }

    if (supplier_id) {
      whereClause.supplier_id = supplier_id;
    }

    if (reference_type && reference_type !== 'all') {
      whereClause.reference_type = reference_type;
    }

    if (start_date && end_date) {
      whereClause.credit_note_date = {
        [Op.between]: [new Date(start_date), new Date(end_date)]
      };
    } else if (start_date) {
      whereClause.credit_note_date = {
        [Op.gte]: new Date(start_date)
      };
    } else if (end_date) {
      whereClause.credit_note_date = {
        [Op.lte]: new Date(end_date)
      };
    }

    if (search) {
      whereClause[Op.or] = [
        { credit_note_number: { [Op.like]: `%${search}%` } },
        { reference_number: { [Op.like]: `%${search}%` } }
      ];
    }

    // Calculate offset
    const offset = (page - 1) * limit;

    // Get credit notes
    const { count, rows } = await CreditNote.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Customer,
          as: 'customer',
          attributes: ['id', 'name', 'phone']
        },
        {
          model: Supplier,
          as: 'supplier',
          attributes: ['id', 'name', 'phone']
        }
      ],
      order: [[sort_by, sort_direction]],
      limit: parseInt(limit),
      offset: parseInt(offset)
    });

    // Calculate pagination
    const totalPages = Math.ceil(count / limit);

    return {
      success: true,
      data: rows,
      pagination: {
        total: count,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: totalPages
      }
    };
  }

  /**
   * Update credit note
   * @param {number} id - Credit note ID
   * @param {Object} updateData - Data to update
   * @returns {Promise<Object>} Update result
   */
  async updateCreditNote(id, updateData) {
    const transaction = await sequelize.transaction();

    try {
      const creditNote = await CreditNote.findByPk(id);

      if (!creditNote) {
        await transaction.rollback();
        return {
          success: false,
          message: `Credit note with ID ${id} not found`
        };
      }

      // Check if credit note can be updated
      if (creditNote.status === 'applied') {
        await transaction.rollback();
        return {
          success: false,
          message: 'Cannot update a credit note that has already been applied'
        };
      }

      // Update credit note
      await creditNote.update(updateData, { transaction });

      await transaction.commit();

      return {
        success: true,
        message: 'Credit note updated successfully'
      };
    } catch (error) {
      await transaction.rollback();
      logger.error(`Error updating credit note: ${error.message}`);
      return {
        success: false,
        message: error.message
      };
    }
  }

  /**
   * Issue a credit note (send to KRA)
   * @param {number} id - Credit note ID
   * @returns {Promise<Object>} Issue result
   */
  async issueCreditNote(id) {
    const transaction = await sequelize.transaction();

    try {
      const creditNote = await CreditNote.findByPk(id, {
        include: [{ model: CreditNoteItem, as: 'items' }]
      });

      if (!creditNote) {
        await transaction.rollback();
        return {
          success: false,
          message: `Credit note with ID ${id} not found`
        };
      }

      // Check if credit note can be issued
      if (creditNote.status !== 'draft') {
        await transaction.rollback();
        return {
          success: false,
          message: `Credit note with status ${creditNote.status} cannot be issued`
        };
      }

      // Register with KRA
      const kraService = new KraTimsService();
      const kraResponse = await kraService.registerCreditNote(creditNote);

      // Update credit note with KRA data
      await creditNote.update({
        status: 'issued',
        kra_verification_code: kraResponse.verification_code,
        kra_fiscal_receipt_number: kraResponse.fiscal_receipt_number,
        kra_verification_url: kraResponse.verification_url,
        kra_verification_timestamp: new Date(),
        kra_integration_status: 'completed',
        kra_response_data: JSON.stringify(kraResponse)
      }, { transaction });

      await transaction.commit();

      return {
        success: true,
        message: 'Credit note issued successfully',
        data: await this.getCreditNoteById(id)
      };
    } catch (error) {
      await transaction.rollback();
      logger.error(`Error issuing credit note: ${error.message}`);
      return {
        success: false,
        message: error.message
      };
    }
  }
}

module.exports = new CreditNoteService();
