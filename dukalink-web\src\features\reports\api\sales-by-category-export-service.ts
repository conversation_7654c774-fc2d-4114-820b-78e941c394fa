import apiClient from "@/lib/api-client";

/**
 * Sales by Category Export Service
 * Provides methods for comprehensive Excel export functionality for sales by category data
 */

export interface SalesByCategoryExportParams {
  start_date?: string;
  end_date?: string;
  branch_id?: number;
  region_id?: number;
  category_id?: number;
  include_summary?: boolean;
  include_details?: boolean;
  include_product_breakdown?: boolean;
  include_charts?: boolean;
}

export interface CustomSalesByCategoryExportParams extends SalesByCategoryExportParams {
  columns?: string; // comma-separated list or "all"
  format_type?: 'detailed' | 'summary';
}

const salesByCategoryExportService = {
  /**
   * Export all sales by category data with comprehensive sheets
   */
  exportAllSalesByCategory: async (params?: SalesByCategoryExportParams): Promise<Blob> => {
    try {
      console.log("Starting comprehensive sales by category export...", params);

      const response: any = await apiClient.get("/reports/sales-by-category/export/all", {
        params: {
          ...params,
          include_summary: params?.include_summary ?? true,
          include_details: params?.include_details ?? true,
          include_product_breakdown: params?.include_product_breakdown ?? true,
          include_charts: params?.include_charts ?? true,
        },
        responseType: "blob",
        timeout: 60000, // 1 minute timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting all sales by category:", error);
      
      // Provide more specific error messages
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export sales reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. Try filtering the data or use summary export.");
      } else {
        throw new Error(error.message || "Failed to export sales by category");
      }
    }
  },

  /**
   * Export sales by category with custom options
   */
  exportCustomSalesByCategory: async (params?: CustomSalesByCategoryExportParams): Promise<Blob> => {
    try {
      console.log("Starting custom sales by category export...", params);

      const response: any = await apiClient.get("/reports/sales-by-category/export/custom", {
        params: {
          ...params,
          columns: params?.columns || "all",
          format_type: params?.format_type || "detailed",
        },
        responseType: "blob",
        timeout: 45000, // 45 seconds timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting custom sales by category:", error);
      
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export sales reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. Try using summary format or filtering the data.");
      } else {
        throw new Error(error.message || "Failed to export custom sales by category");
      }
    }
  },

  /**
   * Export lightweight sales by category data (fastest option)
   */
  exportLightweightSalesByCategory: async (params?: SalesByCategoryExportParams): Promise<Blob> => {
    try {
      console.log("Starting lightweight sales by category export...", params);

      const response: any = await apiClient.get("/reports/sales-by-category/export/lightweight", {
        params: {
          ...params,
        },
        responseType: "blob",
        timeout: 20000, // 20 seconds timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting lightweight sales by category:", error);
      
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export sales reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. The system may be busy, please try again.");
      } else {
        throw new Error(error.message || "Failed to export lightweight sales by category");
      }
    }
  },

  /**
   * Download blob as file with proper filename
   */
  downloadBlob: (blob: Blob, filename?: string): void => {
    try {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Generate filename if not provided
      if (!filename) {
        const timestamp = new Date().toISOString().split('T')[0];
        filename = `sales-by-category-export-${timestamp}.xlsx`;
      }
      
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      console.log(`File downloaded: ${filename}`);
    } catch (error) {
      console.error('Error downloading file:', error);
      throw new Error('Failed to download the export file');
    }
  },

  /**
   * Generate descriptive filename based on filters
   */
  generateFilename: (params?: SalesByCategoryExportParams, formatType: string = 'comprehensive'): string => {
    const timestamp = new Date().toISOString().split('T')[0];
    const parts = ['sales-by-category'];
    
    if (formatType !== 'comprehensive') {
      parts.push(formatType);
    }
    
    if (params?.branch_id) {
      parts.push(`branch-${params.branch_id}`);
    }
    
    if (params?.region_id) {
      parts.push(`region-${params.region_id}`);
    }
    
    if (params?.category_id) {
      parts.push(`category-${params.category_id}`);
    }
    
    if (params?.start_date && params?.end_date) {
      parts.push(`${params.start_date}-to-${params.end_date}`);
    }
    
    parts.push(timestamp);
    
    return `${parts.join('-')}.xlsx`;
  },

  /**
   * Estimate export size and provide recommendations
   */
  getExportRecommendation: (estimatedRecords: number): {
    recommended: 'all' | 'custom' | 'lightweight';
    message: string;
    estimatedTime: string;
  } => {
    if (estimatedRecords <= 50) {
      return {
        recommended: 'all',
        message: 'Small dataset - comprehensive export recommended',
        estimatedTime: '< 20 seconds'
      };
    } else if (estimatedRecords <= 200) {
      return {
        recommended: 'custom',
        message: 'Medium dataset - custom export with selected sheets recommended',
        estimatedTime: '20-45 seconds'
      };
    } else {
      return {
        recommended: 'lightweight',
        message: 'Large dataset - lightweight export recommended for faster processing',
        estimatedTime: '< 20 seconds'
      };
    }
  },

  /**
   * Validate export parameters
   */
  validateExportParams: (params?: SalesByCategoryExportParams): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (params?.branch_id && (params.branch_id < 1 || !Number.isInteger(params.branch_id))) {
      errors.push('Branch ID must be a positive integer');
    }
    
    if (params?.region_id && (params.region_id < 1 || !Number.isInteger(params.region_id))) {
      errors.push('Region ID must be a positive integer');
    }
    
    if (params?.category_id && (params.category_id < 1 || !Number.isInteger(params.category_id))) {
      errors.push('Category ID must be a positive integer');
    }
    
    if (params?.start_date && params?.end_date) {
      const fromDate = new Date(params.start_date);
      const toDate = new Date(params.end_date);
      if (fromDate > toDate) {
        errors.push('Start date must be before end date');
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Get export format options for UI
   */
  getExportFormatOptions: () => [
    {
      value: 'lightweight',
      label: 'Quick Export',
      description: 'Category summary data only - fastest option',
      icon: 'Zap',
      estimatedTime: '< 20 seconds'
    },
    {
      value: 'custom',
      label: 'Custom Export',
      description: 'Select specific sheets and data to include',
      icon: 'Settings',
      estimatedTime: '20-45 seconds'
    },
    {
      value: 'all',
      label: 'Comprehensive Export',
      description: 'All category data with summary, details, and product breakdown',
      icon: 'FileSpreadsheet',
      estimatedTime: '45-60 seconds'
    }
  ],

  /**
   * Get available column options for custom export
   */
  getColumnOptions: () => [
    { value: 'summary', label: 'Category Summary', category: 'basic' },
    { value: 'sales_details', label: 'Sales Details', category: 'basic' },
    { value: 'product_breakdown', label: 'Product Breakdown', category: 'products' },
    { value: 'branch_info', label: 'Branch Information', category: 'location' },
    { value: 'region_info', label: 'Region Information', category: 'location' },
    { value: 'profit_analysis', label: 'Profit Analysis', category: 'financial' },
    { value: 'percentage_analysis', label: 'Percentage Analysis', category: 'financial' },
    { value: 'quantity_analysis', label: 'Quantity Analysis', category: 'details' },
    { value: 'dates', label: 'Date Information', category: 'details' },
  ]
};

export default salesByCategoryExportService;
