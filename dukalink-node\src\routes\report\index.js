const express = require("express");
const router = express.Router();

// Import comprehensive report routes
const dashboardRoutes = require("../reports/dashboard.routes");
const financialRoutes = require("../reports/financial.routes");
const inventoryEnhancedRoutes = require("../reports/inventory.routes");
const salesAnalyticsRoutes = require("../reports/sales.routes");
const operationsRoutes = require("../reports/operations.routes");
const usersRoutes = require("../reports/users.routes");

// Legacy report routes (existing)
const cashStatusRoutes = require("./cash-status.routes");
const stockAgingRoutes = require("./stock-aging.routes");
const stockHistoryRoutes = require("./stock-history.routes");
const stockLevelsRoutes = require("./stock-levels.routes");
const stockLevelsExportRoutes = require("./stock-levels-export.routes");
const salesSummaryRoutes = require("./sales-summary.routes");
const salesByCategoryRoutes = require("./sales-by-category.routes");
const bankingTransactionsRoutes = require("./banking-transactions.routes");
const mpesaTransactionsRoutes = require("./mpesa-transactions.routes");
const runningBalancesRoutes = require("../running-balances.routes");
const cashFloatRoutes = require("./cash-float.routes");

// Mount comprehensive report routes
router.use("/dashboard", dashboardRoutes);
router.use("/financial", financialRoutes);
router.use("/inventory-enhanced", inventoryEnhancedRoutes);
router.use("/sales-analytics", salesAnalyticsRoutes);
router.use("/operations", operationsRoutes);
router.use("/users", usersRoutes);

// Mount legacy report routes (for backward compatibility)
router.use("/cash-status", cashStatusRoutes);
router.use("/stock-aging", stockAgingRoutes);
router.use("/stock-history", stockHistoryRoutes);
router.use("/stock-levels", stockLevelsRoutes);
router.use("/stock-levels-export", stockLevelsExportRoutes);
router.use("/sales-summary", salesSummaryRoutes);
router.use("/sales-by-category", salesByCategoryRoutes);
router.use("/banking-transactions", bankingTransactionsRoutes);
router.use("/mpesa-transactions", mpesaTransactionsRoutes);
router.use("/running-balances", runningBalancesRoutes);
router.use("/cash-float", cashFloatRoutes);

module.exports = router;
