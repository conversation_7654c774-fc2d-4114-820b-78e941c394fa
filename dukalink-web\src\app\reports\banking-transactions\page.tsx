"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layouts/main-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { ReportChart } from "@/features/reports/components/report-chart";
import { ReportDataTable } from "@/features/reports/components/report-data-table";
import { ReportFilters } from "@/features/reports/components/report-filters";
import { ReportStatCard } from "@/features/reports/components/report-stat-card";
import { BankingTransactionsExportDialog } from "@/features/reports/components/banking-transactions-export-dialog";
import { ExportPerformanceNotice } from "@/features/reports/components/export-performance-notice";
import { useBankingTransactionsReport } from "@/features/reports/hooks/use-reports-api";
import { handleBankingTransactionsExport } from "@/features/reports/utils/export-handlers";
import { formatCurrency } from "@/lib/utils";
import { BankingTransactionsParams } from "@/types/reports-api";
import { format, subDays } from "date-fns";
import {
  Download,
  CreditCard,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Zap,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";

export default function BankingTransactionsPage() {
  const [filters, setFilters] = useState<BankingTransactionsParams>({
    start_date: format(subDays(new Date(), 30), "yyyy-MM-dd"),
    end_date: format(new Date(), "yyyy-MM-dd"),
    page: 1,
    limit: 100, // Increased default page size for better user experience
  });

  const [isExporting, setIsExporting] = useState(false);

  const { data, isLoading, error } = useBankingTransactionsReport(filters);

  const handleFilterChange = (newFilters: any) => {
    setFilters((prev) => ({
      ...prev,
      ...newFilters,
      page: 1, // Reset to first page when filters change
    }));
  };

  const handlePageChange = (page: number) => {
    setFilters((prev) => ({ ...prev, page }));
  };

  const handleExport = async () => {
    setIsExporting(true);
    try {
      await handleBankingTransactionsExport(filters);
    } finally {
      setIsExporting(false);
    }
  };

  // Column definitions for banking transactions
  const transactionColumns: ColumnDef<any>[] = [
    {
      accessorKey: "transaction_date",
      header: "Date",
      cell: ({ row }) =>
        format(new Date(row.getValue("transaction_date")), "MMM dd, yyyy"),
    },
    {
      accessorKey: "reference_number",
      header: "Reference",
      cell: ({ row }) => (
        <span className="font-mono text-sm">
          {row.getValue("reference_number")}
        </span>
      ),
    },
    {
      accessorKey: "banking_method",
      header: "Method",
      cell: ({ row }) => (
        <span
          className={`px-2 py-1 rounded text-xs font-medium ${
            row.getValue("banking_method") === "bank"
              ? "bg-blue-100 text-blue-800"
              : row.getValue("banking_method") === "mpesa"
              ? "bg-green-100 text-green-800"
              : "bg-purple-100 text-purple-800"
          }`}
        >
          {row.getValue("banking_method")?.toUpperCase()}
        </span>
      ),
    },
    {
      accessorKey: "transaction_type",
      header: "Type",
      cell: ({ row }) => (
        <span
          className={`px-2 py-1 rounded text-xs font-medium ${
            row.getValue("transaction_type") === "deposit"
              ? "bg-green-100 text-green-800"
              : row.getValue("transaction_type") === "withdrawal"
              ? "bg-red-100 text-red-800"
              : "bg-yellow-100 text-yellow-800"
          }`}
        >
          {row.getValue("transaction_type")?.toUpperCase()}
        </span>
      ),
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: ({ row }) => {
        const amount = row.getValue("amount") as number;
        const type = row.original.transaction_type;
        return (
          <span
            className={`font-medium ${
              type === "deposit"
                ? "text-green-600"
                : type === "withdrawal"
                ? "text-red-600"
                : "text-blue-600"
            }`}
          >
            {type === "deposit" ? "+" : type === "withdrawal" ? "-" : ""}
            {formatCurrency(Math.abs(amount))}
          </span>
        );
      },
    },
    {
      accessorKey: "bank_name",
      header: "Bank",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        return (
          <span
            className={`px-2 py-1 rounded text-xs font-medium ${
              status === "completed"
                ? "bg-green-100 text-green-800"
                : status === "pending"
                ? "bg-yellow-100 text-yellow-800"
                : status === "failed"
                ? "bg-red-100 text-red-800"
                : "bg-blue-100 text-blue-800"
            }`}
          >
            {status?.toUpperCase()}
          </span>
        );
      },
    },
    {
      accessorKey: "branch_name",
      header: "Branch",
    },
    {
      accessorKey: "region_name",
      header: "Region",
    },
    {
      accessorKey: "user_name",
      header: "User",
    },
    {
      accessorKey: "has_receipt",
      header: "Receipt",
      cell: ({ row }) => (
        <div className="flex items-center">
          {row.getValue("has_receipt") ? (
            <CheckCircle className="h-4 w-4 text-green-500" />
          ) : (
            <AlertCircle className="h-4 w-4 text-yellow-500" />
          )}
        </div>
      ),
    },
  ];

  // Process chart data for method breakdown
  const processMethodChartData = () => {
    if (!data?.summary?.by_method || !Array.isArray(data.summary.by_method)) {
      return { labels: [], datasets: [] };
    }

    return {
      labels: data.summary.by_method.map((item) =>
        item?.method ? item.method.toUpperCase() : "UNKNOWN"
      ),
      datasets: [
        {
          label: "Amount",
          data: data.summary.by_method.map((item) => item?.total || 0),
        },
      ],
    };
  };

  // Process chart data for type breakdown
  const processTypeChartData = () => {
    if (!data?.summary?.by_type || !Array.isArray(data.summary.by_type)) {
      return { labels: [], datasets: [] };
    }

    return {
      labels: data.summary.by_type.map((item) =>
        item?.type ? item.type.toUpperCase() : "UNKNOWN"
      ),
      datasets: [
        {
          label: "Amount",
          data: data.summary.by_type.map((item) => item?.total || 0),
        },
      ],
    };
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              Banking Transactions Report
            </h1>
            <p className="text-muted-foreground">
              Track and analyze banking transactions across all branches
            </p>
            {/* Quick pagination info in header */}
            {data?.pagination && data.pagination.total > 0 && (
              <div className="flex items-center gap-4 mt-2">
                <span className="text-sm text-muted-foreground">
                  {data.pagination.total.toLocaleString()} total transactions
                  {isLoading && (
                    <span className="ml-2 text-xs text-blue-600">(Loading...)</span>
                  )}
                </span>
                <div className="flex items-center gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(Math.max(1, filters.page! - 1))}
                    disabled={!data.pagination || data.pagination.page <= 1 || isLoading}
                    className="h-7 w-7 p-0"
                  >
                    <ChevronLeft className="h-3 w-3" />
                  </Button>
                  <span className="text-xs px-2 py-1 bg-muted rounded">
                    Page {data.pagination.page} of {data.pagination.pages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange(Math.min(data.pagination!.pages, filters.page! + 1))}
                    disabled={!data.pagination || data.pagination.page >= data.pagination.pages || isLoading}
                    className="h-7 w-7 p-0"
                  >
                    <ChevronRight className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <BankingTransactionsExportDialog
              filters={filters}
              totalRecords={data?.transactions?.length || 0}
              trigger={
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export Options
                </Button>
              }
            />
            <BankingTransactionsExportDialog
              filters={filters}
              totalRecords={data?.transactions?.length || 0}
              trigger={
                <Button>
                  <Zap className="mr-2 h-4 w-4" />
                  Quick Export
                </Button>
              }
            />
          </div>
        </div>

        {/* Export Performance Notice */}
        <ExportPerformanceNotice
          totalRecords={data?.transactions?.length || 0}
          hasDateFilter={!!(filters.start_date && filters.end_date)}
          className="mb-6"
        />

        <ReportFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          showTimeFilter={true}
          showBranchFilter={true}
          showRegionFilter={true}
          showBankingMethodFilter={true}
          showTransactionTypeFilter={true}
          showStatusFilter={true}
          showUserFilter={false}
          showPaymentMethodFilter={false}
          showProductFilter={false}
        />

        {isLoading ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className="h-32" />
              ))}
            </div>
            <Skeleton className="h-[250px]" />
            <Skeleton className="h-96" />
          </div>
        ) : error ? (
          <div className="rounded-md bg-destructive/10 p-4 text-destructive">
            Error loading banking transactions data. Please try again.
          </div>
        ) : data ? (
          <>
            {/* Summary Statistics Cards */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <ReportStatCard
                title="Total Transactions"
                value={data.summary?.total_transactions || 0}
                icon={<CreditCard className="h-4 w-4" />}
                description="All banking transactions"
              />
              <ReportStatCard
                title="Total Amount"
                value={data.summary?.total_amount || 0}
                icon={<TrendingUp className="h-4 w-4" />}
                isCurrency={true}
                description="Sum of all transactions"
              />
              <ReportStatCard
                title="Completed"
                value={
                  data.transactions?.filter((t) => t?.status === "completed")
                    ?.length || 0
                }
                icon={<CheckCircle className="h-4 w-4" />}
                description="Completed transactions"
              />
              <ReportStatCard
                title="Pending"
                value={
                  data.transactions?.filter((t) => t?.status === "pending")
                    ?.length || 0
                }
                icon={<AlertCircle className="h-4 w-4" />}
                description="Pending transactions"
              />
            </div>

            {/* Summary Charts */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <ReportChart
                title="Transactions by Method"
                description="Breakdown by banking method"
                data={processMethodChartData()}
                chartTypes={["pie", "bar"]}
                defaultChartType="pie"
              />
              <ReportChart
                title="Transactions by Type"
                description="Breakdown by transaction type"
                data={processTypeChartData()}
                chartTypes={["pie", "bar"]}
                defaultChartType="pie"
              />
            </div>

            {/* Pagination Summary */}
            {data.pagination && data.pagination.total > 0 && (
              <Card>
                <CardContent className="py-4">
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <div className="flex items-center gap-4">
                      <span>
                        Showing{" "}
                        <strong>
                          {((data.pagination.page - 1) * data.pagination.limit) + 1}
                        </strong>{" "}
                        to{" "}
                        <strong>
                          {Math.min(
                            data.pagination.page * data.pagination.limit,
                            data.pagination.total
                          )}
                        </strong>{" "}
                        of{" "}
                        <strong>{data.pagination.total.toLocaleString()}</strong>{" "}
                        transactions
                      </span>
                      <span className="text-xs bg-muted px-2 py-1 rounded">
                        Page {data.pagination.page} of {data.pagination.pages}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span>Items per page:</span>
                      <Select
                        value={filters.limit?.toString() || "100"}
                        onValueChange={(value) => {
                          setFilters((prev) => ({
                            ...prev,
                            limit: parseInt(value),
                            page: 1
                          }));
                        }}
                        disabled={isLoading}
                      >
                        <SelectTrigger className="h-8 w-[70px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {[25, 50, 100, 250, 500, 1000].map((size) => (
                            <SelectItem key={size} value={size.toString()}>
                              {size}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Transactions Table */}
            <ReportDataTable
              columns={transactionColumns}
              data={data.transactions || []}
              title="Banking Transactions"
              description="Detailed list of all banking transactions"
              searchColumn="reference_number"
              searchPlaceholder="Search by reference number..."
              exportFilename="banking-transactions"
              showExport={false}
              pagination={{
                currentPage: data.pagination?.page || 1,
                totalPages: data.pagination?.pages || 1,
                pageSize: data.pagination?.limit || 100,
                totalItems: data.pagination?.total || 0,
                onPageChange: handlePageChange,
                onPageSizeChange: (newPageSize: number) => {
                  setFilters((prev) => ({ ...prev, limit: newPageSize, page: 1 }));
                },
                isLoading: isLoading,
              }}
            />
          </>
        ) : null}
      </div>
    </MainLayout>
  );
}
