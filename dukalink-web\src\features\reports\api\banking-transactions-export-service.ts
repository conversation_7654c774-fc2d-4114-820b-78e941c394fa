import apiClient from "@/lib/api-client";

/**
 * Banking Transactions Export Service
 * Provides methods for comprehensive Excel export functionality for banking transactions data
 */

export interface BankingTransactionsExportParams {
  start_date?: string;
  end_date?: string;
  branch_id?: number;
  region_id?: number;
  banking_method?: string;
  transaction_type?: string;
  status?: string;
  bank_id?: number;
  include_summary?: boolean;
  include_details?: boolean;
  include_method_breakdown?: boolean;
  include_type_breakdown?: boolean;
  include_status_breakdown?: boolean;
  include_charts?: boolean;
}

export interface CustomBankingTransactionsExportParams extends BankingTransactionsExportParams {
  columns?: string; // comma-separated list or "all"
  format_type?: 'detailed' | 'summary';
}

const bankingTransactionsExportService = {
  /**
   * Export all banking transactions data with comprehensive sheets
   */
  exportAllBankingTransactions: async (params?: BankingTransactionsExportParams): Promise<Blob> => {
    try {
      console.log("Starting comprehensive banking transactions export...", params);

      const response: any = await apiClient.get("/reports/banking-transactions/export/all", {
        params: {
          ...params,
          include_summary: params?.include_summary ?? true,
          include_details: params?.include_details ?? true,
          include_method_breakdown: params?.include_method_breakdown ?? true,
          include_type_breakdown: params?.include_type_breakdown ?? true,
          include_status_breakdown: params?.include_status_breakdown ?? true,
          include_charts: params?.include_charts ?? true,
        },
        responseType: "blob",
        timeout: 60000, // 1 minute timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting all banking transactions:", error);
      
      // Provide more specific error messages
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export banking reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. Try filtering the data or use summary export.");
      } else {
        throw new Error(error.message || "Failed to export banking transactions");
      }
    }
  },

  /**
   * Export banking transactions with custom options
   */
  exportCustomBankingTransactions: async (params?: CustomBankingTransactionsExportParams): Promise<Blob> => {
    try {
      console.log("Starting custom banking transactions export...", params);

      const response: any = await apiClient.get("/reports/banking-transactions/export/custom", {
        params: {
          ...params,
          columns: params?.columns || "all",
          format_type: params?.format_type || "detailed",
        },
        responseType: "blob",
        timeout: 45000, // 45 seconds timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting custom banking transactions:", error);
      
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export banking reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. Try using summary format or filtering the data.");
      } else {
        throw new Error(error.message || "Failed to export custom banking transactions");
      }
    }
  },

  /**
   * Export lightweight banking transactions data (fastest option)
   */
  exportLightweightBankingTransactions: async (params?: BankingTransactionsExportParams): Promise<Blob> => {
    try {
      console.log("Starting lightweight banking transactions export...", params);

      const response: any = await apiClient.get("/reports/banking-transactions/export/lightweight", {
        params: {
          ...params,
        },
        responseType: "blob",
        timeout: 20000, // 20 seconds timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting lightweight banking transactions:", error);
      
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export banking reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. The system may be busy, please try again.");
      } else {
        throw new Error(error.message || "Failed to export lightweight banking transactions");
      }
    }
  },

  /**
   * Download blob as file with proper filename
   */
  downloadBlob: (blob: Blob, filename?: string): void => {
    try {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // Generate filename if not provided
      if (!filename) {
        const timestamp = new Date().toISOString().split('T')[0];
        filename = `banking-transactions-export-${timestamp}.xlsx`;
      }
      
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      console.log(`File downloaded: ${filename}`);
    } catch (error) {
      console.error('Error downloading file:', error);
      throw new Error('Failed to download the export file');
    }
  },

  /**
   * Generate descriptive filename based on filters
   */
  generateFilename: (params?: BankingTransactionsExportParams, formatType: string = 'comprehensive'): string => {
    const timestamp = new Date().toISOString().split('T')[0];
    const parts = ['banking-transactions'];
    
    if (formatType !== 'comprehensive') {
      parts.push(formatType);
    }
    
    if (params?.branch_id) {
      parts.push(`branch-${params.branch_id}`);
    }
    
    if (params?.region_id) {
      parts.push(`region-${params.region_id}`);
    }
    
    if (params?.banking_method) {
      parts.push(`method-${params.banking_method}`);
    }
    
    if (params?.transaction_type) {
      parts.push(`type-${params.transaction_type}`);
    }
    
    if (params?.status) {
      parts.push(`status-${params.status}`);
    }
    
    if (params?.start_date && params?.end_date) {
      parts.push(`${params.start_date}-to-${params.end_date}`);
    }
    
    parts.push(timestamp);
    
    return `${parts.join('-')}.xlsx`;
  },

  /**
   * Estimate export size and provide recommendations
   */
  getExportRecommendation: (estimatedRecords: number): {
    recommended: 'all' | 'custom' | 'lightweight';
    message: string;
    estimatedTime: string;
  } => {
    if (estimatedRecords <= 500) {
      return {
        recommended: 'all',
        message: 'Small dataset - comprehensive export recommended',
        estimatedTime: '< 30 seconds'
      };
    } else if (estimatedRecords <= 2000) {
      return {
        recommended: 'custom',
        message: 'Medium dataset - custom export with selected sheets recommended',
        estimatedTime: '30-60 seconds'
      };
    } else {
      return {
        recommended: 'lightweight',
        message: 'Large dataset - lightweight export recommended for faster processing',
        estimatedTime: '< 30 seconds'
      };
    }
  },

  /**
   * Validate export parameters
   */
  validateExportParams: (params?: BankingTransactionsExportParams): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (params?.branch_id && (params.branch_id < 1 || !Number.isInteger(params.branch_id))) {
      errors.push('Branch ID must be a positive integer');
    }
    
    if (params?.region_id && (params.region_id < 1 || !Number.isInteger(params.region_id))) {
      errors.push('Region ID must be a positive integer');
    }
    
    if (params?.bank_id && (params.bank_id < 1 || !Number.isInteger(params.bank_id))) {
      errors.push('Bank ID must be a positive integer');
    }
    
    if (params?.start_date && params?.end_date) {
      const fromDate = new Date(params.start_date);
      const toDate = new Date(params.end_date);
      if (fromDate > toDate) {
        errors.push('Start date must be before end date');
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Get export format options for UI
   */
  getExportFormatOptions: () => [
    {
      value: 'lightweight',
      label: 'Quick Export',
      description: 'Transaction summary data only - fastest option',
      icon: 'Zap',
      estimatedTime: '< 30 seconds'
    },
    {
      value: 'custom',
      label: 'Custom Export',
      description: 'Select specific sheets and data to include',
      icon: 'Settings',
      estimatedTime: '30-60 seconds'
    },
    {
      value: 'all',
      label: 'Comprehensive Export',
      description: 'All transaction data with summary, details, and breakdowns',
      icon: 'FileSpreadsheet',
      estimatedTime: '60-90 seconds'
    }
  ],

  /**
   * Get available column options for custom export
   */
  getColumnOptions: () => [
    { value: 'summary', label: 'Transaction Summary', category: 'basic' },
    { value: 'transaction_details', label: 'Transaction Details', category: 'basic' },
    { value: 'method_breakdown', label: 'Method Breakdown', category: 'analysis' },
    { value: 'type_breakdown', label: 'Type Breakdown', category: 'analysis' },
    { value: 'status_breakdown', label: 'Status Breakdown', category: 'analysis' },
    { value: 'branch_info', label: 'Branch Information', category: 'location' },
    { value: 'region_info', label: 'Region Information', category: 'location' },
    { value: 'bank_info', label: 'Bank Information', category: 'financial' },
    { value: 'user_info', label: 'User Information', category: 'details' },
    { value: 'receipt_info', label: 'Receipt Information', category: 'details' },
    { value: 'amounts', label: 'Amount Details', category: 'financial' },
    { value: 'dates', label: 'Date Information', category: 'details' },
  ]
};

export default bankingTransactionsExportService;
