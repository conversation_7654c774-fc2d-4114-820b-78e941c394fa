import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Search, ArrowRight, Package, Filter, Download, Info } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { EnhancedPagination } from '@/components/ui/enhanced-pagination';
import { StockMovementItemsExportDialog } from './stock-movement-items-export-dialog';
import Link from 'next/link';

export interface StockMovementItemWithDetails {
  id: number;
  stock_movement_id: number;
  reference_number: string;
  from_branch_name: string;
  to_branch_name: string;
  product_id: number;
  product_name: string;
  product_sku: string;
  requested_quantity: number;
  approved_quantity: number;
  dispatched_quantity: number;
  received_quantity: number;
  status: string;
  created_at: string;
}

interface StockMovementItemsTableProps {
  items: StockMovementItemWithDetails[];
  isLoading: boolean;
  onSearch?: (query: string) => void;
  pagination?: {
    currentPage: number;
    totalPages: number;
    onPageChange: (page: number) => void;
    total?: number;
    limit?: number;
    onItemsPerPageChange?: (value: string) => void;
  };
  // Props for enhanced export functionality
  currentFilters?: {
    status?: string;
    from_branch_id?: number;
    to_branch_id?: number;
    product_id?: number;
    reference_number?: string;
    search?: string;
    date_from?: string;
    date_to?: string;
  };
}

export function StockMovementItemsTable({
  items,
  isLoading,
  onSearch,
  pagination,
  currentFilters,
}: StockMovementItemsTableProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = () => {
    if (onSearch) {
      onSearch(searchQuery);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const getStatusBadge = (status: string) => {
    // Normalize status to uppercase for case-insensitive comparison
    const normalizedStatus = status.toUpperCase();

    // Pending statuses
    if (["PENDING", "REQUESTED", "PARTIALLY_APPROVED", "FULLY_APPROVED"].includes(normalizedStatus)) {
      return <Badge variant="outline">Pending</Badge>;
    }

    // In Transit statuses
    if (["IN_TRANSIT", "DISPATCHED"].includes(normalizedStatus)) {
      return <Badge className="bg-blue-500">In Transit</Badge>;
    }

    // Completed statuses
    if (["COMPLETED", "APPROVED", "FULLY_RECEIVED", "PARTIALLY_RECEIVED"].includes(normalizedStatus)) {
      return <Badge className="bg-green-500">Completed</Badge>;
    }

    // Cancelled statuses
    if (["CANCELLED", "REJECTED"].includes(normalizedStatus)) {
      return <Badge className="bg-red-500">Cancelled</Badge>;
    }

    // Default case for unknown statuses
    return <Badge>{status}</Badge>;
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch (error) {
      return dateString;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between gap-4">
        <div className="flex flex-col w-full max-w-sm space-y-2">
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search by product name, SKU, reference, or branch..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={handleKeyDown}
              />
            </div>
            <Button type="button" onClick={handleSearch}>
              Search
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <p className="text-xs text-muted-foreground">
              Search across product names, SKUs, reference numbers, and branch names
            </p>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="max-w-xs">
                  <div className="space-y-1">
                    <p className="font-medium">Search Examples:</p>
                    <ul className="text-xs space-y-0.5">
                      <li>• Product: "Coca Cola", "Bread", "Milk"</li>
                      <li>• SKU: "CC001", "BR002"</li>
                      <li>• Reference: "REQ-001", "SM-123"</li>
                      <li>• Branch: "Downtown", "HQ"</li>
                      <li>• Status: "pending", "received"</li>
                    </ul>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <div className="flex gap-2">
          <StockMovementItemsExportDialog
            filters={currentFilters || {}}
            totalRecords={pagination?.total || items.length}
            trigger={
              <Button
                variant="outline"
                disabled={isLoading || items.length === 0}
              >
                <Download className="mr-2 h-4 w-4" /> Export Items
              </Button>
            }
          />
        </div>
      </div>

      {/* Search Results Indicator */}
      {currentFilters?.search && (
        <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg px-4 py-2">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-blue-600" />
            <span className="text-sm text-blue-800">
              Search results for: <strong>"{currentFilters.search}"</strong>
            </span>
            {pagination?.total !== undefined && (
              <span className="text-sm text-blue-600">
                ({pagination.total.toLocaleString()} {pagination.total === 1 ? 'result' : 'results'})
              </span>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onSearch?.('')}
            className="text-blue-600 hover:text-blue-800"
          >
            Clear search
          </Button>
        </div>
      )}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Reference</TableHead>
              <TableHead>Product</TableHead>
              <TableHead>SKU</TableHead>
              <TableHead>From</TableHead>
              <TableHead>To</TableHead>
              <TableHead className="text-right">Requested</TableHead>
              <TableHead className="text-right">Dispatched</TableHead>
              <TableHead className="text-right">Received</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: 5 }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-6 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-9 w-9 ml-auto" /></TableCell>
                </TableRow>
              ))
            ) : items.length === 0 ? (
              <TableRow>
                <TableCell colSpan={11} className="h-24 text-center">
                  No stock transfer items found.
                </TableCell>
              </TableRow>
            ) : (
              items.map((item) => (
                <TableRow key={`${item.stock_movement_id}-${item.id}`}>
                  <TableCell className="font-medium">
                    {item.reference_number || `SM-${item.stock_movement_id}`}
                  </TableCell>
                  <TableCell>{item.product_name || `Product #${item.product_id}`}</TableCell>
                  <TableCell>{item.product_sku || 'N/A'}</TableCell>
                  <TableCell>{item.from_branch_name || 'HQ'}</TableCell>
                  <TableCell>{item.to_branch_name || 'Unknown'}</TableCell>
                  <TableCell className="text-right">{item.requested_quantity}</TableCell>
                  <TableCell className="text-right">{item.dispatched_quantity || 0}</TableCell>
                  <TableCell className="text-right">{item.received_quantity || 0}</TableCell>
                  <TableCell>{getStatusBadge(item.status || 'pending')}</TableCell>
                  <TableCell>{formatDate(item.created_at)}</TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/inventory/transfers/${item.stock_movement_id}`}>
                        <ArrowRight className="h-4 w-4" />
                      </Link>
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {pagination && (
        <EnhancedPagination
          currentPage={pagination.currentPage}
          totalPages={pagination.totalPages}
          onPageChange={pagination.onPageChange}
          total={pagination.total}
          limit={pagination.limit}
          onItemsPerPageChange={pagination.onItemsPerPageChange}
          showItemsPerPage={!!pagination.onItemsPerPageChange}
          showTotalRecords={true}
          showFirstLast={true}
          itemsPerPageOptions={[10, 25, 50, 100]}
          isLoading={isLoading}
        />
      )}
    </div>
  );
}
