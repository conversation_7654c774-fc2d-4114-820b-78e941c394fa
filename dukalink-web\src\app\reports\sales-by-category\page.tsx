"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layouts/main-layout";
import { But<PERSON> } from "@/components/ui/button";
import { ReportFilters } from "@/features/reports/components/report-filters";
import { ReportChart } from "@/features/reports/components/report-chart";
import { ReportDataTable } from "@/features/reports/components/report-data-table";
import { SalesByCategoryExportDialog } from "@/features/reports/components/sales-by-category-export-dialog";
import { ExportPerformanceNotice } from "@/features/reports/components/export-performance-notice";
import { useSalesByCategoryReport } from "@/features/reports/hooks/use-reports";
import { ReportFilterParams } from "@/types";

import { format, subDays } from "date-fns";
import { formatCurrency } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { Download, Zap, BarChart3 } from "lucide-react";

export default function SalesByCategoryPage() {
  // Initialize with default filters (last 7 days)
  const today = new Date();
  const sevenDaysAgo = subDays(today, 7);

  const [filters, setFilters] = useState<ReportFilterParams>({
    start_date: format(sevenDaysAgo, "yyyy-MM-dd"),
    end_date: format(today, "yyyy-MM-dd"),
  });

  const { data, isLoading, error } = useSalesByCategoryReport(filters);

  const handleFilterChange = (newFilters: ReportFilterParams) => {
    setFilters(newFilters);
  };

  // Define columns for the categories table
  const columns = [
    {
      accessorKey: "categoryName",
      header: "Category",
    },
    {
      accessorKey: "quantity",
      header: "Quantity",
      cell: ({ row }) => row.original.quantity.toLocaleString(),
    },
    {
      accessorKey: "totalSales",
      header: "Total Sales",
      cell: ({ row }) => formatCurrency(row.original.totalSales),
    },
    {
      accessorKey: "profit",
      header: "Profit",
      cell: ({ row }) => formatCurrency(row.original.profit),
    },
    {
      accessorKey: "percentage",
      header: "% of Total Sales",
      cell: ({ row }) => `${row.original.percentage.toFixed(2)}%`,
    },
  ];

  return (
    <MainLayout>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              Sales by Category
            </h1>
            <p className="text-muted-foreground">
              Analysis of sales performance by product category
            </p>
          </div>
          <div className="flex gap-2">
            <SalesByCategoryExportDialog
              filters={filters}
              totalRecords={data?.categoryData?.length || 0}
              trigger={
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Export Options
                </Button>
              }
            />
            <SalesByCategoryExportDialog
              filters={filters}
              totalRecords={data?.categoryData?.length || 0}
              trigger={
                <Button>
                  <Zap className="mr-2 h-4 w-4" />
                  Quick Export
                </Button>
              }
            />
          </div>
        </div>

        {/* Export Performance Notice */}
        <ExportPerformanceNotice
          totalRecords={data?.categoryData?.length || 0}
          hasDateFilter={!!(filters.start_date && filters.end_date)}
          className="mb-6"
        />

        <ReportFilters
          filters={filters}
          onFilterChange={handleFilterChange}
          showTimeFilter={false}
          showBranchFilter={true}
          showRegionFilter={true}
        />

        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-80" />
            <Skeleton className="h-96" />
          </div>
        ) : error ? (
          <div className="rounded-md bg-destructive/10 p-4 text-destructive">
            Error loading report data. Please try again.
          </div>
        ) : data ? (
          <>
            <ReportChart
              title="Sales Distribution by Category"
              description="Percentage of sales by product category"
              data={data.chartData}
              chartTypes={["pie", "bar"]}
              defaultChartType="pie"
            />

            <ReportDataTable
              columns={columns}
              data={data.categoryData}
              title="Category Sales Details"
              description="Detailed breakdown of sales by category"
              searchColumn="categoryName"
              searchPlaceholder="Search by category name..."
              exportFilename="sales-by-category-report"
            />
          </>
        ) : null}
      </div>
    </MainLayout>
  );
}
