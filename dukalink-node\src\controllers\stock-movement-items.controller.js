const { StockMovementItem, StockMovement, Branch, User, Product, Tenant } = require('../models');
const { Op } = require('sequelize');
const logger = require('../utils/logger');

/**
 * Get all stock movement items with search and pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllStockMovementItems = async (req, res, next) => {
  try {
    const {
      search,
      status,
      from_branch_id,
      to_branch_id,
      product_id,
      reference_number,
      date_from,
      date_to,
      page = 1,
      limit = 100,
      sort_by = 'created_at',
      sort_direction = 'desc'
    } = req.query;

    console.log('Getting all stock movement items with params:', req.query);

    // Build where clause for stock movement items
    const whereClause = {};
    
    if (status) {
      whereClause.status = status;
    }
    
    if (product_id) {
      whereClause.product_id = product_id;
    }
    
    if (date_from && date_to) {
      whereClause.created_at = {
        [Op.between]: [new Date(date_from), new Date(date_to)]
      };
    } else if (date_from) {
      whereClause.created_at = {
        [Op.gte]: new Date(date_from)
      };
    } else if (date_to) {
      whereClause.created_at = {
        [Op.lte]: new Date(date_to)
      };
    }

    // Build include clause for stock movement
    const stockMovementWhere = {};
    
    if (from_branch_id) {
      stockMovementWhere.from_branch_id = from_branch_id;
    }
    
    if (to_branch_id) {
      stockMovementWhere.to_branch_id = to_branch_id;
    }
    
    if (reference_number) {
      stockMovementWhere.reference_number = { [Op.like]: `%${reference_number}%` };
    }

    // Build product search conditions
    const productWhere = {};
    
    // Add comprehensive search functionality
    if (search) {
      const searchTerm = search.trim();
      console.log(`Searching stock movement items for: "${searchTerm}"`);
      
      // Search in product name and SKU
      productWhere[Op.or] = [
        { name: { [Op.like]: `%${searchTerm}%` } },
        { sku: { [Op.like]: `%${searchTerm}%` } }
      ];
      
      // Also search in stock movement reference and notes
      if (Object.keys(stockMovementWhere).length === 0) {
        stockMovementWhere[Op.or] = [
          { reference_number: { [Op.like]: `%${searchTerm}%` } },
          { notes: { [Op.like]: `%${searchTerm}%` } }
        ];
      } else {
        // Combine existing conditions with search
        const existingConditions = { ...stockMovementWhere };
        stockMovementWhere[Op.and] = [
          existingConditions,
          {
            [Op.or]: [
              { reference_number: { [Op.like]: `%${searchTerm}%` } },
              { notes: { [Op.like]: `%${searchTerm}%` } }
            ]
          }
        ];
      }
      
      // Search in stock movement item notes
      if (Object.keys(whereClause).length === 0) {
        whereClause[Op.or] = [
          { notes: { [Op.like]: `%${searchTerm}%` } }
        ];
      } else {
        // Combine existing conditions with search
        const existingConditions = { ...whereClause };
        whereClause[Op.and] = [
          existingConditions,
          {
            [Op.or]: [
              { notes: { [Op.like]: `%${searchTerm}%` } }
            ]
          }
        ];
      }
    }

    // Calculate pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Get total count for pagination
    const totalCount = await StockMovementItem.count({
      where: whereClause,
      include: [
        {
          model: StockMovement,
          where: Object.keys(stockMovementWhere).length > 0 ? stockMovementWhere : undefined,
          required: true
        },
        {
          model: Product,
          where: Object.keys(productWhere).length > 0 ? productWhere : undefined,
          required: true
        }
      ]
    });

    const totalPages = Math.ceil(totalCount / parseInt(limit));

    console.log(`Stock movement items query - Page: ${page}, Limit: ${limit}, Total: ${totalCount}, Pages: ${totalPages}`);
    console.log('Search conditions:', {
      whereClause,
      stockMovementWhere,
      productWhere
    });

    // Fetch stock movement items with all related data
    const items = await StockMovementItem.findAll({
      where: whereClause,
      include: [
        {
          model: StockMovement,
          where: Object.keys(stockMovementWhere).length > 0 ? stockMovementWhere : undefined,
          required: true,
          include: [
            {
              model: Branch,
              as: 'FromBranch',
              attributes: ['id', 'name', 'location']
            },
            {
              model: Branch,
              as: 'ToBranch',
              attributes: ['id', 'name', 'location']
            },
            {
              model: User,
              as: 'RequestedBy',
              attributes: ['id', 'name', 'email']
            }
          ]
        },
        {
          model: Product,
          where: Object.keys(productWhere).length > 0 ? productWhere : undefined,
          required: true,
          attributes: ['id', 'name', 'sku', 'has_serial']
        }
      ],
      order: [[sort_by, sort_direction.toUpperCase()]],
      limit: parseInt(limit),
      offset: offset
    });

    console.log(`Found ${items.length} stock movement items`);

    // Transform items to a consistent format
    const transformedItems = items.map((item) => ({
      id: item.id,
      stock_movement_id: item.stock_movement_id,
      reference_number: item.StockMovement?.reference_number || `SM-${item.stock_movement_id}`,
      from_branch_name: item.StockMovement?.FromBranch?.name || 'HQ',
      to_branch_name: item.StockMovement?.ToBranch?.name || 'Unknown',
      product_id: item.product_id,
      product_name: item.Product?.name || `Product #${item.product_id}`,
      product_sku: item.Product?.sku || 'N/A',
      requested_quantity: item.requested_quantity || 0,
      dispatched_quantity: item.dispatched_quantity || 0,
      received_quantity: item.received_quantity || 0,
      status: item.StockMovement?.status || 'pending',
      created_at: item.created_at,
      updated_at: item.updated_at,
      notes: item.notes || '',
      // Additional fields
      from_branch_location: item.StockMovement?.FromBranch?.location || '',
      to_branch_location: item.StockMovement?.ToBranch?.location || '',
      requested_by: item.StockMovement?.RequestedBy?.name || 'Unknown',
      requested_by_email: item.StockMovement?.RequestedBy?.email || '',
      has_serial: item.Product?.has_serial || false
    }));

    // Return paginated response
    res.json({
      data: transformedItems,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        totalPages: totalPages
      }
    });

  } catch (error) {
    console.error('Error getting stock movement items:', error);
    next(error);
  }
};

/**
 * Get stock movement item by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getStockMovementItemById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const item = await StockMovementItem.findByPk(id, {
      include: [
        {
          model: StockMovement,
          include: [
            {
              model: Branch,
              as: 'FromBranch',
              attributes: ['id', 'name', 'location']
            },
            {
              model: Branch,
              as: 'ToBranch',
              attributes: ['id', 'name', 'location']
            },
            {
              model: User,
              as: 'RequestedBy',
              attributes: ['id', 'name', 'email']
            }
          ]
        },
        {
          model: Product,
          attributes: ['id', 'name', 'sku', 'has_serial']
        }
      ]
    });

    if (!item) {
      return res.status(404).json({ error: 'Stock movement item not found' });
    }

    res.json(item);
  } catch (error) {
    console.error('Error getting stock movement item by ID:', error);
    next(error);
  }
};

module.exports = {
  getAllStockMovementItems,
  getStockMovementItemById
};
