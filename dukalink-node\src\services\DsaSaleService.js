const { Sale, SaleItem, DsaStockAssignment, Product, Customer, User, Branch, PaymentMethod } = require('../models');
const { sequelize } = require('../models');
const AppError = require('../utils/error');
const logger = require('../utils/logger');

/**
 * DSA Sale Service
 * Handles DSA sales separately from assignments
 */
class DsaSaleService {
  /**
   * Create a DSA sale (when DSA agent sells to end customer)
   * This is different from DSA assignments
   */
  static async createDsaSale(saleData, transaction = null) {
    const t = transaction || await sequelize.transaction();
    
    try {
      const {
        dsa_agent_id,
        product_id,
        quantity_sold,
        unit_price,
        payment_method_id = 1, // Default to Cash
        payment_reference = '',
        notes = '',
        employee_id
      } = saleData;

      // Validate DSA agent exists
      const dsaAgent = await User.findOne({
        where: { id: dsa_agent_id, is_dsa: true },
        include: [{ model: Branch }]
      });

      if (!dsaAgent) {
        throw new AppError(404, 'DSA agent not found');
      }

      // Check DSA stock assignment
      const assignment = await DsaStockAssignment.findOne({
        where: {
          user_id: dsa_agent_id,
          product_id,
          reconciled: false
        }
      });

      if (!assignment) {
        throw new AppError(400, 'Product not assigned to this DSA agent');
      }

      // Check available quantity
      const availableQty = assignment.quantity_assigned - 
                          (assignment.quantity_sold || 0) - 
                          (assignment.quantity_returned || 0);

      if (availableQty < quantity_sold) {
        throw new AppError(400, `Insufficient stock. Available: ${availableQty}, Requested: ${quantity_sold}`);
      }

      // Get walk-in customer
      const walkInCustomer = await Customer.findOne({
        where: { name: 'Walk-in' }
      });

      // Create sale record
      const sale = await Sale.create({
        user_id: dsa_agent_id,
        branch_id: dsaAgent.branch_id,
        customer_id: walkInCustomer?.id || 1,
        employee_id,
        total_amount: quantity_sold * unit_price,
        payment_method_id,
        payment_reference: payment_reference || 'DSA Sale',
        receipt_number: `DSA-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        status: 'completed',
        notes: notes || `DSA Sale by ${dsaAgent.name}`,
        is_dsa: true,
        created_by: employee_id,
        last_updated_by: employee_id
      }, { transaction: t });

      // Create sale item
      await SaleItem.create({
        sale_id: sale.id,
        product_id,
        quantity: quantity_sold,
        unit_price,
        total_price: quantity_sold * unit_price,
        created_by: employee_id,
        last_updated_by: employee_id
      }, { transaction: t });

      // Update DSA assignment
      await assignment.update({
        quantity_sold: (assignment.quantity_sold || 0) + quantity_sold,
        last_updated_by: employee_id
      }, { transaction: t });

      if (!transaction) {
        await t.commit();
      }

      return sale;
    } catch (error) {
      if (!transaction) {
        await t.rollback();
      }
      throw error;
    }
  }

  /**
   * Get DSA sales summary
   */
  static async getDsaSalesSummary(filters = {}) {
    try {
      const { branch_id, dsa_agent_id, start_date, end_date } = filters;
      
      const whereClause = { is_dsa: true };
      
      if (branch_id) whereClause.branch_id = branch_id;
      if (dsa_agent_id) whereClause.user_id = dsa_agent_id;
      if (start_date && end_date) {
        whereClause.created_at = {
          [Op.between]: [start_date, end_date]
        };
      }

      const sales = await Sale.findAll({
        where: whereClause,
        include: [
          { model: User, attributes: ['id', 'name'] },
          { model: SaleItem, include: [{ model: Product }] }
        ],
        order: [['created_at', 'DESC']]
      });

      return sales;
    } catch (error) {
      logger.error('Error getting DSA sales summary:', error);
      throw error;
    }
  }

  /**
   * Reconcile DSA assignment (when DSA returns stock and pays)
   */
  static async reconcileAssignment(reconciliationData, transaction = null) {
    const t = transaction || await sequelize.transaction();
    
    try {
      const {
        assignment_identifier,
        dsa_agent_id,
        items_returned = [],
        cash_received = 0,
        paybill_amount = 0,
        notes = ''
      } = reconciliationData;

      // Get all assignments for this identifier
      const assignments = await DsaStockAssignment.findAll({
        where: { assignment_identifier, customer_id: dsa_agent_id }
      });

      if (assignments.length === 0) {
        throw new AppError(404, 'Assignment not found');
      }

      // Update returned quantities
      for (const returnItem of items_returned) {
        const assignment = assignments.find(a => a.product_id === returnItem.product_id);
        if (assignment) {
          await assignment.update({
            quantity_returned: returnItem.quantity_returned,
            reconciled: true,
            reconciled_at: new Date()
          }, { transaction: t });
        }
      }

      // Record payment if any
      if (cash_received > 0 || paybill_amount > 0) {
        // Create payment record using existing payment service
        // This would integrate with the existing payment system
      }

      if (!transaction) {
        await t.commit();
      }

      return { success: true, message: 'Assignment reconciled successfully' };
    } catch (error) {
      if (!transaction) {
        await t.rollback();
      }
      throw error;
    }
  }
}

module.exports = DsaSaleService;
