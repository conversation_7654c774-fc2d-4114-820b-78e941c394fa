import apiClient from "@/lib/api-client";

/**
 * Enhanced Sales Summary Export Service
 * Provides methods for comprehensive Excel export functionality for sales summary data
 */

export interface SalesSummaryExportParams {
  start_date?: string;
  end_date?: string;
  branch_id?: number;
  region_id?: number;
  product_id?: number;
  category_id?: number;
  location_id?: number;
  user_id?: number;
  payment_method?: string;
  include_summary?: boolean;
  include_sales_details?: boolean;
  include_product_breakdown?: boolean;
  include_branch_breakdown?: boolean;
  include_payment_breakdown?: boolean;
  include_charts?: boolean;
}

export interface CustomSalesSummaryExportParams extends SalesSummaryExportParams {
  columns?: string; // comma-separated list or "all"
  format_type?: 'detailed' | 'summary';
}

const salesSummaryExportService = {
  /**
   * Export all sales summary data with comprehensive sheets
   */
  exportAllSalesSummary: async (params?: SalesSummaryExportParams): Promise<Blob> => {
    try {
      console.log("Starting comprehensive sales summary export...", params);

      const response: any = await apiClient.get("/reports/sales-summary/export/all", {
        params: {
          ...params,
          include_summary: params?.include_summary ?? true,
          include_sales_details: params?.include_sales_details ?? true,
          include_product_breakdown: params?.include_product_breakdown ?? true,
          include_branch_breakdown: params?.include_branch_breakdown ?? true,
          include_payment_breakdown: params?.include_payment_breakdown ?? true,
          include_charts: params?.include_charts ?? true,
        },
        responseType: "blob",
        timeout: 45000, // 45 seconds timeout for large exports
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting all sales summary:", error);

      // Provide more specific error messages
      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export sales reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. The dataset might be too large. Try filtering the data.");
      } else {
        throw new Error(error.message || "Failed to export sales summary");
      }
    }
  },

  /**
   * Export sales summary with custom options
   */
  exportCustomSalesSummary: async (params?: CustomSalesSummaryExportParams): Promise<Blob> => {
    try {
      console.log("Starting custom sales summary export...", params);

      const response: any = await apiClient.get("/reports/sales-summary/export/custom", {
        params: {
          ...params,
          columns: params?.columns || "all",
          format_type: params?.format_type || "detailed",
        },
        responseType: "blob",
        timeout: 30000, // 30 seconds timeout
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting custom sales summary:", error);

      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export sales reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. Try using summary format or filtering the data.");
      } else {
        throw new Error(error.message || "Failed to export custom sales summary");
      }
    }
  },

  /**
   * Export summary format (lightweight)
   */
  exportSummarySales: async (params?: SalesSummaryExportParams): Promise<Blob> => {
    return salesSummaryExportService.exportCustomSalesSummary({
      ...params,
      format_type: 'summary',
      columns: 'summary,totals,branch_breakdown',
    });
  },

  /**
   * Export ultra-lightweight sales data (fastest option)
   */
  exportLightweightSales: async (params?: SalesSummaryExportParams): Promise<Blob> => {
    try {
      console.log("Starting ultra-lightweight sales export...", params);

      const response: any = await apiClient.get("/reports/sales-summary/export/lightweight", {
        params: {
          ...params,
        },
        responseType: "blob",
        timeout: 15000, // 15 seconds timeout for fastest export
      });

      if (!(response instanceof Blob)) {
        throw new Error('Invalid response format for Excel export');
      }

      return response;
    } catch (error: any) {
      console.error("Error exporting lightweight sales:", error);

      if (error.response?.status === 403) {
        throw new Error("You don't have permission to export sales reports");
      } else if (error.response?.status === 500) {
        throw new Error("Server error during export. Please try again later.");
      } else if (error.code === 'ECONNABORTED') {
        throw new Error("Export timeout. The system may be busy, please try again.");
      } else {
        throw new Error(error.message || "Failed to export lightweight sales data");
      }
    }
  },

  /**
   * Download blob as file with proper filename
   */
  downloadBlob: (blob: Blob, filename?: string): void => {
    try {
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Generate filename if not provided
      if (!filename) {
        const timestamp = new Date().toISOString().split('T')[0];
        filename = `sales-summary-export-${timestamp}.xlsx`;
      }

      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log(`File downloaded: ${filename}`);
    } catch (error) {
      console.error('Error downloading file:', error);
      throw new Error('Failed to download the export file');
    }
  },

  /**
   * Generate descriptive filename based on filters
   */
  generateFilename: (params?: SalesSummaryExportParams, formatType: string = 'comprehensive'): string => {
    const timestamp = new Date().toISOString().split('T')[0];
    const parts = ['sales-summary'];

    if (formatType !== 'comprehensive') {
      parts.push(formatType);
    }

    if (params?.branch_id) {
      parts.push(`branch-${params.branch_id}`);
    }

    if (params?.region_id) {
      parts.push(`region-${params.region_id}`);
    }

    if (params?.product_id) {
      parts.push(`product-${params.product_id}`);
    }

    if (params?.category_id) {
      parts.push(`category-${params.category_id}`);
    }

    if (params?.payment_method) {
      parts.push(`payment-${params.payment_method.toLowerCase()}`);
    }

    if (params?.start_date && params?.end_date) {
      parts.push(`${params.start_date}-to-${params.end_date}`);
    }

    parts.push(timestamp);

    return `${parts.join('-')}.xlsx`;
  },

  /**
   * Estimate export size and provide recommendations
   */
  getExportRecommendation: (estimatedRecords: number): {
    recommended: 'all' | 'custom' | 'summary';
    message: string;
    estimatedTime: string;
  } => {
    if (estimatedRecords <= 1000) {
      return {
        recommended: 'all',
        message: 'Small dataset - comprehensive export recommended',
        estimatedTime: '< 30 seconds'
      };
    } else if (estimatedRecords <= 5000) {
      return {
        recommended: 'custom',
        message: 'Medium dataset - custom export with selected sheets recommended',
        estimatedTime: '30-90 seconds'
      };
    } else {
      return {
        recommended: 'summary',
        message: 'Large dataset - summary export recommended for faster processing',
        estimatedTime: '1-3 minutes'
      };
    }
  },

  /**
   * Validate export parameters
   */
  validateExportParams: (params?: SalesSummaryExportParams): { valid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (params?.branch_id && (params.branch_id < 1 || !Number.isInteger(params.branch_id))) {
      errors.push('Branch ID must be a positive integer');
    }

    if (params?.region_id && (params.region_id < 1 || !Number.isInteger(params.region_id))) {
      errors.push('Region ID must be a positive integer');
    }

    if (params?.product_id && (params.product_id < 1 || !Number.isInteger(params.product_id))) {
      errors.push('Product ID must be a positive integer');
    }

    if (params?.category_id && (params.category_id < 1 || !Number.isInteger(params.category_id))) {
      errors.push('Category ID must be a positive integer');
    }

    if (params?.start_date && params?.end_date) {
      const fromDate = new Date(params.start_date);
      const toDate = new Date(params.end_date);
      if (fromDate > toDate) {
        errors.push('Start date must be before end date');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  },

  /**
   * Get export format options for UI
   */
  getExportFormatOptions: () => [
    {
      value: 'lightweight',
      label: 'Quick Export',
      description: 'Basic sales data only - fastest option (500 records max)',
      icon: 'Zap',
      estimatedTime: '< 15 seconds'
    },
    {
      value: 'summary',
      label: 'Summary Export',
      description: 'Essential sales data with basic analysis (1,500 records max)',
      icon: 'BarChart3',
      estimatedTime: '15-30 seconds'
    },
    {
      value: 'custom',
      label: 'Custom Export',
      description: 'Select specific sheets and data to include (500 records max)',
      icon: 'Settings',
      estimatedTime: '30-60 seconds'
    },
    {
      value: 'all',
      label: 'Comprehensive Export',
      description: 'Sales data with summary and details (1,000 records max)',
      icon: 'FileSpreadsheet',
      estimatedTime: '60-90 seconds'
    }
  ],

  /**
   * Get available column options for custom export
   */
  getColumnOptions: () => [
    { value: 'summary', label: 'Summary Statistics', category: 'basic' },
    { value: 'sales_details', label: 'Sales Details', category: 'basic' },
    { value: 'receipt_number', label: 'Receipt Numbers', category: 'basic' },
    { value: 'branch_info', label: 'Branch Information', category: 'location' },
    { value: 'region_info', label: 'Region Information', category: 'location' },
    { value: 'product_breakdown', label: 'Product Breakdown', category: 'products' },
    { value: 'category_breakdown', label: 'Category Breakdown', category: 'products' },
    { value: 'payment_methods', label: 'Payment Methods', category: 'payments' },
    { value: 'user_info', label: 'User Information', category: 'details' },
    { value: 'amounts', label: 'Amount Details (Total, Discount, Net)', category: 'financial' },
    { value: 'dates', label: 'Date Information', category: 'details' },
  ]
};

export default salesSummaryExportService;
