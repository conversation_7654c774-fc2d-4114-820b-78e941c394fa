const { DsaPayment, Customer, User, DsaStockAssignment, Product, Branch, PosSession } = require('../models');
const { Op } = require('sequelize');
const sequelize = require('../../config/database');
const logger = require('../utils/logger');
const AppError = require('../utils/error');
const DsaPricingService = require('../services/DsaPricingService');
const EnhancedDsaBalanceService = require('../services/EnhancedDsaBalanceService');
const DsaItemPaymentService = require('../services/DsaItemPaymentService');

/**
 * Enhanced DSA Payment Controller
 * Uses centralized pricing and balance services for consistent calculations
 */

/**
 * Create a DSA payment with enhanced validation and balance calculation
 */
exports.createEnhancedPayment = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      assignment_identifier,
      customer_id,
      branch_id,
      cash_amount = 0,
      paybill_amount = 0,
      cash_received, // Alternative field name used by mobile app
      notes = '',
      selected_item_payments = null // Array of { itemId, itemName, amountPaid } for item-level payments
    } = req.body;

    // Validate required fields
    if (!assignment_identifier || !customer_id || !branch_id) {
      throw new AppError(400, 'Assignment identifier, customer ID, and branch ID are required');
    }

    // Handle different field names from mobile app
    // Prioritize cash_received if it has a value, otherwise use cash_amount
    const cashAmountValue = (cash_received !== undefined && cash_received !== 0) ? cash_received : (cash_amount !== undefined ? cash_amount : 0);

    // Log received values for debugging
    logger.info(`DSA Payment received values: cash_amount=${cash_amount}, cash_received=${cash_received}, paybill_amount=${paybill_amount}, cashAmountValue=${cashAmountValue}`);

    // Parse and validate payment amounts
    const parsedCashAmount = parseFloat(cashAmountValue) || 0;
    const parsedPaybillAmount = parseFloat(paybill_amount) || 0;
    const totalAmount = parsedCashAmount + parsedPaybillAmount;

    logger.info(`DSA Payment parsed values: parsedCashAmount=${parsedCashAmount}, parsedPaybillAmount=${parsedPaybillAmount}, totalAmount=${totalAmount}`);

    // Get current balance before payment to check if all items are returned
    const currentBalance = await EnhancedDsaBalanceService.getCurrentBalance(assignment_identifier, transaction);

    // Only validate zero amount if not all items are returned
    if (totalAmount <= 0 && !currentBalance.all_items_returned) {
      throw new AppError(400, 'Total payment amount must be greater than zero');
    }

    // Validate payment amount using pricing service
    const validation = DsaPricingService.validatePaymentAmount(
      totalAmount,
      currentBalance.balance,
      {
        allowOverpayment: true,
        overpaymentTolerance: 0.1,
        allowZeroPayment: currentBalance.all_items_returned
      }
    );

    if (!validation.isValid) {
      throw new AppError(400, `Payment validation failed: ${validation.errors.join(', ')}`);
    }

    // Log warnings if any
    if (validation.warnings.length > 0) {
      logger.warn(`Payment warnings for ${assignment_identifier}: ${validation.warnings.join(', ')}`);
    }

    // Get the active POS session for the branch (if any)
    let activePosSession = null;
    try {
      activePosSession = await PosSession.findOne({
        where: {
          branch_id: branch_id,
          status: 'open',
          deleted_at: null
        },
        order: [['start_time', 'DESC']],
        transaction
      });
    } catch (sessionError) {
      logger.warn(`Could not find active POS session for branch ${branch_id}: ${sessionError.message}`);
    }

    // Create payment record
    const payment = await DsaPayment.create({
      assignment_identifier,
      customer_id,
      branch_id,
      pos_session_id: activePosSession?.id || null,
      cash_amount: parsedCashAmount,
      paybill_amount: parsedPaybillAmount,
      total_amount: totalAmount,
      payment_date: new Date(),
      notes,
      created_by: req.user?.id
    }, { transaction });

    // Process item-level payments if selected_item_payments is provided
    let itemPaymentResult = null;
    if (selected_item_payments && selected_item_payments.length > 0) {
      logger.info(`Processing item-level payment for ${selected_item_payments.length} items`);
      itemPaymentResult = await DsaItemPaymentService.processItemLevelPaymentByAmount(
        assignment_identifier,
        selected_item_payments,
        totalAmount,
        transaction
      );
    }

    // Recalculate balance using enhanced service
    const updatedBalance = await EnhancedDsaBalanceService.recalculateBalance(assignment_identifier, transaction);

    // Commit transaction
    await transaction.commit();

    // Fetch the created payment with associations for response
    const paymentWithAssociations = await DsaPayment.findByPk(payment.id, {
      include: [
        {
          model: Customer,
          as: 'DsaCustomer',
          attributes: ['id', 'name', 'phone']
        },
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'name']
        }
      ]
    });

    logger.info(`Enhanced DSA payment created successfully: ID=${payment.id}, Amount=${totalAmount}, Assignment=${assignment_identifier}`);

    res.status(201).json({
      success: true,
      message: itemPaymentResult ? 'DSA item-level payment created successfully' : 'DSA payment created successfully',
      data: {
        payment: paymentWithAssociations,
        balance_update: updatedBalance,
        item_payment_result: itemPaymentResult,
        validation_result: validation
      }
    });

  } catch (error) {
    await transaction.rollback();
    logger.error('Error creating enhanced DSA payment:', error);
    next(error);
  }
};

/**
 * Get DSA invoice with enhanced calculations
 */
exports.getEnhancedInvoice = async (req, res, next) => {
  try {
    const { assignment_identifier } = req.params;

    if (!assignment_identifier) {
      throw new AppError(400, 'Assignment identifier is required');
    }

    // Get assignments with product data
    const assignments = await DsaStockAssignment.findAll({
      where: { assignment_identifier },
      attributes: [
        'id', 'customer_id', 'user_id', 'branch_id', 'product_id',
        'quantity_assigned', 'quantity_returned', 'quantity_sold',
        'assignment_identifier', 'assigned_at', 'reconciled_at',
        'created_by', 'last_updated_by', 'reconciled', 'reconciliation_id',
        'payment_status', 'total_amount', 'amount_paid', 'balance',
        'last_payment_date', 'sale_id', 'created_at', 'updated_at'
      ],
      include: [
        {
          model: Product,
          attributes: ['id', 'name', 'default_wholesale_price', 'suggested_selling_price', 'sku']
        },
        {
          model: Customer,
          attributes: ['id', 'name', 'phone']
        },
        {
          model: Branch,
          attributes: ['id', 'name']
        }
      ]
    });

    if (assignments.length === 0) {
      throw new AppError(404, 'Assignment not found');
    }

    // Get current balance using enhanced service
    const balanceInfo = await EnhancedDsaBalanceService.getCurrentBalance(assignment_identifier);

    // Get payments
    const payments = await DsaPayment.findAll({
      where: { assignment_identifier },
      include: [
        {
          model: User,
          as: 'Creator',
          attributes: ['id', 'name']
        }
      ],
      order: [['payment_date', 'DESC']]
    });

    // Calculate detailed pricing breakdown
    const invoiceCalculation = DsaPricingService.calculateInvoiceTotal(assignments);

    // Get pricing summary for each item (for debugging/transparency)
    const itemPricingSummary = assignments.map(assignment =>
      DsaPricingService.getPricingSummary(assignment)
    );

    const response = {
      assignment_identifier,
      dsa_agent: assignments[0].Customer,
      branch: assignments[0].Branch,
      created_date: assignments[0].created_at,

      // Financial summary
      financial_summary: {
        total_assigned_value: invoiceCalculation.total_assigned_value,
        total_returned_value: invoiceCalculation.total_returned_value,
        total_remaining_value: invoiceCalculation.total_remaining_value,
        total_paid: balanceInfo.total_paid,
        balance: balanceInfo.balance,
        payment_status: balanceInfo.payment_status,
        all_items_returned: balanceInfo.all_items_returned
      },

      // Item breakdown
      items: invoiceCalculation.item_breakdown,

      // Payment history
      payments: payments.map(payment => ({
        id: payment.id,
        cash_amount: parseFloat(payment.cash_amount || 0),
        paybill_amount: parseFloat(payment.paybill_amount || 0),
        total_amount: parseFloat(payment.total_amount || 0),
        payment_date: payment.payment_date,
        notes: payment.notes,
        created_by: payment.Creator ? {
          id: payment.Creator.id,
          name: payment.Creator.name
        } : null
      })),

      // Pricing transparency (for debugging)
      pricing_summary: itemPricingSummary,

      // Metadata
      calculation_timestamp: new Date(),
      calculation_method: 'enhanced_pricing_service'
    };

    res.status(200).json({
      success: true,
      data: response
    });

  } catch (error) {
    logger.error('Error getting enhanced DSA invoice:', error);
    next(error);
  }
};

/**
 * Get enhanced DSA invoices list with consistent calculations
 */
exports.getEnhancedInvoicesList = async (req, res, next) => {
  try {
    const {
      branch_id,
      payment_status,
      dsa_agent_id,
      page = 1,
      limit = 20,
      sort_by = 'created_at',
      sort_order = 'DESC'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Build where clause
    const whereClause = {};
    if (branch_id) whereClause.branch_id = branch_id;
    if (dsa_agent_id) whereClause.customer_id = dsa_agent_id;

    // Get unique assignment identifiers
    const assignments = await DsaStockAssignment.findAll({
      where: whereClause,
      attributes: ['assignment_identifier'],
      group: ['assignment_identifier'],
      raw: true
    });

    const assignmentIdentifiers = assignments.map(a => a.assignment_identifier);

    if (assignmentIdentifiers.length === 0) {
      return res.status(200).json({
        success: true,
        data: {
          invoices: [],
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: 0,
            pages: 0
          }
        }
      });
    }

    // Get balance summary for all assignments
    const balanceSummary = await EnhancedDsaBalanceService.getBalanceSummary(assignmentIdentifiers);

    // Filter by payment status if specified
    let filteredSummaries = balanceSummary.individual_summaries;
    if (payment_status) {
      // Handle comma-separated payment statuses
      const statusArray = payment_status.split(',').map(status => status.trim());
      filteredSummaries = filteredSummaries.filter(summary =>
        statusArray.includes(summary.payment_status)
      );
    }

    // Apply pagination
    const total = filteredSummaries.length;
    const pages = Math.ceil(total / parseInt(limit));
    const paginatedSummaries = filteredSummaries.slice(offset, offset + parseInt(limit));

    // Format response
    const invoices = paginatedSummaries.map(summary => ({
      assignment_identifier: summary.assignment_identifier,
      dsa_agent: summary.assignments[0]?.Customer || null,
      branch: summary.assignments[0]?.Branch || null,
      total_remaining_value: summary.total_remaining_value,
      total_paid: summary.total_paid,
      balance: summary.balance,
      payment_status: summary.payment_status,
      all_items_returned: summary.all_items_returned,
      item_count: summary.assignments.length,
      payment_count: summary.payments.length,
      created_date: summary.assignments[0]?.created_at || null,
      last_payment_date: summary.payments[0]?.payment_date || null,
      // Include items for mobile app compatibility
      items: summary.assignments.map(assignment => ({
        product_id: assignment.product_id,
        product_name: assignment.Product?.name || 'Unknown Product',
        quantity_assigned: assignment.quantity_assigned || 0,
        quantity_returned: assignment.quantity_returned || 0,
        wholesale_price: assignment.Product?.default_wholesale_price || 0
      }))
    }));

    res.status(200).json({
      success: true,
      data: {
        invoices,
        summary: balanceSummary.total_summary,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages
        }
      }
    });

  } catch (error) {
    logger.error('Error getting enhanced DSA invoices list:', error);
    next(error);
  }
};

/**
 * Validate payment amount before processing
 */
exports.validatePaymentAmount = async (req, res, next) => {
  try {
    const { assignment_identifier, payment_amount } = req.body;

    if (!assignment_identifier || payment_amount === undefined) {
      throw new AppError(400, 'Assignment identifier and payment amount are required');
    }

    const parsedAmount = parseFloat(payment_amount);
    if (isNaN(parsedAmount)) {
      throw new AppError(400, 'Invalid payment amount');
    }

    // Get current balance
    const currentBalance = await EnhancedDsaBalanceService.getCurrentBalance(assignment_identifier);

    // Validate payment amount
    const validation = DsaPricingService.validatePaymentAmount(
      parsedAmount,
      currentBalance.balance,
      {
        allowOverpayment: true,
        overpaymentTolerance: 0.1,
        allowZeroPayment: currentBalance.all_items_returned
      }
    );

    res.status(200).json({
      success: true,
      data: {
        assignment_identifier,
        payment_amount: parsedAmount,
        current_balance: currentBalance.balance,
        validation_result: validation,
        balance_info: {
          total_remaining_value: currentBalance.total_remaining_value,
          total_paid: currentBalance.total_paid,
          payment_status: currentBalance.payment_status,
          all_items_returned: currentBalance.all_items_returned
        }
      }
    });

  } catch (error) {
    logger.error('Error validating payment amount:', error);
    next(error);
  }
};
