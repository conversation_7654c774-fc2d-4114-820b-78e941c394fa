const { DataTypes } = require('sequelize');
const sequelize = require('../../config/database');

const Sale = sequelize.define('Sale', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  pos_session_id: {
    type: DataTypes.INTEGER,
    allowNull: true // Allow null for DSA sales
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'This field now takes the value of employee_id (seller ID)'
  },
  branch_id: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  customer_id: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  employee_id: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Required in API but nullable in database for backward compatibility'
  },
  total_amount: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false
  },
  total_vat_amount: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: 'Total VAT amount for the entire sale'
  },
  total_excluding_vat: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: 'Total amount excluding VAT'
  },
  vat_invoice_number: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'VAT invoice number if applicable'
  },
  total_discount: {
    type: DataTypes.DECIMAL(12, 2),
    allowNull: false,
    defaultValue: 0.00,
    comment: 'Total discount amount applied to the sale'
  },
  payment_method_id: {
    type: DataTypes.INTEGER,
    allowNull: false
  },
  payment_reference: {
    type: DataTypes.STRING,
    allowNull: true
  },
  receipt_number: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Receipt number for the sale'
  },
  credit_partner_id: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('completed', 'cancelled', 'refunded'),
    defaultValue: 'completed'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  // KRA integration fields
  kra_verification_code: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'KRA verification code for the sale'
  },
  kra_fiscal_receipt_number: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'KRA fiscal receipt number for the sale'
  },
  kra_verification_url: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'URL for verifying the sale with KRA'
  },
  kra_verification_timestamp: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Timestamp of KRA verification'
  },
  kra_integration_status: {
    type: DataTypes.ENUM('pending', 'completed', 'failed', 'offline'),
    defaultValue: 'pending',
    comment: 'Status of KRA integration'
  },
  kra_response_data: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Full response data from KRA API'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  deleted_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  is_dsa: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  sale_type: {
    type: DataTypes.ENUM('normal', 'dsa', 'corporate', 'dsa_assignment', 'dsa_customer_sale'),
    allowNull: false,
    defaultValue: 'normal',
    comment: 'Type of sale: normal (POS sale), dsa (legacy DSA), corporate (corporate sale), dsa_assignment (stock assigned to DSA), dsa_customer_sale (DSA sells to customer)'
  },

  created_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'User ID who created the sale'
  },
  last_updated_by: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'User ID who last updated the sale'
  }
}, {
  tableName: 'sales',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  deletedAt: 'deleted_at',
  paranoid: true // Soft deletes
});

module.exports = Sale;
