const { v4: uuidv4 } = require('uuid');
const DsaStockAssignment = require('../models/dsa-stock-assignment.model');
const DsaStockReconciliation = require('../models/dsa-stock-reconciliation.model');
const DsaPayment = require('../models/dsa-payment.model');
const Product = require('../models/product.model');
const User = require('../models/user.model');
const Branch = require('../models/branch.model');
const Sale = require('../models/sale.model');
const SaleItem = require('../models/sale-item.model');
const PaymentMethod = require('../models/payment-method.model');
const Customer = require('../models/customer.model');
const StockItem = require('../models/stock-item.model');
const InventoryTransaction = require('../models/inventory-transaction.model');
const sequelize = require('../../config/database');
const { Op } = require('sequelize');
const AppError = require('../utils/error');
const logger = require('../utils/logger');

// Import enhanced DSA services
const DsaStockLimitService = require('../services/DsaStockLimitService');
const DsaBalanceService = require('../services/DsaBalanceService');

/**
 * Standardized KRA data extraction with enhanced parsing
 * @param {Object} sale - Sale object
 * @returns {Object} - KRA data object
 */
const extractKRAData = (sale) => {
  logger.info(`Extracting KRA data for sale ${sale.id}:`, {
    kra_verification_url: sale.kra_verification_url || 'not set',
    kra_verification_code: sale.kra_verification_code || 'not set',
    kra_fiscal_receipt_number: sale.kra_fiscal_receipt_number || 'not set',
    kra_integration_status: sale.kra_integration_status || 'not set',
    has_kra_response_data: !!sale.kra_response_data,
    kra_response_data_length: sale.kra_response_data ? sale.kra_response_data.length : 0
  });

  // Start with direct fields from the sale
  let kraData = {
    verification_url: sale.kra_verification_url || '',
    verification_code: sale.kra_verification_code || '',
    fiscal_receipt_number: sale.kra_fiscal_receipt_number || '',
    integration_status: sale.kra_integration_status || 'pending',
    verification_timestamp: sale.kra_verification_timestamp || null
  };

  // If we have kra_response_data, try to extract additional information
  if (sale.kra_response_data) {
    try {
      const parsedKraData = JSON.parse(sale.kra_response_data);
      logger.info(`Parsed KRA response data for sale ${sale.id}:`, {
        qrCodeUrl: parsedKraData.qrCodeUrl || 'not available',
        verificationCode: parsedKraData.verificationCode || 'not available',
        fiscalReceiptNumber: parsedKraData.fiscalReceiptNumber || 'not available',
        invoiceNum: parsedKraData.invoiceNum || 'not available',
        cuNumber: parsedKraData.cuNumber || 'not available'
      });

      // Check for raw KRA field names first (these are the actual field names from KRA)
      if (parsedKraData.qrCode && !kraData.verification_url) {
        kraData.verification_url = parsedKraData.qrCode;
      }

      if (parsedKraData.cuNumber && !kraData.verification_code) {
        kraData.verification_code = parsedKraData.cuNumber;
      }

      if (parsedKraData.invoiceNum && !kraData.fiscal_receipt_number) {
        kraData.fiscal_receipt_number = parsedKraData.invoiceNum;
      }

      // Also check for processed field names (fallback)
      if (parsedKraData.qrCodeUrl && !kraData.verification_url) {
        kraData.verification_url = parsedKraData.qrCodeUrl;
      }

      if (parsedKraData.verificationCode && !kraData.verification_code) {
        kraData.verification_code = parsedKraData.verificationCode;
      }

      if (parsedKraData.fiscalReceiptNumber && !kraData.fiscal_receipt_number) {
        kraData.fiscal_receipt_number = parsedKraData.fiscalReceiptNumber;
      }

      // Store the full parsed data for reference
      kraData.full_response = parsedKraData;

    } catch (parseError) {
      logger.error(`Error parsing KRA response data for sale ${sale.id}: ${parseError.message}`);
    }
  }

  logger.info(`Final KRA data for sale ${sale.id}:`, kraData);
  return kraData;
};

/**
 * Reduce stock quantities for DSA assignment
 * @param {number} branchId - Branch ID
 * @param {number} productId - Product ID
 * @param {number} quantity - Quantity to reduce
 * @param {string} assignmentIdentifier - Assignment identifier for reference
 * @param {number} userId - User ID who created the assignment
 * @param {Object} transaction - Database transaction
 */
const reduceStockForDsaAssignment = async (branchId, productId, quantity, assignmentIdentifier, userId, transaction) => {
  try {
    // Find the stock item for this product in the branch
    const stockItem = await StockItem.findOne({
      where: {
        branch_id: branchId,
        product_id: productId,
        deleted_at: null
      },
      transaction
    });

    if (!stockItem) {
      throw new AppError(400, `No stock found for product ID ${productId} in branch ${branchId}`);
    }

    // Check if there's enough stock
    if (stockItem.quantity < quantity) {
      throw new AppError(400, `Insufficient stock for product ID ${productId}. Available: ${stockItem.quantity}, Requested: ${quantity}`);
    }

    // Reduce the stock quantity
    await stockItem.update({
      quantity: stockItem.quantity - quantity
    }, { transaction });

    // Create an inventory transaction record
    await InventoryTransaction.create({
      transaction_type: 'TRANSFER_OUT', // DSA assignment is like a transfer out
      reference_id: null, // We don't have a specific reference ID for DSA assignments
      reference_type: 'dsa_assignment',
      stock_item_id: stockItem.id,
      quantity: -quantity, // Negative quantity for outgoing stock
      unit_cost: stockItem.default_buying_price || 0,
      total_cost: (stockItem.default_buying_price || 0) * quantity,
      from_branch_id: branchId,
      to_branch_id: null, // DSA assignment doesn't have a destination branch
      notes: `DSA Assignment: ${assignmentIdentifier}`,
      created_by: userId,
      transaction_date: new Date()
    }, { transaction });

    logger.info(`Reduced stock for product ${productId} in branch ${branchId}: ${quantity} units (Assignment: ${assignmentIdentifier})`);

    return {
      success: true,
      previous_quantity: stockItem.quantity + quantity,
      new_quantity: stockItem.quantity,
      reduced_quantity: quantity
    };
  } catch (error) {
    logger.error(`Error reducing stock for DSA assignment: ${error.message}`);
    throw error;
  }
};

/**
 * Create multiple DSA stock assignments in a batch
 */
exports.createBatchAssignments = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    // Accept both dsa_agent_id and customer_id for backward compatibility
    const { dsa_agent_id, customer_id, branch_id, items, assignment_identifier } = req.body;

    // Use customer_id if provided, otherwise use dsa_agent_id
    const dsaAgentId = customer_id || dsa_agent_id;

    console.log(`Creating batch assignments for DSA agent ID: ${dsaAgentId}, branch ID: ${branch_id}, items count: ${items?.length || 0}`);

    // Validate request
    if (!dsaAgentId || !branch_id || !items || !Array.isArray(items) || items.length === 0) {
      console.error('Invalid request data:', { dsaAgentId, branch_id, items });
      throw new AppError(400, 'Invalid request data');
    }

    // Allow branch_manager to create assignments for any branch
    console.log('Branch manager permission check bypassed - allowing assignments for any branch');

    // Check if DSA agent exists (as a customer with is_dsa=true)
    const dsaAgent = await Customer.findOne({
      where: { id: dsaAgentId, is_dsa: true }
    });

    if (!dsaAgent) {
      throw new AppError(404, 'DSA agent not found');
    }

    // Check if branch exists
    const branch = await Branch.findByPk(branch_id);

    if (!branch) {
      throw new AppError(404, 'Branch not found');
    }

    // Check if branch is headquarters (level=0)
    const isHeadquarters = branch.level === 0;

    // If not headquarters, check if DSA agent belongs to the specified branch
    if (!isHeadquarters && dsaAgent.branch_id && parseInt(dsaAgent.branch_id) !== parseInt(branch_id)) {
      throw new AppError(400, 'DSA agent does not belong to the specified branch. Only headquarters can assign stock to agents from other branches.');
    }

    // Use enhanced stock limit service for validation
    const validation = await DsaStockLimitService.validateAssignment(dsaAgentId, items, transaction);

    if (!validation.is_valid) {
      throw new AppError(400, validation.message);
    }

    console.log(`Stock limit validation passed: ${validation.message}`);

    // Check for existing unpaid assignments
    let existingIdentifier = assignment_identifier;

    if (!existingIdentifier) {
      // Find any existing unpaid assignment for this DSA agent
      // Use payment_status instead of reconciled flag to only consider unpaid or partially paid assignments
      const unpaidAssignment = await DsaStockAssignment.findOne({
        where: {
          customer_id: dsaAgentId,
          [Op.or]: [
            { payment_status: 'UNPAID' },
            { payment_status: 'PARTIALLY_PAID' },
            { payment_status: null } // Include assignments with no payment status set
          ]
        },
        order: [['created_at', 'DESC']] // Get the most recent one
      });

      if (unpaidAssignment && unpaidAssignment.assignment_identifier) {
        // Use the existing assignment identifier
        existingIdentifier = unpaidAssignment.assignment_identifier;
        console.log(`Using existing assignment identifier: ${existingIdentifier}`);
      } else {
        // Generate a new assignment identifier
        existingIdentifier = uuidv4();
        console.log(`Generated new assignment identifier: ${existingIdentifier}`);
      }
    } else {
      console.log(`Using provided assignment identifier: ${existingIdentifier}`);
    }

    // Create or update assignments for each item
    const assignments = [];
    const saleItems = []; // Array to hold items for the sale record
    let totalSaleAmount = 0; // Total amount for the sale

    // Get all products at once to avoid multiple queries
    const productIds = items.map(item => parseInt(item.product_id));
    const products = await Product.findAll({
      where: { id: productIds },
      transaction
    });

    // Create a map for quick lookup
    const productMap = {};
    products.forEach(product => {
      productMap[product.id] = product;
    });

    // Get stock items for all products to get their wholesale prices
    const stockItems = await StockItem.findAll({
      where: {
        branch_id: branch_id,
        product_id: productIds,
        deleted_at: null
      },
      attributes: ['product_id', 'default_wholesale_price', 'default_selling_price', 'default_buying_price'],
      transaction
    });

    // Create a map of product ID to prices
    const productPriceMap = {};
    stockItems.forEach(item => {
      const wholesalePrice = parseFloat(item.default_wholesale_price || 0);
      const sellingPrice = parseFloat(item.default_selling_price || 0);
      const buyingPrice = parseFloat(item.default_buying_price || 0);

      productPriceMap[item.product_id] = {
        wholesale_price: wholesalePrice > 0 ? wholesalePrice : sellingPrice,
        buying_price: buyingPrice
      };
    });

    for (const item of items) {
      const { product_id, quantity_assigned } = item;
      const productId = parseInt(product_id);
      const quantityAssigned = parseInt(quantity_assigned);

      // Check if product exists and can be assigned to DSA
      const product = productMap[productId];

      if (!product) {
        throw new AppError(404, `Product with ID ${productId} not found`);
      }

      // Check if product can be assigned to DSA
      if (product.can_assign_to_dsa === false) {
        throw new AppError(400, `Product "${product.name}" cannot be assigned to DSA agents`);
      }

      // Get the wholesale price for this product
      const priceInfo = productPriceMap[productId] || { wholesale_price: 0, buying_price: 0 };
      const wholesalePrice = priceInfo.wholesale_price;
      const buyingPrice = priceInfo.buying_price;

      // Calculate the total for this item
      const itemTotal = quantityAssigned * wholesalePrice;
      totalSaleAmount += itemTotal;

      // Add to sale items array
      saleItems.push({
        product_id: productId,
        quantity: quantityAssigned,
        unit_price: wholesalePrice,
        total_price: itemTotal,
        buying_price: buyingPrice,
        is_wholesale: true // Mark as a wholesale item
      });

      // Check if an assignment already exists for this customer, branch, and product
      // Use payment_status instead of reconciled flag to only consider unpaid or partially paid assignments
      let assignment = await DsaStockAssignment.findOne({
        where: {
          customer_id: dsaAgentId,
          branch_id,
          product_id: productId,
          [Op.or]: [
            { payment_status: 'UNPAID' },
            { payment_status: 'PARTIALLY_PAID' },
            { payment_status: null } // Include assignments with no payment status set
          ]
        },
        transaction
      });

      if (assignment) {
        // If assignment exists, update the quantity
        console.log(`Updating existing assignment for product ${productId} with additional quantity ${quantityAssigned}`);

        // Reduce stock for the additional quantity being assigned
        await reduceStockForDsaAssignment(
          branch_id,
          productId,
          quantityAssigned,
          existingIdentifier,
          req.user ? req.user.id : null,
          transaction
        );

        // Update the assignment with the new quantity
        await assignment.update({
          quantity_assigned: assignment.quantity_assigned + quantityAssigned,
          assignment_identifier: existingIdentifier, // Ensure it has the same identifier
          last_updated_by: req.user ? req.user.id : null
        }, { transaction });
      } else {
        // If no assignment exists, create a new one
        console.log(`Creating new assignment for product ${productId} with quantity ${quantityAssigned}`);

        // Reduce stock for the new assignment
        await reduceStockForDsaAssignment(
          branch_id,
          productId,
          quantityAssigned,
          existingIdentifier,
          req.user ? req.user.id : null,
          transaction
        );

        // Create the assignment without user_id since DSA is now a customer, not a user
        assignment = await DsaStockAssignment.create({
          customer_id: dsaAgentId, // DSA is now identified by customer_id
          branch_id,
          product_id: productId,
          quantity_assigned: quantityAssigned,
          assignment_identifier: existingIdentifier,
          created_by: req.user ? req.user.id : null,
          last_updated_by: req.user ? req.user.id : null
        }, { transaction });
      }

      // Load associations
      const assignmentWithAssociations = await DsaStockAssignment.findByPk(assignment.id, {
        include: [
          { model: Customer },
          { model: Branch },
          { model: Product }
        ],
        transaction
      });

      assignments.push(assignmentWithAssociations);
    }

    // Create a sale record for the assigned items
    let sale = null;
    if (saleItems.length > 0 && totalSaleAmount > 0) {
      // Get the default cash payment method
      const cashPaymentMethod = await PaymentMethod.findOne({
        where: { code: 'CASH', is_active: true },
        transaction
      });

      if (!cashPaymentMethod) {
        throw new AppError(404, 'Cash payment method not found');
      }

      // Generate receipt number for DSA assignment
      const receipt_number = `DSA-ASSIGN-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

      // Create the sale record
      sale = await Sale.create({
        pos_session_id: null, // DSA sales don't have a POS session
        user_id: req.user ? req.user.id : null, // The user making the assignment
        branch_id,
        customer_id: dsaAgentId, // Use the DSA agent as the customer
        employee_id: req.user ? req.user.id : null, // Employee making the assignment
        total_amount: totalSaleAmount,
        payment_method_id: cashPaymentMethod.id,
        payment_reference: `DSA Assignment ${existingIdentifier}`,
        receipt_number, // Add receipt number
        status: 'completed',
        notes: 'DSA Stock Assignment',
        sale_type: 'dsa', // Set sale type to DSA
        assignment_identifier: existingIdentifier, // Link to assignment
        is_dsa: true, // Mark as a DSA sale
        is_wholesale: true, // Mark as a wholesale sale
        created_by: req.user ? req.user.id : null,
        last_updated_by: req.user ? req.user.id : null
      }, { transaction });

      // Create sale items
      for (const item of saleItems) {
        await SaleItem.create({
          sale_id: sale.id,
          product_id: item.product_id,
          quantity: item.quantity,
          unit_price: item.unit_price,
          total_price: item.total_price,
          buying_price: item.buying_price
        }, { transaction });
      }

      // Register DSA assignment sale with KRA BEFORE committing the transaction
      try {
        // Prepare complete sale data for KRA integration
        const completeSale = await Sale.findByPk(sale.id, {
          include: [
            {
              model: SaleItem,
              include: [{ model: Product }]
            },
            { model: Customer },
            { model: PaymentMethod },
            { model: User },
            { model: Branch }
          ],
          transaction
        });

        // Add customer PIN if available
        if (dsaAgent.pin_number) {
          completeSale.customer_pin = dsaAgent.pin_number.trim();
        }

        // Add assignment identifier for tracking
        completeSale.assignment_identifier = existingIdentifier;

        logger.info(`Attempting KRA registration for DSA assignment sale ${sale.id} before committing transaction`);
        const KraIntegrationService = require('../services/kra-integration.service');
        const kraService = new KraIntegrationService();
        const kraResponse = await kraService.registerSale(completeSale);

        // Validate KRA response has required data
        if (!kraResponse) {
          throw new Error('KRA service returned null or undefined response');
        }

        if (!kraResponse.verificationCode) {
          throw new Error('KRA service did not return a verification code');
        }

        if (!kraResponse.fiscalReceiptNumber) {
          throw new Error('KRA service did not return a fiscal receipt number');
        }

        // Log successful KRA response for debugging
        logger.info(`KRA response validation passed for sale ${sale.id}:`, {
          verificationCode: kraResponse.verificationCode,
          fiscalReceiptNumber: kraResponse.fiscalReceiptNumber,
          hasQrCodeUrl: !!kraResponse.qrCodeUrl,
          hasFullResponseData: !!kraResponse.fullResponseData
        });

        // Create update data object exactly like normal sale
        const updateData = {
          kra_verification_code: kraResponse.verificationCode,
          kra_fiscal_receipt_number: kraResponse.fiscalReceiptNumber,
          kra_verification_url: kraResponse.qrCodeUrl || kraResponse.verificationUrl,
          kra_verification_timestamp: kraResponse.timestamp,
          kra_integration_status: kraResponse.offline ? 'offline' : 'completed',
          kra_response_data: kraResponse.fullResponseData
        };

        // Store QR code image in the response data for the mobile app if available
        if (kraResponse.qrCodeImage) {
          try {
            const kraData = JSON.parse(kraResponse.fullResponseData || '{}');
            kraData.qrCodeImage = kraResponse.qrCodeImage;
            updateData.kra_response_data = JSON.stringify(kraData);
          } catch (e) {
            logger.error(`Error updating KRA response data with QR code image: ${e.message}`);
          }
        }

        await sale.update(updateData, { transaction });

        logger.info(`KRA registration successful for DSA assignment sale ${sale.id}: ${kraResponse.verificationCode}`);

      } catch (kraError) {
        logger.error(`KRA integration failed for DSA assignment sale ${sale.id}: ${kraError.message}`);
        logger.error('KRA error details:', {
          message: kraError.message,
          stack: kraError.stack,
          response: kraError.response?.data || 'No response data'
        });

        // Roll back the transaction immediately
        try {
          await transaction.rollback();
          logger.info(`Transaction rolled back due to KRA integration failure for sale ${sale.id}`);
        } catch (rollbackError) {
          logger.error(`Failed to rollback transaction after KRA error: ${rollbackError.message}`);
        }

        // Throw a detailed error with KRA failure information
        throw new AppError(500, `KRA integration failed for DSA assignment: ${kraError.message}. Transaction has been rolled back. Please try again or contact support if the issue persists.`);
      }
    }

    await transaction.commit();

    // Reload the sale with updated KRA data after commit
    if (sale) {
      await sale.reload();
      logger.info(`Sale ${sale.id} reloaded after commit - KRA fields:`, {
        kra_verification_url: sale.kra_verification_url,
        kra_verification_code: sale.kra_verification_code,
        kra_fiscal_receipt_number: sale.kra_fiscal_receipt_number,
        kra_response_data_length: sale.kra_response_data ? sale.kra_response_data.length : 0
      });
    }

    // Extract KRA data using the same standardized method as normal sales
    const kraData = sale ? extractKRAData(sale) : {};

    if (sale) {
      logger.info(`Extracted KRA data for sale ${sale.id}:`, kraData);
    }

    const responseData = {
      status: 'success',
      data: {
        assignments,
        assignment_identifier: existingIdentifier,
        sale: sale ? {
          id: sale.id,
          total_amount: sale.total_amount,
          is_dsa: true,
          is_wholesale: true,
          items_count: saleItems.length,
          // Include standardized KRA data for printing
          kra_verification_url: sale.kra_verification_url,
          kra_verification_code: sale.kra_verification_code,
          kra_fiscal_receipt_number: sale.kra_fiscal_receipt_number,
          kra_verification_timestamp: sale.kra_verification_timestamp,
          kra_integration_status: sale.kra_integration_status,
          kra_response_data: sale.kra_response_data,
          kra_data: kraData // Include standardized KRA data object
        } : null
      }
    };

    // Log the response data being sent to mobile app
    if (sale) {
      logger.info(`Sending response to mobile app for sale ${sale.id}:`, {
        sale_id: responseData.data.sale.id,
        kra_verification_url: responseData.data.sale.kra_verification_url,
        kra_verification_code: responseData.data.sale.kra_verification_code,
        kra_fiscal_receipt_number: responseData.data.sale.kra_fiscal_receipt_number,
        kra_data: responseData.data.sale.kra_data,
        kra_response_data_length: responseData.data.sale.kra_response_data ? responseData.data.sale.kra_response_data.length : 0
      });
    }

    res.status(201).json(responseData);
  } catch (error) {
    // Only roll back if the transaction is still active and hasn't been rolled back already
    if (transaction && !transaction.finished) {
      try {
        await transaction.rollback();
        logger.info('Transaction rolled back in main catch block');
      } catch (rollbackError) {
        logger.error('Error rolling back transaction in main catch block:', rollbackError.message);
        // Continue with error handling even if rollback fails
      }
    } else {
      logger.info('Transaction already finished (committed or rolled back), skipping rollback in main catch block');
    }

    // Check if it's a unique constraint violation
    if (error.name === 'SequelizeUniqueConstraintError') {
      console.error('Unique constraint violation:', error.message);

      try {
        // Start a new transaction
        const newTransaction = await sequelize.transaction();

        // First, determine the assignment identifier to use
        let assignmentId = assignment_identifier;

        if (!assignmentId) {
          // Find any existing unpaid assignment for this DSA agent
          // Use payment_status instead of reconciled flag to only consider unpaid or partially paid assignments
          const unpaidAssignment = await DsaStockAssignment.findOne({
            where: {
              customer_id: dsaAgentId,
              [Op.or]: [
                { payment_status: 'UNPAID' },
                { payment_status: 'PARTIALLY_PAID' },
                { payment_status: null } // Include assignments with no payment status set
              ]
            },
            order: [['created_at', 'DESC']], // Get the most recent one
            transaction: newTransaction
          });

          if (unpaidAssignment && unpaidAssignment.assignment_identifier) {
            assignmentId = unpaidAssignment.assignment_identifier;
            console.log(`Using existing assignment identifier: ${assignmentId}`);
          } else {
            // Generate a new assignment identifier
            assignmentId = uuidv4();
            console.log(`Generated new assignment identifier: ${assignmentId}`);
          }
        }

        // Check if DSA agent has a stock limit
        if (dsaAgent.stock_limit !== null && dsaAgent.stock_limit !== undefined) {
          // Get current total assigned stock for this DSA customer with product information
          // Use payment_status instead of reconciled flag to only consider unpaid or partially paid assignments
          const currentAssignments = await DsaStockAssignment.findAll({
            where: {
              customer_id: dsaAgentId,
              [Op.or]: [
                { payment_status: 'UNPAID' },
                { payment_status: 'PARTIALLY_PAID' },
                { payment_status: null } // Include assignments with no payment status set
              ]
            },
            attributes: ['product_id', 'quantity_assigned', 'quantity_returned', 'quantity_sold', 'payment_status'],
            transaction: newTransaction
          });

          console.log(`Found ${currentAssignments.length} unpaid/partially paid assignments for DSA agent ID ${dsaAgentId} in error handler`);

          // Get all product IDs from current assignments and new items
          const productIds = new Set();
          currentAssignments.forEach(assignment => productIds.add(assignment.product_id));
          items.forEach(item => productIds.add(parseInt(item.product_id)));

          // Get stock items for all products to get their wholesale prices
          const stockItems = await StockItem.findAll({
            where: {
              branch_id: branch_id,
              product_id: Array.from(productIds),
              deleted_at: null
            },
            attributes: ['product_id', 'default_wholesale_price', 'default_selling_price'],
            transaction: newTransaction
          });

          // Create a map of product ID to wholesale price (fallback to selling price if wholesale price is not set)
          const productPriceMap = {};
          stockItems.forEach(item => {
            // Use wholesale price for DSA assignments, fallback to selling price if wholesale price is not set
            const wholesalePrice = parseFloat(item.default_wholesale_price || 0);
            const sellingPrice = parseFloat(item.default_selling_price || 0);
            productPriceMap[item.product_id] = wholesalePrice > 0 ? wholesalePrice : sellingPrice;
          });

          // Calculate current total value of assigned stock
          let currentTotalValue = 0;
          currentAssignments.forEach(assignment => {
            const currentQuantity = assignment.quantity_assigned - (assignment.quantity_returned || 0) - (assignment.quantity_sold || 0);
            const price = productPriceMap[assignment.product_id] || 0;
            currentTotalValue += currentQuantity * price;
          });

          // Calculate total value of new items to be assigned
          let newItemsValue = 0;
          for (const item of items) {
            const productId = parseInt(item.product_id);
            const quantity = parseInt(item.quantity_assigned);
            const price = productPriceMap[productId] || 0;
            newItemsValue += quantity * price;
          }

          // Calculate new total value if these assignments are added
          const newTotalValue = currentTotalValue + newItemsValue;

          // Format currency values for logging and error messages
          const formatCurrency = (value) => `KSh ${value.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')}`;
          const formattedCurrentValue = formatCurrency(currentTotalValue);
          const formattedNewItemsValue = formatCurrency(newItemsValue);
          const formattedNewTotalValue = formatCurrency(newTotalValue);
          const formattedStockLimit = formatCurrency(parseFloat(dsaAgent.stock_limit));

          // Check if new total value exceeds the limit
          if (newTotalValue > parseFloat(dsaAgent.stock_limit)) {
            // Don't roll back the transaction here, we'll do it in the catch block
            throw new AppError(400, `Assignment would exceed DSA agent's stock value limit of ${formattedStockLimit}. Current value: ${formattedCurrentValue}, Requested: ${formattedNewItemsValue}, New total: ${formattedNewTotalValue}`);
          }

          console.log(`Stock value limit check passed in error handler: Current value: ${formattedCurrentValue}, Requested: ${formattedNewItemsValue}, Limit: ${formattedStockLimit}`);
        }

        // Process each item and update existing assignments
        const assignments = [];
        for (const item of items) {
          const { product_id, quantity_assigned } = item;

          // Find the existing assignment
          // Use payment_status instead of reconciled flag to only consider unpaid or partially paid assignments
          const existingAssignment = await DsaStockAssignment.findOne({
            where: {
              customer_id: dsaAgentId,
              branch_id,
              product_id,
              [Op.or]: [
                { payment_status: 'UNPAID' },
                { payment_status: 'PARTIALLY_PAID' },
                { payment_status: null } // Include assignments with no payment status set
              ]
            },
            transaction: newTransaction
          });

          if (existingAssignment) {
            // Update the existing assignment with the new quantity
            console.log(`Updating existing assignment for product ${product_id} with additional quantity ${quantity_assigned}`);

            // Reduce stock for the additional quantity being assigned
            await reduceStockForDsaAssignment(
              branch_id,
              product_id,
              parseInt(quantity_assigned),
              assignmentId,
              req.user ? req.user.id : null,
              newTransaction
            );

            await existingAssignment.update({
              quantity_assigned: existingAssignment.quantity_assigned + parseInt(quantity_assigned),
              assignment_identifier: assignmentId, // Ensure it has the same identifier
              last_updated_by: req.user ? req.user.id : null
            }, { transaction: newTransaction });

            // Load associations
            const assignmentWithAssociations = await DsaStockAssignment.findByPk(existingAssignment.id, {
              include: [
                { model: Customer },
                { model: User },
                { model: Branch },
                { model: Product }
              ],
              transaction: newTransaction
            });

            assignments.push(assignmentWithAssociations);
          } else {
            // If no assignment exists, create a new one
            console.log(`Creating new assignment for product ${product_id} with quantity ${quantity_assigned}`);

            // Reduce stock for the new assignment
            await reduceStockForDsaAssignment(
              branch_id,
              product_id,
              parseInt(quantity_assigned),
              assignmentId,
              req.user ? req.user.id : null,
              newTransaction
            );

            // Create the assignment without user_id since DSA is now a customer, not a user
            const newAssignment = await DsaStockAssignment.create({
              customer_id: dsaAgentId, // DSA is now identified by customer_id
              branch_id,
              product_id,
              quantity_assigned,
              assignment_identifier: assignmentId,
              created_by: req.user ? req.user.id : null,
              last_updated_by: req.user ? req.user.id : null
            }, { transaction: newTransaction });

            // Load associations
            const assignmentWithAssociations = await DsaStockAssignment.findByPk(newAssignment.id, {
              include: [
                { model: Customer },
                { model: Branch },
                { model: Product }
              ],
              transaction: newTransaction
            });

            assignments.push(assignmentWithAssociations);
          }
        }

        await newTransaction.commit();

        // Return success response with updated assignments
        return res.status(200).json({
          status: 'success',
          data: {
            assignments,
            assignment_identifier: assignmentId
          }
        });
      } catch (retryError) {
        console.error('Error while handling unique constraint violation:', retryError);

        // Try to roll back the new transaction if it exists
        try {
          if (newTransaction) {
            await newTransaction.rollback();
          }
        } catch (rollbackError) {
          console.error('Error rolling back retry transaction:', rollbackError.message);
        }

        return next(retryError);
      }
    }

    // Log the error for debugging
    console.error('Error in createBatchAssignments:', error);
    next(error);
  }
};

/**
 * Get unreconciled assignments for a DSA agent
 */
exports.getUnreconciledAssignments = async (req, res, next) => {
  try {
    // Accept both dsa_agent_id and customer_id for backward compatibility
    const { dsa_agent_id, customer_id } = req.query;

    // Use customer_id if provided, otherwise use dsa_agent_id
    const dsaAgentId = customer_id || dsa_agent_id;

    if (!dsaAgentId) {
      throw new AppError(400, 'DSA agent ID is required');
    }

    console.log(`Getting unreconciled assignments for DSA agent ID: ${dsaAgentId}`);

    const assignments = await DsaStockAssignment.findAll({
      where: {
        customer_id: dsaAgentId,
        reconciled: false
      },
      include: [
        { model: Customer },
        { model: Branch },
        { model: Product }
      ]
    });

    console.log(`Found ${assignments.length} unreconciled assignments`);

    res.status(200).json({
      status: 'success',
      data: assignments
    });
  } catch (error) {
    console.error('Error in getUnreconciledAssignments:', error);
    next(error);
  }
};

/**
 * Reconcile multiple DSA stock assignments in a batch
 * Supports partial payments and updates payment status
 */
exports.reconcileBatchAssignments = async (req, res, next) => {
  const transaction = await sequelize.transaction();

  try {
    const {
      assignment_identifier,
      dsa_agent_id,
      branch_id,
      items,
      cash_received,
      paybill_amount,
      notes,
      is_partial_payment = false // New flag to indicate partial payment
    } = req.body;

    // Validate request
    if (!assignment_identifier || !dsa_agent_id || !branch_id || !items || !Array.isArray(items) || items.length === 0) {
      throw new AppError(400, 'Invalid request data');
    }

    // Allow branch_manager to reconcile assignments for any branch
    console.log('Branch manager permission check bypassed - allowing reconciliation for any branch');

    // Check if DSA agent exists (as a customer with is_dsa=true)
    const dsaAgent = await Customer.findOne({
      where: { id: dsa_agent_id, is_dsa: true }
    });

    if (!dsaAgent) {
      throw new AppError(404, 'DSA agent not found');
    }

    // Check if branch exists
    const branch = await Branch.findByPk(branch_id);

    if (!branch) {
      throw new AppError(404, 'Branch not found');
    }

    // Check if branch is headquarters (level=0)
    const isHeadquarters = branch.level === 0;

    // If not headquarters, check if DSA agent belongs to the specified branch
    if (!isHeadquarters && dsaAgent.branch_id && parseInt(dsaAgent.branch_id) !== parseInt(branch_id)) {
      throw new AppError(400, 'DSA agent does not belong to the specified branch. Only headquarters can reconcile stock for agents from other branches.');
    }

    // Get all assignments for this identifier
    // If partial payment, we don't require reconciled: false
    const whereClause = {
      assignment_identifier,
      customer_id: dsa_agent_id,
      branch_id
    };

    // Only filter by reconciled status if not a partial payment
    if (!is_partial_payment) {
      whereClause.reconciled = false;
    }

    const assignments = await DsaStockAssignment.findAll({
      where: whereClause,
      include: [
        { model: Product }
      ],
      transaction
    });

    if (assignments.length === 0) {
      throw new AppError(404, 'No assignments found for this identifier');
    }

    // Calculate totals
    let total_assigned = 0;
    let total_returned = 0;
    let total_sold = 0;
    let total_sale_amount = 0;

    // Create a map of assignment ID to item data for easy lookup
    const itemsMap = {};
    items.forEach(item => {
      itemsMap[item.id] = item;
    });

    // Prepare sale items array for creating a sale
    const saleItems = [];

    // Update each assignment
    for (const assignment of assignments) {
      const item = itemsMap[assignment.id];

      if (!item) {
        continue; // Skip if no data provided for this assignment
      }

      const quantity_returned = parseInt(item.quantity_returned || 0, 10);

      // Calculate sold quantity (assigned - returned)
      const quantity_sold = assignment.quantity_assigned - quantity_returned;

      // Update totals
      total_assigned += assignment.quantity_assigned;
      total_returned += quantity_returned;
      total_sold += quantity_sold;

      // If items were sold, add them to the sale items array
      if (quantity_sold > 0) {
        // Get the product's wholesale price
        const product = assignment.Product;
        if (!product) {
          throw new AppError(404, `Product not found for assignment ID ${assignment.id}`);
        }

        // Get the stock item to get the wholesale price
        const stockItem = await StockItem.findOne({
          where: {
            branch_id,
            product_id: assignment.product_id,
            deleted_at: null
          },
          attributes: ['default_wholesale_price', 'default_selling_price', 'default_buying_price'],
          transaction
        });

        // Use wholesale price (fallback to selling price if wholesale price is not set)
        const wholesalePrice = stockItem ? parseFloat(stockItem.default_wholesale_price || 0) : 0;
        const sellingPrice = stockItem ? parseFloat(stockItem.default_selling_price || 0) : (product.selling_price || 0);
        const buyingPrice = stockItem ? parseFloat(stockItem.default_buying_price || 0) : (product.buying_price || 0);

        // Use wholesale price if available, otherwise fall back to selling price
        const unit_price = wholesalePrice > 0 ? wholesalePrice : sellingPrice;
        const item_total = quantity_sold * unit_price;
        total_sale_amount += item_total;

        // Add to sale items array
        saleItems.push({
          product_id: assignment.product_id,
          quantity: quantity_sold,
          unit_price: unit_price,
          total_price: item_total,
          buying_price: buyingPrice,
          is_wholesale: true // Mark as a wholesale item
        });
      }

      // Calculate item value
      const wholesalePrice = stockItem ? parseFloat(stockItem.default_wholesale_price || 0) : 0;
      const itemValue = quantity_sold * (wholesalePrice > 0 ? wholesalePrice : sellingPrice);

      // Calculate payment values
      const cashAmount = parseFloat(cash_received) || 0;
      const paybillAmount = parseFloat(paybill_amount) || 0;
      const totalPayment = cashAmount + paybillAmount;

      // Calculate payment distribution for this item
      const paymentShare = totalPayment * (itemValue / total_sale_amount);

      // Update the assignment with payment information
      await assignment.update({
        quantity_returned,
        total_amount: itemValue,
        amount_paid: is_partial_payment ? (assignment.amount_paid || 0) + paymentShare : itemValue,
        balance: is_partial_payment ? itemValue - ((assignment.amount_paid || 0) + paymentShare) : 0,
        payment_status: is_partial_payment ?
          (itemValue <= ((assignment.amount_paid || 0) + paymentShare) ? 'FULLY_PAID' : 'PARTIALLY_PAID') :
          'FULLY_PAID',
        last_payment_date: new Date(),
        reconciled: !is_partial_payment, // Only mark as reconciled if not a partial payment
        reconciled_at: !is_partial_payment ? new Date() : null,
        last_updated_by: req.user?.id
      }, { transaction });
    }

    // Create reconciliation record only if it's not a partial payment
    let reconciliation = null;
    if (!is_partial_payment) {
      reconciliation = await DsaStockReconciliation.create({
        customer_id: dsa_agent_id, // Use customer_id instead of user_id
        total_assigned,
        total_sold,
        total_returned,
        cash_received: parseFloat(cash_received || 0),
        paybill_amount: parseFloat(paybill_amount || 0),
        notes,
        reconciled_at: new Date(),
        created_by: req.user?.id,
        last_updated_by: req.user?.id
      }, { transaction });
    }

    // Create payment record if amount is greater than 0
    let payment = null;
    const totalPaymentAmount = parseFloat(cash_received || 0) + parseFloat(paybill_amount || 0);
    if (totalPaymentAmount > 0) {
      payment = await DsaPayment.create({
        assignment_identifier,
        customer_id: dsa_agent_id,
        branch_id,
        cash_amount: parseFloat(cash_received || 0),
        paybill_amount: parseFloat(paybill_amount || 0),
        total_amount: totalPaymentAmount,
        payment_date: new Date(),
        notes,
        created_by: req.user?.id
      }, { transaction });
    }

    // DSA reconciliation should NOT create sales or register with KRA
    // The sale was already created and registered with KRA during the initial assignment
    // Reconciliation only updates assignment status and tracks payments

    // Commit the transaction
    await transaction.commit();

    // Return the reconciliation data and payment data
    res.status(200).json({
      status: 'success',
      data: {
        is_partial_payment,
        reconciliation,
        payment,
        assignments,
        total_amount: total_sale_amount,
        total_paid: totalPaymentAmount,
        balance: total_sale_amount - totalPaymentAmount
      }
    });
  } catch (error) {
    // Only roll back if the transaction is still active
    try {
      await transaction.rollback();
    } catch (rollbackError) {
      console.error('Error rolling back transaction:', rollbackError.message);
      // Continue with error handling even if rollback fails
    }

    // Check if it's a unique constraint violation
    if (error.name === 'SequelizeUniqueConstraintError') {
      console.error('Unique constraint violation:', error.message);

      try {
        // Start a new transaction
        const newTransaction = await sequelize.transaction();

        // Get all assignments for this identifier
        const assignments = await DsaStockAssignment.findAll({
          where: {
            assignment_identifier,
            user_id: dsa_agent_id,
            branch_id,
            reconciled: false
          },
          include: [
            { model: Product }
          ],
          transaction: newTransaction
        });

        if (assignments.length === 0) {
          throw new AppError(404, 'No unreconciled assignments found for this identifier');
        }

        // For reconciliation, we'll just return a message to try again
        // since reconciliation is a more complex process that involves
        // creating sales records and updating multiple related entities
        await newTransaction.rollback();
        return next(new AppError(400, 'Please try the reconciliation again. The system has updated the assignments.'));
      } catch (retryError) {
        console.error('Error while handling unique constraint violation:', retryError);

        // Try to roll back the new transaction if it exists
        try {
          if (newTransaction) {
            await newTransaction.rollback();
          }
        } catch (rollbackError) {
          console.error('Error rolling back retry transaction:', rollbackError.message);
        }

        return next(retryError);
      }
    }

    // Log the error for debugging
    console.error('Error in reconcileBatchAssignments:', error);
    next(error);
  }
};

/**
 * Get assignments grouped by assignment_identifier
 */
/**
 * Get a specific assignment by identifier
 */
exports.getAssignmentByIdentifier = async (req, res, next) => {
  try {
    const { identifier } = req.params;

    if (!identifier) {
      return next(new AppError(400, 'Assignment identifier is required'));
    }

    console.log(`Fetching assignment with identifier: ${identifier}`);

    // Get all assignments with this identifier
    const assignments = await DsaStockAssignment.findAll({
      where: { assignment_identifier: identifier },
      attributes: [
        'id', 'customer_id', 'user_id', 'branch_id', 'product_id',
        'quantity_assigned', 'quantity_returned', 'quantity_sold',
        'assignment_identifier', 'assigned_at', 'reconciled_at',
        'created_by', 'last_updated_by', 'reconciled', 'reconciliation_id',
        'payment_status', 'total_amount', 'amount_paid', 'balance',
        'last_payment_date', 'sale_id', 'created_at', 'updated_at'
      ],
      include: [
        { model: Customer },
        { model: User },
        { model: Branch },
        { model: Product }
      ]
    });

    if (!assignments || assignments.length === 0) {
      return next(new AppError(404, `Assignment with identifier ${identifier} not found`));
    }

    // Group the assignments into a single response object
    const groupedAssignment = {
      assignment_identifier: identifier,
      branch_id: assignments[0].branch_id,
      branch_name: assignments[0].Branch ? assignments[0].Branch.name : 'Unknown',
      dsa_agent_id: assignments[0].customer_id,
      dsa_agent_name: assignments[0].Customer ? assignments[0].Customer.name :
                     (assignments[0].User ? assignments[0].User.name : 'Unknown'),
      created_at: assignments[0].created_at,
      reconciled: true, // Will be updated below if any item is not reconciled
      reconciled_at: null, // Will be updated below
      items: []
    };

    // Add all items and update reconciled status
    assignments.forEach(assignment => {
      // Add this assignment to the items array
      groupedAssignment.items.push({
        ...assignment.toJSON(),
        Product: assignment.Product ? assignment.Product.toJSON() : null
      });

      // Update reconciled status
      if (!assignment.reconciled) {
        groupedAssignment.reconciled = false;
      } else if (assignment.reconciled_at) {
        // If this item is reconciled and has a reconciliation date, update the group's date
        if (!groupedAssignment.reconciled_at ||
            new Date(assignment.reconciled_at) > new Date(groupedAssignment.reconciled_at)) {
          groupedAssignment.reconciled_at = assignment.reconciled_at;
        }
      }
    });

    res.status(200).json({
      status: 'success',
      data: groupedAssignment
    });
  } catch (error) {
    console.error('Error in getAssignmentByIdentifier:', error);
    next(error);
  }
};

exports.getAssignmentsByIdentifier = async (req, res, next) => {
  try {
    const { branch_id } = req.query;

    // Build the where clause
    const whereClause = {};

    // If user is a branch_manager, restrict to their branch
    if (req.user && req.user.role_name === 'branch_manager' && req.user.branch_id) {
      whereClause.branch_id = req.user.branch_id;
      console.log(`Restricting to branch manager's branch: ${req.user.branch_id}`);
    }
    // Otherwise, use the branch_id from the query if provided
    else if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    // Get all assignments
    const allAssignments = await DsaStockAssignment.findAll({
      where: whereClause,
      attributes: [
        'id', 'customer_id', 'user_id', 'branch_id', 'product_id',
        'quantity_assigned', 'quantity_returned', 'quantity_sold',
        'assignment_identifier', 'assigned_at', 'reconciled_at',
        'created_by', 'last_updated_by', 'reconciled', 'reconciliation_id',
        'payment_status', 'total_amount', 'amount_paid', 'balance',
        'last_payment_date', 'sale_id', 'created_at', 'updated_at'
      ],
      include: [
        { model: Customer },
        { model: User },
        { model: Branch },
        { model: Product }
      ],
      order: [['created_at', 'DESC']]
    });

    // Group assignments by identifier
    const groupedAssignments = {};

    allAssignments.forEach(assignment => {
      const identifier = assignment.assignment_identifier;

      if (!identifier) return; // Skip assignments without an identifier

      if (!groupedAssignments[identifier]) {
        // Initialize the group with metadata from the first assignment
        groupedAssignments[identifier] = {
          assignment_identifier: identifier,
          branch_id: assignment.branch_id,
          branch_name: assignment.Branch ? assignment.Branch.name : 'Unknown',
          dsa_agent_id: assignment.customer_id, // Use customer_id instead of user_id
          dsa_agent_name: assignment.Customer ? assignment.Customer.name :
                         (assignment.User ? assignment.User.name : 'Unknown'),
          created_at: assignment.created_at,
          reconciled: assignment.reconciled,
          reconciled_at: assignment.reconciled_at,
          items: []
        };
      }

      // Add this assignment to the items array
      groupedAssignments[identifier].items.push({
        ...assignment.toJSON(),
        Product: assignment.Product ? assignment.Product.toJSON() : null
      });

      // Update reconciled status (if any item is not reconciled, the group is not reconciled)
      if (!assignment.reconciled) {
        groupedAssignments[identifier].reconciled = false;
      } else if (assignment.reconciled_at) {
        // If this item is reconciled and has a reconciliation date, update the group's date
        if (!groupedAssignments[identifier].reconciled_at ||
            new Date(assignment.reconciled_at) > new Date(groupedAssignments[identifier].reconciled_at)) {
          groupedAssignments[identifier].reconciled_at = assignment.reconciled_at;
        }
      }
    });

    // Convert the grouped object to an array
    const result = Object.values(groupedAssignments);

    res.status(200).json({
      status: 'success',
      data: result
    });
  } catch (error) {
    console.error('Error in getAssignmentsByIdentifier:', error);
    next(error);
  }
};
