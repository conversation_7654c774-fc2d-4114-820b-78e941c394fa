/**
 * DSA-related model associations
 */
const User = require('./user.model');
const Customer = require('./customer.model');
const DsaStockAssignment = require('./dsa-stock-assignment.model');
const DsaStockReconciliation = require('./dsa-stock-reconciliation.model');
const DsaPayment = require('./dsa-payment.model');
const Branch = require('./branch.model');
const Product = require('./product.model');
const Tenant = require('./tenant.model');
const Sale = require('./sale.model');
const PosSession = require('./pos-session.model');

function setupDsaAssociations() {
  // Customer associations with DSA-related models
  Customer.hasMany(DsaStockAssignment, { foreignKey: 'customer_id', as: 'DsaStockAssignments' });
  Customer.hasMany(DsaStockReconciliation, { foreignKey: 'customer_id', as: 'DsaStockReconciliations' });
  Customer.hasMany(DsaPayment, { foreignKey: 'customer_id', as: 'DsaPayments' });

  // Customer association with Branch and Tenant
  Customer.belongsTo(Branch, { foreignKey: 'branch_id' });
  Branch.hasMany(Customer, { foreignKey: 'branch_id' });

  Customer.belongsTo(Tenant, { foreignKey: 'tenant_id' });
  Tenant.hasMany(Customer, { foreignKey: 'tenant_id' });

  // DsaStockAssignment associations
  DsaStockAssignment.belongsTo(Customer, { foreignKey: 'customer_id' });
  DsaStockAssignment.belongsTo(User, { foreignKey: 'user_id' }); // Add association to User model
  DsaStockAssignment.belongsTo(Branch, { foreignKey: 'branch_id' });
  DsaStockAssignment.belongsTo(Product, { foreignKey: 'product_id' });
  DsaStockAssignment.belongsTo(Sale, { foreignKey: 'sale_id', as: 'Sale' }); // Add association to Sale model
  DsaStockAssignment.belongsTo(User, { foreignKey: 'created_by', as: 'Creator' });
  DsaStockAssignment.belongsTo(User, { foreignKey: 'last_updated_by', as: 'Updater' });

  // Sale associations with DSA Stock Assignment
  Sale.hasMany(DsaStockAssignment, { foreignKey: 'sale_id', as: 'DsaStockAssignments' });

  // DsaStockReconciliation associations
  DsaStockReconciliation.belongsTo(Customer, { foreignKey: 'customer_id', as: 'DsaCustomer' });
  DsaStockReconciliation.belongsTo(User, { foreignKey: 'created_by', as: 'ReconciliationCreator' });
  DsaStockReconciliation.belongsTo(User, { foreignKey: 'last_updated_by', as: 'ReconciliationUpdater' });

  // DsaPayment associations
  DsaPayment.belongsTo(Customer, { foreignKey: 'customer_id', as: 'DsaCustomer' });
  DsaPayment.belongsTo(Branch, { foreignKey: 'branch_id' });
  DsaPayment.belongsTo(PosSession, { foreignKey: 'pos_session_id' });
  DsaPayment.belongsTo(User, { foreignKey: 'created_by', as: 'Creator' });
  DsaPayment.belongsTo(User, { foreignKey: 'last_updated_by', as: 'Updater' });
}

module.exports = setupDsaAssociations;
