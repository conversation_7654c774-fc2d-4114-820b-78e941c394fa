const { DsaStockAssignment, User, Branch, Product, Role, StockItem, InventoryTransaction, sequelize } = require('../models');
const AppError = require('../utils/error');
const logger = require('../utils/logger');

/**
 * Reduce stock quantities for DSA assignment
 * @param {number} branchId - Branch ID
 * @param {number} productId - Product ID
 * @param {number} quantity - Quantity to reduce
 * @param {number} userId - User ID who created the assignment
 * @param {Object} transaction - Database transaction
 */
const reduceStockForDsaAssignment = async (branchId, productId, quantity, userId, transaction = null) => {
  try {
    // Find the stock item for this product in the branch
    const stockItem = await StockItem.findOne({
      where: {
        branch_id: branchId,
        product_id: productId,
        deleted_at: null
      },
      transaction
    });

    if (!stockItem) {
      throw new AppError(400, `No stock found for product ID ${productId} in branch ${branchId}`);
    }

    // Check if there's enough stock
    if (stockItem.quantity < quantity) {
      throw new AppError(400, `Insufficient stock for product ID ${productId}. Available: ${stockItem.quantity}, Requested: ${quantity}`);
    }

    // Reduce the stock quantity
    await stockItem.update({
      quantity: stockItem.quantity - quantity
    }, { transaction });

    // Create an inventory transaction record
    await InventoryTransaction.create({
      transaction_type: 'TRANSFER_OUT', // DSA assignment is like a transfer out
      reference_id: null, // We don't have a specific reference ID for DSA assignments
      reference_type: 'dsa_assignment',
      stock_item_id: stockItem.id,
      quantity: -quantity, // Negative quantity for outgoing stock
      unit_cost: stockItem.default_buying_price || 0,
      total_cost: (stockItem.default_buying_price || 0) * quantity,
      from_branch_id: branchId,
      to_branch_id: null, // DSA assignment doesn't have a destination branch
      notes: `DSA Assignment - Product ID: ${productId}`,
      created_by: userId,
      transaction_date: new Date()
    }, { transaction });

    logger.info(`Reduced stock for product ${productId} in branch ${branchId}: ${quantity} units`);

    return {
      success: true,
      previous_quantity: stockItem.quantity + quantity,
      new_quantity: stockItem.quantity,
      reduced_quantity: quantity
    };
  } catch (error) {
    logger.error(`Error reducing stock for DSA assignment: ${error.message}`);
    throw error;
  }
};

/**
 * Get all DSA stock assignments
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getAllDsaStockAssignments = async (req, res, next) => {
  try {
    const { user_id, branch_id, product_id } = req.query;

    const whereClause = {};

    if (user_id) {
      whereClause.user_id = user_id;
    }

    if (branch_id) {
      whereClause.branch_id = branch_id;
    }

    if (product_id) {
      whereClause.product_id = product_id;
    }

    // Log the query for debugging
    logger.info('Fetching DSA stock assignments with where clause:', whereClause);

    try {
      // Disabled query logging
      const dsaStockAssignments = await DsaStockAssignment.findAll({
        where: whereClause,
        attributes: [
          'id', 'user_id', 'branch_id', 'product_id', 'quantity_assigned',
          'quantity_returned', 'assigned_at', 'reconciled_at', 'created_by',
          'last_updated_by', 'reconciled', 'reconciliation_id', 'created_at', 'updated_at'
        ],
        include: [
          {
            model: User,
            attributes: ['id', 'name', 'phone', 'email']
          },
          {
            model: Branch,
            attributes: ['id', 'name', 'location']
          },
          {
            model: Product,
            attributes: ['id', 'name', 'sku', 'has_serial']
          }
        ]
      });

      return res.status(200).json(dsaStockAssignments);
    } catch (error) {
      logger.error(`Error in query: ${error.message}`);
      return next(error);
    }

  } catch (error) {
    logger.error(`Error fetching DSA stock assignments: ${error.message}`);
    next(error);
  }
};

/**
 * Get DSA stock assignment by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getDsaStockAssignmentById = async (req, res, next) => {
  try {
    const { id } = req.params;

    const dsaStockAssignment = await DsaStockAssignment.findByPk(id, {
      attributes: [
        'id', 'user_id', 'branch_id', 'product_id', 'quantity_assigned',
        'quantity_returned', 'assigned_at', 'reconciled_at', 'created_by',
        'last_updated_by', 'reconciled', 'reconciliation_id', 'created_at', 'updated_at'
      ],
      include: [
        {
          model: User,
          attributes: ['id', 'name', 'phone', 'email']
        },
        {
          model: Branch,
          attributes: ['id', 'name', 'location']
        },
        {
          model: Product,
          attributes: ['id', 'name', 'sku', 'has_serial']
        }
      ]
    });

    if (!dsaStockAssignment) {
      return next(new AppError(404, 'DSA stock assignment not found'));
    }

    return res.status(200).json(dsaStockAssignment);
  } catch (error) {
    logger.error(`Error fetching DSA stock assignment: ${error.message}`);
    next(error);
  }
};

/**
 * Create a new DSA stock assignment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const createDsaStockAssignment = async (req, res, next) => {
  try {
    const { user_id, branch_id, product_id, quantity_assigned } = req.body;

    // Validate required fields
    if (!user_id || !branch_id || !product_id || !quantity_assigned) {
      return next(new AppError(400, 'User ID, branch ID, product ID, and quantity assigned are required'));
    }

    // Check if DSA user exists
    const dsaUser = await User.findOne({
      where: {
        id: user_id,
        is_dsa: true,
        deleted_at: null
      }
    });

    // Log the user for debugging
    logger.info(`DSA user check: ${JSON.stringify(dsaUser ? dsaUser.toJSON() : null)}`);
    logger.info(`User ID: ${user_id}, is_dsa check`);

    if (!dsaUser) {
      return next(new AppError(404, 'DSA user not found'));
    }

    // Check if branch exists
    const branch = await Branch.findOne({
      where: { id: branch_id, deleted_at: null }
    });

    if (!branch) {
      return next(new AppError(404, 'Branch not found'));
    }

    // Check if product exists
    const product = await Product.findOne({
      where: { id: product_id, deleted_at: null }
    });

    if (!product) {
      return next(new AppError(404, 'Product not found'));
    }

    // Check if there's an existing assignment for this product
    const existingAssignment = await DsaStockAssignment.findOne({
      where: {
        user_id,
        product_id,
        reconciled: false // Only consider unreconciled assignments
      }
    });

    // If stock limit is set, check if the assignment would exceed the limit
    if (dsaUser.stock_limit !== null && dsaUser.stock_limit !== undefined) {
      // Get current total assigned stock for this DSA user with product information
      const currentAssignments = await DsaStockAssignment.findAll({
        where: {
          user_id,
          reconciled: false // Only consider unreconciled assignments
        },
        attributes: ['product_id', 'quantity_assigned', 'quantity_returned', 'quantity_sold']
      });

      // Get all product IDs from current assignments and the new product
      const productIds = new Set();
      currentAssignments.forEach(assignment => productIds.add(assignment.product_id));
      productIds.add(parseInt(product_id));

      // Get stock items for all products to get their wholesale prices
      const stockItems = await StockItem.findAll({
        where: {
          branch_id: branch_id,
          product_id: Array.from(productIds),
          deleted_at: null
        },
        attributes: ['product_id', 'default_wholesale_price', 'default_selling_price']
      });

      // Create a map of product ID to wholesale price (fallback to selling price if wholesale price is not set)
      const productPriceMap = {};
      stockItems.forEach(item => {
        // Use wholesale price for DSA assignments, fallback to selling price if wholesale price is not set
        const wholesalePrice = parseFloat(item.default_wholesale_price || 0);
        const sellingPrice = parseFloat(item.default_selling_price || 0);
        productPriceMap[item.product_id] = wholesalePrice > 0 ? wholesalePrice : sellingPrice;
      });

      // Calculate current total value of assigned stock
      let currentTotalValue = 0;
      currentAssignments.forEach(assignment => {
        const currentQuantity = assignment.quantity_assigned - (assignment.quantity_returned || 0) - (assignment.quantity_sold || 0);
        const price = productPriceMap[assignment.product_id] || 0;
        currentTotalValue += currentQuantity * price;
      });

      // Get the price of the product being assigned
      const productPrice = productPriceMap[parseInt(product_id)] || 0;

      // Calculate the value of the new assignment
      const newAssignmentValue = parseInt(quantity_assigned) * productPrice;

      // Calculate new total value if this assignment is added
      const newTotalValue = currentTotalValue + newAssignmentValue;

      // Format currency values for logging and error messages
      const formatCurrency = (value) => `KSh ${value.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')}`;
      const formattedCurrentValue = formatCurrency(currentTotalValue);
      const formattedNewAssignmentValue = formatCurrency(newAssignmentValue);
      const formattedNewTotalValue = formatCurrency(newTotalValue);
      const formattedStockLimit = formatCurrency(parseFloat(dsaUser.stock_limit));

      // Log for debugging
      logger.info(`Stock value check: Current: ${formattedCurrentValue}, New: ${formattedNewAssignmentValue}, Total: ${formattedNewTotalValue}, Limit: ${formattedStockLimit}`);

      // Check if new total value exceeds the limit
      if (newTotalValue > parseFloat(dsaUser.stock_limit)) {
        return next(new AppError(400, `Assignment would exceed DSA agent's stock value limit of ${formattedStockLimit}. Current value: ${formattedCurrentValue}, Requested: ${formattedNewAssignmentValue}, New total: ${formattedNewTotalValue}`));
      }
    }

    // If there's an existing assignment, update it instead of creating a new one
    if (existingAssignment) {
      // Reduce stock for the additional quantity being assigned
      await reduceStockForDsaAssignment(
        branch_id,
        product_id,
        parseInt(quantity_assigned),
        req.user ? req.user.id : null
      );

      // Update the existing assignment by adding the new quantity
      await existingAssignment.update({
        quantity_assigned: existingAssignment.quantity_assigned + parseInt(quantity_assigned),
        last_updated_by: req.user ? req.user.id : null
      });

      return res.status(200).json(existingAssignment);
    }

    // Reduce stock for the new assignment
    await reduceStockForDsaAssignment(
      branch_id,
      product_id,
      parseInt(quantity_assigned),
      req.user ? req.user.id : null
    );

    // Create a new DSA stock assignment
    const dsaStockAssignment = await DsaStockAssignment.create({
      user_id,
      branch_id,
      product_id,
      quantity_assigned,
      quantity_returned: 0,
      created_by: req.user ? req.user.id : null,
      last_updated_by: req.user ? req.user.id : null
    });

    return res.status(201).json(dsaStockAssignment);
  } catch (error) {
    logger.error(`Error creating DSA stock assignment: ${error.message}`);
    next(error);
  }
};

/**
 * Update a DSA stock assignment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const updateDsaStockAssignment = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { quantity_assigned, quantity_returned, reconciled_at, reconciled } = req.body;

    // Find DSA stock assignment
    const dsaStockAssignment = await DsaStockAssignment.findByPk(id);

    if (!dsaStockAssignment) {
      return next(new AppError(404, 'DSA stock assignment not found'));
    }

    // Update DSA stock assignment
    await dsaStockAssignment.update({
      quantity_assigned: quantity_assigned !== undefined ? quantity_assigned : dsaStockAssignment.quantity_assigned,
      quantity_returned: quantity_returned !== undefined ? quantity_returned : dsaStockAssignment.quantity_returned,
      reconciled_at: reconciled_at !== undefined ? reconciled_at : dsaStockAssignment.reconciled_at,
      reconciled: reconciled !== undefined ? reconciled : dsaStockAssignment.reconciled,
      last_updated_by: req.user ? req.user.id : null
    });

    return res.status(200).json(dsaStockAssignment);
  } catch (error) {
    logger.error(`Error updating DSA stock assignment: ${error.message}`);
    next(error);
  }
};

/**
 * Record stock return
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const recordStockReturn = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { quantity_returned } = req.body;

    if (!quantity_returned || quantity_returned <= 0) {
      return next(new AppError(400, 'Quantity returned must be a positive number'));
    }

    // Find DSA stock assignment
    const dsaStockAssignment = await DsaStockAssignment.findByPk(id);

    if (!dsaStockAssignment) {
      return next(new AppError(404, 'DSA stock assignment not found'));
    }

    // Calculate new total returned
    const newTotalReturned = dsaStockAssignment.quantity_returned + quantity_returned;

    // Check if return quantity is valid
    if (newTotalReturned > dsaStockAssignment.quantity_assigned) {
      return next(new AppError(400, 'Return quantity exceeds assigned quantity'));
    }

    // Update DSA stock assignment
    await dsaStockAssignment.update({
      quantity_returned: newTotalReturned,
      last_updated_by: req.user ? req.user.id : null
    });

    return res.status(200).json(dsaStockAssignment);
  } catch (error) {
    logger.error(`Error recording stock return: ${error.message}`);
    next(error);
  }
};

/**
 * Delete a DSA stock assignment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const deleteDsaStockAssignment = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find DSA stock assignment
    const dsaStockAssignment = await DsaStockAssignment.findByPk(id);

    if (!dsaStockAssignment) {
      return next(new AppError(404, 'DSA stock assignment not found'));
    }

    // Delete DSA stock assignment
    await dsaStockAssignment.destroy();

    return res.status(200).json({
      message: 'DSA stock assignment deleted successfully'
    });
  } catch (error) {
    logger.error(`Error deleting DSA stock assignment: ${error.message}`);
    next(error);
  }
};

module.exports = {
  getAllDsaStockAssignments,
  getDsaStockAssignmentById,
  createDsaStockAssignment,
  updateDsaStockAssignment,
  recordStockReturn,
  deleteDsaStockAssignment
};
