const { DataTypes } = require('sequelize');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // First, check if sale_type column exists and what values it has
    const tableDescription = await queryInterface.describeTable('sales');

    if (tableDescription.sale_type) {
      // Column exists, modify it to add new enum values
      await queryInterface.changeColumn('sales', 'sale_type', {
        type: DataTypes.ENUM('normal', 'dsa', 'corporate', 'dsa_assignment', 'dsa_customer_sale'),
        allowNull: false,
        defaultValue: 'normal',
        comment: 'Type of sale: normal (POS sale), dsa (legacy DSA), corporate (corporate sale), dsa_assignment (stock assigned to DSA), dsa_customer_sale (DSA sells to customer)'
      });
    } else {
      // Column doesn't exist, create it
      await queryInterface.addColumn('sales', 'sale_type', {
        type: DataTypes.ENUM('normal', 'dsa', 'corporate', 'dsa_assignment', 'dsa_customer_sale'),
        allowNull: false,
        defaultValue: 'normal',
        comment: 'Type of sale: normal (POS sale), dsa (legacy DSA), corporate (corporate sale), dsa_assignment (stock assigned to DSA), dsa_customer_sale (DSA sells to customer)'
      });
    }

    // Add assignment_identifier field if it doesn't exist
    if (!tableDescription.assignment_identifier) {
      await queryInterface.addColumn('sales', 'assignment_identifier', {
        type: DataTypes.STRING(100),
        allowNull: true,
        comment: 'DSA assignment identifier for linking sales to assignments'
      });
    }

    // Update existing DSA sales to have proper sale_type
    await queryInterface.sequelize.query(`
      UPDATE sales
      SET sale_type = CASE
        WHEN is_dsa = true AND payment_method_id = 5 THEN 'dsa_assignment'
        WHEN is_dsa = true AND payment_method_id != 5 THEN 'dsa_customer_sale'
        WHEN sale_type = 'dsa' THEN 'dsa_customer_sale'
        ELSE 'normal'
      END
      WHERE sale_type IN ('normal', 'dsa', 'corporate') OR sale_type IS NULL
    `);

    // Add indexes for better query performance
    try {
      await queryInterface.addIndex('sales', ['sale_type'], {
        name: 'idx_sales_sale_type'
      });
    } catch (error) {
      console.log('Index idx_sales_sale_type already exists or failed to create');
    }

    try {
      await queryInterface.addIndex('sales', ['assignment_identifier'], {
        name: 'idx_sales_assignment_identifier'
      });
    } catch (error) {
      console.log('Index idx_sales_assignment_identifier already exists or failed to create');
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes
    try {
      await queryInterface.removeIndex('sales', 'idx_sales_assignment_identifier');
    } catch (error) {
      console.log('Index idx_sales_assignment_identifier does not exist');
    }

    try {
      await queryInterface.removeIndex('sales', 'idx_sales_sale_type');
    } catch (error) {
      console.log('Index idx_sales_sale_type does not exist');
    }

    // Revert sale_type to original enum values
    await queryInterface.changeColumn('sales', 'sale_type', {
      type: DataTypes.ENUM('normal', 'dsa', 'corporate'),
      allowNull: false,
      defaultValue: 'normal',
      comment: 'Type of sale: normal (normal POS sale), dsa (DSA sale), or corporate (corporate sale)'
    });

    // Remove assignment_identifier column
    try {
      await queryInterface.removeColumn('sales', 'assignment_identifier');
    } catch (error) {
      console.log('Column assignment_identifier does not exist');
    }
  }
};
