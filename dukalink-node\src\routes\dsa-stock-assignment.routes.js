const express = require('express');
const router = express.Router();
const dsaStockAssignmentController = require('../controllers/dsa-stock-assignment.controller');
const dsaStockAssignmentBatchController = require('../controllers/dsa-stock-assignment-batch.controller');
const dsaStockAssignmentBatchReturnController = require('../controllers/dsa-stock-assignment-batch-return.controller');
const dsaPaymentController = require('../controllers/dsa-payment.controller');
const { authenticate } = require('../middleware/auth');
const rbac = require('../middleware/rbac.middleware');

/**
 * @swagger
 * components:
 *   schemas:
 *     DsaStockAssignment:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: The DSA stock assignment ID
 *         user_id:
 *           type: integer
 *           description: The DSA user ID
 *         branch_id:
 *           type: integer
 *           description: The branch ID
 *         product_id:
 *           type: integer
 *           description: The product ID
 *         quantity_assigned:
 *           type: integer
 *           description: The quantity assigned
 *         quantity_returned:
 *           type: integer
 *           description: The quantity returned
 *         assigned_at:
 *           type: string
 *           format: date-time
 *           description: The date and time when the stock was assigned
 *         reconciled_at:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           description: The date and time when the stock was reconciled
 *         created_by:
 *           type: integer
 *           nullable: true
 *           description: The ID of the user who created the record
 *         last_updated_by:
 *           type: integer
 *           nullable: true
 *           description: The ID of the user who last updated the record
 *       example:
 *         id: 1
 *         user_id: 1
 *         branch_id: 1
 *         product_id: 1
 *         quantity_assigned: 10
 *         quantity_returned: 0
 *         assigned_at: 2023-01-01T00:00:00.000Z
 *         reconciled_at: null
 *         created_by: 1
 *         last_updated_by: 1
 */

/**
 * @swagger
 * /dsa-stock-assignments:
 *   get:
 *     summary: Get all DSA stock assignments
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: user_id
 *         schema:
 *           type: integer
 *         description: Filter by DSA user ID
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         description: Filter by branch ID
 *       - in: query
 *         name: product_id
 *         schema:
 *           type: integer
 *         description: Filter by product ID
 *     responses:
 *       200:
 *         description: List of DSA stock assignments
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/DsaStockAssignment'
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/', authenticate, rbac.checkPermission('dsa-stock-assignments', 'read'), dsaStockAssignmentController.getAllDsaStockAssignments);

/**
 * @swagger
 * /dsa-stock-assignments/unreconciled:
 *   get:
 *     summary: Get unreconciled assignments for a DSA agent
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: dsa_agent_id
 *         schema:
 *           type: integer
 *         required: true
 *         description: The ID of the DSA agent
 *     responses:
 *       200:
 *         description: Unreconciled assignments retrieved successfully
 *       400:
 *         description: DSA agent ID is required
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/unreconciled', authenticate, rbac.checkPermission('unreconciled', 'read'), dsaStockAssignmentBatchController.getUnreconciledAssignments);

/**
 * @swagger
 * /dsa-stock-assignments/by-identifier:
 *   get:
 *     summary: Get assignments grouped by assignment_identifier
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: branch_id
 *         schema:
 *           type: integer
 *         description: Filter by branch ID
 *     responses:
 *       200:
 *         description: Assignments grouped by identifier retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/by-identifier', authenticate, rbac.checkPermission('by-identifier', 'read'), dsaStockAssignmentBatchController.getAssignmentsByIdentifier);

/**
 * @swagger
 * /dsa-stock-assignments/by-identifier/{identifier}:
 *   get:
 *     summary: Get assignment by identifier
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: identifier
 *         required: true
 *         schema:
 *           type: string
 *         description: Assignment identifier
 *     responses:
 *       200:
 *         description: Assignment details
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Assignment not found
 *       500:
 *         description: Server error
 */
router.get('/by-identifier/:identifier', authenticate, rbac.checkPermission('by-identifier', 'read'), dsaStockAssignmentBatchController.getAssignmentByIdentifier);

/**
 * @swagger
 * /dsa-stock-assignments/{id}:
 *   get:
 *     summary: Get DSA stock assignment by ID
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: DSA stock assignment ID
 *     responses:
 *       200:
 *         description: DSA stock assignment details
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DsaStockAssignment'
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA stock assignment not found
 *       500:
 *         description: Server error
 */
router.get('/:id', authenticate, rbac.checkPermission('dsa-stock-assignments', 'read'), dsaStockAssignmentController.getDsaStockAssignmentById);

/**
 * @swagger
 * /dsa-stock-assignments:
 *   post:
 *     summary: Create a new DSA stock assignment
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - branch_id
 *               - product_id
 *               - quantity_assigned
 *             properties:
 *               user_id:
 *                 type: integer
 *                 description: The DSA user ID
 *               branch_id:
 *                 type: integer
 *               product_id:
 *                 type: integer
 *               quantity_assigned:
 *                 type: integer
 *     responses:
 *       201:
 *         description: DSA stock assignment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DsaStockAssignment'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', authenticate, rbac.checkPermission('dsa-stock-assignments', 'create'), dsaStockAssignmentController.createDsaStockAssignment);

/**
 * @swagger
 * /dsa-stock-assignments/batch:
 *   post:
 *     summary: Create multiple DSA stock assignments in a batch
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - dsa_agent_id
 *               - branch_id
 *               - items
 *             properties:
 *               dsa_agent_id:
 *                 type: integer
 *                 description: The ID of the DSA agent
 *               branch_id:
 *                 type: integer
 *                 description: The ID of the branch
 *               items:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - product_id
 *                     - quantity_assigned
 *                   properties:
 *                     product_id:
 *                       type: integer
 *                       description: The ID of the product
 *                     quantity_assigned:
 *                       type: integer
 *                       description: The quantity to assign
 *               assignment_identifier:
 *                 type: string
 *                 description: Optional identifier for the assignment batch
 *     responses:
 *       201:
 *         description: DSA stock assignments created successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA agent, branch, or product not found
 *       500:
 *         description: Server error
 */
router.post('/batch', authenticate, rbac.checkPermission('batch', 'read'), dsaStockAssignmentBatchController.createBatchAssignments);

/**
 * @swagger
 * /dsa-stock-assignments/reconcile:
 *   post:
 *     summary: Reconcile multiple DSA stock assignments in a batch
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assignment_identifier
 *               - dsa_agent_id
 *               - branch_id
 *               - items
 *             properties:
 *               assignment_identifier:
 *                 type: string
 *                 description: The identifier for the batch of assignments
 *               dsa_agent_id:
 *                 type: integer
 *                 description: The ID of the DSA agent
 *               branch_id:
 *                 type: integer
 *                 description: The ID of the branch
 *               items:
 *                 type: array
 *                 description: The items to reconcile
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: The ID of the assignment
 *                     quantity_returned:
 *                       type: integer
 *                       description: The quantity returned
 *               cash_received:
 *                 type: number
 *                 description: The cash amount received from the agent
 *               paybill_amount:
 *                 type: number
 *                 description: The amount paid through paybill
 *               notes:
 *                 type: string
 *                 description: Additional notes about the reconciliation
 *     responses:
 *       200:
 *         description: Assignments reconciled successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA agent or branch not found
 *       500:
 *         description: Server error
 */
router.post('/reconcile', authenticate, rbac.checkPermission('reconcile', 'read'), dsaStockAssignmentBatchController.reconcileBatchAssignments);

/**
 * @swagger
 * /dsa-stock-assignments/return-batch:
 *   post:
 *     summary: Return multiple DSA stock assignments in a batch
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assignment_identifier
 *               - dsa_agent_id
 *               - branch_id
 *               - items
 *             properties:
 *               assignment_identifier:
 *                 type: string
 *                 description: The identifier for the batch of assignments
 *               dsa_agent_id:
 *                 type: integer
 *                 description: The ID of the DSA agent
 *               branch_id:
 *                 type: integer
 *                 description: The ID of the branch
 *               items:
 *                 type: array
 *                 description: Array of items to return
 *                 items:
 *                   type: object
 *                   required:
 *                     - id
 *                     - product_id
 *                     - quantity_returned
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: The ID of the assignment
 *                     product_id:
 *                       type: integer
 *                       description: The ID of the product
 *                     quantity_returned:
 *                       type: integer
 *                       description: The quantity being returned
 *               notes:
 *                 type: string
 *                 description: Additional notes about the return
 *     responses:
 *       200:
 *         description: Items returned successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA agent or branch not found
 *       500:
 *         description: Server error
 */
// Allow branch managers to access the return-batch endpoint
router.post('/return-batch', authenticate, rbac.checkPermission('return-batch', 'create'), dsaStockAssignmentBatchReturnController.returnBatchAssignments);

/**
 * @swagger
 * /dsa-stock-assignments/{id}:
 *   put:
 *     summary: Update a DSA stock assignment
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: DSA stock assignment ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               quantity_assigned:
 *                 type: integer
 *               quantity_returned:
 *                 type: integer
 *               reconciled_at:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       200:
 *         description: DSA stock assignment updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DsaStockAssignment'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA stock assignment not found
 *       500:
 *         description: Server error
 */
router.put('/:id', authenticate, rbac.checkPermission('dsa-stock-assignments', 'update'), dsaStockAssignmentController.updateDsaStockAssignment);

/**
 * @swagger
 * /dsa-stock-assignments/{id}/return:
 *   post:
 *     summary: Record stock return from DSA user
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: DSA stock assignment ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - quantity_returned
 *             properties:
 *               quantity_returned:
 *                 type: integer
 *     responses:
 *       200:
 *         description: Stock return recorded successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/DsaStockAssignment'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA stock assignment not found
 *       500:
 *         description: Server error
 */
router.post('/:id/return', authenticate, rbac.checkPermission('return-batch', 'create'), dsaStockAssignmentController.recordStockReturn);

/**
 * @swagger
 * /dsa-stock-assignments/{id}:
 *   delete:
 *     summary: Delete a DSA stock assignment
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: DSA stock assignment ID
 *     responses:
 *       200:
 *         description: DSA stock assignment deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA stock assignment not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', authenticate, rbac.checkPermission('dsa-stock-assignments', 'delete'), dsaStockAssignmentController.deleteDsaStockAssignment);

/**
 * @swagger
 * /dsa-stock-assignments/payment:
 *   post:
 *     summary: Create a new DSA payment (alternative endpoint)
 *     description: Record a payment for a DSA stock assignment
 *     tags: [DSA Stock Assignments]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - assignment_identifier
 *               - customer_id
 *               - branch_id
 *             properties:
 *               assignment_identifier:
 *                 type: string
 *                 description: The identifier of the assignment batch
 *               customer_id:
 *                 type: integer
 *                 description: The ID of the DSA agent (customer)
 *               branch_id:
 *                 type: integer
 *                 description: The ID of the branch
 *               cash_amount:
 *                 type: number
 *                 description: The amount paid in cash
 *               paybill_amount:
 *                 type: number
 *                 description: The amount paid via paybill
 *               notes:
 *                 type: string
 *                 description: Additional notes about the payment
 *     responses:
 *       201:
 *         description: Payment created successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: DSA agent or branch not found
 *       500:
 *         description: Server error
 */
router.post('/payment', authenticate, rbac.checkPermission('dsa', 'create'), dsaPaymentController.createPayment);

module.exports = router;
