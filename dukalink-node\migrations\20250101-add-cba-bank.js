'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add CBA bank to the banks table
    await queryInterface.bulkInsert('banks', [
      {
        tenant_id: 1,
        name: 'CBA Bank',
        code: 'CBA',
        type: 'bank',
        status: 'active',
        created_by: 1,
        last_updated_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        tenant_id: 1,
        name: 'Commercial Bank of Africa',
        code: 'CBA',
        type: 'bank',
        status: 'active',
        created_by: 1,
        last_updated_by: 1,
        created_at: new Date(),
        updated_at: new Date()
      }
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    // Remove CBA banks
    await queryInterface.bulkDelete('banks', {
      code: 'CBA'
    });
  }
};
