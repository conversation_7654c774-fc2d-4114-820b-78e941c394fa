'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Add sale_id column to dsa_stock_assignments table
    await queryInterface.addColumn('dsa_stock_assignments', 'sale_id', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'sales',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      comment: 'Reference to the sale that caused the stock assignment update'
    });

    // Add index for performance
    await queryInterface.addIndex('dsa_stock_assignments', ['sale_id'], {
      name: 'idx_dsa_stock_assignments_sale_id'
    });

    // Add composite index for better query performance
    await queryInterface.addIndex('dsa_stock_assignments', ['customer_id', 'product_id', 'sale_id'], {
      name: 'idx_dsa_assignments_customer_product_sale'
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Remove indexes first
    await queryInterface.removeIndex('dsa_stock_assignments', 'idx_dsa_assignments_customer_product_sale');
    await queryInterface.removeIndex('dsa_stock_assignments', 'idx_dsa_stock_assignments_sale_id');
    
    // Remove the column
    await queryInterface.removeColumn('dsa_stock_assignments', 'sale_id');
  }
};
