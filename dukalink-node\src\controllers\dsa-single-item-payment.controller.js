const DsaSingleItemPaymentService = require('../services/DsaSingleItemPaymentService');
const AppError = require('../utils/error');
const logger = require('../utils/logger');

/**
 * Process payment for a single DSA item
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.processSingleItemPayment = async (req, res, next) => {
  try {
    const {
      assignment_identifier,
      dsa_agent_id,
      customer_id,
      branch_id,
      item_id,
      payment_quantity,
      cash_amount,
      paybill_amount,
      notes
    } = req.body;

    // Validate request data
    const validation = DsaSingleItemPaymentService.validatePaymentData(req.body);
    
    if (!validation.isValid) {
      return next(new AppError(400, `Validation failed: ${validation.errors.join(', ')}`));
    }

    // Log warnings if any
    if (validation.warnings.length > 0) {
      logger.warn(`Payment warnings: ${validation.warnings.join(', ')}`);
    }

    // Prepare payment data
    const paymentData = {
      assignment_identifier,
      dsa_agent_id: dsa_agent_id || customer_id, // Support both field names
      customer_id: dsa_agent_id || customer_id,
      branch_id,
      item_id,
      payment_quantity: parseInt(payment_quantity),
      cash_amount: parseFloat(cash_amount || 0),
      paybill_amount: parseFloat(paybill_amount || 0),
      notes,
      created_by: req.user?.id
    };

    logger.info(`Processing single item payment: ${JSON.stringify({
      assignment_identifier,
      item_id,
      payment_quantity: paymentData.payment_quantity,
      total_amount: paymentData.cash_amount + paymentData.paybill_amount
    })}`);

    // Process the payment
    const result = await DsaSingleItemPaymentService.processSingleItemPayment(paymentData);

    res.status(200).json({
      success: true,
      message: 'Single item payment processed successfully',
      data: result
    });

  } catch (error) {
    logger.error(`Error processing single item payment: ${error.message}`);
    next(error);
  }
};

/**
 * Get unpaid items for an assignment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUnpaidItems = async (req, res, next) => {
  try {
    const { assignment_identifier } = req.params;

    if (!assignment_identifier) {
      return next(new AppError(400, 'Assignment identifier is required'));
    }

    logger.info(`Getting unpaid items for assignment: ${assignment_identifier}`);

    const unpaidItems = await DsaSingleItemPaymentService.getUnpaidItems(assignment_identifier);

    res.status(200).json({
      success: true,
      data: {
        assignment_identifier,
        unpaid_items: unpaidItems,
        count: unpaidItems.length
      }
    });

  } catch (error) {
    logger.error(`Error getting unpaid items: ${error.message}`);
    next(error);
  }
};

/**
 * Get payment summary for an assignment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getPaymentSummary = async (req, res, next) => {
  try {
    const { assignment_identifier } = req.params;

    if (!assignment_identifier) {
      return next(new AppError(400, 'Assignment identifier is required'));
    }

    logger.info(`Getting payment summary for assignment: ${assignment_identifier}`);

    const summary = await DsaSingleItemPaymentService.getPaymentSummary(assignment_identifier);

    res.status(200).json({
      success: true,
      data: summary
    });

  } catch (error) {
    logger.error(`Error getting payment summary: ${error.message}`);
    next(error);
  }
};

/**
 * Validate single item payment before processing
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.validateSingleItemPayment = async (req, res, next) => {
  try {
    const {
      assignment_identifier,
      item_id,
      payment_quantity,
      cash_amount,
      paybill_amount
    } = req.body;

    // Basic validation
    const validation = DsaSingleItemPaymentService.validatePaymentData(req.body);
    
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: validation.errors,
        warnings: validation.warnings
      });
    }

    // Get the specific item to validate against
    const unpaidItems = await DsaSingleItemPaymentService.getUnpaidItems(assignment_identifier);
    const targetItem = unpaidItems.find(item => item.id === parseInt(item_id));

    if (!targetItem) {
      return res.status(404).json({
        success: false,
        message: 'Item not found or already fully paid'
      });
    }

    // Validate payment quantity
    const requestedQuantity = parseInt(payment_quantity);
    if (requestedQuantity > targetItem.max_payable_quantity) {
      return res.status(400).json({
        success: false,
        message: `Payment quantity exceeds maximum payable quantity (${targetItem.max_payable_quantity})`
      });
    }

    // Validate payment amount
    const expectedAmount = requestedQuantity * targetItem.unit_price;
    const totalPayment = validation.total_payment_amount;
    
    if (Math.abs(totalPayment - expectedAmount) > 0.01) {
      return res.status(400).json({
        success: false,
        message: `Payment amount mismatch. Expected: ${expectedAmount}, Received: ${totalPayment}`
      });
    }

    res.status(200).json({
      success: true,
      message: 'Payment validation successful',
      data: {
        item_details: {
          id: targetItem.id,
          product_name: targetItem.Product?.name || 'Unknown Product',
          unit_price: targetItem.unit_price,
          max_payable_quantity: targetItem.max_payable_quantity,
          remaining_value: targetItem.remaining_value
        },
        payment_details: {
          requested_quantity: requestedQuantity,
          expected_amount: expectedAmount,
          total_payment: totalPayment,
          cash_amount: parseFloat(cash_amount || 0),
          paybill_amount: parseFloat(paybill_amount || 0)
        },
        validation: {
          is_valid: true,
          warnings: validation.warnings
        }
      }
    });

  } catch (error) {
    logger.error(`Error validating single item payment: ${error.message}`);
    next(error);
  }
};

/**
 * Get item payment history
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getItemPaymentHistory = async (req, res, next) => {
  try {
    const { assignment_identifier, item_id } = req.params;

    if (!assignment_identifier || !item_id) {
      return next(new AppError(400, 'Assignment identifier and item ID are required'));
    }

    logger.info(`Getting payment history for item ${item_id} in assignment ${assignment_identifier}`);

    // Get payment summary which includes recent payments
    const summary = await DsaSingleItemPaymentService.getPaymentSummary(assignment_identifier);
    
    // Filter payments that might be related to this specific item
    // Note: This is a simplified approach since payments are tracked at assignment level
    const itemPayments = summary.recent_payments.filter(payment => 
      payment.notes && payment.notes.includes(item_id)
    );

    res.status(200).json({
      success: true,
      data: {
        assignment_identifier,
        item_id: parseInt(item_id),
        payments: itemPayments,
        total_payments: itemPayments.length,
        total_amount_paid: itemPayments.reduce((sum, payment) => sum + payment.total_amount, 0)
      }
    });

  } catch (error) {
    logger.error(`Error getting item payment history: ${error.message}`);
    next(error);
  }
};
