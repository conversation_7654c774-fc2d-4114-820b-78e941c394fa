import { toast } from "sonner";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
import apiClient from "@/lib/api-client";
import { PaginatedResponse } from "@/types/api";

export interface Supplier {
  id: number;
  tenant_id: number;
  name: string;
  krapin: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  po_box?: string;
  city?: string;
  country?: string;
  status: "active" | "inactive";
  created_at: string;
  updated_at: string;
}

export interface CreateSupplierRequest {
  name: string;
  krapin: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  po_box?: string;
  city?: string;
  country?: string;
}

export interface UpdateSupplierRequest {
  name?: string;
  krapin?: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  po_box?: string;
  city?: string;
  country?: string;
  status?: "active" | "inactive";
}

// API Functions
export const suppliersApi = {
  getSuppliers: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
    search?: string;
  }): Promise<PaginatedResponse<Supplier>> => {
    console.log("Fetching suppliers with params:", params);
    try {
      const response = await apiClient.get("/suppliers", { params });
      console.log("Suppliers API response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      throw error;
    }
  },

  getSupplierById: async (id: number): Promise<{ data: Supplier }> => {
    const response = await apiClient.get(`/suppliers/${id}`);
    return response.data;
  },

  createSupplier: async (
    data: CreateSupplierRequest
  ): Promise<{ data: Supplier }> => {
    const response = await apiClient.post("/suppliers", data);
    return response.data;
  },

  updateSupplier: async (
    id: number,
    data: UpdateSupplierRequest
  ): Promise<{ data: Supplier }> => {
    const response = await apiClient.put(`/suppliers/${id}`, data);
    return response.data;
  },

  deleteSupplier: async (id: number): Promise<{ message: string }> => {
    const response = await apiClient.delete(`/suppliers/${id}`);
    return response.data;
  },
};

// Hooks
export const useSuppliers = (params?: {
  page?: number;
  limit?: number;
  status?: string;
  search?: string;
}) => {
  console.log("useSuppliers hook called with params:", params);
  return useQuery({
    queryKey: ["suppliers", params],
    queryFn: () => suppliersApi.getSuppliers(params),
    onSuccess: (data) => {
      console.log("useSuppliers query succeeded with data:", data);
    },
    onError: (error) => {
      console.error("useSuppliers query failed with error:", error);
    },
  });
};

export const useSupplier = (id: number) => {
  return useQuery({
    queryKey: ["supplier", id],
    queryFn: () => suppliersApi.getSupplierById(id),
    enabled: !!id,
  });
};

export const useCreateSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSupplierRequest) => suppliersApi.createSupplier(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["suppliers"] });
      toast.success("Supplier created successfully");
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to create supplier"
      );
    },
  });
};

export const useUpdateSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: number;
      data: UpdateSupplierRequest;
    }) => suppliersApi.updateSupplier(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["suppliers"] });
      queryClient.invalidateQueries({ queryKey: ["supplier", id] });
      toast.success("Supplier updated successfully");
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to update supplier"
      );
    },
  });
};

export const useDeleteSupplier = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => suppliersApi.deleteSupplier(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["suppliers"] });
      toast.success("Supplier deleted successfully");
    },
    onError: (error: any) => {
      toast.error(
        error.response?.data?.message || "Failed to delete supplier"
      );
    },
  });
};
